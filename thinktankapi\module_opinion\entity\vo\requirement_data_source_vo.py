from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Dict, Any
from module_admin.entity.vo.common_vo import PageQueryModel
from module_admin.annotation.pydantic_annotation import as_query


class RequirementDataSourceModel(BaseModel):
    """
    需求数据源模型
    """
    model_config = ConfigDict(from_attributes=True)

    id: Optional[int] = Field(default=None, description='数据源ID')
    requirement_id: int = Field(..., description='需求ID')
    source_type: str = Field(..., description='数据源类型')
    source_name: Optional[str] = Field(default=None, description='数据源名称')
    source_url: Optional[str] = Field(default=None, description='数据源URL')
    source_config: Optional[str] = Field(default=None, description='数据源配置（JSON格式）')
    is_selected: Optional[int] = Field(default=1, description='是否选中（0-否，1-是）')
    is_active: Optional[int] = Field(default=1, description='是否激活（0-否，1-是）')
    crawl_frequency: Optional[str] = Field(default='daily', description='抓取频率')
    last_crawl_time: Optional[datetime] = Field(default=None, description='最后抓取时间')
    crawl_status: Optional[str] = Field(default=None, description='抓取状态')
    error_message: Optional[str] = Field(default=None, description='错误信息')
    sort_order: Optional[int] = Field(default=0, description='排序顺序')
    create_by: Optional[str] = Field(default=None, description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[str] = Field(default=None, description='更新者')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')


@as_query
class RequirementDataSourcePageQueryModel(PageQueryModel):
    """
    需求数据源分页查询模型
    """
    requirement_id: Optional[int] = Field(default=None, description='需求ID')
    source_type: Optional[str] = Field(default=None, description='数据源类型')
    source_name: Optional[str] = Field(default=None, description='数据源名称')
    is_selected: Optional[int] = Field(default=None, description='是否选中')
    is_active: Optional[int] = Field(default=None, description='是否激活')
    created_by: Optional[str] = Field(default=None, description='创建者')


class CreateRequirementDataSourceModel(BaseModel):
    """
    创建需求数据源模型
    """
    requirement_id: int = Field(..., description='需求ID')
    source_type: str = Field(..., description='数据源类型')
    source_name: str = Field(..., description='数据源名称')
    source_url: Optional[str] = Field(default=None, description='数据源URL')
    source_config: Optional[Dict[str, Any]] = Field(default=None, description='数据源配置')
    remark: Optional[str] = Field(default=None, description='备注')


class UpdateRequirementDataSourceModel(BaseModel):
    """
    更新需求数据源模型
    """
    id: int = Field(..., description='数据源ID')
    source_type: Optional[str] = Field(default=None, description='数据源类型')
    source_name: Optional[str] = Field(default=None, description='数据源名称')
    source_url: Optional[str] = Field(default=None, description='数据源URL')
    source_config: Optional[Dict[str, Any]] = Field(default=None, description='数据源配置')
    status: Optional[int] = Field(default=None, description='状态')
    remark: Optional[str] = Field(default=None, description='备注')


class DeleteRequirementDataSourceModel(BaseModel):
    """
    删除需求数据源模型
    """
    ids: List[int] = Field(..., description='数据源ID列表')


class DataSourceInfoModel(BaseModel):
    """
    数据源信息模型（用于批量创建）
    """
    source_type: str = Field(..., description='数据源类型')
    source_name: str = Field(..., description='数据源名称')
    source_url: Optional[str] = Field(default=None, description='数据源URL')
    source_config: Optional[Dict[str, Any]] = Field(default=None, description='数据源配置')
    remark: Optional[str] = Field(default=None, description='备注')


class BatchCreateDataSourceModel(BaseModel):
    """
    批量创建数据源模型
    """
    requirement_id: int = Field(..., description='需求ID')
    data_sources: List[DataSourceInfoModel] = Field(..., description='数据源信息列表')


class DataSourceConfigModel(BaseModel):
    """
    数据源配置模型
    """
    # 通用配置
    enabled: Optional[bool] = Field(default=True, description='是否启用')
    priority: Optional[int] = Field(default=1, description='优先级')
    timeout: Optional[int] = Field(default=30, description='超时时间（秒）')
    
    # API配置
    api_key: Optional[str] = Field(default=None, description='API密钥')
    api_secret: Optional[str] = Field(default=None, description='API密钥')
    access_token: Optional[str] = Field(default=None, description='访问令牌')
    
    # 爬虫配置
    headers: Optional[Dict[str, str]] = Field(default=None, description='请求头')
    cookies: Optional[Dict[str, str]] = Field(default=None, description='Cookie')
    proxy: Optional[str] = Field(default=None, description='代理设置')
    
    # 数据筛选配置
    keywords: Optional[List[str]] = Field(default=None, description='关键词列表')
    date_range: Optional[Dict[str, str]] = Field(default=None, description='时间范围')
    region: Optional[str] = Field(default=None, description='地区限制')
    
    # 其他配置
    custom_config: Optional[Dict[str, Any]] = Field(default=None, description='自定义配置')


class DataSourceTypeModel(BaseModel):
    """
    数据源类型模型
    """
    type_code: str = Field(..., description='类型代码')
    type_name: str = Field(..., description='类型名称')
    description: Optional[str] = Field(default=None, description='类型描述')
    icon: Optional[str] = Field(default=None, description='图标')
    config_template: Optional[Dict[str, Any]] = Field(default=None, description='配置模板')


class DataSourceStatisticsModel(BaseModel):
    """
    数据源统计模型
    """
    total_count: int = Field(..., description='总数据源数')
    active_count: int = Field(..., description='启用数据源数')
    inactive_count: int = Field(..., description='禁用数据源数')
    type_counts: Dict[str, int] = Field(..., description='各类型数据源数量')
    status_counts: Dict[int, int] = Field(..., description='各状态数据源数量')


class DataSourceTestModel(BaseModel):
    """
    数据源测试模型
    """
    source_type: str = Field(..., description='数据源类型')
    source_url: str = Field(..., description='数据源URL')
    source_config: Optional[Dict[str, Any]] = Field(default=None, description='数据源配置')


class DataSourceTestResultModel(BaseModel):
    """
    数据源测试结果模型
    """
    success: bool = Field(..., description='测试是否成功')
    message: str = Field(..., description='测试结果消息')
    response_time: Optional[float] = Field(default=None, description='响应时间（毫秒）')
    data_sample: Optional[Dict[str, Any]] = Field(default=None, description='数据样例')
    error_details: Optional[str] = Field(default=None, description='错误详情')


class DataSourceSyncModel(BaseModel):
    """
    数据源同步模型
    """
    data_source_id: int = Field(..., description='数据源ID')
    sync_type: str = Field(..., description='同步类型（full-全量，incremental-增量）')
    start_time: Optional[datetime] = Field(default=None, description='开始时间')
    end_time: Optional[datetime] = Field(default=None, description='结束时间')


class DataSourceSyncResultModel(BaseModel):
    """
    数据源同步结果模型
    """
    sync_id: str = Field(..., description='同步任务ID')
    status: str = Field(..., description='同步状态')
    total_records: int = Field(..., description='总记录数')
    success_records: int = Field(..., description='成功记录数')
    failed_records: int = Field(..., description='失败记录数')
    start_time: datetime = Field(..., description='开始时间')
    end_time: Optional[datetime] = Field(default=None, description='结束时间')
    error_message: Optional[str] = Field(default=None, description='错误消息')


class DataSourceMonitorModel(BaseModel):
    """
    数据源监控模型
    """
    data_source_id: int = Field(..., description='数据源ID')
    check_time: datetime = Field(..., description='检查时间')
    status: str = Field(..., description='状态（online-在线，offline-离线，error-错误）')
    response_time: Optional[float] = Field(default=None, description='响应时间（毫秒）')
    error_message: Optional[str] = Field(default=None, description='错误消息')
    data_count: Optional[int] = Field(default=None, description='数据量')


class DataSourceHealthModel(BaseModel):
    """
    数据源健康状态模型
    """
    data_source_id: int = Field(..., description='数据源ID')
    health_score: float = Field(..., description='健康评分（0-100）')
    availability: float = Field(..., description='可用性（0-100%）')
    avg_response_time: float = Field(..., description='平均响应时间（毫秒）')
    error_rate: float = Field(..., description='错误率（0-100%）')
    last_check_time: datetime = Field(..., description='最后检查时间')
    status: str = Field(..., description='状态')
    issues: Optional[List[str]] = Field(default=None, description='问题列表')
