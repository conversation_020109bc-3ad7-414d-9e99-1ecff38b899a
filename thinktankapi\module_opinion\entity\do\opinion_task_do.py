from datetime import datetime
from sqlalchemy import BigInteger, Column, DateTime, Integer, String, Text
from config.database import Base


class OpinionTask(Base):
    """
    舆情分析任务表
    """

    __tablename__ = 'opinion_task'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    requirement_id = Column(BigInteger, nullable=False, comment='需求ID，关联opinion_requirement表')
    user_id = Column(BigInteger, nullable=False, comment='用户ID')
    task_name = Column(String(200), nullable=False, comment='任务名称')
    task_description = Column(Text, nullable=True, comment='任务描述')
    task_type = Column(String(50), nullable=False, comment='任务类型')
    frequency = Column(String(20), nullable=True, comment='执行频率')
    execute_time = Column(String(10), nullable=True, comment='执行时间')
    cron_expression = Column(String(100), nullable=True, comment='Cron表达式')
    status = Column(String(20), default='completed', comment='任务状态')
    push_url = Column(String(1000), nullable=True, comment='推送URL')
    push_config = Column(Text, nullable=True, comment='推送配置（JSON格式）')
    last_execute_time = Column(DateTime, nullable=True, comment='最后执行时间')
    next_execute_time = Column(DateTime, nullable=True, comment='下次执行时间')
    execute_count = Column(Integer, default=0, comment='执行次数')
    success_count = Column(Integer, default=0, comment='成功次数')
    fail_count = Column(Integer, default=0, comment='失败次数')
    is_enabled = Column(Integer, default=1, comment='是否启用：0-否，1-是')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), default='', comment='备注信息')
    report_oss_url = Column(String(500), nullable=True, comment='报告OSS访问URL')
    positive_count = Column(Integer, default=0, comment='正面情感数量')
    negative_count = Column(Integer, default=0, comment='负面情感数量')
    neutral_count = Column(Integer, default=0, comment='中性情感数量')
