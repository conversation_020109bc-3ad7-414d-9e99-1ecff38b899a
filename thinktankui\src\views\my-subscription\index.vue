<template>
  <div class="subscription-container" v-loading="loading">
    <!-- 用户信息区域 -->
    <div class="user-info-card">
      <div class="user-info-left">
        <div class="user-avatar">
          <span>{{ userInitial }}</span>
        </div>
        <div class="user-details">
          <h3 class="username">{{ user_info.username }}</h3>
          <p v-if="user_info.email && user_info.email !== '<EMAIL>'" class="user-email">{{ user_info.email }}</p>
          <p v-else class="no-email">未设置邮箱</p>
        </div>
      </div>
      <div class="user-info-right">
        <span v-if="getMembershipStatusText()" class="member-tag">{{ getMembershipStatusText() }}</span>
        <span class="expire-date" v-if="subscription_info.expire_date && subscription_info.expire_date !== '无'">
          到期时间：{{ subscription_info.expire_date }}
        </span>
        <span class="expire-date" v-else-if="shouldShowExpireTime()">
          永久有效
        </span>
      </div>
    </div>

    <!-- 我的订阅标题 -->
    <h2 class="subscription-title">我的订阅</h2>

    <!-- 订阅信息卡片 -->
    <div class="subscription-card">
      <div class="subscription-content">
        <div class="plan-info">
          <div class="current-plan">当前套餐：{{ subscription_info.plan_name }}</div>
          <div class="plan-expire">到期时间：{{ subscription_info.expire_date }}</div>
        </div>
        <div class="action-buttons">
          <button class="renew-btn" @click="handleRenew">续费</button>
          <button class="report-btn" @click="handleMonthlyReport">升级</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCurrentUserSubscriptionInfo } from '@/api/subscription'

export default {
  name: 'MySubscription',
  data() {
    return {
      loading: false,
      user_info: {
        username: '',
        email: ''
      },
      subscription_info: {
        plan_name: '',
        expire_date: ''
      }
    }
  },
  computed: {
    userInitial() {
      return this.user_info.username ? this.user_info.username.charAt(0).toUpperCase() : 'D'
    }
  },
  mounted() {
    this.loadSubscriptionData()
  },
  methods: {
    async loadSubscriptionData() {
      this.loading = true
      try {
        const response = await getCurrentUserSubscriptionInfo()

        if (response.code === 200 && response.data) {
          const data = response.data

          // 设置用户信息
          if (data.user_info) {
            this.user_info = {
              username: data.user_info.nick_name || data.user_info.user_name || 'demo_user',
              email: data.user_info.email || ''
            }
          }

          // 设置订阅信息
          this.subscription_info = {
            plan_name: data.current_package ? data.current_package.package_name : '',
            membership_status: data.membership_status || '',
            expire_date: data.expire_time ? this.formatDate(data.expire_time) : ''
          }
        }
      } catch (error) {
        console.error('获取订阅信息失败:', error)
        this.$message.error('获取订阅信息失败，请稍后重试')

        // 使用默认值
        this.user_info = {
          username: 'demo_user',
          email: ''
        }
        this.subscription_info = {
          plan_name: '',
          membership_status: '',
          expire_date: ''
        }
      } finally {
        this.loading = false
      }
    },
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    },

    // 获取会员状态显示文本
    getMembershipStatusText() {
      const status = this.subscription_info.membership_status
      if (!status) {
        return ''  // 没有数据时返回空字符串
      }
      // 统一术语显示
      if (status === '基础版') {
        return '基础版会员'
      }
      if (status === '专业版') {
        return '专业版会员'
      }
      if (status === '企业版') {
        return '企业版会员'
      }
      return status
    },

    // 判断是否应该显示到期时间
    shouldShowExpireTime() {
      const status = this.subscription_info.membership_status
      // 如果是普通用户或没有状态，不显示到期时间
      if (!status || status === '普通用户') {
        return false
      }
      return true
    },
    handleRenew() {
      // 跳转到套餐页面
      this.$router.push('/set-meal/set-meal')
    },
    handleMonthlyReport() {
      // 跳转到套餐页面（升级）
      this.$router.push('/set-meal/set-meal')
    }
  }
}
</script>

<style scoped>
.subscription-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 统一的用户信息卡片样式 */
.user-info-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.user-info-left {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #36cfc9;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.user-avatar span {
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.user-details h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #303133;
}

.user-details p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.user-details .user-email {
  color: #909399;
}

.user-details .no-email {
  color: #c0c4cc;
  font-style: italic;
}

.user-info-right {
  text-align: right;
}

.member-tag {
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  display: block;
  margin-bottom: 8px;
}

.expire-date {
  font-size: 14px;
  color: #666;
}

.subscription-title {
  font-size: 18px;
  color: #333;
  margin: 20px 0;
  font-weight: bold;
}

.subscription-card {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subscription-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.plan-info {
  margin-bottom: 30px;
}

.current-plan {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.plan-expire {
  font-size: 14px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 15px;
}

.renew-btn {
  background-color: #40a9ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
}

.renew-btn:hover {
  background-color: #1890ff;
}

.report-btn {
  background-color: white;
  color: #666;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
}

.report-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}
</style>
