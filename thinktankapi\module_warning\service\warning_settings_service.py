from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_warning.dao.warning_settings_dao import WarningSettingsDao
from module_warning.dao.warning_scheme_dao import WarningSchemeDao
from module_warning.entity.vo.warning_vo import (
    WarningSettingsModel,
    WarningSettingsConfigModel,
    DeleteWarningSettingsModel
)
from utils.common_util import CamelCaseUtil
from utils.log_util import logger


class WarningSettingsService:
    """
    预警设置管理模块服务层
    """

    @classmethod
    async def get_warning_settings_services(cls, query_db: AsyncSession, scheme_id: int):
        """
        获取预警设置信息service

        :param query_db: orm对象
        :param scheme_id: 预警方案ID
        :return: 预警设置信息对象
        """
        try:
            # 检查方案是否存在
            existing_scheme = await WarningSchemeDao.get_warning_scheme_by_id(query_db, scheme_id)
            if not existing_scheme:
                raise ServiceException(message='预警方案不存在')

            settings_result = await WarningSettingsDao.get_warning_settings_by_scheme_id(query_db, scheme_id)
            
            if settings_result:
                # 转换为配置模型
                config_model = WarningSettingsDao.convert_to_config_model(settings_result)
                result = CamelCaseUtil.transform_result(config_model)
                logger.info(f'获取预警设置成功，方案ID: {scheme_id}')
                return result
            else:
                # 返回默认配置
                default_config = await WarningSettingsDao.get_default_settings_config()
                default_config.scheme_id = scheme_id
                result = CamelCaseUtil.transform_result(default_config)
                logger.info(f'获取默认预警设置，方案ID: {scheme_id}')
                return result
        except ServiceException:
            raise
        except Exception as e:
            logger.error(f'获取预警设置失败: {str(e)}')
            raise ServiceException(message=f'获取预警设置失败: {str(e)}')

    @classmethod
    async def save_warning_settings_services(cls, query_db: AsyncSession, page_object: WarningSettingsConfigModel):
        """
        保存预警设置信息service

        :param query_db: orm对象
        :param page_object: 预警设置配置对象
        :return: 保存预警设置校验结果
        """
        try:
            # 检查方案是否存在
            existing_scheme = await WarningSchemeDao.get_warning_scheme_by_id(query_db, page_object.scheme_id)
            if not existing_scheme:
                raise ServiceException(message='预警方案不存在')

            # 验证设置配置
            if not await WarningSettingsDao.validate_settings_config(page_object):
                raise ServiceException(message='预警设置配置无效')

            # 设置创建者和更新者信息
            page_object.create_by = getattr(page_object, 'create_by', '')
            page_object.update_by = getattr(page_object, 'update_by', '')

            await WarningSettingsDao.save_warning_settings(query_db, page_object)
            await query_db.commit()
            logger.info(f'保存预警设置成功，方案ID: {page_object.scheme_id}')
            return CrudResponseModel(is_success=True, message='保存成功')
        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f'保存预警设置失败: {str(e)}')
            raise ServiceException(message=f'保存预警设置失败: {str(e)}')

    @classmethod
    async def validate_settings_services(cls, page_object: WarningSettingsConfigModel):
        """
        验证预警设置有效性service

        :param page_object: 预警设置配置对象
        :return: 验证结果
        """
        try:
            is_valid = await WarningSettingsDao.validate_settings_config(page_object)
            if is_valid:
                logger.info(f'预警设置验证通过，方案ID: {page_object.scheme_id}')
                return CrudResponseModel(is_success=True, message='设置验证通过')
            else:
                logger.warning(f'预警设置验证失败，方案ID: {page_object.scheme_id}')
                return CrudResponseModel(is_success=False, message='设置验证失败，请检查配置项')
        except Exception as e:
            logger.error(f'验证预警设置失败: {str(e)}')
            raise ServiceException(message=f'验证预警设置失败: {str(e)}')

    @classmethod
    async def get_default_settings_services(cls):
        """
        获取默认预警设置service

        :return: 默认预警设置配置
        """
        try:
            default_config = await WarningSettingsDao.get_default_settings_config()
            result = CamelCaseUtil.transform_result(default_config)
            logger.info('获取默认预警设置成功')
            return result
        except Exception as e:
            logger.error(f'获取默认预警设置失败: {str(e)}')
            raise ServiceException(message=f'获取默认预警设置失败: {str(e)}')

    @classmethod
    async def delete_warning_settings_services(cls, query_db: AsyncSession, page_object: DeleteWarningSettingsModel):
        """
        删除预警设置信息service

        :param query_db: orm对象
        :param page_object: 删除预警设置对象
        :return: 删除预警设置校验结果
        """
        try:
            if not page_object.setting_ids:
                raise ServiceException(message='请选择要删除的预警设置')

            # 检查设置是否存在
            for setting_id in page_object.setting_ids:
                existing_setting = await WarningSettingsDao.get_warning_settings_by_id(query_db, setting_id)
                if not existing_setting:
                    raise ServiceException(message=f'预警设置ID {setting_id} 不存在')

            await WarningSettingsDao.delete_warning_settings_dao(query_db, page_object.setting_ids)
            await query_db.commit()
            logger.info(f'删除预警设置成功，设置数量: {len(page_object.setting_ids)}')
            return CrudResponseModel(is_success=True, message='删除成功')
        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f'删除预警设置失败: {str(e)}')
            raise ServiceException(message=f'删除预警设置失败: {str(e)}')

    @classmethod
    async def copy_settings_services(cls, query_db: AsyncSession, source_scheme_id: int, target_scheme_id: int):
        """
        复制预警设置service

        :param query_db: orm对象
        :param source_scheme_id: 源方案ID
        :param target_scheme_id: 目标方案ID
        :return: 复制结果
        """
        try:
            # 检查源方案和目标方案是否存在
            source_scheme = await WarningSchemeDao.get_warning_scheme_by_id(query_db, source_scheme_id)
            target_scheme = await WarningSchemeDao.get_warning_scheme_by_id(query_db, target_scheme_id)
            
            if not source_scheme:
                raise ServiceException(message='源预警方案不存在')
            if not target_scheme:
                raise ServiceException(message='目标预警方案不存在')

            # 获取源设置
            source_settings = await WarningSettingsDao.get_warning_settings_by_scheme_id(query_db, source_scheme_id)
            if not source_settings:
                raise ServiceException(message='源方案没有设置信息')

            # 转换为配置模型并设置新的方案ID
            config_model = WarningSettingsDao.convert_to_config_model(source_settings)
            config_model.scheme_id = target_scheme_id

            # 保存到目标方案
            await WarningSettingsDao.save_warning_settings(query_db, config_model)
            await query_db.commit()
            logger.info(f'复制预警设置成功，从方案{source_scheme_id}到方案{target_scheme_id}')
            return CrudResponseModel(is_success=True, message='复制设置成功')
        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f'复制预警设置失败: {str(e)}')
            raise ServiceException(message=f'复制预警设置失败: {str(e)}')

    @classmethod
    async def reset_settings_services(cls, query_db: AsyncSession, scheme_id: int):
        """
        重置预警设置为默认值service

        :param query_db: orm对象
        :param scheme_id: 方案ID
        :return: 重置结果
        """
        try:
            # 检查方案是否存在
            existing_scheme = await WarningSchemeDao.get_warning_scheme_by_id(query_db, scheme_id)
            if not existing_scheme:
                raise ServiceException(message='预警方案不存在')

            # 获取默认配置并设置方案ID
            default_config = await WarningSettingsDao.get_default_settings_config()
            default_config.scheme_id = scheme_id

            # 保存默认设置
            await WarningSettingsDao.save_warning_settings(query_db, default_config)
            await query_db.commit()
            logger.info(f'重置预警设置成功，方案ID: {scheme_id}')
            return CrudResponseModel(is_success=True, message='重置设置成功')
        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f'重置预警设置失败: {str(e)}')
            raise ServiceException(message=f'重置预警设置失败: {str(e)}')
