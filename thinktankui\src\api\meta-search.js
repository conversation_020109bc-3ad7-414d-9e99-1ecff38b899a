import request from '@/utils/request'

// 全文检索搜索
export function fulltextSearch(params) {
  return request({
    url: '/api/meta-search/fulltext/search',
    method: 'get',
    params: params
  })
}

// 元搜索 - 多搜索引擎结果
export function metaSearch(params) {
  return request({
    url: '/api/meta-search/meta/search',
    method: 'get',
    params: params
  })
}

// 获取搜索统计信息
export function getSearchStatistics() {
  return request({
    url: '/api/meta-search/statistics',
    method: 'get'
  })
}

// 获取搜索建议
export function getSearchSuggestions(keyword) {
  return request({
    url: '/api/meta-search/suggestions',
    method: 'get',
    params: { keyword }
  })
}

// 获取搜索筛选器选项
export function getSearchFilters(keyword) {
  return request({
    url: '/api/meta-search/filters',
    method: 'get',
    params: { keyword }
  })
}

// 简化接口 - 兼容现有前端调用
export function getSimpleFulltextSearch(params) {
  return request({
    url: '/api/meta-search/simple/fulltext',
    method: 'get',
    params: params
  })
}

export function getSimpleMetaSearch(keyword) {
  return request({
    url: '/api/meta-search/simple/meta',
    method: 'get',
    params: { keyword }
  })
}

export function getSimpleStatistics() {
  return request({
    url: '/api/meta-search/simple/statistics',
    method: 'get'
  })
}

// 健康检查
export function healthCheck() {
  return request({
    url: '/api/meta-search/health',
    method: 'get'
  })
}
