from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from module_opinion.dao.dashboard_dao import DashboardDao
from module_opinion.entity.vo.dashboard_vo import (
    RecentAnalysisRecordModel,
    RecentAnalysisRecordsResponseModel,
    DashboardStatisticsModel
)
from utils.common_util import CamelCaseUtil
from utils.log_util import logger


class DashboardService:
    """
    Dashboard业务逻辑层
    """

    @classmethod
    async def get_recent_analysis_records_services(
        cls,
        query_db: AsyncSession,
        limit: int = 3,
        user_id: int = None
    ) -> RecentAnalysisRecordsResponseModel:
        """
        获取最新分析记录service

        :param query_db: 数据库会话
        :param limit: 返回记录数量限制，默认3条
        :param user_id: 用户ID，用于过滤特定用户的分析记录
        :return: 最新分析记录响应模型
        """
        try:
            # 获取最新分析记录，传递用户ID参数
            records_data = await DashboardDao.get_recent_analysis_records(query_db, limit, user_id)

            # 获取总记录数，传递用户ID参数
            total_count = await DashboardDao.get_analysis_records_count(query_db, user_id)

            # 转换为响应模型
            records = []
            for record_data in records_data:
                record_model = RecentAnalysisRecordModel(**record_data)
                records.append(record_model)

            response = RecentAnalysisRecordsResponseModel(
                records=records,
                total_count=total_count
            )

            logger.info(f"成功获取最新{len(records)}条分析记录，用户ID: {user_id}，总记录数: {total_count}")
            return response
            
        except Exception as e:
            logger.error(f"获取最新分析记录服务失败: {str(e)}")
            raise e

    @classmethod
    async def get_dashboard_statistics_services(
        cls,
        query_db: AsyncSession,
        user_id: int = None
    ) -> DashboardStatisticsModel:
        """
        获取Dashboard统计数据service

        :param query_db: 数据库会话
        :param user_id: 用户ID，用于过滤特定用户的统计数据
        :return: Dashboard统计数据模型
        """
        try:
            logger.info("=== Dashboard Service: 开始获取统计数据 ===")

            # 获取统计数据
            statistics_data = await DashboardDao.get_dashboard_statistics(query_db, user_id)
            logger.info(f"=== Dashboard Service: 从DAO获取的原始数据: {statistics_data} ===")

            # 转换为响应模型
            statistics = DashboardStatisticsModel(**statistics_data)
            logger.info(f"=== Dashboard Service: 转换后的模型数据: {statistics} ===")

            logger.info(f"=== Dashboard Service: 成功获取Dashboard统计数据 ===")
            return statistics

        except Exception as e:
            logger.error(f"=== Dashboard Service: 获取Dashboard统计数据服务失败: {str(e)} ===")
            logger.error(f"=== Dashboard Service: 错误详情: {e} ===", exc_info=True)
            # 返回默认统计数据
            default_stats = DashboardStatisticsModel()
            logger.info(f"=== Dashboard Service: 返回默认统计数据: {default_stats} ===")
            return default_stats

    @classmethod
    async def get_current_month_analysis_count_service(
        cls,
        query_db: AsyncSession,
        user_id: int
    ) -> int:
        """
        获取用户当前月份的分析记录数量service（备用统计）

        :param query_db: 数据库会话
        :param user_id: 用户ID
        :return: 当前月份分析记录数量
        """
        try:
            logger.info(f"=== Dashboard Service: 开始获取用户{user_id}当前月份分析记录数 ===")

            # 获取当前月份的分析记录数量
            monthly_count = await DashboardDao.get_current_month_analysis_count(query_db, user_id)
            logger.info(f"=== Dashboard Service: 从DAO获取的当前月份记录数: {monthly_count} ===")

            logger.info(f"=== Dashboard Service: 成功获取当前月份分析记录数 ===")
            return monthly_count

        except Exception as e:
            logger.error(f"=== Dashboard Service: 获取当前月份分析记录数服务失败: {str(e)} ===")
            logger.error(f"=== Dashboard Service: 错误详情: {e} ===", exc_info=True)
            # 返回默认值0
            logger.info(f"=== Dashboard Service: 返回默认记录数: 0 ===")
            return 0

    @classmethod
    async def get_opinion_trend_data_services(
        cls,
        query_db: AsyncSession,
        days: int = 7
    ) -> 'OpinionTrendResponseModel':
        """
        获取舆情分析趋势数据service

        :param query_db: 数据库会话
        :param days: 天数，默认7天
        :return: 舆情趋势响应模型
        """
        try:
            from module_opinion.entity.vo.dashboard_vo import (
                OpinionTrendResponseModel, OpinionTrendDataModel
            )
            from datetime import datetime, timedelta

            # 获取趋势数据
            trend_data_raw = await DashboardDao.get_opinion_trend_data(query_db, days)

            # 转换为响应模型
            trend_data = []
            date_labels = []
            positive_series = []
            neutral_series = []
            negative_series = []

            # 生成日期标签（中文格式）
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days-1)

            weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

            for item in trend_data_raw:
                # 转换为模型
                trend_model = OpinionTrendDataModel(**item)
                trend_data.append(trend_model)

                # 生成日期标签
                date_obj = datetime.strptime(item['date'], '%Y-%m-%d').date()
                if days <= 7:
                    # 7天内显示星期
                    weekday_index = date_obj.weekday()
                    date_labels.append(weekdays[weekday_index])
                else:
                    # 超过7天显示月/日
                    date_labels.append(date_obj.strftime('%m/%d'))

                # 构建数据系列
                positive_series.append(item['positive_count'])
                neutral_series.append(item['neutral_count'])
                negative_series.append(item['negative_count'])

            response = OpinionTrendResponseModel(
                trend_data=trend_data,
                date_labels=date_labels,
                positive_series=positive_series,
                neutral_series=neutral_series,
                negative_series=negative_series
            )

            logger.info(f"成功获取{days}天舆情趋势数据，共{len(trend_data)}条记录")
            return response

        except Exception as e:
            logger.error(f"获取舆情趋势数据服务失败: {str(e)}")
            # 返回空数据
            from module_opinion.entity.vo.dashboard_vo import OpinionTrendResponseModel
            return OpinionTrendResponseModel()
