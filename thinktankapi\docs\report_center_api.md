# 报告中心API接口文档

## 概述

报告中心API提供了完整的方案管理和报告模板管理功能，支持前端报告中心页面的所有数据需求。

## 基础信息

- **基础路径**: `/report`
- **认证方式**: <PERSON><PERSON> (通过登录获取)
- **数据格式**: JSON

## 方案管理接口

### 1. 获取方案列表

**接口地址**: `GET /report/scheme/list`

**请求参数**:
```
pageNum: int = 1          # 页码
pageSize: int = 10        # 每页数量
name: string (可选)       # 方案名称
typeId: int (可选)        # 方案类型ID
status: int (可选)        # 状态
searchKeyword: string (可选) # 搜索关键词
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "id": 11,
        "name": "方太品牌监控",
        "typeName": "品牌监控",
        "createTime": "2025-06-09 08:52:41",
        "status": 1,
        "refreshStatus": 0
      }
    ],
    "total": 1,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

### 2. 获取方案详情

**接口地址**: `GET /report/scheme/{schemeId}`

**路径参数**:
- `schemeId`: 方案ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 11,
    "name": "方太品牌监控",
    "description": "方太品牌相关的舆情监控方案",
    "status": 1,
    "refreshStatus": 0,
    "schemeType": {
      "id": 1,
      "name": "品牌监控"
    }
  }
}
```

### 3. 获取方案类型列表

**接口地址**: `GET /report/scheme-type/list`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "品牌监控",
      "description": "品牌相关舆情监控"
    },
    {
      "id": 2,
      "name": "行业监控",
      "description": "行业整体舆情监控"
    }
  ]
}
```

### 4. 获取侧边栏菜单分类

**接口地址**: `GET /report/menu/categories`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 0,
      "name": "品牌监控",
      "count": 2,
      "isItem": false,
      "children": [
        {
          "id": 11,
          "name": "方太品牌监控",
          "count": 0,
          "isItem": true
        }
      ]
    }
  ]
}
```

## 报告模板管理接口

### 1. 获取模板列表

**接口地址**: `GET /report/template/list`

**请求参数**:
```
pageNum: int = 1          # 页码
pageSize: int = 10        # 每页数量
name: string (可选)       # 模板名称
templateType: string (可选) # 模板类型 (normal/competitor)
isActive: boolean (可选)  # 是否启用
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "品牌-热议话题",
        "templateType": "normal",
        "createTime": "2025-06-13 14:11:56",
        "isActive": true
      }
    ],
    "total": 1,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

### 2. 根据类型获取模板列表

**接口地址**: `GET /report/template/type/{templateType}`

**路径参数**:
- `templateType`: 模板类型 (normal/competitor)

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "品牌-热议话题",
      "templateType": "normal",
      "createTime": "2025-06-13 14:11:56",
      "isActive": true
    }
  ]
}
```

## 前端集成示例

### Vue.js 集成示例

```javascript
// 获取方案列表
async fetchReportList() {
  try {
    const response = await this.$http.get('/report/scheme/list', {
      params: {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        searchKeyword: this.searchKeyword
      }
    });
    
    if (response.data.code === 200) {
      this.reportList = response.data.data.records;
      this.total = response.data.data.total;
    }
  } catch (error) {
    console.error('获取方案列表失败:', error);
  }
}

// 获取侧边栏菜单
async fetchMenuCategories() {
  try {
    const response = await this.$http.get('/report/menu/categories');
    
    if (response.data.code === 200) {
      this.menuCategories = response.data.data;
    }
  } catch (error) {
    console.error('获取菜单分类失败:', error);
  }
}

// 获取模板列表
async fetchTemplateList() {
  try {
    const response = await this.$http.get('/report/template/list', {
      params: {
        templateType: this.templateType,
        pageNum: this.currentPage,
        pageSize: this.pageSize
      }
    });
    
    if (response.data.code === 200) {
      this.templateList = response.data.data.records;
      this.total = response.data.data.total;
    }
  } catch (error) {
    console.error('获取模板列表失败:', error);
  }
}
```

## 权限说明

所有接口都需要相应的权限：

- `report:scheme:list` - 查看方案列表
- `report:scheme:query` - 查看方案详情
- `report:scheme:add` - 新增方案
- `report:scheme:edit` - 编辑方案
- `report:scheme:remove` - 删除方案
- `report:template:list` - 查看模板列表
- `report:template:query` - 查看模板详情
- `report:template:add` - 新增模板
- `report:template:edit` - 编辑模板
- `report:template:remove` - 删除模板

## 数据库表结构

### scheme 表
- 存储方案基本信息
- 关联 scheme_type 表获取类型信息
- 关联 scheme_config 表获取配置信息
- 关联 scheme_summary_statistic 表获取统计信息

### report_template 表
- 存储报告模板信息
- 支持普通模板和竞对模板两种类型
