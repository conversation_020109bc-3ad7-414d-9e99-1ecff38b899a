from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.get_db import get_db
from module_bill.service.bill_service import BillService
from module_bill.entity.vo.bill_vo import (
    BillListQueryModel,
    BillListResponseModel,
    UserOrderModel,
    UserMembershipInfoModel,
    UserPackageModel
)
from utils.response_util import ResponseUtil
from utils.log_util import logger
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.user_vo import CurrentUserModel


billController = APIRouter(prefix='/bill', tags=['账单管理'])


@billController.get('/orders', response_model=BillListResponseModel)
async def get_user_orders_page(
    request: Request,
    query_model: BillListQueryModel = Depends(BillListQueryModel.as_query),
    db: AsyncSession = Depends(get_db)
):
    """
    分页查询用户订单
    """
    try:
        # 从请求中获取当前用户ID（这里需要根据实际的认证机制来获取）
        # 暂时使用查询参数中的user_id，实际应该从JWT token或session中获取
        if not query_model.user_id:
            return ResponseUtil.error(message="用户ID不能为空")
        
        result = await BillService.get_user_orders_page(db, query_model)
        logger.info(f"查询用户{query_model.user_id}的订单列表成功")
        
        return ResponseUtil.success(data=result)
    except Exception as e:
        logger.error(f"查询用户订单失败: {str(e)}")
        return ResponseUtil.error(message=f"查询失败: {str(e)}")


@billController.get('/user/{user_id}/membership')
async def get_user_membership_info(
    request: Request,
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户会员信息
    """
    try:
        result = await BillService.get_user_membership_info(db, user_id)
        logger.info(f"获取用户{user_id}的会员信息成功")

        return ResponseUtil.success(data=result)
    except Exception as e:
        logger.error(f"获取用户会员信息失败: {str(e)}")
        return ResponseUtil.error(message=f"获取失败: {str(e)}")


@billController.get('/my-subscription')
async def get_current_user_subscription_info(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    获取当前登录用户的订阅信息（用于订阅页面）
    """
    try:
        result = await BillService.get_user_membership_info(db, current_user.user.user_id)
        logger.info(f"获取用户{current_user.user.user_id}的订阅信息成功")

        return ResponseUtil.success(data=result)
    except Exception as e:
        logger.error(f"获取用户订阅信息失败: {str(e)}")
        return ResponseUtil.error(message=f"获取失败: {str(e)}")


@billController.get('/packages')
async def get_all_packages(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    获取所有套餐
    """
    try:
        result = await BillService.get_all_packages(db)
        logger.info("获取套餐列表成功")
        
        return ResponseUtil.success(data=result)
    except Exception as e:
        logger.error(f"获取套餐列表失败: {str(e)}")
        return ResponseUtil.error(message=f"获取失败: {str(e)}")


@billController.get('/order/{order_id}')
async def get_order_by_id(
    request: Request,
    order_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    根据订单ID获取订单详情
    """
    try:
        result = await BillService.get_order_by_id(db, order_id)
        if not result:
            return ResponseUtil.error(message="订单不存在")
        
        logger.info(f"获取订单{order_id}详情成功")
        return ResponseUtil.success(data=result)
    except Exception as e:
        logger.error(f"获取订单详情失败: {str(e)}")
        return ResponseUtil.error(message=f"获取失败: {str(e)}")


@billController.get('/user/{user_id}/orders')
async def get_user_orders_by_user_id(
    request: Request,
    user_id: int,
    limit: int = 10,
    db: AsyncSession = Depends(get_db)
):
    """
    根据用户ID获取订单列表
    """
    try:
        result = await BillService.get_user_orders_by_user_id(db, user_id, limit)
        logger.info(f"获取用户{user_id}的订单列表成功")
        
        return ResponseUtil.success(data=result)
    except Exception as e:
        logger.error(f"获取用户订单列表失败: {str(e)}")
        return ResponseUtil.error(message=f"获取失败: {str(e)}")


# 为账单管理页面提供的专用接口
@billController.get('/dashboard/{user_id}')
async def get_bill_dashboard_data(
    request: Request,
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    获取账单管理页面的仪表板数据
    """
    try:
        # 获取用户会员信息
        membership_info = await BillService.get_user_membership_info(db, user_id)
        
        # 获取最近的订单列表
        recent_orders = await BillService.get_user_orders_by_user_id(db, user_id, 5)
        
        # 组装仪表板数据
        dashboard_data = {
            "user_info": membership_info.user_info,
            "membership_info": {
                "membership_status": membership_info.membership_status,
                "expire_time": membership_info.expire_time,
                "current_package": membership_info.current_package
            },
            "recent_orders": recent_orders
        }
        
        logger.info(f"获取用户{user_id}的账单仪表板数据成功")
        return ResponseUtil.success(data=dashboard_data)
    except Exception as e:
        logger.error(f"获取账单仪表板数据失败: {str(e)}")
        return ResponseUtil.error(message=f"获取失败: {str(e)}")


# 格式化订单状态的辅助接口
@billController.get('/status/format')
async def get_formatted_status():
    """
    获取格式化的订单状态和支付状态
    """
    try:
        status_mapping = {
            "order_status": {
                "pending": "待处理",
                "paid": "已支付",
                "cancelled": "已取消",
                "refunded": "已退款"
            },
            "payment_status": {
                "unpaid": "未支付",
                "paid": "已支付",
                "failed": "支付失败",
                "refunded": "已退款"
            },
            "payment_method": {
                "alipay": "支付宝",
                "wechat": "微信支付",
                "stripe": "信用卡支付"
            }
        }
        
        return ResponseUtil.success(data=status_mapping)
    except Exception as e:
        logger.error(f"获取状态映射失败: {str(e)}")
        return ResponseUtil.error(message=f"获取失败: {str(e)}")
