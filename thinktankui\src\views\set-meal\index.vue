<template>
    <div class="set-meal-container">
        <!-- 页面标题 -->
        <div class="page-title">
            <h1>选择适合您的套餐</h1>
            <p>根据您的需求选择最合适的舆情分析服务</p>
        </div>

        <!-- 套餐卡片 -->
        <div class="packages-container" v-loading="loading">
            <div
                v-for="pkg in packages"
                :key="pkg.id"
                class="package-card"
                :class="{ recommended: pkg.recommended }">

                <div class="package-header">
                    <div class="package-name">{{ pkg.name }}</div>
                    <div class="package-price">
                        <span class="currency">¥</span>{{ pkg.price }}
                        <div v-if="pkg.original_price && pkg.original_price > pkg.price" class="original-price">
                            原价：¥{{ pkg.original_price }}
                        </div>
                    </div>
                    <div class="package-period">{{ pkg.period }}</div>
                </div>

                <div class="package-description" v-if="pkg.description">
                    {{ pkg.description }}
                </div>

                <ul class="package-features">
                    <li v-for="feature in pkg.features" :key="feature">{{ feature }}</li>
                </ul>

                <el-button
                    class="package-button"
                    :type="pkg.recommended ? 'primary' : 'default'"
                    @click="selectPackage(pkg)"
                    size="large">
                    {{ pkg.button_text }}
                </el-button>
            </div>
        </div>

        <!-- 免费试用区域 -->
        <div class="trial-section">
            <div class="trial-title">🎯 免费试用</div>
            <div class="trial-description">
                不确定哪个套餐适合您？先免费试用7天，体验完整功能
            </div>
            <el-button class="trial-button" type="warning" @click="startTrial" size="large">
                开始免费试用
            </el-button>
        </div>

        <!-- 功能对比表 -->
        <div class="comparison-table">
            <div class="comparison-header">功能对比</div>
            <el-table :data="comparison_data" style="width: 100%">
                <el-table-column prop="feature" label="功能" width="200"></el-table-column>
                <el-table-column prop="basic" label="基础版" align="center"></el-table-column>
                <el-table-column prop="premium" label="专业版" align="center"></el-table-column>
                <el-table-column prop="enterprise" label="企业版" align="center"></el-table-column>
            </el-table>
        </div>

        <!-- 支付弹窗 -->
        <el-dialog
            title="确认订单"
            :visible.sync="payment_dialog"
            width="500px"
            center>
            <div v-if="selected_package">
                <div style="text-align: center; margin-bottom: 20px;">
                    <h3>{{ selected_package.name }}</h3>
                    <div style="font-size: 24px; color: #409EFF; margin: 10px 0;">
                        ¥{{ selected_package.price }}
                    </div>
                    <div v-if="selected_package.description" style="color: #666; font-size: 14px;">
                        {{ selected_package.description }}
                    </div>
                </div>

                <el-form :model="order_form" label-width="80px">
                    <el-form-item label="优惠券">
                        <el-input v-model="order_form.coupon_code" placeholder="请输入优惠券代码">
                            <el-button slot="append" @click="applyCoupon">使用</el-button>
                        </el-input>
                    </el-form-item>

                    <el-form-item label="支付方式">
                        <el-radio-group v-model="order_form.payment_method">
                            <el-radio label="alipay">支付宝</el-radio>
                            <el-radio label="wechat">微信支付</el-radio>
                            <el-radio label="stripe">信用卡</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>

                <div style="border-top: 1px solid #eee; padding-top: 15px; margin-top: 20px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>原价：</span>
                        <span>¥{{ selected_package.price }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;" v-if="order_form.discount > 0">
                        <span>优惠：</span>
                        <span style="color: #f56c6c;">-¥{{ order_form.discount }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-weight: bold; font-size: 18px;">
                        <span>实付：</span>
                        <span style="color: #409EFF;">¥{{ final_price }}</span>
                    </div>
                </div>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="payment_dialog = false">取消</el-button>
                <el-button type="primary" @click="confirmPayment" :loading="payment_loading">
                    {{ payment_loading ? '处理中...' : '确认支付' }}
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getAllPackages } from '@/api/bill'

export default {
    name: 'SetMeal',
    data() {
        return {
            packages: [],
            loading: false,
            comparison_data: [],
            payment_dialog: false,
            selected_package: null,
            order_form: {
                coupon_code: '',
                payment_method: 'alipay',
                discount: 0
            },
            payment_loading: false
        }
    },
    computed: {
        final_price() {
            if (!this.selected_package) return 0;
            return Math.max(0, this.selected_package.current_price - this.order_form.discount);
        }
    },
    async mounted() {
        await this.loadPackages();
        this.generateComparisonData();
    },
    methods: {
        async loadPackages() {
            try {
                this.loading = true;
                const response = await getAllPackages();
                if (response.code === 200) {
                    this.packages = response.data.map(pkg => ({
                        id: pkg.package_id,
                        name: pkg.package_name,
                        price: parseFloat(pkg.current_price),
                        original_price: parseFloat(pkg.original_price),
                        period: `${pkg.duration_days}天`,
                        features: this.parseFeatures(pkg.features),
                        button_text: `选择${pkg.package_name}`,
                        recommended: pkg.package_type === 'premium',
                        package_type: pkg.package_type,
                        analysis_limit: pkg.analysis_limit,
                        description: pkg.description
                    }));
                } else {
                    this.$message.error('获取套餐数据失败');
                }
            } catch (error) {
                console.error('加载套餐数据失败:', error);
                this.$message.error('加载套餐数据失败');
            } finally {
                this.loading = false;
            }
        },
        parseFeatures(features) {
            if (!features) return [];
            if (typeof features === 'string') {
                try {
                    const parsed = JSON.parse(features);
                    return parsed.features || [];
                } catch (e) {
                    return [];
                }
            }
            return features.features || [];
        },
        generateComparisonData() {
            // 根据套餐数据生成对比表数据
            const basic = this.packages.find(p => p.package_type === 'basic');
            const premium = this.packages.find(p => p.package_type === 'premium');
            const enterprise = this.packages.find(p => p.package_type === 'enterprise');

            this.comparison_data = [
                {
                    feature: '分析次数',
                    basic: basic ? (basic.analysis_limit === -1 ? '无限制' : `${basic.analysis_limit}次/月`) : '-',
                    premium: premium ? (premium.analysis_limit === -1 ? '无限制' : `${premium.analysis_limit}次/月`) : '-',
                    enterprise: enterprise ? (enterprise.analysis_limit === -1 ? '无限制' : `${enterprise.analysis_limit}次/月`) : '-'
                },
                {
                    feature: '有效期',
                    basic: basic ? `${basic.period}` : '-',
                    premium: premium ? `${premium.period}` : '-',
                    enterprise: enterprise ? `${enterprise.period}` : '-'
                },
                { feature: 'API调用', basic: '❌', premium: '✅', enterprise: '✅' },
                { feature: '实时监控', basic: '❌', premium: '✅', enterprise: '✅' },
                { feature: '定制报告', basic: '❌', premium: '✅', enterprise: '✅' },
                { feature: '专属客服', basic: '❌', premium: '❌', enterprise: '✅' }
            ];
        },
        selectPackage(pkg) {
            this.selected_package = pkg;
            this.payment_dialog = true;
        },
        startTrial() {
            this.$confirm('确定要开始7天免费试用吗？', '免费试用', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                this.$message.success('免费试用已开通！');
                // 跳转到主系统
                this.$router.push('/dashboard');
            });
        },
        applyCoupon() {
            if (!this.order_form.coupon_code) {
                this.$message.warning('请输入优惠券代码');
                return;
            }

            // 模拟优惠券验证
            if (this.order_form.coupon_code === 'WELCOME10') {
                this.order_form.discount = this.selected_package.price * 0.1;
                this.$message.success('优惠券使用成功！');
            } else {
                this.$message.error('优惠券无效或已过期');
            }
        },
        confirmPayment() {
            this.payment_loading = true;

            // 模拟支付处理
            setTimeout(() => {
                this.payment_loading = false;
                this.payment_dialog = false;
                this.$message.success('支付成功！正在跳转...');

                // 跳转到支付页面或主系统
                setTimeout(() => {
                    this.$router.push('/dashboard');
                }, 1500);
            }, 2000);
        }
    }
}
</script>

<style scoped>
.set-meal-container {
    padding: 20px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-title {
    text-align: center;
    margin-bottom: 20px;
}

.page-title h1 {
    font-size: 32px;
    color: #333;
    margin-bottom: 10px;
}

.page-title p {
    font-size: 18px;
    color: #666;
}

.packages-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 50px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.package-card {
    background: white;
    border-radius: 16px;
    padding: 40px 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.package-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.package-card.recommended {
    border: 3px solid #409EFF;
    transform: scale(1.05);
}

.package-card.recommended::before {
    content: '推荐';
    position: absolute;
    top: 20px;
    right: -30px;
    background: #409EFF;
    color: white;
    padding: 5px 40px;
    font-size: 14px;
    font-weight: bold;
    transform: rotate(45deg);
}

.package-header {
    text-align: center;
    margin-bottom: 30px;
}

.package-name {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
}

.package-price {
    font-size: 48px;
    font-weight: bold;
    color: #409EFF;
    margin-bottom: 5px;
}

.package-price .currency {
    font-size: 24px;
}

.original-price {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
    margin-top: 5px;
}

.package-period {
    color: #666;
    font-size: 16px;
}

.package-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
    text-align: center;
    line-height: 1.4;
}

.package-features {
    list-style: none;
    margin-bottom: 30px;
    padding: 0;
}

.package-features li {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 16px;
    color: #555;
}

.package-features li::before {
    content: '✓';
    color: #409EFF;
    font-weight: bold;
    margin-right: 12px;
    font-size: 18px;
}

.package-button {
    width: 100%;
    height: 50px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s;
}

.package-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
}

.trial-section {
    text-align: center;
    margin-top: 50px;
    padding: 40px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.trial-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
}

.trial-description {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
}

.trial-button {
    padding: 15px 40px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s;
}

.trial-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(230, 162, 60, 0.3);
}

.comparison-table {
    margin-top: 60px;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.comparison-header {
    background: #409EFF;
    color: white;
    padding: 20px;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
}

@media (max-width: 768px) {
    .packages-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .package-card.recommended {
        transform: none;
    }

    .set-meal-container {
        padding: 15px;
    }
}
</style>