-- =====================================================
-- 关键词数据表测试数据插入脚本
-- 创建时间: 2025-01-14
-- 说明: 为keyword_data表提供近7天的测试数据
-- 用途: 支持舆情分析趋势图表显示
-- =====================================================

-- 插入近7天的测试数据
INSERT INTO keyword_data (
    title, content, url, keyword, type, createtime, web, sentiment
) VALUES 
-- 今天的数据
('某品牌新产品发布获得好评', '某知名品牌今日发布新产品，获得消费者一致好评，市场反响热烈。', 'https://example.com/news1', '品牌', '新闻', NOW(), '新浪网', 'positive'),
('用户体验优化升级', '该品牌持续优化用户体验，新功能受到用户欢迎。', 'https://example.com/news2', '品牌', '新闻', NOW(), '腾讯网', 'positive'),
('产品质量问题投诉', '部分用户反映产品存在质量问题，需要改进。', 'https://example.com/news3', '品牌', '投诉', NOW(), '微博', 'negative'),
('市场表现平稳', '该品牌在市场中表现平稳，没有明显波动。', 'https://example.com/news4', '品牌', '分析', NOW(), '财经网', 'neutral'),
('客服响应及时', '客服团队响应及时，解决用户问题效率高。', 'https://example.com/news5', '品牌', '服务', NOW(), '知乎', 'positive'),

-- 昨天的数据
('品牌营销活动成功', '昨日举办的营销活动取得圆满成功，参与度很高。', 'https://example.com/news6', '品牌', '营销', DATE_SUB(NOW(), INTERVAL 1 DAY), '搜狐网', 'positive'),
('产品功能更新', '产品功能更新，增加了用户期待的新特性。', 'https://example.com/news7', '品牌', '更新', DATE_SUB(NOW(), INTERVAL 1 DAY), '网易', 'positive'),
('价格调整引争议', '产品价格调整引发部分用户不满。', 'https://example.com/news8', '品牌', '价格', DATE_SUB(NOW(), INTERVAL 1 DAY), '微博', 'negative'),
('行业地位稳固', '该品牌在行业中地位稳固，竞争力强。', 'https://example.com/news9', '品牌', '行业', DATE_SUB(NOW(), INTERVAL 1 DAY), '36氪', 'neutral'),

-- 前天的数据
('技术创新突破', '在技术创新方面取得重大突破，领先同行。', 'https://example.com/news10', '品牌', '技术', DATE_SUB(NOW(), INTERVAL 2 DAY), '科技日报', 'positive'),
('用户满意度调查', '最新用户满意度调查结果显示整体表现良好。', 'https://example.com/news11', '品牌', '调查', DATE_SUB(NOW(), INTERVAL 2 DAY), '调研网', 'neutral'),
('售后服务投诉', '部分用户对售后服务表示不满。', 'https://example.com/news12', '品牌', '售后', DATE_SUB(NOW(), INTERVAL 2 DAY), '消费者网', 'negative'),
('合作伙伴关系', '与重要合作伙伴建立长期战略关系。', 'https://example.com/news13', '品牌', '合作', DATE_SUB(NOW(), INTERVAL 2 DAY), '商业周刊', 'positive'),

-- 3天前的数据
('环保理念获赞', '品牌环保理念获得社会各界点赞。', 'https://example.com/news14', '品牌', '环保', DATE_SUB(NOW(), INTERVAL 3 DAY), '环保网', 'positive'),
('市场份额分析', '市场份额保持稳定，无明显变化。', 'https://example.com/news15', '品牌', '市场', DATE_SUB(NOW(), INTERVAL 3 DAY), '市场研究', 'neutral'),
('产品召回事件', '因安全隐患主动召回部分产品。', 'https://example.com/news16', '品牌', '召回', DATE_SUB(NOW(), INTERVAL 3 DAY), '安全网', 'negative'),

-- 4天前的数据
('员工福利提升', '公司提升员工福利，获得内部好评。', 'https://example.com/news17', '品牌', '福利', DATE_SUB(NOW(), INTERVAL 4 DAY), '人力资源网', 'positive'),
('财务报告发布', '季度财务报告发布，业绩符合预期。', 'https://example.com/news18', '品牌', '财务', DATE_SUB(NOW(), INTERVAL 4 DAY), '财经时报', 'neutral'),
('竞争对手挑战', '面临竞争对手的激烈挑战。', 'https://example.com/news19', '品牌', '竞争', DATE_SUB(NOW(), INTERVAL 4 DAY), '行业观察', 'negative'),

-- 5天前的数据
('社会责任活动', '积极参与社会责任活动，树立良好形象。', 'https://example.com/news20', '品牌', '公益', DATE_SUB(NOW(), INTERVAL 5 DAY), '公益网', 'positive'),
('产品线扩展', '产品线扩展计划按期推进。', 'https://example.com/news21', '品牌', '扩展', DATE_SUB(NOW(), INTERVAL 5 DAY), '产业网', 'neutral'),
('供应链问题', '供应链出现问题，影响产品交付。', 'https://example.com/news22', '品牌', '供应链', DATE_SUB(NOW(), INTERVAL 5 DAY), '供应链网', 'negative'),

-- 6天前的数据
('品牌价值提升', '品牌价值评估结果显示大幅提升。', 'https://example.com/news23', '品牌', '价值', DATE_SUB(NOW(), INTERVAL 6 DAY), '品牌网', 'positive'),
('行业会议参与', '参与重要行业会议，分享经验。', 'https://example.com/news24', '品牌', '会议', DATE_SUB(NOW(), INTERVAL 6 DAY), '会议网', 'neutral'),
('数据安全事件', '发生数据安全事件，正在调查处理。', 'https://example.com/news25', '品牌', '安全', DATE_SUB(NOW(), INTERVAL 6 DAY), '安全资讯', 'negative');

-- 查询验证数据
SELECT 
    DATE(createtime) as date,
    COUNT(*) as total_count,
    SUM(CASE WHEN sentiment = 'positive' THEN 1 ELSE 0 END) as positive_count,
    SUM(CASE WHEN sentiment = 'neutral' THEN 1 ELSE 0 END) as neutral_count,
    SUM(CASE WHEN sentiment = 'negative' THEN 1 ELSE 0 END) as negative_count
FROM keyword_data 
WHERE DATE(createtime) >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
GROUP BY DATE(createtime)
ORDER BY date;
