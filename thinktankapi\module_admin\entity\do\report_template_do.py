from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean
from config.database import Base


class ReportTemplateDO(Base):
    """
    报告模板数据对象
    """
    __tablename__ = 'report_template'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='模板ID')
    name = Column(String(100), nullable=False, comment='模板名称')
    template_type = Column(String(50), nullable=False, comment='模板类型：normal-普通模板，competitor-竞对模板')
    description = Column(Text, comment='模板描述')
    template_content = Column(Text, comment='模板内容（JSON格式）')
    is_active = Column(Boolean, default=True, comment='是否启用')
    create_time = Column(DateTime, comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')
    create_by = Column(String(64), comment='创建者')
    update_by = Column(String(64), comment='更新者')
    remark = Column(String(500), comment='备注')
