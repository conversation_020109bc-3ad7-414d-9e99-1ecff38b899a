import json
import asyncio
from typing import Dict, Set, Optional, Any
from fastapi import WebSocket, WebSocketDisconnect
from utils.log_util import logger


class WebSocketManager:
    """
    WebSocket连接管理器
    """
    
    def __init__(self):
        # 存储活跃的WebSocket连接
        # 格式: {task_id: {connection_id: websocket}}
        self.active_connections: Dict[str, Dict[str, WebSocket]] = {}
        # 存储连接ID到任务ID的映射
        self.connection_task_map: Dict[str, str] = {}
        # 连接计数器
        self.connection_counter = 0
    
    def generate_connection_id(self) -> str:
        """生成连接ID"""
        self.connection_counter += 1
        return f"conn_{self.connection_counter}"
    
    async def connect(self, websocket: WebSocket, task_id: str) -> str:
        """
        建立WebSocket连接
        
        :param websocket: WebSocket对象
        :param task_id: 任务ID
        :return: 连接ID
        """
        await websocket.accept()
        
        connection_id = self.generate_connection_id()
        
        # 初始化任务连接组
        if task_id not in self.active_connections:
            self.active_connections[task_id] = {}
        
        # 添加连接
        self.active_connections[task_id][connection_id] = websocket
        self.connection_task_map[connection_id] = task_id
        
        logger.info(f"WebSocket连接建立: {connection_id} -> 任务: {task_id}")
        
        # 发送连接成功消息
        await self.send_to_connection(connection_id, {
            "type": "connection_established",
            "connection_id": connection_id,
            "task_id": task_id,
            "message": "WebSocket连接已建立"
        })
        
        return connection_id
    
    async def disconnect(self, connection_id: str):
        """
        断开WebSocket连接
        
        :param connection_id: 连接ID
        """
        if connection_id in self.connection_task_map:
            task_id = self.connection_task_map[connection_id]
            
            # 从任务连接组中移除
            if task_id in self.active_connections:
                if connection_id in self.active_connections[task_id]:
                    del self.active_connections[task_id][connection_id]
                
                # 如果任务没有连接了，清理任务组
                if not self.active_connections[task_id]:
                    del self.active_connections[task_id]
            
            # 从连接映射中移除
            del self.connection_task_map[connection_id]
            
            logger.info(f"WebSocket连接断开: {connection_id} -> 任务: {task_id}")
    
    async def send_to_connection(self, connection_id: str, data: Dict[str, Any]) -> bool:
        """
        向指定连接发送消息
        
        :param connection_id: 连接ID
        :param data: 要发送的数据
        :return: 是否发送成功
        """
        if connection_id not in self.connection_task_map:
            logger.warning(f"连接不存在: {connection_id}")
            return False
        
        task_id = self.connection_task_map[connection_id]
        
        if (task_id not in self.active_connections or 
            connection_id not in self.active_connections[task_id]):
            logger.warning(f"连接已断开: {connection_id}")
            return False
        
        try:
            websocket = self.active_connections[task_id][connection_id]
            await websocket.send_text(json.dumps(data, ensure_ascii=False, default=str))
            return True
        except Exception as e:
            logger.error(f"发送消息失败: {connection_id} - {str(e)}")
            # 连接异常，清理连接
            await self.disconnect(connection_id)
            return False
    
    async def broadcast_to_task(self, task_id: str, data: Dict[str, Any]) -> int:
        """
        向任务的所有连接广播消息
        
        :param task_id: 任务ID
        :param data: 要发送的数据
        :return: 成功发送的连接数
        """
        if task_id not in self.active_connections:
            logger.debug(f"任务没有活跃连接: {task_id}")
            return 0
        
        success_count = 0
        failed_connections = []
        
        # 复制连接列表，避免在迭代过程中修改
        connections = list(self.active_connections[task_id].items())
        
        for connection_id, websocket in connections:
            try:
                await websocket.send_text(json.dumps(data, ensure_ascii=False, default=str))
                success_count += 1
            except Exception as e:
                logger.error(f"广播消息失败: {connection_id} - {str(e)}")
                failed_connections.append(connection_id)
        
        # 清理失败的连接
        for connection_id in failed_connections:
            await self.disconnect(connection_id)
        
        if success_count > 0:
            logger.debug(f"广播消息成功: 任务 {task_id}, {success_count} 个连接")
        
        return success_count
    
    async def send_progress_update(self, task_id: str, progress_data: Dict[str, Any]):
        """
        发送进度更新
        
        :param task_id: 任务ID
        :param progress_data: 进度数据
        """
        message = {
            "type": "progress_update",
            "task_id": task_id,
            "data": progress_data,
            "timestamp": str(asyncio.get_event_loop().time())
        }
        
        await self.broadcast_to_task(task_id, message)
    
    async def send_log_message(self, task_id: str, log_data: Dict[str, Any]):
        """
        发送日志消息
        
        :param task_id: 任务ID
        :param log_data: 日志数据
        """
        message = {
            "type": "log_message",
            "task_id": task_id,
            "data": log_data,
            "timestamp": str(asyncio.get_event_loop().time())
        }
        
        await self.broadcast_to_task(task_id, message)
    
    async def send_task_status_change(self, task_id: str, status: str, additional_data: Optional[Dict[str, Any]] = None):
        """
        发送任务状态变更
        
        :param task_id: 任务ID
        :param status: 新状态
        :param additional_data: 附加数据
        """
        message = {
            "type": "task_status_change",
            "task_id": task_id,
            "status": status,
            "data": additional_data or {},
            "timestamp": str(asyncio.get_event_loop().time())
        }
        
        await self.broadcast_to_task(task_id, message)
    
    def get_task_connection_count(self, task_id: str) -> int:
        """
        获取任务的连接数
        
        :param task_id: 任务ID
        :return: 连接数
        """
        if task_id not in self.active_connections:
            return 0
        return len(self.active_connections[task_id])
    
    def get_total_connections(self) -> int:
        """
        获取总连接数
        
        :return: 总连接数
        """
        return len(self.connection_task_map)
    
    def get_active_tasks(self) -> Set[str]:
        """
        获取有活跃连接的任务列表
        
        :return: 任务ID集合
        """
        return set(self.active_connections.keys())


# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()
