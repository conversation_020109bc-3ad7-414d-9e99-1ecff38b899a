from sqlalchemy import and_, or_, desc, asc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from module_admin.entity.do.info_do import InfoSummaryDO, NewsDO
from module_admin.entity.vo.info_vo import InfoPageQueryModel, NewsPageQueryModel
from utils.page_util import PageUtil


class InfoSummaryDao:
    """
    信息汇总数据访问层
    """

    @classmethod
    async def get_info_summary_list(
        cls,
        query_db: AsyncSession,
        query_object: InfoPageQueryModel,
        is_page: bool = False
    ) -> List[InfoSummaryDO]:
        """
        根据查询参数获取信息汇总列表
        """
        query = select(InfoSummaryDO)
        
        # 构建查询条件
        conditions = []
        
        if query_object.title:
            conditions.append(InfoSummaryDO.title.like(f'%{query_object.title}%'))
        
        if query_object.platform_type:
            conditions.append(InfoSummaryDO.platform_type == query_object.platform_type)
        
        if query_object.sentiment:
            conditions.append(InfoSummaryDO.sentiment == query_object.sentiment)
        
        if query_object.entity_type:
            conditions.append(InfoSummaryDO.entity_type == query_object.entity_type)
        
        if query_object.entity_name:
            conditions.append(InfoSummaryDO.entity_name.like(f'%{query_object.entity_name}%'))
        
        if query_object.start_time:
            conditions.append(InfoSummaryDO.publish_time >= query_object.start_time)
        
        if query_object.end_time:
            conditions.append(InfoSummaryDO.publish_time <= query_object.end_time)
        
        if query_object.search_keyword:
            conditions.append(
                or_(
                    InfoSummaryDO.title.like(f'%{query_object.search_keyword}%'),
                    InfoSummaryDO.content.like(f'%{query_object.search_keyword}%'),
                    InfoSummaryDO.keywords.like(f'%{query_object.search_keyword}%')
                )
            )
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 排序
        query = query.order_by(desc(InfoSummaryDO.publish_time))
        
        # 分页
        if is_page:
            page_obj = PageUtil(query_object.page_num, query_object.page_size)
            query = query.offset(page_obj.start).limit(page_obj.limit)
        
        result = await query_db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_info_summary_count(
        cls,
        query_db: AsyncSession,
        query_object: InfoPageQueryModel
    ) -> int:
        """
        根据查询参数获取信息汇总总数
        """
        query = select(func.count(InfoSummaryDO.id))
        
        # 构建查询条件
        conditions = []
        
        if query_object.title:
            conditions.append(InfoSummaryDO.title.like(f'%{query_object.title}%'))
        
        if query_object.platform_type:
            conditions.append(InfoSummaryDO.platform_type == query_object.platform_type)
        
        if query_object.sentiment:
            conditions.append(InfoSummaryDO.sentiment == query_object.sentiment)
        
        if query_object.entity_type:
            conditions.append(InfoSummaryDO.entity_type == query_object.entity_type)
        
        if query_object.entity_name:
            conditions.append(InfoSummaryDO.entity_name.like(f'%{query_object.entity_name}%'))
        
        if query_object.start_time:
            conditions.append(InfoSummaryDO.publish_time >= query_object.start_time)
        
        if query_object.end_time:
            conditions.append(InfoSummaryDO.publish_time <= query_object.end_time)
        
        if query_object.search_keyword:
            conditions.append(
                or_(
                    InfoSummaryDO.title.like(f'%{query_object.search_keyword}%'),
                    InfoSummaryDO.content.like(f'%{query_object.search_keyword}%'),
                    InfoSummaryDO.keywords.like(f'%{query_object.search_keyword}%')
                )
            )
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await query_db.execute(query)
        return result.scalar()

    @classmethod
    async def get_info_summary_by_id(
        cls,
        query_db: AsyncSession,
        info_id: int
    ) -> Optional[InfoSummaryDO]:
        """
        根据ID获取信息汇总详情
        """
        query = select(InfoSummaryDO).where(InfoSummaryDO.id == info_id)
        result = await query_db.execute(query)
        return result.scalar_one_or_none()


class NewsDao:
    """
    新闻数据访问层
    """

    @classmethod
    async def get_news_list(
        cls,
        query_db: AsyncSession,
        query_object: NewsPageQueryModel,
        is_page: bool = False
    ) -> List[NewsDO]:
        """
        根据查询参数获取新闻列表
        """
        query = select(NewsDO)
        
        # 构建查询条件
        conditions = []
        
        if query_object.title:
            conditions.append(NewsDO.title.like(f'%{query_object.title}%'))
        
        if query_object.platform:
            conditions.append(NewsDO.platform == query_object.platform)
        
        if query_object.emotion_type:
            conditions.append(NewsDO.emotion_type == query_object.emotion_type)
        
        if query_object.media_level:
            conditions.append(NewsDO.media_level == query_object.media_level)
        
        if query_object.article_type:
            conditions.append(NewsDO.article_type == query_object.article_type)
        
        if query_object.is_sensitive is not None:
            conditions.append(NewsDO.is_sensitive == query_object.is_sensitive)
        
        if query_object.start_time:
            conditions.append(NewsDO.publish_time >= query_object.start_time)
        
        if query_object.end_time:
            conditions.append(NewsDO.publish_time <= query_object.end_time)
        
        if query_object.search_keyword:
            conditions.append(
                or_(
                    NewsDO.title.like(f'%{query_object.search_keyword}%'),
                    NewsDO.content.like(f'%{query_object.search_keyword}%'),
                    NewsDO.keywords.like(f'%{query_object.search_keyword}%')
                )
            )
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 排序
        query = query.order_by(desc(NewsDO.publish_time))
        
        # 分页
        if is_page:
            page_obj = PageUtil(query_object.page_num, query_object.page_size)
            query = query.offset(page_obj.start).limit(page_obj.limit)
        
        result = await query_db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_news_count(
        cls,
        query_db: AsyncSession,
        query_object: NewsPageQueryModel
    ) -> int:
        """
        根据查询参数获取新闻总数
        """
        query = select(func.count(NewsDO.id))
        
        # 构建查询条件
        conditions = []
        
        if query_object.title:
            conditions.append(NewsDO.title.like(f'%{query_object.title}%'))
        
        if query_object.platform:
            conditions.append(NewsDO.platform == query_object.platform)
        
        if query_object.emotion_type:
            conditions.append(NewsDO.emotion_type == query_object.emotion_type)
        
        if query_object.media_level:
            conditions.append(NewsDO.media_level == query_object.media_level)
        
        if query_object.article_type:
            conditions.append(NewsDO.article_type == query_object.article_type)
        
        if query_object.is_sensitive is not None:
            conditions.append(NewsDO.is_sensitive == query_object.is_sensitive)
        
        if query_object.start_time:
            conditions.append(NewsDO.publish_time >= query_object.start_time)
        
        if query_object.end_time:
            conditions.append(NewsDO.publish_time <= query_object.end_time)
        
        if query_object.search_keyword:
            conditions.append(
                or_(
                    NewsDO.title.like(f'%{query_object.search_keyword}%'),
                    NewsDO.content.like(f'%{query_object.search_keyword}%'),
                    NewsDO.keywords.like(f'%{query_object.search_keyword}%')
                )
            )
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await query_db.execute(query)
        return result.scalar()

    @classmethod
    async def get_news_by_id(
        cls,
        query_db: AsyncSession,
        news_id: int
    ) -> Optional[NewsDO]:
        """
        根据ID获取新闻详情
        """
        query = select(NewsDO).where(NewsDO.id == news_id)
        result = await query_db.execute(query)
        return result.scalar_one_or_none()
