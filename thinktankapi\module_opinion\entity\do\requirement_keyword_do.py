from datetime import datetime
from sqlalchemy import BigInteger, Column, DateTime, Integer, String, Text
from config.database import Base


class RequirementKeyword(Base):
    """
    需求关键词表
    """

    __tablename__ = 'requirement_keyword'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    requirement_id = Column(BigInteger, nullable=False, comment='需求ID，关联opinion_requirement表')
    keyword = Column(String(200), nullable=False, comment='关键词')
    keyword_type = Column(String(50), default='generated', comment='关键词类型：entity-实体关键词，generated-生成关键词，custom-自定义关键词')
    category_id = Column(BigInteger, nullable=True, comment='分类ID，关联keyword_category_definition表')
    is_selected = Column(Integer, default=0, comment='是否选中：0-否，1-是')
    weight = Column(Integer, default=1, comment='权重，用于排序')
    status = Column(Integer, default=1, comment='状态：0-禁用，1-启用')
    is_deleted = Column(Integer, default=0, comment='是否删除：0-否，1-是')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), default='', comment='备注信息')
