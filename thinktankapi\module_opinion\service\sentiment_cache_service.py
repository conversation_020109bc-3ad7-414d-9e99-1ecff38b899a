"""
情感分析缓存服务
"""
import hashlib
import time
from typing import Optional, Dict, Any
from module_opinion.config.sentiment_config import SentimentConfig


class SentimentCacheService:
    """情感分析缓存服务类"""
    
    # 内存缓存字典
    _cache: Dict[str, Dict[str, Any]] = {}
    
    @classmethod
    def _generate_cache_key(cls, text: str) -> str:
        """
        生成缓存键
        
        :param text: 文本内容
        :return: 缓存键
        """
        # 使用MD5哈希生成缓存键
        text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()
        return f"sentiment_{text_hash}"
    
    @classmethod
    def get_cached_sentiment(cls, text: str) -> Optional[str]:
        """
        获取缓存的情感分析结果
        
        :param text: 文本内容
        :return: 缓存的情感分析结果，如果不存在或已过期则返回None
        """
        try:
            cache_key = cls._generate_cache_key(text)
            
            if cache_key in cls._cache:
                cache_data = cls._cache[cache_key]
                current_time = time.time()
                
                # 检查是否过期
                if current_time - cache_data['timestamp'] < SentimentConfig.SENTIMENT_CACHE_EXPIRE:
                    return cache_data['sentiment']
                else:
                    # 删除过期缓存
                    del cls._cache[cache_key]
            
            return None
            
        except Exception as e:
            # 缓存错误不应该影响主要功能
            return None
    
    @classmethod
    def set_cached_sentiment(cls, text: str, sentiment: str) -> None:
        """
        设置情感分析结果缓存
        
        :param text: 文本内容
        :param sentiment: 情感分析结果
        """
        try:
            cache_key = cls._generate_cache_key(text)
            
            cls._cache[cache_key] = {
                'sentiment': sentiment,
                'timestamp': time.time()
            }
            
            # 简单的缓存清理：如果缓存过多，清理最旧的一半
            if len(cls._cache) > 1000:
                cls._cleanup_old_cache()
                
        except Exception as e:
            # 缓存错误不应该影响主要功能
            pass
    
    @classmethod
    def _cleanup_old_cache(cls) -> None:
        """
        清理旧的缓存条目
        """
        try:
            current_time = time.time()
            expired_keys = []
            
            # 找出所有过期的键
            for key, data in cls._cache.items():
                if current_time - data['timestamp'] >= SentimentConfig.SENTIMENT_CACHE_EXPIRE:
                    expired_keys.append(key)
            
            # 删除过期的键
            for key in expired_keys:
                del cls._cache[key]
            
            # 如果还是太多，删除最旧的一半
            if len(cls._cache) > 500:
                sorted_items = sorted(
                    cls._cache.items(), 
                    key=lambda x: x[1]['timestamp']
                )
                
                # 保留较新的一半
                keep_count = len(sorted_items) // 2
                cls._cache = dict(sorted_items[-keep_count:])
                
        except Exception as e:
            # 清理失败时，直接清空缓存
            cls._cache.clear()
    
    @classmethod
    def clear_cache(cls) -> None:
        """
        清空所有缓存
        """
        cls._cache.clear()
    
    @classmethod
    def get_cache_stats(cls) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        :return: 缓存统计信息
        """
        current_time = time.time()
        valid_count = 0
        expired_count = 0
        
        for data in cls._cache.values():
            if current_time - data['timestamp'] < SentimentConfig.SENTIMENT_CACHE_EXPIRE:
                valid_count += 1
            else:
                expired_count += 1
        
        return {
            'total_entries': len(cls._cache),
            'valid_entries': valid_count,
            'expired_entries': expired_count,
            'cache_expire_seconds': SentimentConfig.SENTIMENT_CACHE_EXPIRE
        }
