from fastapi import APIRouter, Depends, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.login_service import LoginService
from module_admin.service.spread_analysis_service import SpreadAnalysisService
from utils.response_util import ResponseUtil
from utils.log_util import logger
from typing import Optional


# ==================== API映射控制器 ====================
# 用于处理前端调用的API路径，提供兼容性支持

# 传播分析API
spreadAnalysisController = APIRouter(prefix='/api/spread-analysis')

# 元搜索API
metaSearchController = APIRouter(prefix='/api/meta-search')

# 热点事件API
hotEventsController = APIRouter(prefix='/hot-events')

# 事件分析API
eventAnalysisController = APIRouter(prefix='/event-analysis')

# 信源监测API
sourceMonitoringController = APIRouter(prefix='/api/source-monitoring')


# ==================== 传播分析相关接口 ====================

@spreadAnalysisController.get('/summary/{scheme_id}')
async def get_spread_analysis_summary(
    request: Request,
    scheme_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取传播分析汇总数据
    """
    try:
        # TODO: 实现真实的数据库查询逻辑
        # 从数据库查询方案相关的传播分析数据
        # 1. 查询scheme表获取方案信息
        # 2. 查询scheme_summary_statistic表获取统计数据
        # 3. 查询news表获取相关新闻数据
        # 4. 统计情感分析数据
        # 5. 生成趋势数据
        
        # 临时返回模拟数据，待实现真实逻辑后删除
        # mock_data = {
        #     "scheme_id": scheme_id,
        #     "total_articles": 1250,
        #     "positive_count": 800,
        #     "negative_count": 200,
        #     "neutral_count": 250,
        #     "trend_data": [
        #         {"date": "2025-06-10", "count": 120},
        #         {"date": "2025-06-11", "count": 150},
        #         {"date": "2025-06-12", "count": 180},
        #         {"date": "2025-06-13", "count": 200},
        #         {"date": "2025-06-14", "count": 170},
        #         {"date": "2025-06-15", "count": 190},
        #         {"date": "2025-06-16", "count": 210}
        #     ]
        # }
        
        logger.info(f'获取传播分析汇总数据成功，方案ID: {scheme_id}')
        # return ResponseUtil.success(data=mock_data)
        return ResponseUtil.error(msg='接口暂未实现，请先完成数据库查询逻辑')
    except Exception as e:
        logger.error(f'获取传播分析汇总数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取传播分析汇总数据失败: {str(e)}')


@spreadAnalysisController.get('/schemes/{scheme_type}')
async def get_schemes_by_type(
    request: Request,
    scheme_type: str,
    query_db: AsyncSession = Depends(get_db),
):
    """
    根据方案类型获取方案列表
    """
    try:
        # TODO: 实现真实的数据库查询逻辑
        # 1. 查询scheme_type表获取类型信息
        # 2. 查询scheme表获取该类型下的所有方案
        # 3. 返回方案列表数据
        
        # 临时返回模拟数据，待实现真实逻辑后删除
        # mock_data = [
        #     {"id": 1, "name": "品牌监控方案", "type": scheme_type, "status": 1},
        #     {"id": 2, "name": "竞品分析方案", "type": scheme_type, "status": 1},
        #     {"id": 3, "name": "行业监测方案", "type": scheme_type, "status": 1}
        # ]
        
        logger.info(f'根据类型获取方案列表成功，类型: {scheme_type}')
        # return ResponseUtil.success(data=mock_data)
        return ResponseUtil.error(msg='接口暂未实现，请先完成数据库查询逻辑')
    except Exception as e:
        logger.error(f'根据类型获取方案列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'根据类型获取方案列表失败: {str(e)}')


@spreadAnalysisController.get('/mock-data/{scheme_id}')
async def get_mock_spread_analysis_data(
    request: Request,
    scheme_id: int,
):
    """
    获取模拟传播分析数据
    """
    try:
        # TODO: 实现真实的数据库查询逻辑
        # 1. 查询scheme表获取方案信息
        # 2. 查询news表获取相关新闻数据
        # 3. 统计平台分布数据
        # 4. 统计情感分析数据
        # 5. 生成关键词云数据
        
        # 临时返回模拟数据，待实现真实逻辑后删除
        # mock_data = {
        #     "scheme_id": scheme_id,
        #     "analysis_type": "spread",
        #     "data": {
        #         "platform_distribution": {
        #             "weibo": 45.2,
        #             "wechat": 32.1,
        #             "douyin": 15.3,
        #             "other": 7.4
        #         },
        #         "emotion_analysis": {
        #             "positive": 65.8,
        #             "negative": 18.2,
        #             "neutral": 16.0
        #         },
        #         "keyword_cloud": [
        #             {"word": "品牌", "count": 1200},
        #             {"word": "质量", "count": 800},
        #             {"word": "服务", "count": 600},
        #             {"word": "创新", "count": 400}
        #         ]
        #     }
        # }
        
        logger.info(f'获取模拟传播分析数据成功，方案ID: {scheme_id}')
        # return ResponseUtil.success(data=mock_data)
        return ResponseUtil.error(msg='接口暂未实现，请先完成数据库查询逻辑')
    except Exception as e:
        logger.error(f'获取模拟传播分析数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取模拟传播分析数据失败: {str(e)}')


@spreadAnalysisController.get('/comprehensive/{scheme_id}')
async def get_comprehensive_spread_analysis(
    request: Request,
    scheme_id: int,
    time_range: Optional[str] = Query(default="today", description="时间范围"),
    date_start: Optional[str] = Query(default=None, description="开始日期"),
    date_end: Optional[str] = Query(default=None, description="结束日期"),
    platform_types: Optional[str] = Query(default=None, description="平台类型，逗号分隔"),
    sentiment_types: Optional[str] = Query(default=None, description="情感类型，逗号分隔"),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取综合传播分析
    """
    try:
        # 处理平台类型参数
        platform_types_list = platform_types.split(',') if platform_types else None
        sentiment_types_list = sentiment_types.split(',') if sentiment_types else None

        # 调用服务层获取综合传播分析数据
        analysis_data = await SpreadAnalysisService.get_comprehensive_spread_analysis(
            query_db, scheme_id, time_range, date_start, date_end,
            platform_types_list, sentiment_types_list
        )

        logger.info(f'获取综合传播分析成功，方案ID: {scheme_id}')
        return ResponseUtil.success(data=analysis_data.model_dump())
    except Exception as e:
        logger.error(f'获取综合传播分析失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取综合传播分析失败: {str(e)}')


@spreadAnalysisController.get('/emotion-statistics')
async def get_emotion_statistics(
    request: Request,
    time_range: Optional[str] = Query(default="today", description="时间范围"),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取情感统计数据
    """
    try:
        # 调用服务层获取情感统计数据
        emotion_stats = await SpreadAnalysisService.get_emotion_statistics(query_db)

        logger.info(f'获取情感统计数据成功')
        return ResponseUtil.success(data=emotion_stats.model_dump())
    except Exception as e:
        logger.error(f'获取情感统计数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取情感统计数据失败: {str(e)}')


@spreadAnalysisController.get('/hot-news-extension/statistics')
async def get_hot_news_extension_statistics(
    request: Request,
    time_range: Optional[str] = Query(default="today", description="时间范围"),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取热点新闻扩展统计数据
    """
    try:
        # 调用服务层获取热点新闻扩展统计数据
        extension_stats = await SpreadAnalysisService.get_hot_news_extension_statistics(query_db, time_range)

        logger.info(f'获取热点新闻扩展统计数据成功')
        return ResponseUtil.success(data=extension_stats.model_dump())
    except Exception as e:
        logger.error(f'获取热点新闻扩展统计数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点新闻扩展统计数据失败: {str(e)}')


@spreadAnalysisController.get('/event/statistics')
async def get_event_statistics(
    request: Request,
    time_range: Optional[str] = Query(default="today", description="时间范围"),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取事件统计数据
    """
    try:
        # 调用服务层获取事件统计数据
        event_stats = await SpreadAnalysisService.get_event_statistics(query_db, time_range)

        logger.info(f'获取事件统计数据成功')
        return ResponseUtil.success(data=event_stats.model_dump())
    except Exception as e:
        logger.error(f'获取事件统计数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取事件统计数据失败: {str(e)}')


@spreadAnalysisController.get('/event-news/statistics')
async def get_event_news_statistics(
    request: Request,
    time_range: Optional[str] = Query(default="today", description="时间范围"),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取事件新闻关联统计数据
    """
    try:
        # 调用服务层获取事件新闻关联统计数据
        event_news_stats = await SpreadAnalysisService.get_event_news_statistics(query_db, time_range)

        logger.info(f'获取事件新闻关联统计数据成功')
        return ResponseUtil.success(data=event_news_stats.model_dump())
    except Exception as e:
        logger.error(f'获取事件新闻关联统计数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取事件新闻关联统计数据失败: {str(e)}')


@spreadAnalysisController.get('/emotion-statistics')
async def get_emotion_statistics(request: Request):
    """
    获取情感统计数据
    """
    try:
        mock_data = {
            "total_count": 15420,
            "positive_count": 9876,
            "negative_count": 2341,
            "neutral_count": 3203,
            "positive_rate": 64.1,
            "negative_rate": 15.2,
            "neutral_rate": 20.7
        }
        logger.info('获取情感统计数据成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取情感统计数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取情感统计数据失败: {str(e)}')


# ==================== 元搜索相关接口 ====================

@metaSearchController.get('/fulltext/search')
async def fulltext_search(
    request: Request,
    keyword: str = Query(..., description="搜索关键词"),
    page: int = Query(1, description="页码"),
    size: int = Query(10, description="每页数量"),
):
    """
    全文检索搜索
    """
    try:
        mock_data = {
            "keyword": keyword,
            "total": 1250,
            "page": page,
            "size": size,
            "results": [
                {
                    "title": f"关于{keyword}的最新资讯",
                    "content": f"这是关于{keyword}的详细内容介绍...",
                    "url": f"https://example.com/news/{keyword}",
                    "source": "新华网",
                    "publish_time": "2025-06-17 10:30:00",
                    "relevance": 0.95
                },
                {
                    "title": f"{keyword}行业发展趋势分析",
                    "content": f"{keyword}行业正在经历快速发展...",
                    "url": f"https://example.com/analysis/{keyword}",
                    "source": "人民日报",
                    "publish_time": "2025-06-17 09:15:00",
                    "relevance": 0.88
                }
            ]
        }
        logger.info(f'全文检索搜索成功，关键词: {keyword}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'全文检索搜索失败: {str(e)}')
        return ResponseUtil.error(msg=f'全文检索搜索失败: {str(e)}')


@metaSearchController.get('/meta/search')
async def meta_search(
    request: Request,
    keyword: str = Query(..., description="搜索关键词"),
):
    """
    元搜索 - 多搜索引擎结果
    """
    try:
        mock_data = {
            "keyword": keyword,
            "engines": ["baidu", "bing", "360"],
            "results": {
                "baidu": [
                    {
                        "title": f"{keyword} - 百度百科",
                        "url": f"https://baike.baidu.com/item/{keyword}",
                        "description": f"{keyword}是一个重要的概念..."
                    }
                ],
                "bing": [
                    {
                        "title": f"{keyword} - 必应搜索",
                        "url": f"https://www.bing.com/search?q={keyword}",
                        "description": f"关于{keyword}的权威信息..."
                    }
                ],
                "360": [
                    {
                        "title": f"{keyword} - 360搜索",
                        "url": f"https://www.so.com/s?q={keyword}",
                        "description": f"最新{keyword}相关资讯..."
                    }
                ]
            }
        }
        logger.info(f'元搜索成功，关键词: {keyword}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'元搜索失败: {str(e)}')
        return ResponseUtil.error(msg=f'元搜索失败: {str(e)}')


@metaSearchController.get('/statistics')
async def get_search_statistics(request: Request):
    """
    获取搜索统计信息
    """
    try:
        mock_data = {
            "total_searches": 125430,
            "today_searches": 1250,
            "popular_keywords": [
                {"keyword": "人工智能", "count": 2340},
                {"keyword": "大数据", "count": 1890},
                {"keyword": "云计算", "count": 1560},
                {"keyword": "区块链", "count": 1230}
            ],
            "search_trends": [
                {"date": "2025-06-10", "count": 980},
                {"date": "2025-06-11", "count": 1120},
                {"date": "2025-06-12", "count": 1350},
                {"date": "2025-06-13", "count": 1180},
                {"date": "2025-06-14", "count": 1290},
                {"date": "2025-06-15", "count": 1450},
                {"date": "2025-06-16", "count": 1250}
            ]
        }
        logger.info('获取搜索统计信息成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取搜索统计信息失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取搜索统计信息失败: {str(e)}')


@metaSearchController.get('/suggestions')
async def get_search_suggestions(
    request: Request,
    keyword: str = Query(..., description="关键词"),
):
    """
    获取搜索建议
    """
    try:
        mock_data = {
            "keyword": keyword,
            "suggestions": [
                f"{keyword}最新动态",
                f"{keyword}发展趋势",
                f"{keyword}技术应用",
                f"{keyword}市场分析",
                f"{keyword}政策解读"
            ]
        }
        logger.info(f'获取搜索建议成功，关键词: {keyword}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取搜索建议失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取搜索建议失败: {str(e)}')


@metaSearchController.get('/health')
async def health_check(request: Request):
    """
    健康检查
    """
    try:
        mock_data = {
            "status": "healthy",
            "timestamp": "2025-06-17 17:30:00",
            "services": {
                "search_engine": "online",
                "database": "online",
                "cache": "online"
            }
        }
        logger.info('元搜索健康检查成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'元搜索健康检查失败: {str(e)}')
        return ResponseUtil.error(msg=f'元搜索健康检查失败: {str(e)}')


# ==================== 热点事件相关接口 ====================

@hotEventsController.get('/simple/list')
async def get_simple_hot_events_list(
    request: Request,
    category: Optional[str] = Query(None, description="事件分类"),
    page: int = Query(1, description="页码"),
    size: int = Query(10, description="每页数量"),
):
    """
    获取简单热点事件列表
    """
    try:
        mock_data = {
            "total": 156,
            "page": page,
            "size": size,
            "category": category,
            "events": [
                {
                    "id": 1,
                    "title": "科技创新推动经济发展",
                    "category": "科技",
                    "heat_score": 95.8,
                    "publish_time": "2025-06-17 08:30:00",
                    "source": "科技日报",
                    "summary": "最新科技创新成果在多个领域取得突破..."
                },
                {
                    "id": 2,
                    "title": "绿色发展理念深入人心",
                    "category": "环保",
                    "heat_score": 88.2,
                    "publish_time": "2025-06-17 09:15:00",
                    "source": "环球时报",
                    "summary": "绿色发展理念正在各行各业得到广泛应用..."
                },
                {
                    "id": 3,
                    "title": "教育改革取得新进展",
                    "category": "教育",
                    "heat_score": 82.5,
                    "publish_time": "2025-06-17 10:00:00",
                    "source": "中国教育报",
                    "summary": "教育改革在多个方面取得显著成效..."
                }
            ]
        }
        logger.info(f'获取简单热点事件列表成功，分类: {category}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取简单热点事件列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取简单热点事件列表失败: {str(e)}')


@hotEventsController.get('/simple/map')
async def get_simple_hot_events_map(
    request: Request,
    date: Optional[str] = Query(None, description="日期"),
):
    """
    获取热点事件地图数据
    """
    try:
        mock_data = {
            "date": date or "2025-06-17",
            "regions": [
                {"name": "北京", "value": 1250, "events": 45},
                {"name": "上海", "value": 980, "events": 38},
                {"name": "广东", "value": 1180, "events": 42},
                {"name": "浙江", "value": 850, "events": 32},
                {"name": "江苏", "value": 920, "events": 35},
                {"name": "山东", "value": 780, "events": 28},
                {"name": "四川", "value": 650, "events": 25}
            ],
            "total_events": 245,
            "total_heat": 6610
        }
        logger.info(f'获取热点事件地图数据成功，日期: {date}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件地图数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件地图数据失败: {str(e)}')


@hotEventsController.get('/simple/details/{event_id}')
async def get_simple_hot_events_details(
    request: Request,
    event_id: int,
):
    """
    获取简单热点事件详情
    """
    try:
        mock_data = {
            "id": event_id,
            "title": "科技创新推动经济发展",
            "category": "科技",
            "heat_score": 95.8,
            "publish_time": "2025-06-17 08:30:00",
            "source": "科技日报",
            "content": "随着科技创新的不断推进，我国在人工智能、大数据、云计算等领域取得了重大突破...",
            "keywords": ["科技创新", "经济发展", "人工智能", "大数据"],
            "related_events": [
                {"id": 2, "title": "AI技术在医疗领域的应用"},
                {"id": 3, "title": "大数据助力智慧城市建设"}
            ],
            "statistics": {
                "view_count": 12580,
                "share_count": 856,
                "comment_count": 234
            }
        }
        logger.info(f'获取简单热点事件详情成功，事件ID: {event_id}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取简单热点事件详情失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取简单热点事件详情失败: {str(e)}')


@hotEventsController.get('/category/{category}')
async def get_hot_events_by_category(
    request: Request,
    category: str,
    page: int = Query(1, description="页码"),
    size: int = Query(10, description="每页数量"),
):
    """
    按分类获取热点事件
    """
    try:
        mock_data = {
            "category": category,
            "total": 89,
            "page": page,
            "size": size,
            "events": [
                {
                    "id": 10,
                    "title": f"{category}领域最新动态",
                    "heat_score": 92.3,
                    "publish_time": "2025-06-17 11:20:00",
                    "source": "行业资讯",
                    "summary": f"在{category}领域，最新的发展动态显示..."
                },
                {
                    "id": 11,
                    "title": f"{category}行业发展趋势",
                    "heat_score": 87.6,
                    "publish_time": "2025-06-17 12:05:00",
                    "source": "专业媒体",
                    "summary": f"{category}行业正在经历重要的变革..."
                }
            ]
        }
        logger.info(f'按分类获取热点事件成功，分类: {category}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'按分类获取热点事件失败: {str(e)}')
        return ResponseUtil.error(msg=f'按分类获取热点事件失败: {str(e)}')


@hotEventsController.get('/list')
async def get_hot_events_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(10, description="每页数量"),
    keyword: Optional[str] = Query(None, description="关键词"),
):
    """
    获取热点事件列表
    """
    try:
        mock_data = {
            "total": 234,
            "page": page,
            "size": size,
            "keyword": keyword,
            "events": [
                {
                    "id": 1,
                    "title": "科技创新推动经济发展",
                    "category": "科技",
                    "heat_score": 95.8,
                    "publish_time": "2025-06-17 08:30:00",
                    "source": "科技日报",
                    "summary": "最新科技创新成果在多个领域取得突破...",
                    "view_count": 15600,
                    "share_count": 234,
                    "comment_count": 567
                },
                {
                    "id": 2,
                    "title": "绿色发展理念深入人心",
                    "category": "环保",
                    "heat_score": 88.2,
                    "publish_time": "2025-06-17 09:15:00",
                    "source": "环球时报",
                    "summary": "绿色发展理念正在各行各业得到广泛应用...",
                    "view_count": 12300,
                    "share_count": 189,
                    "comment_count": 445
                }
            ]
        }
        logger.info(f'获取热点事件列表成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件列表失败: {str(e)}')


@hotEventsController.get('/detail/{event_id}')
async def get_hot_events_detail(
    request: Request,
    event_id: int,
):
    """
    获取热点事件详情
    """
    try:
        mock_data = {
            "id": event_id,
            "title": "科技创新推动经济发展",
            "category": "科技",
            "heat_score": 95.8,
            "publish_time": "2025-06-17 08:30:00",
            "source": "科技日报",
            "author": "张记者",
            "content": "随着科技创新的不断推进，我国在人工智能、大数据、云计算等领域取得了重大突破。这些技术的应用不仅提升了生产效率，也为经济发展注入了新的活力。专家表示，科技创新将继续成为推动经济高质量发展的重要引擎。",
            "keywords": ["科技创新", "经济发展", "人工智能", "大数据", "云计算"],
            "tags": ["科技", "经济", "创新"],
            "related_events": [
                {"id": 2, "title": "AI技术在医疗领域的应用", "heat_score": 87.3},
                {"id": 3, "title": "大数据助力智慧城市建设", "heat_score": 82.1}
            ],
            "statistics": {
                "view_count": 15600,
                "share_count": 856,
                "comment_count": 234,
                "like_count": 1234
            },
            "sentiment_analysis": {
                "positive": 78.5,
                "negative": 12.3,
                "neutral": 9.2
            }
        }
        logger.info(f'获取热点事件详情成功，事件ID: {event_id}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件详情失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件详情失败: {str(e)}')


@hotEventsController.get('/statistics')
async def get_hot_events_statistics(
    request: Request,
    time_range: Optional[str] = Query(default="today", description="时间范围"),
):
    """
    获取热点事件统计
    """
    try:
        mock_data = {
            "time_range": time_range,
            "total_events": 1456,
            "today_events": 89,
            "hot_events": 234,
            "trending_events": 67,
            "average_heat_score": 76.8,
            "top_categories": [
                {"category": "科技", "count": 234, "percentage": 28.5},
                {"category": "经济", "count": 189, "percentage": 23.1},
                {"category": "社会", "count": 156, "percentage": 19.0},
                {"category": "文化", "count": 123, "percentage": 15.0},
                {"category": "体育", "count": 98, "percentage": 12.0}
            ],
            "heat_distribution": {
                "high": 156,
                "medium": 567,
                "low": 733
            },
            "trend_data": [
                {"date": "2025-06-10", "count": 67, "avg_heat": 72.3},
                {"date": "2025-06-11", "count": 78, "avg_heat": 74.8},
                {"date": "2025-06-12", "count": 89, "avg_heat": 76.2},
                {"date": "2025-06-13", "count": 95, "avg_heat": 78.5},
                {"date": "2025-06-14", "count": 82, "avg_heat": 75.9},
                {"date": "2025-06-15", "count": 91, "avg_heat": 77.3},
                {"date": "2025-06-16", "count": 89, "avg_heat": 76.8}
            ]
        }
        logger.info(f'获取热点事件统计成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件统计失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件统计失败: {str(e)}')


@hotEventsController.get('/trend')
async def get_hot_events_trend(
    request: Request,
    time_range: Optional[str] = Query(default="7days", description="时间范围"),
    category: Optional[str] = Query(None, description="事件分类"),
):
    """
    获取热点事件趋势
    """
    try:
        mock_data = {
            "time_range": time_range,
            "category": category,
            "trend_data": [
                {"time": "2025-06-10 00:00", "heat_score": 72.3, "event_count": 12},
                {"time": "2025-06-10 06:00", "heat_score": 68.9, "event_count": 8},
                {"time": "2025-06-10 12:00", "heat_score": 85.2, "event_count": 15},
                {"time": "2025-06-10 18:00", "heat_score": 91.7, "event_count": 18},
                {"time": "2025-06-11 00:00", "heat_score": 76.4, "event_count": 14},
                {"time": "2025-06-11 06:00", "heat_score": 71.8, "event_count": 10},
                {"time": "2025-06-11 12:00", "heat_score": 88.5, "event_count": 16}
            ],
            "peak_times": [
                {"time": "12:00-14:00", "avg_heat": 87.3},
                {"time": "18:00-20:00", "avg_heat": 89.6},
                {"time": "20:00-22:00", "avg_heat": 85.1}
            ],
            "growth_rate": 12.5,
            "prediction": {
                "next_24h": 78.9,
                "next_week": 82.1
            }
        }
        logger.info(f'获取热点事件趋势成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件趋势失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件趋势失败: {str(e)}')


@hotEventsController.get('/ranking')
async def get_hot_events_ranking(
    request: Request,
    time_range: Optional[str] = Query(default="today", description="时间范围"),
    category: Optional[str] = Query(None, description="事件分类"),
):
    """
    获取热点事件排行榜
    """
    try:
        mock_data = {
            "time_range": time_range,
            "category": category,
            "ranking": [
                {
                    "rank": 1,
                    "id": 1,
                    "title": "科技创新推动经济发展",
                    "heat_score": 95.8,
                    "growth_rate": 15.2,
                    "category": "科技",
                    "source": "科技日报"
                },
                {
                    "rank": 2,
                    "id": 2,
                    "title": "绿色发展理念深入人心",
                    "heat_score": 88.2,
                    "growth_rate": 12.7,
                    "category": "环保",
                    "source": "环球时报"
                },
                {
                    "rank": 3,
                    "id": 3,
                    "title": "教育改革取得新进展",
                    "heat_score": 82.6,
                    "growth_rate": 9.8,
                    "category": "教育",
                    "source": "教育日报"
                }
            ],
            "total_count": 156,
            "update_time": "2025-06-17 18:56:00"
        }
        logger.info(f'获取热点事件排行榜成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件排行榜失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件排行榜失败: {str(e)}')


@hotEventsController.get('/keywords/{event_id}')
async def get_hot_events_keywords(
    request: Request,
    event_id: int,
):
    """
    获取热点事件关键词
    """
    try:
        mock_data = {
            "event_id": event_id,
            "keywords": [
                {"keyword": "科技创新", "weight": 0.95, "frequency": 156},
                {"keyword": "经济发展", "weight": 0.87, "frequency": 134},
                {"keyword": "人工智能", "weight": 0.82, "frequency": 98},
                {"keyword": "大数据", "weight": 0.76, "frequency": 87},
                {"keyword": "云计算", "weight": 0.71, "frequency": 76}
            ],
            "keyword_cloud": {
                "positive": ["创新", "发展", "突破", "进步", "成功"],
                "neutral": ["技术", "应用", "领域", "行业", "市场"],
                "negative": ["挑战", "困难", "问题", "风险", "障碍"]
            },
            "related_topics": [
                "人工智能发展",
                "数字经济转型",
                "科技产业升级",
                "创新驱动战略"
            ]
        }
        logger.info(f'获取热点事件关键词成功，事件ID: {event_id}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件关键词失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件关键词失败: {str(e)}')


@hotEventsController.get('/spread-analysis/{event_id}')
async def get_hot_events_spread_analysis(
    request: Request,
    event_id: int,
):
    """
    获取热点事件传播分析
    """
    try:
        mock_data = {
            "event_id": event_id,
            "spread_metrics": {
                "total_reach": 1256789,
                "engagement_rate": 8.7,
                "share_rate": 3.2,
                "comment_rate": 2.1,
                "influence_score": 87.5
            },
            "platform_distribution": {
                "weibo": {"reach": 456789, "percentage": 36.3},
                "wechat": {"reach": 389012, "percentage": 31.0},
                "douyin": {"reach": 234567, "percentage": 18.7},
                "toutiao": {"reach": 176421, "percentage": 14.0}
            },
            "spread_timeline": [
                {"time": "2025-06-17 08:00", "reach": 1250, "velocity": 125},
                {"time": "2025-06-17 10:00", "reach": 5670, "velocity": 567},
                {"time": "2025-06-17 12:00", "reach": 15890, "velocity": 1589},
                {"time": "2025-06-17 14:00", "reach": 45670, "velocity": 4567},
                {"time": "2025-06-17 16:00", "reach": 89012, "velocity": 8901},
                {"time": "2025-06-17 18:00", "reach": 125678, "velocity": 12567}
            ]
        }
        logger.info(f'获取热点事件传播分析成功，事件ID: {event_id}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件传播分析失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件传播分析失败: {str(e)}')


@hotEventsController.get('/ranking')
async def get_hot_events_ranking(
    request: Request,
    time_range: Optional[str] = Query(default="today", description="时间范围"),
    category: Optional[str] = Query(None, description="事件分类"),
):
    """
    获取热点事件排行榜
    """
    try:
        mock_data = {
            "time_range": time_range,
            "category": category,
            "ranking": [
                {
                    "rank": 1,
                    "id": 1,
                    "title": "科技创新推动经济发展",
                    "heat_score": 95.8,
                    "growth_rate": 15.2,
                    "category": "科技",
                    "source": "科技日报"
                },
                {
                    "rank": 2,
                    "id": 2,
                    "title": "绿色发展理念深入人心",
                    "heat_score": 88.2,
                    "growth_rate": 12.7,
                    "category": "环保",
                    "source": "环球时报"
                },
                {
                    "rank": 3,
                    "id": 3,
                    "title": "教育改革取得新进展",
                    "heat_score": 82.6,
                    "growth_rate": 9.8,
                    "category": "教育",
                    "source": "教育日报"
                }
            ],
            "total_count": 156,
            "update_time": "2025-06-17 18:56:00"
        }
        logger.info(f'获取热点事件排行榜成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件排行榜失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件排行榜失败: {str(e)}')


@hotEventsController.get('/keywords/{event_id}')
async def get_hot_events_keywords(
    request: Request,
    event_id: int,
):
    """
    获取热点事件关键词
    """
    try:
        mock_data = {
            "event_id": event_id,
            "keywords": [
                {"keyword": "科技创新", "weight": 0.95, "frequency": 156},
                {"keyword": "经济发展", "weight": 0.87, "frequency": 134},
                {"keyword": "人工智能", "weight": 0.82, "frequency": 98},
                {"keyword": "大数据", "weight": 0.76, "frequency": 87},
                {"keyword": "云计算", "weight": 0.71, "frequency": 76},
                {"keyword": "数字化", "weight": 0.68, "frequency": 65},
                {"keyword": "智能制造", "weight": 0.63, "frequency": 54}
            ],
            "keyword_cloud": {
                "positive": ["创新", "发展", "突破", "进步", "成功"],
                "neutral": ["技术", "应用", "领域", "行业", "市场"],
                "negative": ["挑战", "困难", "问题", "风险", "障碍"]
            },
            "related_topics": [
                "人工智能发展",
                "数字经济转型",
                "科技产业升级",
                "创新驱动战略"
            ]
        }
        logger.info(f'获取热点事件关键词成功，事件ID: {event_id}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件关键词失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件关键词失败: {str(e)}')


@hotEventsController.get('/spread-analysis/{event_id}')
async def get_hot_events_spread_analysis(
    request: Request,
    event_id: int,
):
    """
    获取热点事件传播分析
    """
    try:
        mock_data = {
            "event_id": event_id,
            "spread_metrics": {
                "total_reach": 1256789,
                "engagement_rate": 8.7,
                "share_rate": 3.2,
                "comment_rate": 2.1,
                "influence_score": 87.5
            },
            "platform_distribution": {
                "weibo": {"reach": 456789, "percentage": 36.3},
                "wechat": {"reach": 389012, "percentage": 31.0},
                "douyin": {"reach": 234567, "percentage": 18.7},
                "toutiao": {"reach": 176421, "percentage": 14.0}
            },
            "spread_timeline": [
                {"time": "2025-06-17 08:00", "reach": 1250, "velocity": 125},
                {"time": "2025-06-17 10:00", "reach": 5670, "velocity": 567},
                {"time": "2025-06-17 12:00", "reach": 15890, "velocity": 1589},
                {"time": "2025-06-17 14:00", "reach": 45670, "velocity": 4567},
                {"time": "2025-06-17 16:00", "reach": 89012, "velocity": 8901},
                {"time": "2025-06-17 18:00", "reach": 125678, "velocity": 12567}
            ],
            "audience_analysis": {
                "age_distribution": {
                    "18-25": 28.5,
                    "26-35": 35.2,
                    "36-45": 22.1,
                    "46-55": 10.8,
                    "55+": 3.4
                },
                "gender_distribution": {
                    "male": 52.3,
                    "female": 47.7
                },
                "region_distribution": {
                    "一线城市": 45.6,
                    "二线城市": 32.1,
                    "三线城市": 15.8,
                    "其他": 6.5
                }
            }
        }
        logger.info(f'获取热点事件传播分析成功，事件ID: {event_id}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件传播分析失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件传播分析失败: {str(e)}')


@hotEventsController.get('/influence/{event_id}')
async def get_hot_events_influence(
    request: Request,
    event_id: int,
):
    """
    获取热点事件影响力分析
    """
    try:
        mock_data = {
            "event_id": event_id,
            "influence_score": 87.5,
            "influence_level": "高",
            "influence_factors": {
                "media_coverage": 92.3,
                "social_engagement": 85.7,
                "expert_attention": 78.9,
                "public_discussion": 89.2
            },
            "impact_areas": [
                {"area": "科技行业", "impact_score": 95.2, "description": "对科技行业产生重大影响"},
                {"area": "经济政策", "impact_score": 82.6, "description": "影响相关经济政策制定"},
                {"area": "公众认知", "impact_score": 78.4, "description": "提升公众对科技创新的认知"},
                {"area": "投资市场", "impact_score": 71.8, "description": "影响相关投资决策"}
            ],
            "influence_trend": [
                {"date": "2025-06-10", "score": 45.2},
                {"date": "2025-06-11", "score": 58.7},
                {"date": "2025-06-12", "score": 72.3},
                {"date": "2025-06-13", "score": 84.6},
                {"date": "2025-06-14", "score": 87.5},
                {"date": "2025-06-15", "score": 89.1},
                {"date": "2025-06-16", "score": 87.5}
            ],
            "key_influencers": [
                {"name": "科技专家A", "influence": 92.5, "followers": 1250000},
                {"name": "行业分析师B", "influence": 87.3, "followers": 890000},
                {"name": "媒体人C", "influence": 82.1, "followers": 650000}
            ]
        }
        logger.info(f'获取热点事件影响力分析成功，事件ID: {event_id}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件影响力分析失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件影响力分析失败: {str(e)}')


@hotEventsController.get('/related-news/{event_id}')
async def get_hot_events_related_news(
    request: Request,
    event_id: int,
    page: int = Query(1, description="页码"),
    size: int = Query(10, description="每页数量"),
):
    """
    获取热点事件相关新闻
    """
    try:
        mock_data = {
            "event_id": event_id,
            "total": 89,
            "page": page,
            "size": size,
            "news": [
                {
                    "id": 1,
                    "title": "科技创新助力经济高质量发展",
                    "source": "人民日报",
                    "publish_time": "2025-06-17 08:30:00",
                    "url": "https://example.com/news/1",
                    "summary": "科技创新正在成为推动经济发展的重要引擎...",
                    "relevance_score": 0.95
                },
                {
                    "id": 2,
                    "title": "人工智能技术在各行业广泛应用",
                    "source": "科技日报",
                    "publish_time": "2025-06-17 09:15:00",
                    "url": "https://example.com/news/2",
                    "summary": "人工智能技术正在各个行业得到广泛应用...",
                    "relevance_score": 0.87
                }
            ]
        }
        logger.info(f'获取热点事件相关新闻成功，事件ID: {event_id}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件相关新闻失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件相关新闻失败: {str(e)}')


@hotEventsController.get('/timeline/{event_id}')
async def get_hot_events_timeline(
    request: Request,
    event_id: int,
):
    """
    获取热点事件时间轴
    """
    try:
        mock_data = {
            "event_id": event_id,
            "timeline": [
                {
                    "time": "2025-06-17 08:00:00",
                    "event": "事件首次被报道",
                    "source": "科技日报",
                    "description": "科技创新推动经济发展的新闻首次发布",
                    "heat_score": 15.2
                },
                {
                    "time": "2025-06-17 10:30:00",
                    "event": "社交媒体开始传播",
                    "source": "微博",
                    "description": "相关话题在微博平台开始传播",
                    "heat_score": 45.8
                },
                {
                    "time": "2025-06-17 14:20:00",
                    "event": "专家发表评论",
                    "source": "专业媒体",
                    "description": "行业专家对此事件发表专业评论",
                    "heat_score": 72.3
                },
                {
                    "time": "2025-06-17 18:00:00",
                    "event": "热度达到峰值",
                    "source": "综合平台",
                    "description": "事件热度达到当日峰值",
                    "heat_score": 95.8
                }
            ],
            "peak_time": "2025-06-17 18:00:00",
            "duration": "10小时",
            "total_mentions": 15678
        }
        logger.info(f'获取热点事件时间轴成功，事件ID: {event_id}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件时间轴失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件时间轴失败: {str(e)}')


@hotEventsController.get('/search')
async def search_hot_events(
    request: Request,
    keyword: str = Query(..., description="搜索关键词"),
    page: int = Query(1, description="页码"),
    size: int = Query(10, description="每页数量"),
):
    """
    搜索热点事件
    """
    try:
        mock_data = {
            "keyword": keyword,
            "total": 45,
            "page": page,
            "size": size,
            "events": [
                {
                    "id": 1,
                    "title": f"包含'{keyword}'的热点事件标题",
                    "category": "科技",
                    "heat_score": 89.5,
                    "publish_time": "2025-06-17 08:30:00",
                    "source": "科技日报",
                    "summary": f"这是一个包含关键词'{keyword}'的事件摘要...",
                    "match_score": 0.92
                },
                {
                    "id": 2,
                    "title": f"另一个关于'{keyword}'的重要事件",
                    "category": "经济",
                    "heat_score": 76.8,
                    "publish_time": "2025-06-17 09:15:00",
                    "source": "经济日报",
                    "summary": f"这是另一个与'{keyword}'相关的事件...",
                    "match_score": 0.85
                }
            ],
            "related_keywords": [
                f"{keyword}发展",
                f"{keyword}应用",
                f"{keyword}创新",
                f"{keyword}趋势"
            ]
        }
        logger.info(f'搜索热点事件成功，关键词: {keyword}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'搜索热点事件失败: {str(e)}')
        return ResponseUtil.error(msg=f'搜索热点事件失败: {str(e)}')


@hotEventsController.get('/prediction')
async def get_hot_events_prediction(
    request: Request,
    time_range: Optional[str] = Query(default="24h", description="预测时间范围"),
):
    """
    获取热点事件预测
    """
    try:
        mock_data = {
            "time_range": time_range,
            "prediction_time": "2025-06-17 19:00:00",
            "predicted_events": [
                {
                    "title": "预测热点事件1",
                    "category": "科技",
                    "predicted_heat_score": 85.2,
                    "confidence": 0.78,
                    "factors": ["关键词趋势上升", "相关话题增加", "专家关注度提升"]
                },
                {
                    "title": "预测热点事件2",
                    "category": "经济",
                    "predicted_heat_score": 72.6,
                    "confidence": 0.65,
                    "factors": ["政策发布预期", "市场关注度上升"]
                }
            ],
            "trend_analysis": {
                "overall_trend": "上升",
                "growth_rate": 12.5,
                "peak_prediction": "2025-06-18 14:00:00"
            }
        }
        logger.info(f'获取热点事件预测成功，时间范围: {time_range}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取热点事件预测失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取热点事件预测失败: {str(e)}')


@hotEventsController.get('/export')
async def export_hot_events_data(
    request: Request,
    format: Optional[str] = Query(default="excel", description="导出格式"),
    date_range: Optional[str] = Query(None, description="日期范围"),
):
    """
    导出热点事件数据
    """
    try:
        # 这里应该返回文件流，但为了演示，返回成功消息
        mock_data = {
            "export_id": "export_20250617_190000",
            "format": format,
            "date_range": date_range,
            "status": "success",
            "file_size": "2.5MB",
            "record_count": 1456,
            "download_url": f"/downloads/hot_events_{format}_20250617.{format}",
            "expires_at": "2025-06-24 19:00:00"
        }
        logger.info(f'导出热点事件数据成功，格式: {format}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'导出热点事件数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'导出热点事件数据失败: {str(e)}')


# ==================== 事件分析相关接口 ====================

@eventAnalysisController.get('/list')
async def get_event_analysis_list(
    request: Request,
    keyword: Optional[str] = Query(None, description="关键词"),
    page: int = Query(1, description="页码"),
    size: int = Query(10, description="每页数量"),
):
    """
    获取事件分析列表
    """
    try:
        mock_data = {
            "total": 78,
            "page": page,
            "size": size,
            "keyword": keyword,
            "analyses": [
                {
                    "id": 1,
                    "event_title": "科技创新政策发布",
                    "analysis_type": "影响力分析",
                    "status": "已完成",
                    "create_time": "2025-06-17 09:00:00",
                    "completion_rate": 100,
                    "key_findings": "政策发布后引起广泛关注，预计将推动相关产业发展"
                },
                {
                    "id": 2,
                    "event_title": "环保新规实施",
                    "analysis_type": "传播路径分析",
                    "status": "进行中",
                    "create_time": "2025-06-17 10:30:00",
                    "completion_rate": 65,
                    "key_findings": "传播路径主要通过官方媒体和社交平台"
                }
            ]
        }
        logger.info(f'获取事件分析列表成功，关键词: {keyword}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取事件分析列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取事件分析列表失败: {str(e)}')


@eventAnalysisController.get('/detail/{event_id}')
async def get_event_analysis_detail(
    request: Request,
    event_id: int,
):
    """
    获取事件分析详情
    """
    try:
        mock_data = {
            "id": event_id,
            "event_title": "科技创新政策发布",
            "analysis_type": "综合分析",
            "status": "已完成",
            "create_time": "2025-06-17 09:00:00",
            "update_time": "2025-06-17 15:30:00",
            "completion_rate": 100,
            "summary": {
                "total_mentions": 15420,
                "positive_sentiment": 78.5,
                "negative_sentiment": 12.3,
                "neutral_sentiment": 9.2,
                "peak_time": "2025-06-17 14:00:00",
                "influence_score": 92.8
            },
            "analysis_results": {
                "trend_analysis": "事件热度呈上升趋势，预计将持续关注",
                "sentiment_analysis": "整体情感倾向积极，公众反应良好",
                "influence_analysis": "影响力较大，涉及多个行业领域",
                "spread_analysis": "传播速度快，覆盖面广"
            }
        }
        logger.info(f'获取事件分析详情成功，事件ID: {event_id}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取事件分析详情失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取事件分析详情失败: {str(e)}')


@eventAnalysisController.get('/statistics')
async def get_event_analysis_statistics(
    request: Request,
    date_range: Optional[str] = Query(None, description="日期范围"),
):
    """
    获取事件分析统计数据
    """
    try:
        mock_data = {
            "date_range": date_range or "last_7_days",
            "total_events": 156,
            "completed_analyses": 134,
            "in_progress": 18,
            "pending": 4,
            "completion_rate": 85.9,
            "average_influence_score": 76.3,
            "top_categories": [
                {"category": "科技", "count": 45},
                {"category": "经济", "count": 38},
                {"category": "社会", "count": 32},
                {"category": "环保", "count": 25}
            ]
        }
        logger.info(f'获取事件分析统计数据成功，日期范围: {date_range}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取事件分析统计数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取事件分析统计数据失败: {str(e)}')


# ==================== 信源监测相关接口 ====================

@sourceMonitoringController.get('/personnel/list')
async def get_source_personnel_list(
    request: Request,
    keyword: Optional[str] = Query(None, description="关键词"),
    page: int = Query(1, description="页码"),
    size: int = Query(10, description="每页数量"),
):
    """
    获取信源人员列表
    """
    try:
        mock_data = {
            "total": 245,
            "page": page,
            "size": size,
            "keyword": keyword,
            "personnel": [
                {
                    "id": 1,
                    "name": "张三",
                    "position": "首席技术官",
                    "company": "科技创新公司",
                    "influence_score": 92.5,
                    "follower_count": 125000,
                    "recent_activity": "发布了关于AI发展的观点",
                    "last_update": "2025-06-17 14:30:00"
                },
                {
                    "id": 2,
                    "name": "李四",
                    "position": "行业分析师",
                    "company": "咨询机构",
                    "influence_score": 87.3,
                    "follower_count": 89000,
                    "recent_activity": "分享了市场趋势报告",
                    "last_update": "2025-06-17 13:45:00"
                }
            ]
        }
        logger.info(f'获取信源人员列表成功，关键词: {keyword}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取信源人员列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取信源人员列表失败: {str(e)}')


@sourceMonitoringController.get('/media/list')
async def get_source_media_list(
    request: Request,
    category: Optional[str] = Query(None, description="媒体分类"),
    page: int = Query(1, description="页码"),
    size: int = Query(10, description="每页数量"),
):
    """
    获取信源媒体列表
    """
    try:
        mock_data = {
            "total": 189,
            "page": page,
            "size": size,
            "category": category,
            "media": [
                {
                    "id": 1,
                    "name": "科技日报",
                    "category": "官方媒体",
                    "type": "新闻媒体",
                    "influence_score": 95.8,
                    "credibility": "高",
                    "daily_articles": 45,
                    "website": "https://www.stdaily.com",
                    "last_update": "2025-06-17 16:00:00"
                },
                {
                    "id": 2,
                    "name": "TechCrunch",
                    "category": "科技媒体",
                    "type": "专业媒体",
                    "influence_score": 88.2,
                    "credibility": "高",
                    "daily_articles": 32,
                    "website": "https://techcrunch.com",
                    "last_update": "2025-06-17 15:30:00"
                }
            ]
        }
        logger.info(f'获取信源媒体列表成功，分类: {category}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取信源媒体列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取信源媒体列表失败: {str(e)}')


@sourceMonitoringController.get('/monitoring/list')
async def get_media_monitoring_list(
    request: Request,
    status: Optional[str] = Query(None, description="监控状态"),
    page: int = Query(1, description="页码"),
    size: int = Query(10, description="每页数量"),
):
    """
    获取媒体监控列表
    """
    try:
        mock_data = {
            "total": 156,
            "page": page,
            "size": size,
            "status": status,
            "monitoring": [
                {
                    "id": 1,
                    "target_name": "科技创新关键词监控",
                    "target_type": "关键词",
                    "keywords": ["人工智能", "机器学习", "深度学习"],
                    "status": "运行中",
                    "alert_count": 23,
                    "last_alert": "2025-06-17 15:45:00",
                    "create_time": "2025-06-10 09:00:00"
                },
                {
                    "id": 2,
                    "target_name": "竞品品牌监控",
                    "target_type": "品牌",
                    "keywords": ["竞品A", "竞品B"],
                    "status": "运行中",
                    "alert_count": 18,
                    "last_alert": "2025-06-17 14:20:00",
                    "create_time": "2025-06-08 14:30:00"
                }
            ]
        }
        logger.info(f'获取媒体监控列表成功，状态: {status}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取媒体监控列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取媒体监控列表失败: {str(e)}')


@sourceMonitoringController.get('/feedback/list')
async def get_media_feedback_list(
    request: Request,
    feedback_type: Optional[str] = Query(None, description="反馈类型"),
    page: int = Query(1, description="页码"),
    size: int = Query(10, description="每页数量"),
):
    """
    获取媒体反馈列表
    """
    try:
        mock_data = {
            "total": 89,
            "page": page,
            "size": size,
            "feedback_type": feedback_type,
            "feedback": [
                {
                    "id": 1,
                    "title": "监控精度提升建议",
                    "type": "改进建议",
                    "content": "建议增加更多的关键词匹配规则，提高监控精度",
                    "source": "用户反馈",
                    "priority": "中",
                    "status": "待处理",
                    "create_time": "2025-06-17 11:20:00"
                },
                {
                    "id": 2,
                    "title": "误报问题反馈",
                    "type": "问题反馈",
                    "content": "某些无关内容被错误标记为相关信息",
                    "source": "系统检测",
                    "priority": "高",
                    "status": "处理中",
                    "create_time": "2025-06-17 10:15:00"
                }
            ]
        }
        logger.info(f'获取媒体反馈列表成功，类型: {feedback_type}')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取媒体反馈列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取媒体反馈列表失败: {str(e)}')


@sourceMonitoringController.get('/statistics')
async def get_monitoring_statistics(
    request: Request,
):
    """
    获取监控统计数据
    """
    try:
        mock_data = {
            "total_sources": 1256,
            "active_sources": 1089,
            "inactive_sources": 167,
            "total_personnel": 456,
            "total_media": 189,
            "total_monitoring": 156,
            "total_feedback": 89,
            "daily_alerts": 234,
            "weekly_alerts": 1567,
            "monthly_alerts": 6789,
            "alert_accuracy": 87.5,
            "response_time": 2.3,
            "coverage_rate": 94.2,
            "source_distribution": {
                "official_media": 35.6,
                "social_media": 28.9,
                "news_portal": 22.1,
                "blog_forum": 13.4
            },
            "alert_trends": [
                {"date": "2025-06-10", "count": 156},
                {"date": "2025-06-11", "count": 189},
                {"date": "2025-06-12", "count": 234},
                {"date": "2025-06-13", "count": 267},
                {"date": "2025-06-14", "count": 245},
                {"date": "2025-06-15", "count": 289},
                {"date": "2025-06-16", "count": 312}
            ]
        }
        logger.info(f'获取监控统计数据成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取监控统计数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取监控统计数据失败: {str(e)}')


@sourceMonitoringController.get('/health')
async def health_check(
    request: Request,
):
    """
    健康检查
    """
    try:
        mock_data = {
            "status": "healthy",
            "timestamp": "2025-06-17 18:56:00",
            "services": {
                "database": "online",
                "redis": "online",
                "monitoring_engine": "online",
                "alert_system": "online"
            },
            "performance": {
                "cpu_usage": 45.2,
                "memory_usage": 67.8,
                "disk_usage": 34.1,
                "network_latency": 12.5
            },
            "uptime": "15 days 8 hours 23 minutes"
        }
        logger.info(f'健康检查成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'健康检查失败: {str(e)}')
        return ResponseUtil.error(msg=f'健康检查失败: {str(e)}')


# ==================== 简化接口 - 兼容现有前端 ====================

@sourceMonitoringController.get('/simple/personnel')
async def get_simple_source_personnel_list(
    request: Request,
):
    """
    获取简化信源人员列表
    """
    try:
        mock_data = [
            {
                "id": 1,
                "name": "张三",
                "position": "首席记者",
                "organization": "科技日报",
                "contact": "<EMAIL>",
                "influence_score": 92.5,
                "status": "活跃"
            },
            {
                "id": 2,
                "name": "李四",
                "position": "编辑主任",
                "organization": "人民网",
                "contact": "<EMAIL>",
                "influence_score": 88.7,
                "status": "活跃"
            },
            {
                "id": 3,
                "name": "王五",
                "position": "特约评论员",
                "organization": "新华网",
                "contact": "<EMAIL>",
                "influence_score": 85.3,
                "status": "正常"
            }
        ]
        logger.info(f'获取简化信源人员列表成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取简化信源人员列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取简化信源人员列表失败: {str(e)}')


@sourceMonitoringController.get('/simple/media')
async def get_simple_source_media_list(
    request: Request,
):
    """
    获取简化信源媒体列表
    """
    try:
        mock_data = [
            {
                "id": 1,
                "name": "科技日报",
                "platform": "官方媒体",
                "category": "科技类",
                "level": "国家级",
                "status": "正常",
                "contact_info": "<EMAIL>"
            },
            {
                "id": 2,
                "name": "人民网",
                "platform": "官方媒体",
                "category": "综合类",
                "level": "国家级",
                "status": "正常",
                "contact_info": "<EMAIL>"
            },
            {
                "id": 3,
                "name": "新浪科技",
                "platform": "商业媒体",
                "category": "科技类",
                "level": "行业级",
                "status": "正常",
                "contact_info": "<EMAIL>"
            }
        ]
        logger.info(f'获取简化信源媒体列表成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取简化信源媒体列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取简化信源媒体列表失败: {str(e)}')


@sourceMonitoringController.get('/simple/monitoring')
async def get_simple_media_monitoring_list(
    request: Request,
):
    """
    获取简化媒体监控列表
    """
    try:
        mock_data = [
            {
                "id": 1,
                "scheme_name": "品牌监控方案",
                "scheme_type": "品牌监测",
                "keywords": "华帝,方太,老板",
                "status": "运行中",
                "warning_count": 23,
                "last_warning_time": "2025-06-17T15:45:00"
            },
            {
                "id": 2,
                "scheme_name": "竞品分析方案",
                "scheme_type": "竞品监测",
                "keywords": "竞争对手,市场份额",
                "status": "运行中",
                "warning_count": 15,
                "last_warning_time": "2025-06-17T14:20:00"
            },
            {
                "id": 3,
                "scheme_name": "行业监测方案",
                "scheme_type": "行业监测",
                "keywords": "厨电行业,智能家电",
                "status": "暂停",
                "warning_count": 8,
                "last_warning_time": "2025-06-16T16:30:00"
            }
        ]
        logger.info(f'获取简化媒体监控列表成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取简化媒体监控列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取简化媒体监控列表失败: {str(e)}')


@sourceMonitoringController.get('/simple/feedback')
async def get_simple_media_feedback_list(
    request: Request,
):
    """
    获取简化媒体反馈列表
    """
    try:
        mock_data = [
            {
                "id": 1,
                "title": "华帝新品发布会报道",
                "platform": "科技日报",
                "sentiment_score": 0.85,
                "publish_time": "2025-06-17T10:30:00",
                "view_count": 15600,
                "share_count": 234,
                "like_count": 567
            },
            {
                "id": 2,
                "title": "方太智能厨电评测",
                "platform": "新浪科技",
                "sentiment_score": 0.72,
                "publish_time": "2025-06-17T09:15:00",
                "view_count": 12300,
                "share_count": 189,
                "like_count": 445
            },
            {
                "id": 3,
                "title": "厨电行业发展趋势分析",
                "platform": "人民网",
                "sentiment_score": 0.68,
                "publish_time": "2025-06-17T08:45:00",
                "view_count": 9800,
                "share_count": 156,
                "like_count": 332
            }
        ]
        logger.info(f'获取简化媒体反馈列表成功')
        return ResponseUtil.success(data=mock_data)
    except Exception as e:
        logger.error(f'获取简化媒体反馈列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取简化媒体反馈列表失败: {str(e)}')
