from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Request
from module_admin.dao.scheme_dao import SchemeDao, SchemeTypeDao, SchemeConfigDao
from module_admin.entity.do.scheme_do import SchemeDO, SchemeConfigDO
from module_admin.entity.vo.scheme_vo import (
    SchemeModel, SchemePageQueryModel, CreateSchemeModel, DeleteSchemeModel,
    SchemeMenuModel, SchemeTypeModel
)
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from utils.log_util import logger


class SchemeService:
    """
    方案业务逻辑层
    """

    @classmethod
    async def get_scheme_list_services(
        cls,
        query_db: AsyncSession,
        query_object: SchemePageQueryModel,
        is_page: bool = False
    ) -> PageResponseModel:
        """
        获取方案列表
        """
        scheme_list_result = await SchemeDao.get_scheme_list(query_db, query_object, is_page)
        logger.info(f"从数据库获取到 {len(scheme_list_result)} 条方案记录")

        # 转换为VO模型
        scheme_list = []
        for scheme_do in scheme_list_result:
            # 手动构建字典，使用正确的字段名
            scheme_data = {
                'id': scheme_do.id,
                'userId': scheme_do.user_id,
                'typeId': scheme_do.type_id,
                'name': scheme_do.name,
                'description': scheme_do.description,
                'status': scheme_do.status,
                'refreshStatus': scheme_do.refresh_status,
                'isWarning': scheme_do.is_warning,
                'version': scheme_do.version,
                'createTime': scheme_do.create_time,
                'updateTime': scheme_do.update_time,
                'createBy': scheme_do.create_by,
                'updateBy': scheme_do.update_by,
                'remark': scheme_do.remark
            }

            # 添加关联数据
            if scheme_do.scheme_type:
                scheme_data['schemeType'] = {
                    'id': scheme_do.scheme_type.id,
                    'name': scheme_do.scheme_type.name,
                    'description': scheme_do.scheme_type.description
                }

            scheme_model = SchemeModel.model_validate(scheme_data)
            scheme_list.append(scheme_model)
        
        # 获取总数
        total = await SchemeDao.get_scheme_count(query_db, query_object) if is_page else len(scheme_list)
        logger.info(f"转换后的方案列表数量: {len(scheme_list)}, 总数: {total}")

        result = PageResponseModel(
            records=scheme_list,
            total=total
        )
        logger.info(f"返回的分页结果: records数量={len(result.records)}, total={result.total}")
        return result

    @classmethod
    async def get_scheme_detail_services(
        cls,
        query_db: AsyncSession,
        scheme_id: int
    ) -> SchemeModel:
        """
        获取方案详情
        """
        scheme_do = await SchemeDao.get_scheme_detail_by_id(query_db, scheme_id)
        if not scheme_do:
            raise ValueError(f"方案ID {scheme_id} 不存在")
        
        # 手动构建字典，使用正确的字段名
        scheme_data = {
            'id': scheme_do.id,
            'userId': scheme_do.user_id,
            'typeId': scheme_do.type_id,
            'name': scheme_do.name,
            'description': scheme_do.description,
            'status': scheme_do.status,
            'refreshStatus': scheme_do.refresh_status,
            'isWarning': scheme_do.is_warning,
            'version': scheme_do.version,
            'createTime': scheme_do.create_time,
            'updateTime': scheme_do.update_time,
            'createBy': scheme_do.create_by,
            'updateBy': scheme_do.update_by,
            'remark': scheme_do.remark
        }

        # 添加关联数据
        if scheme_do.scheme_type:
            scheme_data['schemeType'] = {
                'id': scheme_do.scheme_type.id,
                'name': scheme_do.scheme_type.name,
                'description': scheme_do.scheme_type.description
            }

        scheme_model = SchemeModel.model_validate(scheme_data)
        return scheme_model

    @classmethod
    async def add_scheme_services(
        cls,
        request: Request,
        query_db: AsyncSession,
        add_scheme: CreateSchemeModel,
        current_user_id: int,
        current_user_name: str
    ) -> ResponseUtil:
        """
        新增方案
        """
        try:
            # 创建方案DO对象
            scheme_do = SchemeDO(
                user_id=current_user_id,
                type_id=add_scheme.type_id,
                name=add_scheme.name,
                description=add_scheme.description,
                status=1,
                refresh_status=0,
                is_warning=False,
                create_time=datetime.now(),
                update_time=datetime.now(),
                create_by=current_user_name,
                update_by=current_user_name,
                remark=add_scheme.remark
            )
            
            # 保存方案
            new_scheme = await SchemeDao.add_scheme_dao(query_db, scheme_do)
            
            # 创建方案配置
            if add_scheme.monitoring_keywords or add_scheme.excluded_keywords or add_scheme.material_limit:
                config_do = SchemeConfigDO(
                    scheme_id=new_scheme.id,
                    monitoring_keywords=add_scheme.monitoring_keywords,
                    excluded_keywords=add_scheme.excluded_keywords,
                    material_limit=add_scheme.material_limit,
                    create_time=datetime.now(),
                    update_time=datetime.now(),
                    create_by=current_user_name,
                    update_by=current_user_name
                )
                await SchemeConfigDao.add_scheme_config_dao(query_db, config_do)
            
            await query_db.commit()
            return ResponseUtil.success(msg="新增方案成功")
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f"新增方案失败: {str(e)}")
            return ResponseUtil.error(msg=f"新增方案失败: {str(e)}")

    @classmethod
    async def edit_scheme_services(
        cls,
        request: Request,
        query_db: AsyncSession,
        edit_scheme: SchemeModel,
        current_user_name: str
    ) -> ResponseUtil:
        """
        编辑方案
        """
        try:
            # 检查方案是否存在
            existing_scheme = await SchemeDao.get_scheme_detail_by_id(query_db, edit_scheme.id)
            if not existing_scheme:
                return ResponseUtil.error(msg="方案不存在")
            
            # 更新方案信息
            existing_scheme.name = edit_scheme.name
            existing_scheme.description = edit_scheme.description
            existing_scheme.status = edit_scheme.status
            existing_scheme.is_warning = edit_scheme.is_warning
            existing_scheme.update_time = datetime.now()
            existing_scheme.update_by = current_user_name
            existing_scheme.remark = edit_scheme.remark
            
            await SchemeDao.edit_scheme_dao(query_db, existing_scheme)
            await query_db.commit()
            
            return ResponseUtil.success(msg="编辑方案成功")
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f"编辑方案失败: {str(e)}")
            return ResponseUtil.error(msg=f"编辑方案失败: {str(e)}")

    @classmethod
    async def delete_scheme_services(
        cls,
        request: Request,
        query_db: AsyncSession,
        delete_scheme: DeleteSchemeModel
    ) -> ResponseUtil:
        """
        删除方案
        """
        try:
            scheme_ids = [int(scheme_id) for scheme_id in delete_scheme.scheme_ids.split(',')]
            delete_count = await SchemeDao.delete_scheme_dao(query_db, scheme_ids)
            await query_db.commit()
            
            return ResponseUtil.success(msg=f"删除成功，共删除 {delete_count} 个方案")
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f"删除方案失败: {str(e)}")
            return ResponseUtil.error(msg=f"删除方案失败: {str(e)}")

    @classmethod
    async def get_scheme_menu_services(
        cls,
        query_db: AsyncSession,
        user_id: int
    ) -> List[SchemeMenuModel]:
        """
        获取用户方案菜单（用于左侧导航）
        """
        # 获取用户的方案列表
        scheme_list = await SchemeDao.get_scheme_by_user_id(query_db, user_id)
        
        # 按类型分组
        type_groups = {}
        for scheme in scheme_list:
            type_name = scheme.scheme_type.name if scheme.scheme_type else "未分类"
            if type_name not in type_groups:
                type_groups[type_name] = []
            
            # 获取统计数据
            count = 0
            if scheme.scheme_statistics:
                count = scheme.scheme_statistics.all_total or 0
            
            scheme_menu = SchemeMenuModel(
                id=scheme.id,
                name=scheme.name,
                type_name=type_name,
                count=count,
                is_item=True,
                is_active=scheme.status == 1
            )
            type_groups[type_name].append(scheme_menu)
        
        # 构建菜单结构
        menu_list = []
        for type_name, schemes in type_groups.items():
            if len(schemes) == 1:
                # 如果只有一个方案，直接作为菜单项
                menu_list.append(schemes[0])
            else:
                # 如果有多个方案，创建子菜单
                total_count = sum(scheme.count for scheme in schemes)
                type_menu = SchemeMenuModel(
                    id=0,
                    name=type_name,
                    type_name=type_name,
                    count=total_count,
                    is_item=False,
                    children=schemes,
                    is_active=True
                )
                menu_list.append(type_menu)
        
        return menu_list

    @classmethod
    async def get_scheme_types_services(
        cls,
        query_db: AsyncSession
    ) -> List[SchemeTypeModel]:
        """
        获取方案类型列表
        """
        type_list = await SchemeTypeDao.get_scheme_type_list(query_db, is_active=True)

        return [
            SchemeTypeModel(
                id=type_do.id,
                name=type_do.name,
                description=type_do.description,
                display_order=type_do.display_order,
                is_active=type_do.is_active
            )
            for type_do in type_list
        ]

    @classmethod
    async def get_scheme_type_list_services(
        cls,
        query_db: AsyncSession
    ) -> List[SchemeTypeModel]:
        """
        获取方案类型列表（报告中心专用）
        """
        return await cls.get_scheme_types_services(query_db)

    @classmethod
    async def get_scheme_menu_categories_services(
        cls,
        query_db: AsyncSession,
        user_id: int
    ) -> List[SchemeMenuModel]:
        """
        获取报告中心侧边栏菜单分类数据
        """
        try:
            # 获取用户的方案列表
            scheme_list = await SchemeDao.get_scheme_by_user_id(query_db, user_id)

            # 按类型分组统计
            type_stats = {}
            scheme_items = []

            for scheme in scheme_list:
                # 确保 scheme_type 存在，否则跳过或使用默认值
                if not scheme.scheme_type:
                    logger.warning(f"方案 {scheme.id} 缺少类型信息，跳过")
                    continue

                type_name = scheme.scheme_type.name

                # 统计类型数量
                if type_name not in type_stats:
                    type_stats[type_name] = 0
                type_stats[type_name] += 1

                # 获取统计数据
                count = 0
                if scheme.scheme_statistics:
                    count = scheme.scheme_statistics.all_total or 0

                # 创建方案菜单项
                scheme_menu = SchemeMenuModel(
                    id=scheme.id,
                    name=scheme.name,
                    typeName=type_name,
                    count=count,
                    isItem=True,
                    isActive=scheme.status == 1
                )
                scheme_items.append(scheme_menu)

            # 构建菜单分类结构
            menu_categories = []

            # 添加类型分组
            for type_name, count in type_stats.items():
                if count > 0:
                    # 获取该类型下的方案
                    type_schemes = [item for item in scheme_items if item.typeName == type_name]

                    type_menu = SchemeMenuModel(
                        id=0,
                        name=type_name,
                        typeName=type_name,
                        count=count,
                        isItem=False,
                        children=type_schemes,
                        isActive=True
                    )
                    menu_categories.append(type_menu)

            # 暂时移除固定分类项，避免序列化问题
            # 如果需要固定分类项，可以在前端添加
            pass

            logger.info(f"成功创建 {len(menu_categories)} 个菜单分类")
            return menu_categories

        except Exception as e:
            logger.error(f"获取方案菜单分类失败: {str(e)}")
            # 添加更详细的错误信息
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return []
