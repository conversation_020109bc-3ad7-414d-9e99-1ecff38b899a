# 预警管理模块API接口文档

## 概述

预警管理模块为前端预警中心页面提供完整的后端API支持，包括预警方案管理、预警记录管理、预警设置管理等功能。

## 技术架构

- **框架**: FastAPI + SQLAlchemy + MySQL
- **架构模式**: 三层架构（Controller/Service/DAO）
- **认证方式**: JWT Token认证
- **权限控制**: 基于角色的访问控制（RBAC）

## API接口列表

### 1. 预警方案管理 (`/warning/scheme`)

| 方法 | 路径 | 功能 | 权限 |
|------|------|------|------|
| GET | `/warning/scheme/list` | 获取预警方案列表（分页） | `warning:scheme:list` |
| GET | `/warning/scheme/active` | 获取所有启用的预警方案 | `warning:scheme:list` |
| GET | `/warning/scheme/{scheme_id}` | 获取预警方案详情 | `warning:scheme:query` |
| POST | `/warning/scheme` | 新增预警方案 | `warning:scheme:add` |
| PUT | `/warning/scheme` | 编辑预警方案 | `warning:scheme:edit` |
| DELETE | `/warning/scheme` | 删除预警方案 | `warning:scheme:remove` |
| PUT | `/warning/scheme/{scheme_id}/status` | 切换方案状态 | `warning:scheme:edit` |
| GET | `/warning/scheme/check/name` | 检查方案名称唯一性 | `warning:scheme:list` |
| GET | `/warning/scheme/export` | 导出预警方案 | `warning:scheme:export` |

### 2. 预警记录管理 (`/warning/record`)

| 方法 | 路径 | 功能 | 权限 |
|------|------|------|------|
| GET | `/warning/record/list` | 获取预警记录列表（分页） | `warning:record:list` |
| GET | `/warning/record/{record_id}` | 获取预警记录详情 | `warning:record:query` |
| POST | `/warning/record` | 新增预警记录 | `warning:record:add` |
| PUT | `/warning/record` | 编辑预警记录 | `warning:record:edit` |
| DELETE | `/warning/record` | 删除预警记录 | `warning:record:remove` |
| GET | `/warning/record/statistics` | 获取统计数据 | `warning:record:list` |
| PUT | `/warning/record/{record_id}/status` | 更新记录状态 | `warning:record:edit` |
| GET | `/warning/record/export` | 导出预警记录 | `warning:record:export` |

### 3. 预警设置管理 (`/warning/settings`)

| 方法 | 路径 | 功能 | 权限 |
|------|------|------|------|
| GET | `/warning/settings/{scheme_id}` | 获取预警设置 | `warning:settings:query` |
| POST | `/warning/settings` | 保存预警设置 | `warning:settings:add` |
| PUT | `/warning/settings` | 更新预警设置 | `warning:settings:edit` |
| DELETE | `/warning/settings` | 删除预警设置 | `warning:settings:remove` |
| GET | `/warning/settings/default` | 获取默认设置 | `warning:settings:query` |
| POST | `/warning/settings/validate` | 验证设置 | `warning:settings:query` |
| POST | `/warning/settings/copy` | 复制设置 | `warning:settings:add` |
| POST | `/warning/settings/{scheme_id}/reset` | 重置设置 | `warning:settings:edit` |
| GET | `/warning/settings/config/options` | 获取配置选项 | `warning:settings:query` |

### 4. 前端专用接口 (`/warning/frontend`)

| 方法 | 路径 | 功能 | 权限 |
|------|------|------|------|
| GET | `/warning/frontend/data` | 获取前端页面完整数据 | `warning:record:list` |
| GET | `/warning/frontend/dashboard` | 获取仪表板数据 | `warning:record:list` |
| GET | `/warning/frontend/init/{scheme_id}` | 初始化方案数据 | `warning:record:list` |
| GET | `/warning/frontend/quick-stats` | 获取快速统计 | `warning:record:list` |
| POST | `/warning/frontend/batch-process` | 批量处理记录 | `warning:record:edit` |

## 数据模型

### 预警方案模型 (WarningSchemeModel)
```json
{
  "id": 1,
  "schemeName": "示例预警方案",
  "schemeType": "default",
  "description": "方案描述",
  "isActive": true,
  "createBy": "admin",
  "createTime": "2025-01-27T10:00:00",
  "updateBy": "admin",
  "updateTime": "2025-01-27T10:00:00"
}
```

### 预警记录模型 (WarningRecordModel)
```json
{
  "id": 1,
  "schemeId": 1,
  "warningType": "negative",
  "content": "预警内容",
  "keywords": "关键词",
  "status": 0,
  "createTime": "2025-01-27T10:00:00",
  "updateTime": "2025-01-27T10:00:00",
  "createBy": "admin",
  "updateBy": "admin",
  "remark": "备注"
}
```

### 预警设置模型 (WarningSettingsConfigModel)
```json
{
  "schemeId": 1,
  "platformTypes": ["all"],
  "contentProperty": "all",
  "infoType": "noncomment",
  "matchObjects": ["title", "content"],
  "matchMethod": "exact",
  "publishRegions": [],
  "ipAreas": [],
  "mediaCategories": [],
  "articleCategories": [],
  "allowWords": "",
  "rejectWords": "",
  "autoWarningSettings": {}
}
```

## 前端对接说明

### 1. 页面初始化
```javascript
// 获取前端页面完整数据
GET /warning/frontend/data?scheme_id=1&page_num=1&page_size=10

// 返回数据包含：
// - schemes: 方案列表（用于侧边栏）
// - records: 记录列表（分页数据）
// - statistics: 统计信息
// - settings: 预警设置
```

### 2. 方案切换
```javascript
// 初始化指定方案数据
GET /warning/frontend/init/{scheme_id}

// 返回该方案的完整数据
```

### 3. 实时统计更新
```javascript
// 获取快速统计信息
GET /warning/frontend/quick-stats?scheme_id=1
```

### 4. 批量操作
```javascript
// 批量处理记录
POST /warning/frontend/batch-process
{
  "record_ids": [1, 2, 3],
  "action": "approve"  // approve, reject, delete
}
```

## 错误处理

所有接口都使用统一的错误处理机制：

```json
{
  "code": 500,
  "msg": "错误信息",
  "data": null
}
```

## 权限配置

需要在系统中配置以下权限标识：

- `warning:scheme:list` - 查看预警方案列表
- `warning:scheme:query` - 查看预警方案详情
- `warning:scheme:add` - 新增预警方案
- `warning:scheme:edit` - 编辑预警方案
- `warning:scheme:remove` - 删除预警方案
- `warning:scheme:export` - 导出预警方案
- `warning:record:list` - 查看预警记录列表
- `warning:record:query` - 查看预警记录详情
- `warning:record:add` - 新增预警记录
- `warning:record:edit` - 编辑预警记录
- `warning:record:remove` - 删除预警记录
- `warning:record:export` - 导出预警记录
- `warning:settings:query` - 查看预警设置
- `warning:settings:add` - 新增预警设置
- `warning:settings:edit` - 编辑预警设置
- `warning:settings:remove` - 删除预警设置

## 部署说明

1. 确保数据库表已创建（warning_record、warning_scheme、warning_settings）
2. 配置相关权限
3. 重启应用服务
4. 访问 `/docs` 查看完整的API文档
