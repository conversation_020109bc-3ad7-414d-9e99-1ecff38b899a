2025-05-29 21:37:50.473 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-05-29 21:37:50.474 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-05-29 21:37:51.185 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-05-29 21:37:51.185 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-05-29 21:37:51.189 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-05-29 21:37:51.635 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-05-29 21:37:52.181 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-05-29 21:37:52.181 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-05-29 21:38:31.814 | a48e817dab234c12b504c12da8bc676c | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为7bd94f14-91d3-47a2-abb7-a9f6fd25a72a的会话获取图片验证码成功
2025-05-29 21:39:17.901 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-05-29 21:39:17.905 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-05-29 21:39:18.716 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-05-29 21:39:18.716 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-05-29 21:39:18.717 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-05-29 21:39:19.167 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-05-29 21:39:19.723 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-05-29 21:39:19.723 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-05-29 21:41:23.194 | 23474ec77dd04343a9d26c9c8b07542a | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为290b9e67-9a16-40bf-8b20-207da1d96fd4的会话获取图片验证码成功
2025-05-29 21:41:26.540 | 2cc08fffa5d446a3a5c2b9ae7ded8f4e | WARNING  | module_admin.service.login_service:__check_login_captcha:161 - 验证码错误
2025-05-29 21:41:26.540 | 2cc08fffa5d446a3a5c2b9ae7ded8f4e | WARNING  | module_admin.annotation.log_annotation:wrapper:123 - 验证码错误
2025-05-29 21:41:26.637 | de8c129e244845fe9e95aa3c24fc8f69 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为32585791-58f1-4cb7-9ca1-5b4d16957779的会话获取图片验证码成功
2025-05-29 21:41:27.861 | 50cac04591f049729a46304c0fc7f328 | WARNING  | module_admin.service.login_service:__check_login_captcha:161 - 验证码错误
2025-05-29 21:41:27.861 | 50cac04591f049729a46304c0fc7f328 | WARNING  | module_admin.annotation.log_annotation:wrapper:123 - 验证码错误
2025-05-29 21:41:27.946 | a1fcac503219477a89d7aebc1d7cdd3d | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为261f6bf6-5a09-4e7d-891d-5427f2b652d0的会话获取图片验证码成功
2025-05-29 21:41:31.882 | 2d1aa920eb7f43648e139a4b1865ce96 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-29 21:41:32.097 | cf2a9cdb40f84f7eacbd1a043c6406a4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-29 21:41:32.360 | 20384ee5a78a4ed6b11ad6e210ca5ffa | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-29 21:42:11.384 | f0b1133dc9644bec9bbb400a2cc47eb5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-29 21:42:11.503 | c12ed64190bb4b659d9d04c710af8b64 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-29 21:43:02.135 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-05-29 21:43:02.136 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-05-29 21:43:02.999 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-05-29 21:43:02.999 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-05-29 21:43:03.001 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-05-29 21:43:03.466 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-05-29 21:43:04.044 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-05-29 21:43:04.045 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-05-29 21:43:24.836 | 19a687e0935140629f4efc0a64db4731 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-29 21:43:25.100 | 318f86c12e9f4d679a18172f9116211d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
