// 预警管理API测试文件
import { 
  getActiveWarningSchemes, 
  getWarningFrontendData,
  addWarningScheme 
} from './index.js'

// 测试获取启用的预警方案
export async function testGetActiveSchemes() {
  try {
    console.log('测试获取启用的预警方案...')
    const response = await getActiveWarningSchemes()
    console.log('预警方案列表:', response)
    return response
  } catch (error) {
    console.error('获取预警方案失败:', error)
    throw error
  }
}

// 测试获取前端数据
export async function testGetFrontendData(schemeId = 1) {
  try {
    console.log('测试获取前端数据...')
    const response = await getWarningFrontendData({
      schemeId: schemeId,
      pageNum: 1,
      pageSize: 10
    })
    console.log('前端数据:', response)
    return response
  } catch (error) {
    console.error('获取前端数据失败:', error)
    throw error
  }
}

// 测试新建方案
export async function testAddScheme(schemeName = '测试方案') {
  try {
    console.log('测试新建方案...')
    const response = await addWarningScheme({
      schemeName: schemeName,
      schemeType: 'default',
      description: '这是一个测试方案',
      isActive: true
    })
    console.log('新建方案结果:', response)
    return response
  } catch (error) {
    console.error('新建方案失败:', error)
    throw error
  }
}

// 运行所有测试
export async function runAllTests() {
  console.log('=== 开始运行预警管理API测试 ===')
  
  try {
    // 测试1: 获取方案列表
    await testGetActiveSchemes()
    
    // 测试2: 获取前端数据
    await testGetFrontendData()
    
    console.log('=== 所有测试完成 ===')
  } catch (error) {
    console.error('测试过程中出现错误:', error)
  }
}

// 在浏览器控制台中可以调用的测试函数
if (typeof window !== 'undefined') {
  window.warningApiTest = {
    testGetActiveSchemes,
    testGetFrontendData,
    testAddScheme,
    runAllTests
  }
  console.log('预警API测试函数已挂载到 window.warningApiTest')
}
