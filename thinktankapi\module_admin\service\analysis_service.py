from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.dao.analysis_dao import EmotionAnalysisDao, PlatformAnalysisDao, SummaryStatisticDao
from module_admin.entity.vo.analysis_vo import (
    EmotionAnalysisSummaryModel, PlatformAnalysisSummaryModel, AnalysisPageQueryModel,
    EmotionStatisticsModel, PlatformStatisticsModel, TrendAnalysisModel, AnalysisOverviewModel
)
from utils.page_util import PageResponseModel
from utils.log_util import logger


class AnalysisService:
    """
    分析数据业务逻辑层
    """

    @classmethod
    async def get_emotion_analysis_services(
        cls,
        query_db: AsyncSession,
        query_object: AnalysisPageQueryModel,
        is_page: bool = False
    ) -> PageResponseModel:
        """
        获取情感分析列表
        """
        emotion_list_result = await EmotionAnalysisDao.get_emotion_analysis_list(query_db, query_object, is_page)
        
        # 转换为VO模型
        emotion_list = []
        for emotion_do in emotion_list_result:
            emotion_model = EmotionAnalysisSummaryModel(
                id=emotion_do.id,
                scheme_id=emotion_do.scheme_id,
                emotion_id=emotion_do.emotion_id,
                count=emotion_do.count,
                date_range_start=emotion_do.date_range_start,
                date_range_end=emotion_do.date_range_end,
                create_time=emotion_do.create_time,
                update_time=emotion_do.update_time,
                create_by=emotion_do.create_by,
                update_by=emotion_do.update_by,
                remark=emotion_do.remark
            )
            emotion_list.append(emotion_model)
        
        # 获取总数
        total = await EmotionAnalysisDao.get_emotion_analysis_count(query_db, query_object) if is_page else len(emotion_list)
        
        return PageResponseModel(
            rows=emotion_list,
            total=total
        )

    @classmethod
    async def get_platform_analysis_services(
        cls,
        query_db: AsyncSession,
        query_object: AnalysisPageQueryModel,
        is_page: bool = False
    ) -> PageResponseModel:
        """
        获取平台分析列表
        """
        platform_list_result = await PlatformAnalysisDao.get_platform_analysis_list(query_db, query_object, is_page)
        
        # 转换为VO模型
        platform_list = []
        for platform_do in platform_list_result:
            platform_model = PlatformAnalysisSummaryModel(
                id=platform_do.id,
                scheme_id=platform_do.scheme_id,
                platform_id=platform_do.platform_id,
                count=platform_do.count,
                date_range_start=platform_do.date_range_start,
                date_range_end=platform_do.date_range_end,
                create_time=platform_do.create_time,
                update_time=platform_do.update_time,
                create_by=platform_do.create_by,
                update_by=platform_do.update_by,
                remark=platform_do.remark
            )
            platform_list.append(platform_model)
        
        # 获取总数
        total = await PlatformAnalysisDao.get_platform_analysis_count(query_db, query_object) if is_page else len(platform_list)
        
        return PageResponseModel(
            rows=platform_list,
            total=total
        )

    @classmethod
    async def get_analysis_overview_services(
        cls,
        query_db: AsyncSession,
        scheme_id: int,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None
    ) -> AnalysisOverviewModel:
        """
        获取分析概览数据
        """
        logger.info(f"开始获取分析概览数据，方案ID: {scheme_id}")

        try:
            # 获取基础统计数据
            logger.info(f"正在获取方案统计数据，方案ID: {scheme_id}")
            scheme_statistics = await SummaryStatisticDao.get_scheme_statistics(query_db, scheme_id)
            total_count = scheme_statistics.all_total if scheme_statistics else 0
            today_count = scheme_statistics.today_count if scheme_statistics else 0
            logger.info(f"方案统计数据获取完成，总数: {total_count}, 今日数: {today_count}")

            # 获取情感统计数据
            logger.info(f"正在获取情感统计数据，方案ID: {scheme_id}")
            try:
                emotion_data = await EmotionAnalysisDao.get_emotion_statistics_by_scheme(
                    query_db, scheme_id, start_time, end_time
                )
                logger.info(f"情感统计数据获取完成，数据条数: {len(emotion_data)}")
            except Exception as e:
                logger.warning(f"获取情感统计数据失败: {str(e)}，使用空数据")
                emotion_data = []

            # 转换情感统计数据
            emotion_statistics = []
            emotion_total = sum(item['count'] for item in emotion_data) if emotion_data else 0
            emotion_names = {1: '正面', 2: '负面', 3: '中性'}  # 根据实际情况调整

            for item in emotion_data:
                emotion_name = emotion_names.get(item['emotion_id'], f"情感{item['emotion_id']}")
                percentage = (item['count'] / emotion_total * 100) if emotion_total > 0 else 0
                emotion_statistics.append(EmotionStatisticsModel(**{
                    "emotionName": emotion_name,
                    "emotionId": item['emotion_id'],
                    "count": item['count'],
                    "percentage": round(percentage, 2)
                }))
            logger.info(f"情感统计数据转换完成，统计项数: {len(emotion_statistics)}")

            # 获取平台统计数据
            logger.info(f"正在获取平台统计数据，方案ID: {scheme_id}")
            try:
                platform_data = await PlatformAnalysisDao.get_platform_statistics_by_scheme(
                    query_db, scheme_id, start_time, end_time
                )
                logger.info(f"平台统计数据获取完成，数据条数: {len(platform_data)}")
            except Exception as e:
                logger.warning(f"获取平台统计数据失败: {str(e)}，使用空数据")
                platform_data = []

            # 转换平台统计数据
            platform_statistics = []
            platform_total = sum(item['count'] for item in platform_data) if platform_data else 0
            platform_names = {1: '微博', 2: '微信', 3: '抖音', 4: '知乎', 5: '新闻'}  # 根据实际情况调整

            for item in platform_data:
                platform_name = platform_names.get(item['platform_id'], f"平台{item['platform_id']}")
                percentage = (item['count'] / platform_total * 100) if platform_total > 0 else 0
                platform_statistics.append(PlatformStatisticsModel(**{
                    "platformName": platform_name,
                    "platformId": item['platform_id'],
                    "count": item['count'],
                    "percentage": round(percentage, 2)
                }))
            logger.info(f"平台统计数据转换完成，统计项数: {len(platform_statistics)}")

            # 获取趋势数据（最近7天）
            logger.info(f"正在获取趋势数据，方案ID: {scheme_id}")
            try:
                trend_data = await SummaryStatisticDao.get_trend_data_by_scheme(query_db, scheme_id, 7)
                logger.info(f"趋势数据获取完成，数据条数: {len(trend_data) if trend_data else 0}")
            except Exception as e:
                logger.warning(f"获取趋势数据失败: {str(e)}，使用模拟数据")
                trend_data = []

            # 如果没有趋势数据，生成模拟数据
            if not trend_data:
                trend_data = cls._generate_mock_trend_data()
                logger.info(f"使用模拟趋势数据，数据条数: {len(trend_data)}")

            # 热门关键词（模拟数据）
            hot_keywords = ["品牌", "产品", "服务", "质量", "价格"]

            # 创建返回对象
            result = AnalysisOverviewModel(**{
                "schemeId": scheme_id,
                "totalCount": total_count,
                "todayCount": today_count,
                "emotionStatistics": emotion_statistics,
                "platformStatistics": platform_statistics,
                "trendData": trend_data,
                "hotKeywords": hot_keywords,
                "updateTime": datetime.now()
            })

            logger.info(f"分析概览数据创建成功，方案ID: {scheme_id}")
            return result

        except Exception as e:
            logger.error(f"获取分析概览失败: {str(e)}", exc_info=True)
            # 返回默认数据，确保所有字段都有值
            default_trend_data = cls._generate_mock_trend_data()
            default_result = AnalysisOverviewModel(**{
                "schemeId": scheme_id,
                "totalCount": 0,
                "todayCount": 0,
                "emotionStatistics": [],
                "platformStatistics": [],
                "trendData": default_trend_data,
                "hotKeywords": ["品牌", "产品", "服务"],
                "updateTime": datetime.now()
            })
            logger.info(f"返回默认分析概览数据，方案ID: {scheme_id}")
            return default_result

    @classmethod
    def _generate_mock_trend_data(cls) -> List[TrendAnalysisModel]:
        """
        生成模拟趋势数据
        """
        trend_data = []
        base_date = datetime.now() - timedelta(days=6)
        
        for i in range(7):
            current_date = base_date + timedelta(days=i)
            date_str = current_date.strftime('%Y-%m-%d')
            
            # 模拟数据
            total_count = 50 + i * 10
            positive = int(total_count * 0.6)
            negative = int(total_count * 0.2)
            neutral = total_count - positive - negative
            
            trend_data.append(TrendAnalysisModel(**{
                "date": date_str,
                "count": total_count,
                "emotionPositive": positive,
                "emotionNegative": negative,
                "emotionNeutral": neutral
            }))
        
        return trend_data

    @classmethod
    async def get_emotion_statistics_services(
        cls,
        query_db: AsyncSession,
        scheme_id: int,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None
    ) -> List[EmotionStatisticsModel]:
        """
        获取情感统计数据
        """
        emotion_data = await EmotionAnalysisDao.get_emotion_statistics_by_scheme(
            query_db, scheme_id, start_time, end_time
        )
        
        emotion_statistics = []
        emotion_total = sum(item['count'] for item in emotion_data)
        emotion_names = {1: '正面', 2: '负面', 3: '中性'}
        
        for item in emotion_data:
            emotion_name = emotion_names.get(item['emotion_id'], f"情感{item['emotion_id']}")
            percentage = (item['count'] / emotion_total * 100) if emotion_total > 0 else 0
            emotion_statistics.append(EmotionStatisticsModel(**{
                "emotionName": emotion_name,
                "emotionId": item['emotion_id'],
                "count": item['count'],
                "percentage": round(percentage, 2)
            }))
        
        return emotion_statistics

    @classmethod
    async def get_platform_statistics_services(
        cls,
        query_db: AsyncSession,
        scheme_id: int,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None
    ) -> List[PlatformStatisticsModel]:
        """
        获取平台统计数据
        """
        platform_data = await PlatformAnalysisDao.get_platform_statistics_by_scheme(
            query_db, scheme_id, start_time, end_time
        )
        
        platform_statistics = []
        platform_total = sum(item['count'] for item in platform_data)
        platform_names = {1: '微博', 2: '微信', 3: '抖音', 4: '知乎', 5: '新闻'}
        
        for item in platform_data:
            platform_name = platform_names.get(item['platform_id'], f"平台{item['platform_id']}")
            percentage = (item['count'] / platform_total * 100) if platform_total > 0 else 0
            platform_statistics.append(PlatformStatisticsModel(**{
                "platformName": platform_name,
                "platformId": item['platform_id'],
                "count": item['count'],
                "percentage": round(percentage, 2)
            }))
        
        return platform_statistics
