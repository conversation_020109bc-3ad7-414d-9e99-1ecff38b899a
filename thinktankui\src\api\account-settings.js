import request from '@/utils/request'

// 获取当前用户个人信息
export function getCurrentUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}

// 更新当前用户个人信息
export function updateCurrentUserProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data
  })
}

// 更新用户头像
export function updateUserAvatar(data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: data
  })
}

// 修改用户密码
export function updateUserPassword(old_password, new_password) {
  const data = {
    oldPassword: old_password,
    newPassword: new_password
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    data: data
  })
}
