from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, ConfigDict, Field, validator
from pydantic.alias_generators import to_camel


class ReportEmotionStatisticsModel(BaseModel):
    """
    报告情感统计模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='统计ID')
    report_id: str = Field(description='报告ID')
    scheme_id: Optional[int] = Field(default=None, description='关联的方案ID')
    positive_count: int = Field(default=0, description='正面情感数量')
    neutral_count: int = Field(default=0, description='中性情感数量')
    negative_count: int = Field(default=0, description='负面情感数量')
    total_count: int = Field(default=0, description='总数量')
    positive_percentage: float = Field(default=0.0, description='正面情感占比')
    neutral_percentage: float = Field(default=0.0, description='中性情感占比')
    negative_percentage: float = Field(default=0.0, description='负面情感占比')
    statistics_start_time: Optional[datetime] = Field(default=None, description='统计开始时间')
    statistics_end_time: Optional[datetime] = Field(default=None, description='统计结束时间')
    data_source: Optional[str] = Field(default=None, description='数据来源')
    analysis_keywords: Optional[str] = Field(default=None, description='分析关键词')
    report_type: str = Field(default='normal', description='报告类型')
    status: str = Field(default='0', description='状态')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default=None, description='创建者')
    update_by: Optional[str] = Field(default=None, description='更新者')
    remark: Optional[str] = Field(default=None, description='备注')


class CreateReportEmotionStatisticsModel(BaseModel):
    """
    创建报告情感统计模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    report_id: str = Field(description='报告ID')
    scheme_id: Optional[int] = Field(default=None, description='关联的方案ID')
    positive_count: int = Field(default=0, ge=0, description='正面情感数量')
    neutral_count: int = Field(default=0, ge=0, description='中性情感数量')
    negative_count: int = Field(default=0, ge=0, description='负面情感数量')
    statistics_start_time: Optional[datetime] = Field(default=None, description='统计开始时间')
    statistics_end_time: Optional[datetime] = Field(default=None, description='统计结束时间')
    data_source: Optional[str] = Field(default=None, description='数据来源')
    analysis_keywords: Optional[str] = Field(default=None, description='分析关键词（JSON格式）')
    report_type: str = Field(default='normal', description='报告类型')
    remark: Optional[str] = Field(default=None, description='备注')

    @validator('report_type')
    def validate_report_type(cls, v):
        if v not in ['normal', 'competitor']:
            raise ValueError('报告类型必须是 normal 或 competitor')
        return v

    @validator('statistics_end_time')
    def validate_time_range(cls, v, values):
        if v and 'statistics_start_time' in values and values['statistics_start_time']:
            if v <= values['statistics_start_time']:
                raise ValueError('结束时间必须大于开始时间')
        return v


class UpdateReportEmotionStatisticsModel(BaseModel):
    """
    更新报告情感统计模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    positive_count: Optional[int] = Field(default=None, ge=0, description='正面情感数量')
    neutral_count: Optional[int] = Field(default=None, ge=0, description='中性情感数量')
    negative_count: Optional[int] = Field(default=None, ge=0, description='负面情感数量')
    statistics_start_time: Optional[datetime] = Field(default=None, description='统计开始时间')
    statistics_end_time: Optional[datetime] = Field(default=None, description='统计结束时间')
    data_source: Optional[str] = Field(default=None, description='数据来源')
    analysis_keywords: Optional[str] = Field(default=None, description='分析关键词（JSON格式）')
    report_type: Optional[str] = Field(default=None, description='报告类型')
    remark: Optional[str] = Field(default=None, description='备注')

    @validator('report_type')
    def validate_report_type(cls, v):
        if v and v not in ['normal', 'competitor']:
            raise ValueError('报告类型必须是 normal 或 competitor')
        return v


class ReportEmotionStatisticsPageQueryModel(BaseModel):
    """
    报告情感统计分页查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    page_num: int = Field(default=1, ge=1, description='页码')
    page_size: int = Field(default=10, ge=1, le=100, description='每页数量')
    report_id: Optional[str] = Field(default=None, description='报告ID')
    scheme_id: Optional[int] = Field(default=None, description='方案ID')
    report_type: Optional[str] = Field(default=None, description='报告类型')
    data_source: Optional[str] = Field(default=None, description='数据来源')
    start_time: Optional[datetime] = Field(default=None, description='创建开始时间')
    end_time: Optional[datetime] = Field(default=None, description='创建结束时间')
    status: Optional[str] = Field(default=None, description='状态')


class ReportEmotionSummaryModel(BaseModel):
    """
    报告情感统计摘要模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    total_count: int = Field(description='总数量')
    positive: Dict[str, Any] = Field(description='正面情感统计')
    neutral: Dict[str, Any] = Field(description='中性情感统计')
    negative: Dict[str, Any] = Field(description='负面情感统计')
    dominant_emotion: str = Field(description='主导情感类型')


class BatchCreateReportEmotionStatisticsModel(BaseModel):
    """
    批量创建报告情感统计模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    statistics_list: List[CreateReportEmotionStatisticsModel] = Field(description='统计数据列表')

    @validator('statistics_list')
    def validate_statistics_list(cls, v):
        if not v:
            raise ValueError('统计数据列表不能为空')
        if len(v) > 100:
            raise ValueError('批量创建数量不能超过100条')
        
        # 检查report_id是否重复
        report_ids = [item.report_id for item in v]
        if len(report_ids) != len(set(report_ids)):
            raise ValueError('报告ID不能重复')
        
        return v


class ReportEmotionStatisticsResponseModel(BaseModel):
    """
    报告情感统计响应模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    statistics: ReportEmotionStatisticsModel = Field(description='统计数据')
    summary: ReportEmotionSummaryModel = Field(description='统计摘要')


class DeleteReportEmotionStatisticsModel(BaseModel):
    """
    删除报告情感统计模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    report_ids: str = Field(description='报告ID列表，逗号分隔')

    @validator('report_ids')
    def validate_report_ids(cls, v):
        if not v or not v.strip():
            raise ValueError('报告ID列表不能为空')
        
        ids = [id.strip() for id in v.split(',') if id.strip()]
        if not ids:
            raise ValueError('报告ID列表不能为空')
        
        if len(ids) > 50:
            raise ValueError('批量删除数量不能超过50条')
        
        return v


class ReportEmotionTrendModel(BaseModel):
    """
    报告情感趋势模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    date: str = Field(description='日期')
    positive_count: int = Field(description='正面情感数量')
    neutral_count: int = Field(description='中性情感数量')
    negative_count: int = Field(description='负面情感数量')
    total_count: int = Field(description='总数量')


class ReportEmotionComparisonModel(BaseModel):
    """
    报告情感对比模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    report_id: str = Field(description='报告ID')
    report_name: Optional[str] = Field(default=None, description='报告名称')
    positive_percentage: float = Field(description='正面情感占比')
    neutral_percentage: float = Field(description='中性情感占比')
    negative_percentage: float = Field(description='负面情感占比')
    dominant_emotion: str = Field(description='主导情感')
    create_time: datetime = Field(description='创建时间')
