from datetime import datetime
from sqlalchemy import BigInteger, Column, DateTime, Integer, String, Text, text
from config.database import Base


class OpinionRequirement(Base):
    """
    舆情分析需求信息表
    """

    __tablename__ = 'opinion_requirement'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    user_id = Column(BigInteger, nullable=False, comment='用户ID，关联sys_user表')
    requirement_name = Column(String(200), nullable=False, comment='需求名称')
    entity_keyword = Column(String(500), nullable=False, comment='实体关键词')
    specific_requirement = Column(Text, nullable=False, comment='具体需求描述')
    status = Column(Integer, default=1, comment='状态：0-禁用，1-启用，2-已完成')
    analysis_status = Column(Integer, default=0, comment='分析状态：0-未开始，1-分析中，2-已完成，3-失败')
    current_step = Column(Integer, default=1, comment='当前步骤：1-需求配置，2-数据来源选择')
    selected_keywords_count = Column(Integer, default=0, comment='已选择关键词数量')
    max_keywords_limit = Column(Integer, default=5, comment='最大关键词选择数量')
    priority = Column(String(20), default='medium', comment='优先级：high, medium, low')
    is_deleted = Column(Integer, default=0, comment='是否删除：0-否，1-是')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), default='', comment='备注信息')
