from datetime import datetime, date
from decimal import Decimal
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from module_admin.annotation.pydantic_annotation import as_query


class UserOrderModel(BaseModel):
    """
    用户订单模型 - 基于实际数据库表结构
    """
    order_id: Optional[int] = Field(default=None, description='订单ID')
    order_no: Optional[str] = Field(default=None, description='订单号')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    package_id: Optional[int] = Field(default=None, description='套餐ID')
    package_name: Optional[str] = Field(default=None, description='套餐名称')
    original_price: Optional[Decimal] = Field(default=None, description='原价')
    discount_amount: Optional[Decimal] = Field(default=None, description='优惠金额')
    final_price: Optional[Decimal] = Field(default=None, description='实付金额')
    payment_method: Optional[str] = Field(default=None, description='支付方式')
    order_status: Optional[str] = Field(default=None, description='订单状态')
    payment_status: Optional[str] = Field(default=None, description='支付状态')
    payment_time: Optional[datetime] = Field(default=None, description='支付时间')
    expire_time: Optional[datetime] = Field(default=None, description='过期时间')
    transaction_id: Optional[str] = Field(default=None, description='交易ID')
    payment_data: Optional[Dict[str, Any]] = Field(default=None, description='支付数据')
    remark: Optional[str] = Field(default=None, description='备注')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')


class UserPackageModel(BaseModel):
    """
    用户套餐模型 - 基于实际数据库表结构
    """
    package_id: Optional[int] = Field(default=None, description='套餐ID')
    package_name: Optional[str] = Field(default=None, description='套餐名称')
    package_type: Optional[str] = Field(default=None, description='套餐类型')
    analysis_limit: Optional[int] = Field(default=None, description='分析次数限制')
    duration_days: Optional[int] = Field(default=None, description='有效期天数')
    original_price: Optional[Decimal] = Field(default=None, description='原价')
    current_price: Optional[Decimal] = Field(default=None, description='当前价格')
    discount_rate: Optional[Decimal] = Field(default=None, description='折扣率')
    features: Optional[Dict[str, Any]] = Field(default=None, description='功能特性')
    is_active: Optional[int] = Field(default=None, description='是否启用')
    sort_order: Optional[int] = Field(default=None, description='排序')
    description: Optional[str] = Field(default=None, description='描述')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')


@as_query
class BillListQueryModel(BaseModel):
    """
    账单列表查询模型
    """
    user_id: Optional[int] = Field(default=None, description='用户ID')
    payment_status: Optional[str] = Field(default=None, description='支付状态')
    payment_method: Optional[str] = Field(default=None, description='支付方式')
    order_status: Optional[str] = Field(default=None, description='订单状态')
    start_date: Optional[date] = Field(default=None, description='开始日期')
    end_date: Optional[date] = Field(default=None, description='结束日期')
    page_num: int = Field(default=1, description='页码')
    page_size: int = Field(default=10, description='每页数量')


class UserInfoModel(BaseModel):
    """
    用户信息模型
    """
    user_id: Optional[int] = Field(default=None, description='用户ID')
    user_name: Optional[str] = Field(default=None, description='用户名')
    nick_name: Optional[str] = Field(default=None, description='昵称')
    email: Optional[str] = Field(default=None, description='邮箱')
    avatar: Optional[str] = Field(default=None, description='头像')
    vip_level: Optional[int] = Field(default=None, description='VIP等级')
    account_balance: Optional[Decimal] = Field(default=None, description='账户余额')
    total_spent: Optional[Decimal] = Field(default=None, description='累计消费')
    last_payment_time: Optional[datetime] = Field(default=None, description='最后支付时间')


class UserMembershipInfoModel(BaseModel):
    """
    用户会员信息详情模型
    """
    user_info: Optional[UserInfoModel] = Field(default=None, description='用户信息')
    current_package: Optional[UserPackageModel] = Field(default=None, description='当前套餐信息')
    membership_status: Optional[str] = Field(default=None, description='会员状态')
    expire_time: Optional[datetime] = Field(default=None, description='到期时间')


class BillListResponseModel(BaseModel):
    """
    账单列表响应模型
    """
    total: int = Field(description='总数')
    rows: List[UserOrderModel] = Field(description='账单列表')
    page_num: int = Field(description='当前页码')
    page_size: int = Field(description='每页数量')
