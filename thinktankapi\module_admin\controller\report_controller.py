from datetime import datetime
from fastapi import APIRouter, Depends, Request
from pydantic_validation_decorator import <PERSON><PERSON>te<PERSON>ields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.service.login_service import LoginService
from module_admin.service.scheme_service import SchemeService
from module_admin.service.report_template_service import ReportTemplateService
from module_admin.entity.vo.scheme_vo import (
    SchemePageQueryModel,
    CreateSchemeModel,
    DeleteSchemeModel,
    SchemeModel
)
from module_admin.entity.vo.report_template_vo import (
    ReportTemplatePageQueryModel,
    CreateReportTemplateModel,
    DeleteReportTemplateModel,
    ReportTemplateModel
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


reportController = APIRouter(prefix='/report', dependencies=[Depends(LoginService.get_current_user)])

# 创建系统路由别名，兼容前端调用
systemSchemeController = APIRouter(prefix='/system/scheme', dependencies=[Depends(LoginService.get_current_user)])


@reportController.get(
    '/scheme/list',
    response_model=PageResponseModel,
    dependencies=[Depends(CheckUserInterfaceAuth('report:scheme:list'))]
)
async def get_report_scheme_list(
    request: Request,
    scheme_page_query: SchemePageQueryModel = Depends(SchemePageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取报告中心方案列表
    """
    try:
        # 记录请求参数
        logger.info(f'=== 报告中心方案列表请求 ===')
        logger.info(f'用户ID: {current_user.user.user_id}')
        logger.info(f'页码: {scheme_page_query.page_num}')
        logger.info(f'页大小: {scheme_page_query.page_size}')
        logger.info(f'模板类型: {scheme_page_query.template_type}')
        logger.info(f'搜索关键词: {scheme_page_query.search_keyword}')
        logger.info(f'方案类型ID: {scheme_page_query.type_id}')

        # 设置当前用户ID进行数据过滤
        scheme_page_query.user_id = current_user.user.user_id

        # 获取分页数据
        scheme_page_query_result = await SchemeService.get_scheme_list_services(
            query_db, scheme_page_query, is_page=True
        )
        logger.info(f'获取报告中心方案列表成功，共 {scheme_page_query_result.total} 条记录')
        logger.info(f'分页结果类型: {type(scheme_page_query_result)}, 记录数: {len(scheme_page_query_result.records)}')
        return ResponseUtil.success(data=scheme_page_query_result.model_dump())
    except Exception as e:
        logger.error(f'获取报告中心方案列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取报告中心方案列表失败: {str(e)}')


@reportController.get(
    '/scheme/{scheme_id}',
    dependencies=[Depends(CheckUserInterfaceAuth('report:scheme:query'))]
)
async def get_report_scheme_detail(
    request: Request,
    scheme_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取报告中心方案详情
    """
    try:
        scheme_detail = await SchemeService.get_scheme_detail_services(query_db, scheme_id)
        if scheme_detail is None:
            logger.warning(f'方案详情不存在，方案ID: {scheme_id}')
            return ResponseUtil.error(msg=f'方案ID {scheme_id} 不存在')

        logger.info(f'获取方案详情成功，方案ID: {scheme_id}')
        return ResponseUtil.success(data=scheme_detail.model_dump())
    except ValueError as ve:
        logger.error(f'方案不存在: {str(ve)}')
        return ResponseUtil.error(msg=str(ve))
    except Exception as e:
        logger.error(f'获取方案详情失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取方案详情失败: {str(e)}')


@reportController.get(
    '/scheme-type/list',
    dependencies=[Depends(CheckUserInterfaceAuth('report:scheme:list'))]
)
async def get_scheme_type_list(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取方案类型列表
    """
    try:
        scheme_type_list = await SchemeService.get_scheme_type_list_services(query_db)
        logger.info('获取方案类型列表成功')
        return ResponseUtil.success(data=[item.model_dump() for item in scheme_type_list])
    except Exception as e:
        logger.error(f'获取方案类型列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取方案类型列表失败: {str(e)}')


@reportController.get(
    '/menu/categories',
    dependencies=[Depends(CheckUserInterfaceAuth('report:scheme:list'))]
)
async def get_report_menu_categories(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取报告中心侧边栏菜单分类数据
    """
    try:
        menu_categories = await SchemeService.get_scheme_menu_categories_services(
            query_db, current_user.user.user_id
        )
        logger.info(f'获取报告中心菜单分类成功，共 {len(menu_categories)} 项')

        # 尝试序列化每个项目，如果出错则记录详细信息
        serialized_data = []
        for i, item in enumerate(menu_categories):
            try:
                serialized_item = item.model_dump()
                serialized_data.append(serialized_item)
            except Exception as e:
                logger.error(f'序列化第 {i} 项菜单分类失败: {str(e)}, 项目: {item}')
                raise e

        return ResponseUtil.success(data=serialized_data)
    except Exception as e:
        logger.error(f'获取报告中心菜单分类失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取报告中心菜单分类失败: {str(e)}')


@reportController.post(
    '/scheme',
    dependencies=[Depends(CheckUserInterfaceAuth('report:scheme:add'))]
)
@ValidateFields(validate_model='add_scheme')
@Log(title='报告方案', business_type=BusinessType.INSERT)
async def add_report_scheme(
    request: Request,
    add_scheme: CreateSchemeModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增报告方案
    """
    try:
        add_scheme_result = await SchemeService.add_scheme_services(
            request, query_db, add_scheme, 
            current_user.user.user_id, current_user.user.user_name
        )
        logger.info('新增报告方案成功')
        return add_scheme_result
    except Exception as e:
        logger.error(f'新增报告方案失败: {str(e)}')
        return ResponseUtil.error(msg=f'新增报告方案失败: {str(e)}')


@reportController.put(
    '/scheme',
    dependencies=[Depends(CheckUserInterfaceAuth('report:scheme:edit'))]
)
@ValidateFields(validate_model='edit_scheme')
@Log(title='报告方案', business_type=BusinessType.UPDATE)
async def edit_report_scheme(
    request: Request,
    edit_scheme: SchemeModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑报告方案
    """
    try:
        edit_scheme_result = await SchemeService.edit_scheme_services(
            request, query_db, edit_scheme, current_user.user.user_name
        )
        logger.info('编辑报告方案成功')
        return edit_scheme_result
    except Exception as e:
        logger.error(f'编辑报告方案失败: {str(e)}')
        return ResponseUtil.error(msg=f'编辑报告方案失败: {str(e)}')


@reportController.delete(
    '/scheme',
    dependencies=[Depends(CheckUserInterfaceAuth('report:scheme:remove'))]
)
@ValidateFields(validate_model='delete_scheme')
@Log(title='报告方案', business_type=BusinessType.DELETE)
async def delete_report_scheme(
    request: Request,
    delete_scheme: DeleteSchemeModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    删除报告方案
    """
    try:
        delete_scheme_result = await SchemeService.delete_scheme_services(
            request, query_db, delete_scheme
        )
        logger.info('删除报告方案成功')
        return delete_scheme_result
    except Exception as e:
        logger.error(f'删除报告方案失败: {str(e)}')
        return ResponseUtil.error(msg=f'删除报告方案失败: {str(e)}')


# ==================== 报告模板管理接口 ====================

@reportController.get(
    '/template/list',
    response_model=PageResponseModel,
    dependencies=[Depends(CheckUserInterfaceAuth('report:template:list'))]
)
async def get_report_template_list(
    request: Request,
    template_page_query: ReportTemplatePageQueryModel = Depends(ReportTemplatePageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取报告模板列表
    """
    try:
        template_page_query_result = await ReportTemplateService.get_report_template_list_services(
            query_db, template_page_query, is_page=True
        )
        logger.info('获取报告模板列表成功')
        return ResponseUtil.success(data=template_page_query_result.model_dump())
    except Exception as e:
        logger.error(f'获取报告模板列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取报告模板列表失败: {str(e)}')


@reportController.get(
    '/template/{template_id}',
    dependencies=[Depends(CheckUserInterfaceAuth('report:template:query'))]
)
async def get_report_template_detail(
    request: Request,
    template_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取报告模板详情
    """
    try:
        template_detail = await ReportTemplateService.get_report_template_detail_services(
            query_db, template_id
        )
        logger.info(f'获取报告模板详情成功，模板ID: {template_id}')
        return ResponseUtil.success(data=template_detail.model_dump())
    except Exception as e:
        logger.error(f'获取报告模板详情失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取报告模板详情失败: {str(e)}')


@reportController.get(
    '/template/type/{template_type}',
    dependencies=[Depends(CheckUserInterfaceAuth('report:template:list'))]
)
async def get_report_template_by_type(
    request: Request,
    template_type: str,
    query_db: AsyncSession = Depends(get_db),
):
    """
    根据类型获取报告模板列表
    """
    try:
        template_list = await ReportTemplateService.get_report_template_by_type_services(
            query_db, template_type
        )
        logger.info(f'根据类型获取报告模板列表成功，类型: {template_type}')
        return ResponseUtil.success(data=[item.model_dump() for item in template_list])
    except Exception as e:
        logger.error(f'根据类型获取报告模板列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'根据类型获取报告模板列表失败: {str(e)}')


@reportController.post(
    '/template',
    dependencies=[Depends(CheckUserInterfaceAuth('report:template:add'))]
)
@ValidateFields(validate_model='add_report_template')
@Log(title='报告模板', business_type=BusinessType.INSERT)
async def add_report_template(
    request: Request,
    add_template: CreateReportTemplateModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增报告模板
    """
    try:
        add_template_result = await ReportTemplateService.add_report_template_services(
            request, query_db, add_template, current_user.user.user_name
        )
        logger.info('新增报告模板成功')
        return add_template_result
    except Exception as e:
        logger.error(f'新增报告模板失败: {str(e)}')
        return ResponseUtil.error(msg=f'新增报告模板失败: {str(e)}')


@reportController.put(
    '/template',
    dependencies=[Depends(CheckUserInterfaceAuth('report:template:edit'))]
)
@ValidateFields(validate_model='edit_report_template')
@Log(title='报告模板', business_type=BusinessType.UPDATE)
async def edit_report_template(
    request: Request,
    edit_template: ReportTemplateModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑报告模板
    """
    try:
        edit_template_result = await ReportTemplateService.edit_report_template_services(
            request, query_db, edit_template, current_user.user.user_name
        )
        logger.info('编辑报告模板成功')
        return edit_template_result
    except Exception as e:
        logger.error(f'编辑报告模板失败: {str(e)}')
        return ResponseUtil.error(msg=f'编辑报告模板失败: {str(e)}')


@reportController.delete(
    '/template',
    dependencies=[Depends(CheckUserInterfaceAuth('report:template:remove'))]
)
@ValidateFields(validate_model='delete_report_template')
@Log(title='报告模板', business_type=BusinessType.DELETE)
async def delete_report_template(
    request: Request,
    delete_template: DeleteReportTemplateModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    删除报告模板
    """
    try:
        delete_template_result = await ReportTemplateService.delete_report_template_services(
            request, query_db, delete_template
        )
        logger.info('删除报告模板成功')
        return delete_template_result
    except Exception as e:
        logger.error(f'删除报告模板失败: {str(e)}')
        return ResponseUtil.error(msg=f'删除报告模板失败: {str(e)}')


# ==================== 关键词设置接口 ====================

@reportController.get(
    '/scheme/{scheme_id}/keywords',
    dependencies=[Depends(CheckUserInterfaceAuth('report:scheme:query'))]
)
async def get_scheme_keywords(
    request: Request,
    scheme_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取方案关键词设置
    """
    try:
        # 这里需要调用相应的服务来获取关键词设置
        # 暂时返回空数据，实际实现需要查询warning_settings表
        keywords_data = {
            "allowWords": "",
            "rejectWords": ""
        }
        logger.info(f'获取方案关键词设置成功，方案ID: {scheme_id}')
        return ResponseUtil.success(data=keywords_data)
    except Exception as e:
        logger.error(f'获取方案关键词设置失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取方案关键词设置失败: {str(e)}')


@reportController.put(
    '/scheme/{scheme_id}/keywords',
    dependencies=[Depends(CheckUserInterfaceAuth('report:scheme:edit'))]
)
@Log(title='方案关键词设置', business_type=BusinessType.UPDATE)
async def update_scheme_keywords(
    request: Request,
    scheme_id: int,
    keywords_data: dict,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新方案关键词设置
    """
    try:
        # 这里需要调用相应的服务来更新关键词设置
        # 实际实现需要更新warning_settings表的allow_words和reject_words字段
        logger.info(f'更新方案关键词设置成功，方案ID: {scheme_id}')
        return ResponseUtil.success(msg="关键词设置更新成功")
    except Exception as e:
        logger.error(f'更新方案关键词设置失败: {str(e)}')
        return ResponseUtil.error(msg=f'更新方案关键词设置失败: {str(e)}')


# ==================== 系统路由别名 (兼容前端调用) ====================

@systemSchemeController.get('/list')
async def get_system_scheme_list(
    request: Request,
    scheme_page_query: SchemePageQueryModel = Depends(SchemePageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取方案列表 (系统路由别名)
    """
    return await get_report_scheme_list(request, scheme_page_query, query_db, current_user)


@systemSchemeController.get('/{scheme_id}')
async def get_system_scheme_detail(
    request: Request,
    scheme_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取方案详情 (系统路由别名)
    """
    return await get_report_scheme_detail(request, scheme_id, query_db)


@systemSchemeController.post('/add')
async def add_system_scheme(
    request: Request,
    add_scheme: CreateSchemeModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增方案 (系统路由别名)
    """
    return await add_report_scheme(request, add_scheme, query_db, current_user)


@systemSchemeController.put('/edit')
async def edit_system_scheme(
    request: Request,
    edit_scheme: SchemeModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑方案 (系统路由别名)
    """
    return await edit_report_scheme(request, edit_scheme, query_db, current_user)


@systemSchemeController.delete('/delete')
async def delete_system_scheme(
    request: Request,
    delete_scheme: DeleteSchemeModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    删除方案 (系统路由别名)
    """
    return await delete_report_scheme(request, delete_scheme, query_db)
