import request from '@/utils/request'

// 获取用户订阅信息（包含用户信息和会员信息）
export function getUserSubscriptionInfo(user_id) {
  return request({
    url: `/bill/user/${user_id}/membership`,
    method: 'get'
  })
}

// 获取当前登录用户的订阅信息（安全接口，自动获取当前用户）
export function getCurrentUserSubscriptionInfo() {
  return request({
    url: '/bill/my-subscription',
    method: 'get'
  })
}

// 获取用户基本信息
export function getCurrentUserInfo() {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}
