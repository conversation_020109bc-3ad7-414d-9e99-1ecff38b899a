2025-06-06 21:04:42.603 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-06 21:04:42.603 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 21:04:43.518 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 21:04:43.518 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 21:04:43.521 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 21:04:44.290 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 21:04:44.994 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 21:04:44.994 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-06 21:11:43.602 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 21:11:43.603 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 21:11:47.681 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-06 21:11:47.682 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 21:11:48.609 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 21:11:48.609 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 21:11:48.611 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 21:11:49.267 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 21:11:49.922 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 21:11:49.922 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-06 21:14:17.365 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 21:14:17.365 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 21:14:24.236 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-06 21:14:24.237 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 21:14:26.249 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 21:14:26.249 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 21:14:26.252 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 21:14:26.798 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 21:14:27.322 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 21:14:27.322 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-06 21:14:31.455 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-06 21:14:31.456 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 21:14:32.391 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 21:14:32.391 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 21:14:32.393 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 21:14:32.910 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 21:14:33.608 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 21:14:33.608 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-06 21:14:40.243 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-06 21:14:40.243 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 21:14:41.483 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 21:14:41.484 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 21:14:41.485 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 21:14:42.172 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 21:14:42.849 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 21:14:42.849 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-06 21:19:38.419 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 21:19:38.419 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 21:21:10.108 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-06 21:21:10.109 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 21:21:11.947 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 21:21:11.947 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 21:21:11.949 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 21:21:12.544 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 21:21:13.169 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 21:21:13.170 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-06 21:24:26.787 | 665c141cbacd4789bc4c30d1704604a2 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为00d51945-1501-4993-b476-f91891e64c1a的会话获取图片验证码成功
2025-06-06 21:24:39.210 | 9161b2be56da42daaf0eae6bcc03b561 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-06 21:24:39.439 | 5e16033f4ded46b2bb7b462014a32a11 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-06 21:24:39.945 | 04a0be04c997492ba2e5b4c877f7ca6b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-06 21:27:50.612 | fd76aab6a67b4402b99e61fc34950f39 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-06 21:27:50.712 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 21:27:50.712 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 21:27:54.215 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-06 21:27:54.216 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 21:27:55.354 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 21:27:55.355 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 21:27:55.356 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 21:27:56.005 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 21:27:56.589 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 21:27:56.589 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-06 21:27:56.815 | d8456e69912b4467b95fa822b06cb73f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-06 21:32:52.406 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 21:32:52.407 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 21:38:33.968 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-06 21:38:33.969 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 21:38:34.984 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 21:38:34.985 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 21:38:34.986 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 21:38:35.721 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 21:38:36.280 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 21:38:36.280 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-06 21:38:40.429 | 4950d96ddbb74f5a86c159a7e8a3dcc8 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-06 21:38:40.656 | 037b6934b1134b55a09fc3dbd2e0e287 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-06 21:40:45.067 | 3c7feda2633f492487406fe5275cb85a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-06 21:40:45.212 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 21:40:45.212 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 21:40:48.760 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-06 21:40:48.761 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 21:40:50.048 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 21:40:50.048 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 21:40:50.051 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 21:40:50.694 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 21:40:51.242 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 21:40:51.242 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-06 21:40:51.415 | 2b58bbcda09145288d98dfc460863807 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-06 21:41:40.947 | 65e8ad414bde4166928c9ed4669c9170 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-06 21:41:41.190 | c77510554bcf474194141f6c79f5b504 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-06 21:42:00.899 | c2a5316fda2145a7a601c08a988fc1ad | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-06 21:42:01.149 | bd9d9cff57b6469890b95b8444dddcaf | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-06 21:44:16.229 | f1ad9788973e4b36be87e0af50c62dff | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-06 21:44:16.315 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 21:44:16.316 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 21:44:18.852 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-06 21:44:18.853 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 21:44:19.825 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 21:44:19.825 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 21:44:19.827 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 21:44:20.317 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 21:44:21.037 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 21:44:21.037 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-06 21:44:21.185 | 7edcc4393c81489fbc8fa31bcacac68c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-06 21:45:46.842 | 022933f583d3456482a250528f7030c1 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-06 21:45:47.048 | 8696d07be1c644c496a29f8d2bbc1abb | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-06 21:48:29.600 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 21:48:29.600 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 21:48:33.423 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-06 21:48:33.424 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 21:48:34.644 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 21:48:34.645 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 21:48:34.647 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 21:48:35.296 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 21:48:36.059 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 21:48:36.059 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-06 21:49:38.734 | ea308b39a77b428d841a033067a45fe4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-06 21:49:38.844 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-06 21:49:38.844 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-06 21:49:42.335 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-06 21:49:42.336 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-06 21:49:43.334 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-06 21:49:43.334 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-06 21:49:43.336 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-06 21:49:43.859 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-06 21:49:44.526 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-06 21:49:44.526 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-06 21:49:44.692 | 66042c63eba44c0f985a78eba37a6684 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
