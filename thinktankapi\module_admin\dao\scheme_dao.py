from sqlalchemy import and_, or_, desc, asc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from typing import List, Optional, Tuple
from module_admin.entity.do.scheme_do import SchemeDO, SchemeTypeDO, SchemeConfigDO, SchemeSummaryStatisticDO
from module_admin.entity.vo.scheme_vo import SchemePageQueryModel, CreateSchemeModel
from utils.page_util import PageUtil
from utils.log_util import logger


class SchemeDao:
    """
    方案数据访问层
    """

    @classmethod
    async def get_scheme_list(
        cls,
        query_db: AsyncSession,
        query_object: SchemePageQueryModel,
        is_page: bool = False
    ) -> List[SchemeDO]:
        """
        根据查询参数获取方案列表
        """
        query = select(SchemeDO).options(
            selectinload(SchemeDO.scheme_type),
            selectinload(SchemeDO.scheme_config),
            selectinload(SchemeDO.scheme_statistics)
        )
        
        # 构建查询条件
        conditions = []

        if query_object.name:
            conditions.append(SchemeDO.name.like(f'%{query_object.name}%'))

        if query_object.type_id:
            conditions.append(SchemeDO.type_id == query_object.type_id)

        if query_object.status is not None:
            conditions.append(SchemeDO.status == query_object.status)

        if query_object.user_id:
            conditions.append(SchemeDO.user_id == query_object.user_id)

        # 根据模板类型筛选方案
        if query_object.template_type:
            logger.info(f'=== DAO层模板类型筛选 ===')
            logger.info(f'模板类型: {query_object.template_type}')
            if query_object.template_type == 'competitor':
                # 竞对报告：只显示竞品监控类型的方案
                conditions.append(SchemeDO.type_id == 3)  # 竞品监控的type_id是3
                logger.info('添加竞对报告筛选条件: type_id = 3')
            elif query_object.template_type == 'normal':
                # 普通报告：显示除竞品监控外的所有方案类型
                conditions.append(SchemeDO.type_id.in_([1, 2, 4]))  # 品牌监控、行业监控、产品监控
                logger.info('添加普通报告筛选条件: type_id IN (1, 2, 4)')
        
        if query_object.search_keyword:
            conditions.append(
                or_(
                    SchemeDO.name.like(f'%{query_object.search_keyword}%'),
                    SchemeDO.description.like(f'%{query_object.search_keyword}%')
                )
            )
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 排序
        query = query.order_by(desc(SchemeDO.create_time))
        
        # 分页
        if is_page:
            offset = (query_object.page_num - 1) * query_object.page_size
            query = query.offset(offset).limit(query_object.page_size)
        
        result = await query_db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_scheme_count(
        cls,
        query_db: AsyncSession,
        query_object: SchemePageQueryModel
    ) -> int:
        """
        根据查询参数获取方案总数
        """
        query = select(func.count(SchemeDO.id))
        
        # 构建查询条件
        conditions = []
        
        if query_object.name:
            conditions.append(SchemeDO.name.like(f'%{query_object.name}%'))
        
        if query_object.type_id:
            conditions.append(SchemeDO.type_id == query_object.type_id)
        
        if query_object.status is not None:
            conditions.append(SchemeDO.status == query_object.status)
        
        if query_object.user_id:
            conditions.append(SchemeDO.user_id == query_object.user_id)
        
        # 根据模板类型筛选方案
        if query_object.template_type:
            if query_object.template_type == 'competitor':
                # 竞对报告：只显示竞品监控类型的方案
                conditions.append(SchemeDO.type_id == 3)  # 竞品监控的type_id是3
            elif query_object.template_type == 'normal':
                # 普通报告：显示除竞品监控外的所有方案类型
                conditions.append(SchemeDO.type_id.in_([1, 2, 4]))  # 品牌监控、行业监控、产品监控
        
        if query_object.search_keyword:
            conditions.append(
                or_(
                    SchemeDO.name.like(f'%{query_object.search_keyword}%'),
                    SchemeDO.description.like(f'%{query_object.search_keyword}%')
                )
            )
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await query_db.execute(query)
        return result.scalar()

    @classmethod
    async def get_scheme_detail_by_id(
        cls,
        query_db: AsyncSession,
        scheme_id: int
    ) -> Optional[SchemeDO]:
        """
        根据方案ID获取方案详情
        """
        query = select(SchemeDO).options(
            selectinload(SchemeDO.scheme_type),
            selectinload(SchemeDO.scheme_config),
            selectinload(SchemeDO.scheme_statistics)
        ).where(SchemeDO.id == scheme_id)
        
        result = await query_db.execute(query)
        return result.scalar_one_or_none()

    @classmethod
    async def add_scheme_dao(
        cls,
        query_db: AsyncSession,
        scheme_do: SchemeDO
    ) -> SchemeDO:
        """
        新增方案
        """
        query_db.add(scheme_do)
        await query_db.flush()
        await query_db.refresh(scheme_do)
        return scheme_do

    @classmethod
    async def edit_scheme_dao(
        cls,
        query_db: AsyncSession,
        scheme_do: SchemeDO
    ) -> int:
        """
        编辑方案
        """
        await query_db.merge(scheme_do)
        return scheme_do.id

    @classmethod
    async def delete_scheme_dao(
        cls,
        query_db: AsyncSession,
        scheme_ids: List[int]
    ) -> int:
        """
        删除方案
        """
        query = select(SchemeDO).where(SchemeDO.id.in_(scheme_ids))
        result = await query_db.execute(query)
        schemes = result.scalars().all()
        
        for scheme in schemes:
            await query_db.delete(scheme)
        
        return len(schemes)

    @classmethod
    async def get_scheme_by_user_id(
        cls,
        query_db: AsyncSession,
        user_id: int
    ) -> List[SchemeDO]:
        """
        根据用户ID获取方案列表（用于左侧导航）
        """
        query = select(SchemeDO).options(
            selectinload(SchemeDO.scheme_type),
            selectinload(SchemeDO.scheme_statistics)
        ).where(
            and_(
                SchemeDO.user_id == user_id,
                SchemeDO.status == 1
            )
        ).order_by(asc(SchemeDO.type_id), desc(SchemeDO.create_time))
        
        result = await query_db.execute(query)
        return result.scalars().all()


class SchemeTypeDao:
    """
    方案类型数据访问层
    """

    @classmethod
    async def get_scheme_type_list(
        cls,
        query_db: AsyncSession,
        is_active: Optional[bool] = None
    ) -> List[SchemeTypeDO]:
        """
        获取方案类型列表
        """
        query = select(SchemeTypeDO)
        
        if is_active is not None:
            query = query.where(SchemeTypeDO.is_active == is_active)
        
        query = query.order_by(asc(SchemeTypeDO.display_order), asc(SchemeTypeDO.id))
        
        result = await query_db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_scheme_type_by_id(
        cls,
        query_db: AsyncSession,
        type_id: int
    ) -> Optional[SchemeTypeDO]:
        """
        根据ID获取方案类型
        """
        query = select(SchemeTypeDO).where(SchemeTypeDO.id == type_id)
        result = await query_db.execute(query)
        return result.scalar_one_or_none()


class SchemeConfigDao:
    """
    方案配置数据访问层
    """

    @classmethod
    async def get_scheme_config_by_scheme_id(
        cls,
        query_db: AsyncSession,
        scheme_id: int
    ) -> Optional[SchemeConfigDO]:
        """
        根据方案ID获取方案配置
        """
        query = select(SchemeConfigDO).where(SchemeConfigDO.scheme_id == scheme_id)
        result = await query_db.execute(query)
        return result.scalar_one_or_none()

    @classmethod
    async def add_scheme_config_dao(
        cls,
        query_db: AsyncSession,
        config_do: SchemeConfigDO
    ) -> SchemeConfigDO:
        """
        新增方案配置
        """
        query_db.add(config_do)
        await query_db.flush()
        await query_db.refresh(config_do)
        return config_do

    @classmethod
    async def edit_scheme_config_dao(
        cls,
        query_db: AsyncSession,
        config_do: SchemeConfigDO
    ) -> int:
        """
        编辑方案配置
        """
        await query_db.merge(config_do)
        return config_do.id
