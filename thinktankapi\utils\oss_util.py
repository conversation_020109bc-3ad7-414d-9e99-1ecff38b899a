"""
OSS工具类
封装阿里云对象存储服务的操作方法
"""
import io
from typing import Optional, Tuple
from utils.log_util import logger
from config.oss_config import OSSConfig


class OSSUtil:
    """
    阿里云OSS工具类
    """
    
    _client = None
    
    @classmethod
    def _get_client(cls):
        """
        获取OSS客户端实例（单例模式）
        
        :return: OSS客户端
        """
        if cls._client is None:
            try:
                import alibabacloud_oss_v2 as oss
                
                # 验证配置
                if not OSSConfig.validate_config():
                    raise Exception("OSS配置不完整")
                
                # 创建静态凭证提供者
                credentials_provider = oss.credentials.StaticCredentialsProvider(
                    access_key_id=OSSConfig.ACCESS_KEY_ID,
                    access_key_secret=OSSConfig.ACCESS_KEY_SECRET
                )
                
                # 加载SDK的默认配置，并设置凭证提供者
                cfg = oss.config.load_default()
                cfg.credentials_provider = credentials_provider
                cfg.region = OSSConfig.REGION
                cfg.endpoint = OSSConfig.ENDPOINT
                
                # 创建OSS客户端
                cls._client = oss.Client(cfg)
                logger.info("OSS客户端初始化成功")
                
            except Exception as e:
                logger.error(f"OSS客户端初始化失败: {str(e)}")
                raise e
                
        return cls._client
    
    @classmethod
    async def upload_html_content(cls, html_content: str, page_id: str) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        上传HTML内容到OSS

        :param html_content: HTML内容字符串
        :param page_id: 页面ID
        :return: (是否成功, OSS对象键名, 公网访问URL)
        """
        # 首先尝试标准方法
        try:
            return await cls._upload_html_standard(html_content, page_id)
        except Exception as e:
            logger.warning(f"标准上传方法失败: {str(e)}, 尝试备用方法")
            # 如果标准方法失败，尝试备用方法
            try:
                return await cls._upload_html_fallback(html_content, page_id)
            except Exception as e2:
                logger.error(f"备用上传方法也失败: {str(e2)}")
                return False, None, None

    @classmethod
    async def _upload_html_standard(cls, html_content: str, page_id: str) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        标准上传方法
        """
        import alibabacloud_oss_v2 as oss

        # 获取OSS客户端
        client = cls._get_client()

        # 生成对象键名
        object_key = OSSConfig.get_report_object_key(page_id)

        # 将HTML内容编码为UTF-8字节串
        html_bytes = html_content.encode('utf-8')

        # 创建最简单的上传请求，避免复杂的头部设置
        put_request = oss.PutObjectRequest(
            bucket=OSSConfig.BUCKET_NAME,
            key=object_key,
            body=html_bytes
        )

        # 只设置必要的Content-Type
        put_request.content_type = "text/html; charset=utf-8"

        # 执行上传
        result = client.put_object(put_request)

        # 检查上传结果
        if result.status_code == 200:
            # 生成带签名的域名URL，确保可以访问
            public_url = await cls._generate_signed_domain_url(client, object_key, expires_in_seconds=86400*30)  # 30天有效期

            logger.info(f"HTML文件上传OSS成功: {object_key} -> {public_url}")
            logger.info(f"OSS上传结果 - 状态码: {result.status_code}, 请求ID: {result.request_id}")

            return True, object_key, public_url
        else:
            logger.error(f"HTML文件上传OSS失败: 状态码 {result.status_code}")
            return False, None, None

    @classmethod
    async def _upload_html_fallback(cls, html_content: str, page_id: str) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        备用上传方法，使用最简单的参数
        """
        import alibabacloud_oss_v2 as oss

        # 获取OSS客户端
        client = cls._get_client()

        # 生成对象键名
        object_key = OSSConfig.get_report_object_key(page_id)

        # 将HTML内容编码为UTF-8字节串
        html_bytes = html_content.encode('utf-8')

        # 使用最简单的上传请求
        put_request = oss.PutObjectRequest(
            bucket=OSSConfig.BUCKET_NAME,
            key=object_key,
            body=html_bytes
        )

        # 只设置必要的Content-Type
        put_request.content_type = "text/html; charset=utf-8"

        # 执行上传
        result = client.put_object(put_request)

        # 检查上传结果
        if result.status_code == 200:
            # 生成带签名的域名URL，确保可以访问
            public_url = await cls._generate_signed_domain_url(client, object_key, expires_in_seconds=86400*30)  # 30天有效期

            logger.info(f"HTML文件备用方法上传OSS成功: {object_key} -> {public_url}")
            logger.info(f"OSS备用上传结果 - 状态码: {result.status_code}")

            return True, object_key, public_url
        else:
            logger.error(f"HTML文件备用方法上传OSS失败: 状态码 {result.status_code}")
            return False, None, None

    @classmethod
    async def _generate_signed_domain_url(cls, client, object_key: str, expires_in_seconds: int = 3600) -> str:
        """
        生成带签名参数的自定义域名URL，确保在浏览器中直接查看而不是下载

        :param client: OSS客户端
        :param object_key: OSS对象键名
        :param expires_in_seconds: URL有效期（秒），默认1小时
        :return: 带签名参数的自定义域名URL
        """
        try:
            import time
            import hmac
            import hashlib
            import base64
            from urllib.parse import quote

            # 计算过期时间戳
            expires = int(time.time()) + expires_in_seconds

            # 构建签名字符串 - 使用标准的OSS签名格式
            # 格式: GET\n\n\n{expires}\n/{bucket}/{object_key}
            string_to_sign = f"GET\n\n\n{expires}\n/{OSSConfig.BUCKET_NAME}/{object_key}"

            # 使用HMAC-SHA1生成签名
            signature = base64.b64encode(
                hmac.new(
                    OSSConfig.ACCESS_KEY_SECRET.encode('utf-8'),
                    string_to_sign.encode('utf-8'),
                    hashlib.sha1
                ).digest()
            ).decode('utf-8')

            # URL编码签名
            signature_encoded = quote(signature)

            # 构建带签名参数的自定义域名URL，格式与标准OSS签名URL一致
            signed_url = (
                f"https://oss.jingangai.cn/{object_key}"
                f"?OSSAccessKeyId={OSSConfig.ACCESS_KEY_ID}"
                f"&Expires={expires}"
                f"&Signature={signature_encoded}"
            )

            logger.info(f"生成可直接查看的OSS URL: {object_key}")
            return signed_url

        except Exception as e:
            logger.error(f"生成带签名自定义域名URL失败: {str(e)}")
            # 如果生成签名URL失败，回退到普通自定义域名URL
            return OSSConfig.get_public_url(object_key)

    @classmethod
    async def delete_object(cls, object_key: str) -> bool:
        """
        删除OSS对象
        
        :param object_key: OSS对象键名
        :return: 是否删除成功
        """
        try:
            import alibabacloud_oss_v2 as oss
            
            # 获取OSS客户端
            client = cls._get_client()
            
            # 创建删除请求
            delete_request = oss.DeleteObjectRequest(
                bucket=OSSConfig.BUCKET_NAME,
                key=object_key
            )
            
            # 执行删除
            result = client.delete_object(delete_request)
            
            if result.status_code == 204:  # 删除成功的状态码
                logger.info(f"OSS对象删除成功: {object_key}")
                return True
            else:
                logger.error(f"OSS对象删除失败: {object_key}, 状态码: {result.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"删除OSS对象失败: {object_key}, 错误: {str(e)}")
            return False
    
    @classmethod
    async def check_object_exists(cls, object_key: str) -> bool:
        """
        检查OSS对象是否存在
        
        :param object_key: OSS对象键名
        :return: 对象是否存在
        """
        try:
            import alibabacloud_oss_v2 as oss
            
            # 获取OSS客户端
            client = cls._get_client()
            
            # 创建检查请求
            head_request = oss.HeadObjectRequest(
                bucket=OSSConfig.BUCKET_NAME,
                key=object_key
            )
            
            # 执行检查
            result = client.head_object(head_request)
            
            return result.status_code == 200
            
        except Exception as e:
            logger.debug(f"检查OSS对象存在性失败: {object_key}, 错误: {str(e)}")
            return False
