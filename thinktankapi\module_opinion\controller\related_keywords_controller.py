"""
关联词生成控制器
提供关联词生成相关的API接口
"""
from fastapi import APIRouter, Depends, Request, Body
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_opinion.service.related_keywords_service import RelatedKeywordsService
from module_opinion.entity.vo.requirement_keyword_vo import (
    GenerateRelatedKeywordsRequest,
    GenerateRelatedKeywordsResponse
)
from utils.response_util import ResponseUtil
from utils.log_util import logger
from exceptions.exception import ServiceException


# 内部接口（需要认证）
relatedKeywordsController = APIRouter(prefix='/api/opinion', dependencies=[Depends(LoginService.get_current_user)])


@relatedKeywordsController.post('/generate-related-keywords', response_model=GenerateRelatedKeywordsResponse)
async def generate_related_keywords(
    request: Request,
    generate_request: GenerateRelatedKeywordsRequest = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    生成关联词接口
    
    根据用户填写的具体需求内容，调用AI服务生成相关的关联词。
    生成的关联词以JSON数组格式直接返回给前端进行渲染展示。
    
    Args:
        request: FastAPI请求对象
        generate_request: 生成关联词请求参数，包含需求内容和最大生成数量
        query_db: 数据库会话对象
        current_user: 当前登录用户信息
    
    Returns:
        ResponseUtil.success: 成功响应，包含生成的关联词列表和总数
        ResponseUtil.failure: 参数验证失败响应
        ResponseUtil.error: 服务异常响应
    
    Raises:
        ValidationException: 请求参数验证失败
        ServiceException: AI服务调用失败或其他业务异常
        Exception: 其他未预期的系统异常
    """
    try:
        # 记录请求日志
        logger.info(f"用户 {current_user.user.user_name} 请求生成关联词，需求内容长度: {len(generate_request.requirement_content)}")
        
        # 参数验证
        if not generate_request.requirement_content or not generate_request.requirement_content.strip():
            logger.warning("需求内容不能为空")
            return ResponseUtil.failure(msg="需求内容不能为空")
        
        if len(generate_request.requirement_content.strip()) < 5:
            logger.warning("需求内容过短，至少需要5个字符")
            return ResponseUtil.failure(msg="需求内容过短，请提供更详细的需求描述")
        
        # 调用服务层生成关联词
        generate_result = await RelatedKeywordsService.generate_related_keywords_services(
            query_db=query_db,
            requirement_content=generate_request.requirement_content.strip(),
            max_count=generate_request.max_count or 20,
            user_id=current_user.user.user_id
        )
        
        # 记录成功日志
        logger.info(f"关联词生成成功，共生成 {generate_result['total']} 个关联词")
        
        # 返回成功响应
        return ResponseUtil.success(
            data=generate_result,
            msg="关联词生成成功"
        )
        
    except ServiceException as e:
        # 业务异常处理
        logger.error(f"关联词生成业务异常: {str(e)}")
        return ResponseUtil.failure(msg=f"关联词生成失败: {str(e)}")
        
    except ValueError as e:
        # 参数验证异常
        logger.error(f"关联词生成参数异常: {str(e)}")
        return ResponseUtil.failure(msg=f"参数错误: {str(e)}")
        
    except Exception as e:
        # 系统异常处理
        logger.error(f"关联词生成系统异常: {str(e)}")
        logger.error(f"异常类型: {type(e).__name__}")
        logger.error(f"异常详情: {repr(e)}")
        
        # 构建错误响应消息
        error_msg = str(e) if str(e) else f"生成关联词时发生未知错误: {type(e).__name__}"
        return ResponseUtil.error(msg=f"关联词生成失败: {error_msg}")


@relatedKeywordsController.get('/related-keywords/health')
async def health_check(
    request: Request,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    关联词生成服务健康检查接口
    
    用于检查关联词生成服务的可用性和基本配置信息。
    
    Args:
        request: FastAPI请求对象
        current_user: 当前登录用户信息
    
    Returns:
        ResponseUtil.success: 服务健康状态信息
    """
    try:
        logger.info(f"用户 {current_user.user.user_name} 请求关联词服务健康检查")
        
        health_info = {
            "service_name": "关联词生成服务",
            "status": "healthy",
            "ai_service": "豆包AI",
            "max_keywords": 50,
            "default_keywords": 20,
            "timeout_seconds": 180,
            "fallback_enabled": True
        }
        
        logger.info("关联词服务健康检查通过")
        return ResponseUtil.success(
            data=health_info,
            msg="关联词生成服务运行正常"
        )
        
    except Exception as e:
        logger.error(f"关联词服务健康检查异常: {str(e)}")
        return ResponseUtil.error(msg=f"服务健康检查失败: {str(e)}")
