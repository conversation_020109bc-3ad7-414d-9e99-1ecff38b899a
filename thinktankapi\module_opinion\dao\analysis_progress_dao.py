from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy import select, func, and_, or_, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from module_opinion.entity.do.analysis_progress_log_do import AnalysisProgressLog, AnalysisTask
from module_opinion.entity.vo.analysis_progress_vo import (
    AnalysisProgressQueryModel,
    CreateAnalysisTaskModel,
    CreateProgressLogModel,
    UpdateTaskStatusModel
)
from utils.log_util import logger


class AnalysisProgressDao:
    """
    分析进度数据访问层
    """

    @classmethod
    async def create_analysis_task(
        cls, 
        query_db: AsyncSession, 
        task_data: CreateAnalysisTaskModel,
        task_id: str
    ) -> AnalysisTask:
        """
        创建分析任务
        
        :param query_db: 数据库会话
        :param task_data: 任务数据
        :param task_id: 任务ID
        :return: 创建的任务对象
        """
        try:
            new_task = AnalysisTask(
                task_id=task_id,
                requirement_id=task_data.requirement_id,
                user_id=task_data.user_id,
                task_name=task_data.task_name,
                task_status='completed',
                progress_percentage=0,
                analysis_config=task_data.analysis_config,
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            
            query_db.add(new_task)
            await query_db.commit()
            await query_db.refresh(new_task)
            
            logger.info(f"创建分析任务成功，任务ID: {task_id}")
            return new_task
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f"创建分析任务失败: {str(e)}")
            raise e

    @classmethod
    async def get_analysis_task_by_id(
        cls, 
        query_db: AsyncSession, 
        task_id: str
    ) -> Optional[AnalysisTask]:
        """
        根据任务ID获取分析任务
        
        :param query_db: 数据库会话
        :param task_id: 任务ID
        :return: 任务对象或None
        """
        try:
            result = await query_db.execute(
                select(AnalysisTask).where(AnalysisTask.task_id == task_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取分析任务失败: {str(e)}")
            raise e

    @classmethod
    async def update_task_status(
        cls, 
        query_db: AsyncSession, 
        update_data: UpdateTaskStatusModel
    ) -> bool:
        """
        更新任务状态
        
        :param query_db: 数据库会话
        :param update_data: 更新数据
        :return: 是否更新成功
        """
        try:
            # 获取现有任务
            result = await query_db.execute(
                select(AnalysisTask).where(AnalysisTask.task_id == update_data.task_id)
            )
            task = result.scalar_one_or_none()
            
            if not task:
                logger.warning(f"任务不存在: {update_data.task_id}")
                return False
            
            # 更新任务状态
            task.task_status = update_data.task_status
            task.update_time = datetime.now()
            
            if update_data.progress_percentage is not None:
                task.progress_percentage = update_data.progress_percentage
            
            if update_data.error_message is not None:
                task.error_message = update_data.error_message
            
            if update_data.result_summary is not None:
                task.result_summary = update_data.result_summary
            
            # 设置开始和结束时间
            if update_data.task_status == 'running' and task.start_time is None:
                task.start_time = datetime.now()
            elif update_data.task_status in ['completed', 'failed', 'cancelled']:
                if task.end_time is None:
                    task.end_time = datetime.now()
                    if task.start_time:
                        task.duration = int((task.end_time - task.start_time).total_seconds())
            
            await query_db.commit()
            logger.info(f"更新任务状态成功: {update_data.task_id} -> {update_data.task_status}")
            return True
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f"更新任务状态失败: {str(e)}")
            raise e

    @classmethod
    async def add_progress_log(
        cls, 
        query_db: AsyncSession, 
        log_data: CreateProgressLogModel
    ) -> AnalysisProgressLog:
        """
        添加进度日志
        
        :param query_db: 数据库会话
        :param log_data: 日志数据
        :return: 创建的日志对象
        """
        try:
            new_log = AnalysisProgressLog(
                task_id=log_data.task_id,
                requirement_id=log_data.requirement_id,
                user_id=log_data.user_id,
                log_level=log_data.log_level,
                log_message=log_data.log_message,
                progress_percentage=log_data.progress_percentage,
                step_name=log_data.step_name,
                step_status=log_data.step_status,
                execution_time=log_data.execution_time or datetime.now(),
                additional_data=log_data.additional_data,
                create_time=datetime.now()
            )
            
            query_db.add(new_log)
            await query_db.commit()
            await query_db.refresh(new_log)
            
            logger.debug(f"添加进度日志成功: {log_data.task_id} - {log_data.log_message}")
            return new_log
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f"添加进度日志失败: {str(e)}")
            raise e

    @classmethod
    async def get_task_by_task_id(
        cls,
        query_db: AsyncSession,
        task_id: str
    ) -> Optional[AnalysisTask]:
        """
        根据任务ID获取分析任务信息

        :param query_db: 数据库会话
        :param task_id: 任务ID
        :return: 分析任务对象
        """
        try:
            result = await query_db.execute(
                select(AnalysisTask).where(AnalysisTask.task_id == task_id)
            )
            task = result.scalar_one_or_none()
            return task

        except Exception as e:
            logger.error(f"获取分析任务失败: {str(e)}")
            return None

    @classmethod
    async def get_progress_logs(
        cls, 
        query_db: AsyncSession, 
        query_params: AnalysisProgressQueryModel
    ) -> tuple[List[AnalysisProgressLog], int]:
        """
        获取进度日志列表
        
        :param query_db: 数据库会话
        :param query_params: 查询参数
        :return: (日志列表, 总数)
        """
        try:
            # 构建查询条件
            conditions = []
            
            if query_params.task_id:
                conditions.append(AnalysisProgressLog.task_id == query_params.task_id)
            
            if query_params.requirement_id:
                conditions.append(AnalysisProgressLog.requirement_id == query_params.requirement_id)
            
            if query_params.user_id:
                conditions.append(AnalysisProgressLog.user_id == query_params.user_id)
            
            if query_params.log_level:
                conditions.append(AnalysisProgressLog.log_level == query_params.log_level)
            
            if query_params.step_status:
                conditions.append(AnalysisProgressLog.step_status == query_params.step_status)
            
            if query_params.start_time:
                conditions.append(AnalysisProgressLog.create_time >= query_params.start_time)
            
            if query_params.end_time:
                conditions.append(AnalysisProgressLog.create_time <= query_params.end_time)
            
            # 查询总数
            count_query = select(func.count(AnalysisProgressLog.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            count_result = await query_db.execute(count_query)
            total_count = count_result.scalar()
            
            # 查询数据
            data_query = select(AnalysisProgressLog)
            if conditions:
                data_query = data_query.where(and_(*conditions))
            
            # 排序和分页
            data_query = data_query.order_by(desc(AnalysisProgressLog.create_time))
            
            if query_params.page_size and query_params.page_size > 0:
                offset = (query_params.page_num - 1) * query_params.page_size
                data_query = data_query.offset(offset).limit(query_params.page_size)
            
            data_result = await query_db.execute(data_query)
            logs = data_result.scalars().all()
            
            return list(logs), total_count
            
        except Exception as e:
            logger.error(f"获取进度日志失败: {str(e)}")
            raise e

    @classmethod
    async def get_latest_progress(
        cls, 
        query_db: AsyncSession, 
        task_id: str
    ) -> Optional[AnalysisProgressLog]:
        """
        获取任务的最新进度日志
        
        :param query_db: 数据库会话
        :param task_id: 任务ID
        :return: 最新的日志对象或None
        """
        try:
            result = await query_db.execute(
                select(AnalysisProgressLog)
                .where(AnalysisProgressLog.task_id == task_id)
                .order_by(desc(AnalysisProgressLog.create_time))
                .limit(1)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取最新进度失败: {str(e)}")
            raise e

    @classmethod
    async def delete_task_logs(
        cls, 
        query_db: AsyncSession, 
        task_id: str
    ) -> bool:
        """
        删除任务的所有日志
        
        :param query_db: 数据库会话
        :param task_id: 任务ID
        :return: 是否删除成功
        """
        try:
            # 删除日志
            await query_db.execute(
                AnalysisProgressLog.__table__.delete().where(
                    AnalysisProgressLog.task_id == task_id
                )
            )
            
            # 删除任务
            await query_db.execute(
                AnalysisTask.__table__.delete().where(
                    AnalysisTask.task_id == task_id
                )
            )
            
            await query_db.commit()
            logger.info(f"删除任务及其日志成功: {task_id}")
            return True
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f"删除任务日志失败: {str(e)}")
            raise e
