import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from module_opinion.dao.analysis_progress_dao import AnalysisProgressDao
from module_opinion.entity.vo.analysis_progress_vo import (
    AnalysisProgressLogModel,
    AnalysisTaskModel,
    CreateAnalysisTaskModel,
    CreateProgressLogModel,
    UpdateTaskStatusModel,
    AnalysisProgressQueryModel,
    AnalysisProgressResponseModel
)
from utils.log_util import logger
from module_opinion.service.websocket_manager import websocket_manager


class AnalysisProgressService:
    """
    分析进度服务层
    """

    @classmethod
    async def create_analysis_task(
        cls,
        query_db: AsyncSession,
        task_data: CreateAnalysisTaskModel
    ) -> str:
        """
        创建分析任务
        
        :param query_db: 数据库会话
        :param task_data: 任务数据
        :return: 任务ID
        """
        try:
            # 生成唯一任务ID
            task_id = f"analysis_{uuid.uuid4().hex[:16]}"
            
            # 创建任务
            task = await AnalysisProgressDao.create_analysis_task(
                query_db, task_data, task_id
            )
            
            # 添加初始日志
            initial_log = CreateProgressLogModel(
                task_id=task_id,
                requirement_id=task_data.requirement_id,
                user_id=task_data.user_id,
                log_level='info',
                log_message='分析任务已创建',
                progress_percentage=0,
                step_name='初始化',
                step_status='completed'
            )
            
            await AnalysisProgressDao.add_progress_log(query_db, initial_log)
            
            logger.info(f"创建分析任务成功: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"创建分析任务失败: {str(e)}")
            raise e

    @classmethod
    async def start_analysis_task(
        cls,
        query_db: AsyncSession,
        task_id: str
    ) -> bool:
        """
        启动分析任务

        :param query_db: 数据库会话
        :param task_id: 任务ID
        :return: 是否启动成功
        """
        try:
            # 更新 analysis_task 表状态为运行中
            update_data = UpdateTaskStatusModel(
                task_id=task_id,
                task_status='running',
                progress_percentage=0
            )

            success = await AnalysisProgressDao.update_task_status(query_db, update_data)

            if success:
                # 🔥 关键修复：同时更新 opinion_task 表状态
                await cls._sync_opinion_task_status(query_db, task_id, 'running')

                # 获取任务信息用于日志
                task_info = await AnalysisProgressDao.get_task_by_task_id(query_db, task_id)
                requirement_id = task_info.requirement_id if task_info else 0

                # 添加启动日志
                start_log = CreateProgressLogModel(
                    task_id=task_id,
                    requirement_id=requirement_id,
                    log_level='info',
                    log_message='分析任务已启动',
                    progress_percentage=5,
                    step_name='任务启动',
                    step_status='completed'
                )

                await AnalysisProgressDao.add_progress_log(query_db, start_log)
                logger.info(f"启动分析任务成功: {task_id}")

            return success

        except Exception as e:
            logger.error(f"启动分析任务失败: {str(e)}")
            raise e

    @classmethod
    async def add_progress_log(
        cls,
        query_db: AsyncSession,
        log_data: CreateProgressLogModel
    ) -> AnalysisProgressLogModel:
        """
        添加进度日志
        
        :param query_db: 数据库会话
        :param log_data: 日志数据
        :return: 日志模型
        """
        try:
            log = await AnalysisProgressDao.add_progress_log(query_db, log_data)
            
            # 如果有进度更新，同时更新任务进度
            if log_data.progress_percentage is not None:
                update_data = UpdateTaskStatusModel(
                    task_id=log_data.task_id,
                    task_status='running',
                    progress_percentage=log_data.progress_percentage
                )
                await AnalysisProgressDao.update_task_status(query_db, update_data)

                # 发送进度更新通知
                await websocket_manager.send_progress_update(log_data.task_id, {
                    "progress_percentage": log_data.progress_percentage,
                    "step_name": log_data.step_name,
                    "step_status": log_data.step_status
                })

            # 发送日志消息通知
            await websocket_manager.send_log_message(log_data.task_id, {
                "log_level": log_data.log_level,
                "log_message": log_data.log_message,
                "step_name": log_data.step_name,
                "step_status": log_data.step_status,
                "create_time": log.create_time.isoformat() if log.create_time else None
            })
            
            # 转换为模型
            return AnalysisProgressLogModel(
                id=log.id,
                task_id=log.task_id,
                requirement_id=log.requirement_id,
                user_id=log.user_id,
                log_level=log.log_level,
                log_message=log.log_message,
                progress_percentage=log.progress_percentage,
                step_name=log.step_name,
                step_status=log.step_status,
                execution_time=log.execution_time,
                additional_data=log.additional_data,
                create_time=log.create_time
            )
            
        except Exception as e:
            logger.error(f"添加进度日志失败: {str(e)}")
            raise e

    @classmethod
    async def complete_analysis_task(
        cls,
        query_db: AsyncSession,
        task_id: str,
        result_summary: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        完成分析任务

        :param query_db: 数据库会话
        :param task_id: 任务ID
        :param result_summary: 结果摘要
        :return: 是否完成成功
        """
        try:
            # 更新 analysis_task 表状态为已完成
            update_data = UpdateTaskStatusModel(
                task_id=task_id,
                task_status='completed',
                progress_percentage=100,
                result_summary=result_summary
            )

            success = await AnalysisProgressDao.update_task_status(query_db, update_data)

            if success:
                # 🔥 关键修复：同时更新 opinion_task 表状态
                await cls._sync_opinion_task_status(query_db, task_id, 'completed')

                # 获取任务信息用于日志
                task_info = await AnalysisProgressDao.get_task_by_task_id(query_db, task_id)
                requirement_id = task_info.requirement_id if task_info else 0

                # 添加完成日志
                complete_log = CreateProgressLogModel(
                    task_id=task_id,
                    requirement_id=requirement_id,
                    log_level='success',
                    log_message='分析任务已完成',
                    progress_percentage=100,
                    step_name='任务完成',
                    step_status='completed'
                )

                await AnalysisProgressDao.add_progress_log(query_db, complete_log)

                # 发送任务完成通知
                await websocket_manager.send_task_status_change(task_id, 'completed', {
                    "progress_percentage": 100,
                    "result_summary": result_summary
                })

                logger.info(f"完成分析任务成功: {task_id}")

            return success

        except Exception as e:
            logger.error(f"完成分析任务失败: {str(e)}")
            raise e

    @classmethod
    async def fail_analysis_task(
        cls,
        query_db: AsyncSession,
        task_id: str,
        error_message: str
    ) -> bool:
        """
        标记分析任务失败

        :param query_db: 数据库会话
        :param task_id: 任务ID
        :param error_message: 错误信息
        :return: 是否标记成功
        """
        try:
            # 更新 analysis_task 表状态为失败
            update_data = UpdateTaskStatusModel(
                task_id=task_id,
                task_status='failed',
                error_message=error_message
            )

            success = await AnalysisProgressDao.update_task_status(query_db, update_data)

            if success:
                # 🔥 关键修复：同时更新 opinion_task 表状态
                await cls._sync_opinion_task_status(query_db, task_id, 'failed')

                # 获取任务信息用于日志
                task_info = await AnalysisProgressDao.get_task_by_task_id(query_db, task_id)
                requirement_id = task_info.requirement_id if task_info else 0

                # 添加失败日志
                fail_log = CreateProgressLogModel(
                    task_id=task_id,
                    requirement_id=requirement_id,
                    log_level='error',
                    log_message=f'分析任务失败: {error_message}',
                    step_name='任务失败',
                    step_status='failed'
                )

                await AnalysisProgressDao.add_progress_log(query_db, fail_log)

                # 发送任务失败通知
                await websocket_manager.send_task_status_change(task_id, 'failed', {
                    "error_message": error_message
                })

                logger.error(f"分析任务失败: {task_id} - {error_message}")

            return success

        except Exception as e:
            logger.error(f"标记分析任务失败失败: {str(e)}")
            raise e

    @classmethod
    async def get_analysis_progress(
        cls,
        query_db: AsyncSession,
        task_id: str,
        page_num: int = 1,
        page_size: int = 50
    ) -> AnalysisProgressResponseModel:
        """
        获取分析进度
        
        :param query_db: 数据库会话
        :param task_id: 任务ID
        :param page_num: 页码
        :param page_size: 每页大小
        :return: 分析进度响应
        """
        try:
            # 获取任务信息
            task = await AnalysisProgressDao.get_analysis_task_by_id(query_db, task_id)
            
            if not task:
                logger.warning(f"任务不存在: {task_id}")
                return AnalysisProgressResponseModel(
                    task=None,
                    logs=[],
                    total_logs=0,
                    current_progress=0,
                    current_status='not_found'
                )
            
            # 获取日志列表
            query_params = AnalysisProgressQueryModel(
                task_id=task_id,
                page_num=page_num,
                page_size=page_size
            )
            
            logs, total_count = await AnalysisProgressDao.get_progress_logs(query_db, query_params)
            
            # 转换为模型
            task_model = AnalysisTaskModel(
                id=task.id,
                task_id=task.task_id,
                requirement_id=task.requirement_id,
                user_id=task.user_id,
                task_name=task.task_name,
                task_status=task.task_status,
                progress_percentage=task.progress_percentage,
                start_time=task.start_time,
                end_time=task.end_time,
                duration=task.duration,
                analysis_config=task.analysis_config,
                result_summary=task.result_summary,
                error_message=task.error_message,
                create_time=task.create_time,
                update_time=task.update_time
            )
            
            log_models = []
            for log in logs:
                log_models.append(AnalysisProgressLogModel(
                    id=log.id,
                    task_id=log.task_id,
                    requirement_id=log.requirement_id,
                    user_id=log.user_id,
                    log_level=log.log_level,
                    log_message=log.log_message,
                    progress_percentage=log.progress_percentage,
                    step_name=log.step_name,
                    step_status=log.step_status,
                    execution_time=log.execution_time,
                    additional_data=log.additional_data,
                    create_time=log.create_time
                ))
            
            return AnalysisProgressResponseModel(
                task=task_model,
                logs=log_models,
                total_logs=total_count,
                current_progress=task.progress_percentage or 0,
                current_status=task.task_status
            )
            
        except Exception as e:
            logger.error(f"获取分析进度失败: {str(e)}")
            raise e

    @classmethod
    async def cancel_analysis_task(
        cls,
        query_db: AsyncSession,
        task_id: str
    ) -> bool:
        """
        取消分析任务

        :param query_db: 数据库会话
        :param task_id: 任务ID
        :return: 是否取消成功
        """
        try:
            # 更新 analysis_task 表状态为已取消
            update_data = UpdateTaskStatusModel(
                task_id=task_id,
                task_status='cancelled'
            )

            success = await AnalysisProgressDao.update_task_status(query_db, update_data)

            if success:
                # 🔥 关键修复：同时更新 opinion_task 表状态
                await cls._sync_opinion_task_status(query_db, task_id, 'cancelled')

                # 获取任务信息用于日志
                task_info = await AnalysisProgressDao.get_task_by_task_id(query_db, task_id)
                requirement_id = task_info.requirement_id if task_info else 0

                # 添加取消日志
                cancel_log = CreateProgressLogModel(
                    task_id=task_id,
                    requirement_id=requirement_id,
                    log_level='warning',
                    log_message='分析任务已取消',
                    step_name='任务取消',
                    step_status='cancelled'
                )

                await AnalysisProgressDao.add_progress_log(query_db, cancel_log)

                # 发送任务取消通知
                await websocket_manager.send_task_status_change(task_id, 'cancelled')

                logger.info(f"取消分析任务成功: {task_id}")

            return success

        except Exception as e:
            logger.error(f"取消分析任务失败: {str(e)}")
            raise e

    @classmethod
    async def _sync_opinion_task_status(
        cls,
        query_db: AsyncSession,
        task_id: str,
        analysis_status: str
    ) -> bool:
        """
        同步更新 opinion_task 表的状态

        :param query_db: 数据库会话
        :param task_id: 分析任务ID
        :param analysis_status: 分析状态 (running, completed, failed, cancelled)
        :return: 是否更新成功
        """
        try:
            from module_opinion.dao.opinion_task_dao import OpinionTaskDao

            # 首先获取 analysis_task 信息
            task_info = await AnalysisProgressDao.get_task_by_task_id(query_db, task_id)
            if not task_info:
                logger.warning(f"未找到分析任务: {task_id}")
                return False

            # 映射分析状态到 opinion_task 状态
            status_mapping = {
                '0': 'pending',
                '1': 'running',
                '2': 'completed',
                '-1': 'failed',
                '0': 'cancelled'
            }

            opinion_task_status = status_mapping.get(analysis_status, 'pending')

            # 根据 requirement_id 查找对应的 opinion_task 记录
            opinion_tasks = await OpinionTaskDao.get_tasks_by_requirement(
                query_db, task_info.requirement_id
            )

            # 更新所有相关的 opinion_task 状态
            for opinion_task in opinion_tasks:
                await OpinionTaskDao.update_task_status(
                    query_db, opinion_task.id, opinion_task_status
                )
                logger.info(f"同步更新 opinion_task 状态成功: task_id={opinion_task.id}, status={opinion_task_status}")

            return True

        except Exception as e:
            logger.error(f"同步 opinion_task 状态失败: {str(e)}")
            return False
