from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class AnalysisProgressLogModel(BaseModel):
    """
    分析进度日志模型
    """
    id: Optional[int] = Field(default=None, description='主键ID')
    task_id: str = Field(..., description='分析任务ID')
    requirement_id: int = Field(..., description='需求ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    log_level: str = Field(default='info', description='日志级别：info, success, warning, error')
    log_message: str = Field(..., description='日志消息内容')
    progress_percentage: Optional[int] = Field(default=0, description='进度百分比：0-100')
    step_name: Optional[str] = Field(default=None, description='当前步骤名称')
    step_status: Optional[str] = Field(default='running', description='步骤状态：running, completed, failed')
    execution_time: Optional[datetime] = Field(default=None, description='执行时间')
    additional_data: Optional[Dict[str, Any]] = Field(default=None, description='附加数据')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')


class AnalysisTaskModel(BaseModel):
    """
    分析任务模型
    """
    id: Optional[int] = Field(default=None, description='主键ID')
    task_id: str = Field(..., description='分析任务唯一ID')
    requirement_id: int = Field(..., description='需求ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    task_name: str = Field(..., description='任务名称')
    task_status: str = Field(default='pending', description='任务状态：pending, running, completed, failed, cancelled')
    progress_percentage: Optional[int] = Field(default=0, description='总体进度百分比：0-100')
    start_time: Optional[datetime] = Field(default=None, description='开始时间')
    end_time: Optional[datetime] = Field(default=None, description='结束时间')
    duration: Optional[int] = Field(default=None, description='执行时长（秒）')
    analysis_config: Optional[Dict[str, Any]] = Field(default=None, description='分析配置')
    result_summary: Optional[Dict[str, Any]] = Field(default=None, description='结果摘要')
    error_message: Optional[str] = Field(default=None, description='错误信息')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')


class CreateAnalysisTaskModel(BaseModel):
    """
    创建分析任务模型
    """
    requirement_id: int = Field(..., description='需求ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    task_name: str = Field(..., description='任务名称')
    analysis_config: Dict[str, Any] = Field(..., description='分析配置')


class CreateProgressLogModel(BaseModel):
    """
    创建进度日志模型
    """
    task_id: str = Field(..., description='分析任务ID')
    requirement_id: int = Field(..., description='需求ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    log_level: str = Field(default='info', description='日志级别')
    log_message: str = Field(..., description='日志消息内容')
    progress_percentage: Optional[int] = Field(default=0, description='进度百分比')
    step_name: Optional[str] = Field(default=None, description='当前步骤名称')
    step_status: Optional[str] = Field(default='running', description='步骤状态')
    execution_time: Optional[datetime] = Field(default=None, description='执行时间')
    additional_data: Optional[Dict[str, Any]] = Field(default=None, description='附加数据')


class UpdateTaskStatusModel(BaseModel):
    """
    更新任务状态模型
    """
    task_id: str = Field(..., description='分析任务ID')
    task_status: str = Field(..., description='任务状态')
    progress_percentage: Optional[int] = Field(default=None, description='进度百分比')
    error_message: Optional[str] = Field(default=None, description='错误信息')
    result_summary: Optional[Dict[str, Any]] = Field(default=None, description='结果摘要')


class AnalysisProgressQueryModel(BaseModel):
    """
    分析进度查询模型
    """
    task_id: Optional[str] = Field(default=None, description='任务ID')
    requirement_id: Optional[int] = Field(default=None, description='需求ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    log_level: Optional[str] = Field(default=None, description='日志级别')
    step_status: Optional[str] = Field(default=None, description='步骤状态')
    start_time: Optional[datetime] = Field(default=None, description='开始时间')
    end_time: Optional[datetime] = Field(default=None, description='结束时间')
    page_num: Optional[int] = Field(default=1, description='页码')
    page_size: Optional[int] = Field(default=50, description='每页大小')


class AnalysisProgressResponseModel(BaseModel):
    """
    分析进度响应模型
    """
    task: Optional[AnalysisTaskModel] = Field(default=None, description='任务信息')
    logs: List[AnalysisProgressLogModel] = Field(default=[], description='日志列表')
    total_logs: int = Field(default=0, description='日志总数')
    current_progress: int = Field(default=0, description='当前进度')
    current_status: str = Field(default='pending', description='当前状态')
