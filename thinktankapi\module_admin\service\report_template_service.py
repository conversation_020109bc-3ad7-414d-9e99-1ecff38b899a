from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Request
from module_admin.dao.report_template_dao import ReportTemplateDao
from module_admin.entity.do.report_template_do import ReportTemplateDO
from module_admin.entity.vo.report_template_vo import (
    ReportTemplateModel, 
    ReportTemplatePageQueryModel, 
    CreateReportTemplateModel, 
    DeleteReportTemplateModel,
    ReportTemplateListModel
)
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from utils.log_util import logger


class ReportTemplateService:
    """
    报告模板业务逻辑层
    """

    @classmethod
    async def get_report_template_list_services(
        cls,
        query_db: AsyncSession,
        query_object: ReportTemplatePageQueryModel,
        is_page: bool = False
    ):
        """
        获取报告模板列表
        """
        try:
            template_page_query_result = await ReportTemplateDao.get_report_template_list(
                query_db, query_object, is_page
            )

            if is_page:
                # 分页结果处理 - 直接返回已转换的数据，无需再次转换
                return template_page_query_result
            else:
                # 非分页结果处理 - 直接返回已转换的数据，无需再次转换
                return template_page_query_result

        except Exception as e:
            logger.error(f"获取报告模板列表失败: {str(e)}")
            raise

    @classmethod
    async def get_report_template_detail_services(
        cls,
        query_db: AsyncSession,
        template_id: int
    ) -> ReportTemplateModel:
        """
        获取报告模板详情
        """
        template_do = await ReportTemplateDao.get_report_template_detail_by_id(query_db, template_id)
        if not template_do:
            raise ValueError(f"报告模板ID {template_id} 不存在")

        # 手动创建模型实例，确保字段名正确映射
        template_model = ReportTemplateModel(
            id=template_do.id,
            name=template_do.name,
            templateType=template_do.template_type,  # 使用驼峰格式
            description=template_do.description,
            templateContent=template_do.template_content,  # 使用驼峰格式
            isActive=template_do.is_active,  # 使用驼峰格式
            createTime=template_do.create_time,  # 使用驼峰格式
            updateTime=template_do.update_time,  # 使用驼峰格式
            createBy=template_do.create_by,  # 使用驼峰格式
            updateBy=template_do.update_by,  # 使用驼峰格式
            remark=template_do.remark
        )

        return template_model

    @classmethod
    async def add_report_template_services(
        cls,
        request: Request,
        query_db: AsyncSession,
        add_template: CreateReportTemplateModel,
        current_user_name: str
    ) -> ResponseUtil:
        """
        新增报告模板
        """
        try:
            # 创建模板DO对象
            template_do = ReportTemplateDO(
                name=add_template.name,
                template_type=add_template.template_type,
                description=add_template.description,
                template_content=add_template.template_content,
                is_active=True,
                create_time=datetime.now(),
                update_time=datetime.now(),
                create_by=current_user_name,
                update_by=current_user_name,
                remark=add_template.remark
            )
            
            # 保存模板
            new_template = await ReportTemplateDao.add_report_template_dao(query_db, template_do)
            await query_db.commit()
            
            return ResponseUtil.success(msg="新增报告模板成功")
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f"新增报告模板失败: {str(e)}")
            return ResponseUtil.error(msg=f"新增报告模板失败: {str(e)}")

    @classmethod
    async def edit_report_template_services(
        cls,
        request: Request,
        query_db: AsyncSession,
        edit_template: ReportTemplateModel,
        current_user_name: str
    ) -> ResponseUtil:
        """
        编辑报告模板
        """
        try:
            # 检查模板是否存在
            existing_template = await ReportTemplateDao.get_report_template_detail_by_id(
                query_db, edit_template.id
            )
            if not existing_template:
                return ResponseUtil.error(msg="报告模板不存在")
            
            # 更新模板信息
            existing_template.name = edit_template.name
            existing_template.template_type = edit_template.template_type
            existing_template.description = edit_template.description
            existing_template.template_content = edit_template.template_content
            existing_template.is_active = edit_template.is_active
            existing_template.update_time = datetime.now()
            existing_template.update_by = current_user_name
            existing_template.remark = edit_template.remark
            
            await ReportTemplateDao.edit_report_template_dao(query_db, existing_template)
            await query_db.commit()
            
            return ResponseUtil.success(msg="编辑报告模板成功")
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f"编辑报告模板失败: {str(e)}")
            return ResponseUtil.error(msg=f"编辑报告模板失败: {str(e)}")

    @classmethod
    async def delete_report_template_services(
        cls,
        request: Request,
        query_db: AsyncSession,
        delete_template: DeleteReportTemplateModel
    ) -> ResponseUtil:
        """
        删除报告模板
        """
        try:
            template_ids = [int(template_id) for template_id in delete_template.template_ids.split(',')]
            delete_count = await ReportTemplateDao.delete_report_template_dao(query_db, template_ids)
            await query_db.commit()
            
            return ResponseUtil.success(msg=f"删除成功，共删除 {delete_count} 个模板")
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f"删除报告模板失败: {str(e)}")
            return ResponseUtil.error(msg=f"删除报告模板失败: {str(e)}")

    @classmethod
    async def get_report_template_by_type_services(
        cls,
        query_db: AsyncSession,
        template_type: str
    ) -> List[ReportTemplateListModel]:
        """
        根据类型获取报告模板列表
        """
        try:
            template_list = await ReportTemplateDao.get_report_template_by_type(
                query_db, template_type, is_active=True
            )
            
            return [
                ReportTemplateListModel(
                    id=template_do.id,
                    name=template_do.name,
                    template_type=template_do.template_type,
                    create_time=template_do.create_time,
                    is_active=template_do.is_active
                )
                for template_do in template_list
            ]
            
        except Exception as e:
            logger.error(f"根据类型获取报告模板列表失败: {str(e)}")
            return []
