from datetime import datetime
from fastapi import APIRouter, Depends, Request
from pydantic_validation_decorator import <PERSON>idateFields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.info_vo import (
    DeleteInfoSummaryModel,
    InfoSummaryModel,
    InfoSummaryPageQueryModel
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_admin.service.info_summary_service import InfoSummaryService
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


infoSummaryController = APIRouter(prefix='/info-summary', dependencies=[Depends(LoginService.get_current_user)])

# 创建系统路由别名，兼容前端调用
systemInfoSummaryController = APIRouter(prefix='/system/infoSummary', dependencies=[Depends(LoginService.get_current_user)])


@infoSummaryController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('info:summary:list'))]
)
async def get_info_summary_list(
    request: Request,
    info_page_query: InfoSummaryPageQueryModel = Depends(InfoSummaryPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取信息汇总列表
    """
    # 获取分页数据
    info_page_query_result = await InfoSummaryService.get_info_summary_list_services(query_db, info_page_query, is_page=True)
    logger.info('获取信息汇总列表成功')

    return ResponseUtil.success(model_content=info_page_query_result)


@infoSummaryController.get('/statistics')
async def get_platform_statistics(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取平台统计数据
    """
    statistics_result = await InfoSummaryService.get_platform_statistics_services(query_db)
    logger.info('获取平台统计数据成功')

    return ResponseUtil.success(data=statistics_result)


@infoSummaryController.post('', dependencies=[Depends(CheckUserInterfaceAuth('info:summary:add'))])
@ValidateFields(validate_model='add_info_summary')
@Log(title='信息汇总', business_type=BusinessType.INSERT)
async def add_info_summary(
    request: Request,
    add_info: InfoSummaryModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增信息
    """
    add_info.create_by = current_user.user.user_name
    add_info.create_time = datetime.now()
    add_info.update_by = current_user.user.user_name
    add_info.update_time = datetime.now()
    add_info_result = await InfoSummaryService.add_info_summary_services(query_db, add_info)
    logger.info(add_info_result.message)

    return ResponseUtil.success(msg=add_info_result.message)


@infoSummaryController.put('', dependencies=[Depends(CheckUserInterfaceAuth('info:summary:edit'))])
@ValidateFields(validate_model='edit_info_summary')
@Log(title='信息汇总', business_type=BusinessType.UPDATE)
async def edit_info_summary(
    request: Request,
    edit_info: InfoSummaryModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑信息
    """
    edit_info.update_by = current_user.user.user_name
    edit_info.update_time = datetime.now()
    edit_info_result = await InfoSummaryService.edit_info_summary_services(query_db, edit_info)
    logger.info(edit_info_result.message)

    return ResponseUtil.success(msg=edit_info_result.message)


@infoSummaryController.delete('/{info_ids}', dependencies=[Depends(CheckUserInterfaceAuth('info:summary:remove'))])
@Log(title='信息汇总', business_type=BusinessType.DELETE)
async def delete_info_summary(request: Request, info_ids: str, query_db: AsyncSession = Depends(get_db)):
    """
    删除信息
    """
    delete_info = DeleteInfoSummaryModel(info_ids=info_ids)
    delete_info_result = await InfoSummaryService.delete_info_summary_services(query_db, delete_info)
    logger.info(delete_info_result.message)

    return ResponseUtil.success(msg=delete_info_result.message)


@infoSummaryController.get(
    '/{info_id}', response_model=InfoSummaryModel, dependencies=[Depends(CheckUserInterfaceAuth('info:summary:query'))]
)
async def query_detail_info_summary(request: Request, info_id: int, query_db: AsyncSession = Depends(get_db)):
    """
    获取信息详情
    """
    info_detail_result = await InfoSummaryService.info_summary_detail_services(query_db, info_id)
    logger.info(f'获取info_id为{info_id}的信息成功')

    return ResponseUtil.success(data=info_detail_result)


@infoSummaryController.put('/sentiment/{info_id}')
@Log(title='信息汇总', business_type=BusinessType.UPDATE)
async def update_info_sentiment(
    request: Request,
    info_id: int,
    sentiment: str,
    query_db: AsyncSession = Depends(get_db),
):
    """
    更新信息情感倾向
    """
    update_result = await InfoSummaryService.update_sentiment_services(query_db, info_id, sentiment)
    logger.info(f'更新信息{info_id}的情感倾向为{sentiment}成功')

    return ResponseUtil.success(msg=update_result.message)


# ==================== 系统路由别名 (兼容前端调用) ====================

@systemInfoSummaryController.get('/list')
async def get_system_info_summary_list(
    request: Request,
    info_page_query: InfoSummaryPageQueryModel = Depends(InfoSummaryPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取信息汇总列表 (系统路由别名)
    """
    return await get_info_summary_list(request, info_page_query, query_db)


@systemInfoSummaryController.get('/statistics')
async def get_system_platform_statistics(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取平台统计数据 (系统路由别名)
    """
    return await get_platform_statistics(request, query_db)


@systemInfoSummaryController.get('/{info_id}')
async def query_system_detail_info_summary(
    request: Request,
    info_id: int,
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取信息详情 (系统路由别名)
    """
    return await query_detail_info_summary(request, info_id, query_db)
