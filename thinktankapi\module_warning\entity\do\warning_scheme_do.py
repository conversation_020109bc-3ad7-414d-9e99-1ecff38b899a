from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text, Boolean
from config.database import Base


class WarningScheme(Base):
    """
    预警方案表
    """

    __tablename__ = 'warning_scheme'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='方案ID')
    scheme_name = Column(String(100), nullable=False, comment='方案名称')
    scheme_type = Column(String(50), nullable=True, default='default', comment='方案类型')
    description = Column(Text, nullable=True, comment='方案描述')
    is_active = Column(Boolean, nullable=True, default=True, comment='是否启用')
    create_by = Column(String(64), nullable=True, comment='创建者')
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_by = Column(String(64), nullable=True, comment='更新者')
    update_time = Column(DateTime, nullable=True, default=datetime.now, onupdate=datetime.now, comment='更新时间')
