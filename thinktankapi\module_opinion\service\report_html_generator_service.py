import os
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from config.env import UploadConfig
from utils.log_util import logger
from utils.oss_util import OSSUtil


class ReportHtmlGeneratorService:
    """
    报告HTML生成服务
    """
    
    @classmethod
    async def generate_report_html(
        cls,
        report_data: Dict[str, Any],
        analysis_results: Dict[str, Any],
        requirement_name: str,
        entity_keyword: str
    ) -> tuple[str, str]:
        """
        生成报告HTML页面并上传到OSS

        :param report_data: 报告数据
        :param analysis_results: 分析结果
        :param requirement_name: 需求名称
        :param entity_keyword: 实体关键词
        :return: (页面ID, OSS公网访问URL)
        """
        try:
            # 生成唯一页面ID
            page_id = f"report_{uuid.uuid4().hex[:12]}_{int(datetime.now().timestamp())}"

            # 生成HTML内容
            html_content = cls._generate_html_content(
                report_data, analysis_results, requirement_name, entity_keyword
            )

            # 上传HTML内容到OSS
            upload_success, object_key, public_url = await OSSUtil.upload_html_content(html_content, page_id)

            if upload_success and public_url:
                logger.info(f"报告HTML页面生成并上传OSS成功: {page_id} -> {public_url}")
                return page_id, public_url
            else:
                # OSS上传失败，回退到本地存储
                logger.warning(f"OSS上传失败，回退到本地存储: {page_id}")

                # 创建报告页面目录
                report_dir = os.path.join(UploadConfig.UPLOAD_PATH, 'reports')
                if not os.path.exists(report_dir):
                    os.makedirs(report_dir)

                # 保存HTML文件到本地
                file_path = os.path.join(report_dir, f"{page_id}.html")
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                logger.info(f"报告HTML页面本地存储成功: {page_id}")
                return page_id, file_path

        except Exception as e:
            logger.error(f"生成报告HTML页面失败: {str(e)}")
            raise e
    
    @classmethod
    def _generate_html_content(
        cls,
        report_data: Dict[str, Any],
        analysis_results: Dict[str, Any],
        requirement_name: str,
        entity_keyword: str
    ) -> str:
        """
        生成HTML内容
        """
        # 添加调试日志
        logger.info(f"生成HTML - report_data keys: {list(report_data.keys())}")
        logger.info(f"生成HTML - analysis_results keys: {list(analysis_results.keys())}")
        logger.info(f"生成HTML - selectedKeywords: {report_data.get('selectedKeywords', [])}")

        # 获取当前时间
        current_time = datetime.now().strftime("%Y年%m月%d日 %H:%M")
        
        # 处理情感分析数据
        sentiment = report_data.get('sentiment', {})
        positive_pct = sentiment.get('positive', 0)
        neutral_pct = sentiment.get('neutral', 0)
        negative_pct = sentiment.get('negative', 0)
        
        # 处理文章数据 - 从analysis_results中提取
        articles = []
        if analysis_results.get('analysis_results'):
            results = analysis_results['analysis_results']

            # 从联网搜索结果中获取文章
            if results.get('online_search', {}).get('data', {}).get('articles'):
                articles.extend(results['online_search']['data']['articles'])

            # 从自定义数据源结果中获取文章
            if results.get('custom_data_source', {}).get('data', {}).get('articles'):
                articles.extend(results['custom_data_source']['data']['articles'])

        total_articles = len(articles)
        logger.info(f"生成HTML - 提取到 {total_articles} 篇文章")

        # 处理关键词数据 - 从report_data中获取
        selected_keywords = report_data.get('selectedKeywords', [])
        logger.info(f"生成HTML - 提取到 {len(selected_keywords)} 个关键词: {selected_keywords}")
        
        # 处理数据源统计
        online_search_count = report_data.get('onlineSearchCount', 0)
        custom_source_counts = report_data.get('customSourceCounts', {})
        
        # 生成文章列表HTML
        articles_html = cls._generate_articles_html(articles)
        
        # 生成关键词列表HTML
        keywords_html = cls._generate_keywords_html(selected_keywords)
        
        # 生成数据源列表HTML
        sources_html = cls._generate_sources_html(online_search_count, custom_source_counts)
        
        # 生成完整HTML
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#5470c6">
    <title>舆情分析报告 - {requirement_name}</title>
    <style>
        {cls._get_css_styles()}
    </style>
</head>
<body>
    <div class="report-container">
        <!-- 报告头部 -->
        <div class="report-header">
            <div class="header-content">
                <h1 class="report-title">舆情分析报告</h1>
                <div class="report-meta">
                    <div class="meta-item">
                        <span class="meta-label">需求名称:</span>
                        <span class="meta-value">{requirement_name}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">实体关键词:</span>
                        <span class="meta-value">{entity_keyword}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">生成时间:</span>
                        <span class="meta-value">{current_time}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 分析概览 -->
        <div class="section">
            <h2 class="section-title">分析概览</h2>
            <div class="overview-stats">
                <div class="stat-card">
                    <div class="stat-number">{total_articles}</div>
                    <div class="stat-label">相关文章</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{len(selected_keywords)}</div>
                    <div class="stat-label">关键词</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{len(custom_source_counts) + (1 if online_search_count > 0 else 0)}</div>
                    <div class="stat-label">数据源</div>
                </div>
            </div>
        </div>
        
        <!-- 情感倾向分析 -->
        <div class="section">
            <h2 class="section-title">情感倾向分析</h2>
            <div class="sentiment-analysis">
                <div class="sentiment-item positive">
                    <div class="sentiment-bar">
                        <div class="bar-fill" style="width: {positive_pct}%"></div>
                    </div>
                    <div class="sentiment-info">
                        <span class="sentiment-label">正面</span>
                        <span class="sentiment-value">{positive_pct}%</span>
                    </div>
                </div>
                <div class="sentiment-item neutral">
                    <div class="sentiment-bar">
                        <div class="bar-fill" style="width: {neutral_pct}%"></div>
                    </div>
                    <div class="sentiment-info">
                        <span class="sentiment-label">中性</span>
                        <span class="sentiment-value">{neutral_pct}%</span>
                    </div>
                </div>
                <div class="sentiment-item negative">
                    <div class="sentiment-bar">
                        <div class="bar-fill" style="width: {negative_pct}%"></div>
                    </div>
                    <div class="sentiment-info">
                        <span class="sentiment-label">负面</span>
                        <span class="sentiment-value">{negative_pct}%</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 关键词分析 -->
        <div class="section">
            <h2 class="section-title">关键词分析</h2>
            <div class="keywords-container">
                {keywords_html}
            </div>
        </div>
        
        <!-- 数据来源统计 -->
        <div class="section">
            <h2 class="section-title">数据来源统计</h2>
            <div class="sources-container">
                {sources_html}
            </div>
        </div>
        
        <!-- 详细数据 -->
        <div class="section">
            <h2 class="section-title">详细数据</h2>
            <div class="articles-container">
                {articles_html}
            </div>
        </div>
        
        <!-- 页面底部 -->
        <div class="report-footer">
            <p class="footer-text">本报告由智库系统自动生成</p>
            <p class="footer-time">生成时间: {current_time}</p>
        </div>
    </div>
</body>
</html>"""
        
        return html_content

    @classmethod
    def _generate_articles_html(cls, articles: list) -> str:
        """
        生成文章列表HTML
        """
        if not articles:
            return '<div class="empty-state">暂无相关文章数据</div>'

        articles_html = ""
        for article in articles[:20]:  # 限制显示前20篇文章
            title = article.get('title', '无标题')
            content = article.get('content', '无内容')
            source = article.get('source', '未知来源')
            sentiment = article.get('sentiment', 'neutral')
            publish_time = article.get('publish_time', '')
            url = article.get('url', '')

            # 处理内容摘要
            content_summary = content[:200] + '...' if len(content) > 200 else content

            # 情感标签
            sentiment_labels = {
                'positive': '正面',
                'neutral': '中性',
                'negative': '负面'
            }
            sentiment_label = sentiment_labels.get(sentiment, '未知')

            # 格式化发布时间
            formatted_time = cls._format_publish_time(publish_time)

            articles_html += f"""
            <div class="article-item">
                <div class="article-header">
                    <h4 class="article-title">{title}</h4>
                    <div class="article-meta">
                        <span class="article-source">{source}</span>
                        <span class="sentiment-tag {sentiment}">{sentiment_label}</span>
                    </div>
                </div>
                <div class="article-content">
                    <p class="content-text">{content_summary}</p>
                </div>
                <div class="article-footer">
                    <span class="publish-time">{formatted_time}</span>
                    {f'<a href="{url}" target="_blank" class="article-link">查看原文</a>' if url and url != 'https://search.doubao.com/search' else ''}
                </div>
            </div>
            """

        return articles_html

    @classmethod
    def _generate_keywords_html(cls, keywords: list) -> str:
        """
        生成关键词列表HTML
        """
        if not keywords:
            return '<div class="empty-state">暂无关键词数据</div>'

        keywords_html = '<div class="keywords-list">'
        for keyword in keywords:
            keywords_html += f'<span class="keyword-tag">{keyword}</span>'
        keywords_html += '</div>'

        return keywords_html

    @classmethod
    def _generate_sources_html(cls, online_search_count: int, custom_source_counts: dict) -> str:
        """
        生成数据源列表HTML
        """
        sources_html = ""

        # 联网搜索数据源
        if online_search_count > 0:
            sources_html += f"""
            <div class="source-item">
                <div class="source-icon online">🔍</div>
                <div class="source-info">
                    <div class="source-name">联网搜索</div>
                    <div class="source-desc">AI搜索引擎数据</div>
                </div>
                <div class="source-count">{online_search_count} 条</div>
            </div>
            """

        # 自定义数据源
        for source_url, count in custom_source_counts.items():
            domain_name = cls._extract_domain_name(source_url)
            sources_html += f"""
            <div class="source-item">
                <div class="source-icon custom">🔗</div>
                <div class="source-info">
                    <div class="source-name">{domain_name}</div>
                    <div class="source-desc">自定义数据源</div>
                </div>
                <div class="source-count">{count} 条</div>
            </div>
            """

        if not sources_html:
            sources_html = '<div class="empty-state">暂无数据源信息</div>'

        return sources_html

    @classmethod
    def _format_publish_time(cls, publish_time: str) -> str:
        """
        格式化发布时间
        """
        if not publish_time:
            return '时间未知'

        try:
            # 尝试解析时间字符串
            if isinstance(publish_time, str):
                # 处理不同的时间格式
                if 'T' in publish_time:
                    dt = datetime.fromisoformat(publish_time.replace('Z', '+00:00'))
                else:
                    dt = datetime.strptime(publish_time, '%Y-%m-%d %H:%M:%S')
                return dt.strftime('%Y-%m-%d %H:%M')
            return str(publish_time)
        except:
            return str(publish_time)

    @classmethod
    def _extract_domain_name(cls, url: str) -> str:
        """
        从URL中提取域名
        """
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            domain = parsed.netloc
            if domain.startswith('www.'):
                domain = domain[4:]
            return domain
        except:
            return url

    @classmethod
    def _get_css_styles(cls) -> str:
        """
        获取CSS样式
        """
        return """
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            line-height: 1.5;
            color: #333;
            background-color: #f5f5f5;
            padding: 0;
            margin: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* 容器样式 */
        .report-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 报告头部 */
        .report-header {
            background: #5470c6;
            color: white;
            padding: 24px 20px;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .report-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
        }

        .report-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            font-size: 14px;
        }

        .meta-item {
            display: flex;
            align-items: center;
        }

        .meta-label {
            font-weight: 500;
            margin-right: 8px;
            opacity: 0.9;
        }

        .meta-value {
            opacity: 0.9;
        }

        /* 内容区域 */
        .section {
            padding: 24px 20px;
            border-bottom: 1px solid #eee;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-left: 10px;
            border-left: 4px solid #5470c6;
        }

        /* 概览统计 */
        .overview-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: space-around;
        }

        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            min-width: 120px;
            flex: 1;
        }

        .stat-number {
            font-size: 28px;
            font-weight: 700;
            color: #5470c6;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
        }

        /* 情感分析 */
        .sentiment-analysis {
            margin-top: 20px;
        }

        .sentiment-item {
            margin-bottom: 16px;
        }

        .sentiment-bar {
            height: 24px;
            background: #eee;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .bar-fill {
            height: 100%;
            border-radius: 4px;
        }

        .sentiment-item.positive .bar-fill {
            background: #67C23A;
        }

        .sentiment-item.neutral .bar-fill {
            background: #E6A23C;
        }

        .sentiment-item.negative .bar-fill {
            background: #F56C6C;
        }

        .sentiment-info {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
        }

        .sentiment-label {
            font-weight: 500;
        }

        .sentiment-value {
            font-weight: 600;
        }

        /* 关键词 */
        .keywords-container {
            margin-top: 20px;
        }

        .keywords-list {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .keyword-tag {
            background: #ecf5ff;
            color: #409EFF;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 14px;
            border: 1px solid #d9ecff;
        }

        /* 数据源 */
        .sources-container {
            margin-top: 20px;
        }

        .source-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
            background: #f8f9fa;
            margin-bottom: 12px;
        }

        .source-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 16px;
        }

        .source-icon.online {
            background: #ecf5ff;
            color: #409EFF;
        }

        .source-icon.custom {
            background: #f0f9eb;
            color: #67C23A;
        }

        .source-info {
            flex: 1;
        }

        .source-name {
            font-weight: 500;
            font-size: 16px;
        }

        .source-desc {
            font-size: 12px;
            color: #999;
        }

        .source-count {
            font-weight: 600;
            color: #5470c6;
        }

        /* 文章列表 */
        .articles-container {
            margin-top: 20px;
        }

        .article-item {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background: white;
        }

        .article-header {
            margin-bottom: 12px;
        }

        .article-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .article-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 12px;
        }

        .article-source {
            color: #666;
        }

        .sentiment-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .sentiment-tag.positive {
            background: #f0f9eb;
            color: #67C23A;
            border: 1px solid #c2e7b0;
        }

        .sentiment-tag.neutral {
            background: #fdf6ec;
            color: #E6A23C;
            border: 1px solid #f5dab1;
        }

        .sentiment-tag.negative {
            background: #fef0f0;
            color: #F56C6C;
            border: 1px solid #fbc4c4;
        }

        .article-content {
            margin-bottom: 12px;
        }

        .content-text {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }

        .article-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #999;
        }

        .article-link {
            color: #5470c6;
            text-decoration: none;
        }

        .article-link:hover {
            text-decoration: underline;
        }

        /* 页脚 */
        .report-footer {
            text-align: center;
            padding: 24px;
            color: #999;
            font-size: 12px;
            background: #f8f9fa;
        }

        .footer-text {
            margin-bottom: 8px;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #999;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (min-width: 768px) {
            .report-container {
                max-width: 1200px;
                margin: 20px auto;
                border-radius: 8px;
            }

            .report-header {
                border-radius: 8px 8px 0 0;
            }

            .report-title {
                font-size: 28px;
            }
        }

        @media (max-width: 767px) {
            .overview-stats {
                flex-direction: column;
                gap: 12px;
            }

            .stat-card {
                width: 100%;
                padding: 16px;
            }

            .report-meta {
                flex-direction: column;
                gap: 8px;
            }

            .section {
                padding: 20px 16px;
            }

            .section-title {
                font-size: 18px;
                margin-bottom: 16px;
            }

            .report-title {
                font-size: 20px;
            }

            .report-header {
                padding: 20px 16px;
            }

            .article-item {
                padding: 16px;
                margin-bottom: 12px;
            }

            .article-title {
                font-size: 16px;
                line-height: 1.4;
            }

            .article-summary {
                font-size: 14px;
                line-height: 1.5;
                margin: 8px 0;
            }

            .sentiment-analysis {
                gap: 16px;
            }

            .sentiment-item {
                padding: 12px;
            }

            /* 移动端触摸优化 */
            .article-link {
                display: block;
                padding: 4px 0;
                min-height: 44px;
                line-height: 1.4;
            }

            /* 防止移动端缩放 */
            input, textarea, select {
                font-size: 16px;
            }
        }
        """
