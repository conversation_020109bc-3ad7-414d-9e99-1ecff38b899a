from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, JSON, ForeignKey
from config.database import Base


class WarningSettings(Base):
    """
    预警设置表
    """

    __tablename__ = 'warning_settings'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='设置ID')
    scheme_id = Column(Integer, ForeignKey('warning_scheme.id'), nullable=False, comment='方案ID')
    platform_types = Column(JSON, nullable=True, comment='平台类型配置')
    content_property = Column(String(50), nullable=True, comment='内容属性')
    info_type = Column(String(50), nullable=True, comment='信息类型')
    match_objects = Column(JSON, nullable=True, comment='匹配对象配置')
    match_method = Column(String(50), nullable=True, comment='匹配方式')
    publish_regions = Column(JSON, nullable=True, comment='发布地区配置')
    ip_areas = Column(JSON, nullable=True, comment='IP属地配置')
    media_categories = Column(JSON, nullable=True, comment='媒体类别配置')
    article_categories = Column(JSON, nullable=True, comment='文章类别配置')
    create_by = Column(String(64), nullable=True, comment='创建者')
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_by = Column(String(64), nullable=True, comment='更新者')
    update_time = Column(DateTime, nullable=True, default=datetime.now, onupdate=datetime.now, comment='更新时间')
