from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from module_admin.entity.vo.common_vo import PageQueryModel
from module_admin.annotation.pydantic_annotation import as_query


class OpinionRequirementModel(BaseModel):
    """
    舆情需求信息模型
    """
    id: Optional[int] = Field(default=None, description='主键ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    requirement_name: str = Field(..., description='需求名称')
    entity_keyword: str = Field(..., description='实体关键词')
    specific_requirement: str = Field(..., description='具体需求描述')
    status: Optional[int] = Field(default=1, description='状态：0-禁用，1-启用，2-已完成')
    analysis_status: Optional[int] = Field(default=0, description='分析状态：0-未开始，1-分析中，2-已完成，3-失败')
    current_step: Optional[int] = Field(default=1, description='当前步骤：1-需求配置，2-数据来源选择')
    selected_keywords_count: Optional[int] = Field(default=0, description='已选择关键词数量')
    max_keywords_limit: Optional[int] = Field(default=5, description='最大关键词选择数量')
    priority: Optional[str] = Field(default='medium', description='优先级：high, medium, low')
    is_deleted: Optional[int] = Field(default=0, description='是否删除：0-否，1-是')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default='', description='创建者')
    update_by: Optional[str] = Field(default='', description='更新者')
    remark: Optional[str] = Field(default='', description='备注信息')


@as_query
class OpinionRequirementPageQueryModel(PageQueryModel):
    """
    舆情需求分页查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    requirement_name: Optional[str] = Field(default=None, description='需求名称')
    entity_keyword: Optional[str] = Field(default=None, description='实体关键词')
    status: Optional[int] = Field(default=None, description='状态')
    analysis_status: Optional[int] = Field(default=None, description='分析状态')
    priority: Optional[str] = Field(default=None, description='优先级')
    create_time_start: Optional[str] = Field(default=None, description='创建时间开始')
    create_time_end: Optional[str] = Field(default=None, description='创建时间结束')
    user_id: Optional[int] = Field(default=None, description='用户ID，用于数据权限过滤')
    user_id: Optional[int] = Field(default=None, description='用户ID，用于数据权限过滤')


class CreateOpinionRequirementModel(BaseModel):
    """
    创建舆情需求模型
    """
    requirement_name: str = Field(..., description='需求名称')
    entity_keyword: str = Field(..., description='实体关键词')
    specific_requirement: str = Field(..., description='具体需求描述')
    priority: Optional[str] = Field(default='medium', description='优先级：high, medium, low')
    max_keywords_limit: Optional[int] = Field(default=5, description='最大关键词选择数量')
    remark: Optional[str] = Field(default='', description='备注信息')


class UpdateOpinionRequirementModel(BaseModel):
    """
    更新舆情需求模型
    """
    id: int = Field(..., description='主键ID')
    requirement_name: Optional[str] = Field(default=None, description='需求名称')
    entity_keyword: Optional[str] = Field(default=None, description='实体关键词')
    specific_requirement: Optional[str] = Field(default=None, description='具体需求描述')
    status: Optional[int] = Field(default=None, description='状态')
    analysis_status: Optional[int] = Field(default=None, description='分析状态')
    current_step: Optional[int] = Field(default=None, description='当前步骤')
    priority: Optional[str] = Field(default=None, description='优先级')
    max_keywords_limit: Optional[int] = Field(default=None, description='最大关键词选择数量')
    remark: Optional[str] = Field(default=None, description='备注信息')


class DeleteOpinionRequirementModel(BaseModel):
    """
    删除舆情需求模型
    """
    ids: List[int] = Field(..., description='需要删除的ID列表')


class OpinionRequirementListModel(BaseModel):
    """
    舆情需求列表模型（用于下拉选择）
    """
    id: int = Field(..., description='主键ID')
    requirement_name: str = Field(..., description='需求名称')
    entity_keyword: str = Field(..., description='实体关键词')
    status: int = Field(..., description='状态')
    analysis_status: int = Field(..., description='分析状态')
    create_time: datetime = Field(..., description='创建时间')
