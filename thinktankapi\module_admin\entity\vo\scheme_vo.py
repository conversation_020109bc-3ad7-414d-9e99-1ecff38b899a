from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, ConfigDict, Field, validator
from pydantic.alias_generators import to_camel
from module_admin.annotation.pydantic_annotation import as_query


class SchemeTypeModel(BaseModel):
    """
    方案类型模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='类型ID')
    name: str = Field(description='类型名称')
    description: Optional[str] = Field(default=None, description='类型描述')
    display_order: Optional[int] = Field(default=0, description='显示顺序')
    is_active: Optional[bool] = Field(default=True, description='是否激活')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default=None, description='创建者')
    update_by: Optional[str] = Field(default=None, description='更新者')
    remark: Optional[str] = Field(default=None, description='备注')


class SchemeConfigModel(BaseModel):
    """
    方案配置模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='配置ID')
    scheme_id: int = Field(description='方案ID')
    monitoring_keywords: Optional[str] = Field(default=None, description='监控关键词')
    excluded_keywords: Optional[str] = Field(default=None, description='排除关键词')
    material_limit: Optional[int] = Field(default=0, description='素材限制数量')
    user_condition: Optional[str] = Field(default=None, description='用户条件')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default=None, description='创建者')
    update_by: Optional[str] = Field(default=None, description='更新者')
    remark: Optional[str] = Field(default=None, description='备注')


class SchemeSummaryStatisticModel(BaseModel):
    """
    方案汇总统计模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='统计ID')
    scheme_id: int = Field(description='方案ID')
    all_total: Optional[int] = Field(default=0, description='总数量')
    media_count: Optional[int] = Field(default=0, description='媒体数量')
    negative_count: Optional[int] = Field(default=0, description='负面数量')
    today_count: Optional[int] = Field(default=0, description='今日数量')
    start_time: Optional[datetime] = Field(default=None, description='开始时间')
    end_time: Optional[datetime] = Field(default=None, description='结束时间')
    refresh_time: Optional[datetime] = Field(default=None, description='刷新时间')


class SchemeModel(BaseModel):
    """
    方案模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='方案ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    type_id: int = Field(description='方案类型ID')
    name: str = Field(description='方案名称')
    description: Optional[str] = Field(default=None, description='方案描述')
    status: Optional[int] = Field(default=1, description='状态：0-禁用，1-启用')
    refresh_status: Optional[int] = Field(default=0, description='刷新状态：0-未刷新，1-刷新中，2-已刷新')
    is_warning: Optional[bool] = Field(default=False, description='是否开启预警')
    version: Optional[str] = Field(default=None, description='版本号')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default=None, description='创建者')
    update_by: Optional[str] = Field(default=None, description='更新者')
    remark: Optional[str] = Field(default=None, description='备注')

    # 关联数据
    scheme_type: Optional[SchemeTypeModel] = Field(default=None, description='方案类型')
    scheme_config: Optional[SchemeConfigModel] = Field(default=None, description='方案配置')
    scheme_statistics: Optional[SchemeSummaryStatisticModel] = Field(default=None, description='方案统计')


@as_query
class SchemePageQueryModel(BaseModel):
    """
    方案分页查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    page_num: int = Field(default=1, ge=1, description='页码')
    page_size: int = Field(default=10, ge=1, le=100, description='每页数量')
    name: Optional[str] = Field(default=None, description='方案名称')
    type_id: Optional[int] = Field(default=None, description='方案类型ID')
    status: Optional[int] = Field(default=None, description='状态')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    search_keyword: Optional[str] = Field(default=None, description='搜索关键词')
    template_type: Optional[str] = Field(default=None, description='模板类型(normal/competitor)')

    @validator('template_type')
    def validate_template_type(cls, v):
        """验证模板类型"""
        if v and v not in ['normal', 'competitor']:
            raise ValueError('模板类型必须是 normal 或 competitor')
        return v
    
    @validator('search_keyword', 'name')
    def validate_string_fields(cls, v):
        """验证字符串字段，去除首尾空格"""
        if v:
            return v.strip()
        return v
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典，过滤空值"""
        return {k: v for k, v in self.model_dump().items() if v is not None}
    
    def has_search_conditions(self) -> bool:
        """检查是否有搜索条件"""
        return any([
            self.name,
            self.type_id,
            self.search_keyword,
            self.template_type,
            self.status is not None
        ])
    
    def get_offset(self) -> int:
        """计算数据库查询偏移量"""
        return (self.page_num - 1) * self.page_size


class CreateSchemeModel(BaseModel):
    """
    创建方案模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    type_id: int = Field(description='方案类型ID')
    name: str = Field(description='方案名称')
    description: Optional[str] = Field(default=None, description='方案描述')
    monitoring_keywords: Optional[str] = Field(default=None, description='监控关键词')
    excluded_keywords: Optional[str] = Field(default=None, description='排除关键词')
    material_limit: Optional[int] = Field(default=0, description='素材限制数量')
    remark: Optional[str] = Field(default=None, description='备注')


class DeleteSchemeModel(BaseModel):
    """
    删除方案模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    scheme_ids: str = Field(description='方案ID列表，逗号分隔')


class SchemeMenuModel(BaseModel):
    """
    方案菜单模型（用于左侧导航）
    """
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(description='方案ID')
    name: str = Field(description='方案名称')
    typeName: str = Field(description='方案类型名称')
    count: int = Field(default=0, description='数据数量')
    isItem: bool = Field(default=True, description='是否为叶子节点')
    children: Optional[List['SchemeMenuModel']] = Field(default=None, description='子节点')
    icon: Optional[str] = Field(default=None, description='图标')
    isActive: bool = Field(default=True, description='是否激活')
