import httpx
import json
import re
from datetime import datetime
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from utils.log_util import logger
from exceptions.exception import ServiceException
from module_opinion.entity.vo.push_report_vo import (
    PushReportRequestModel,
    PushReportResponseModel,
    DingTalkMessageModel,
    PushLogModel
)
from module_opinion.service.report_html_generator_service import ReportHtmlGeneratorService
from config.env import AppConfig


class PushReportService:
    """
    推送报告服务类
    """

    @classmethod
    async def push_report(
        cls,
        query_db: AsyncSession,
        push_request: PushReportRequestModel,
        user_id: Optional[int] = None
    ) -> PushReportResponseModel:
        """
        推送报告到目标URL
        
        :param query_db: 数据库会话
        :param push_request: 推送请求数据
        :param user_id: 用户ID
        :return: 推送结果
        """
        push_time = datetime.now()
        push_id = f"push_{int(push_time.timestamp() * 1000)}"
        
        try:
            logger.info(f'开始推送报告到: {push_request.target_url}')

            # 优先使用前端传递的报告OSS URL
            requirement_name = push_request.report_data.get('requirementName', '舆情分析报告')
            entity_keyword = push_request.report_data.get('entityKeyword', '')

            # 检查是否有现有的报告OSS URL
            existing_report_url = push_request.report_data.get('reportOssUrl') or push_request.analysis_results.get('reportUrl')

            if existing_report_url:
                # 使用现有的报告URL
                report_page_url = existing_report_url
                page_id = f"existing_{int(push_time.timestamp() * 1000)}"
                logger.info(f'使用现有报告URL: {report_page_url}')
            else:
                # 生成新的静态HTML页面并上传到OSS
                page_id, report_page_url = await ReportHtmlGeneratorService.generate_report_html(
                    push_request.report_data,
                    push_request.analysis_results,
                    requirement_name,
                    entity_keyword
                )

            # 检查是否为OSS URL（以https开头）或本地文件路径
            if report_page_url.startswith('https://'):
                # OSS URL，直接使用
                logger.info(f'报告页面已上传OSS: {report_page_url}')
            else:
                # 本地文件路径，构建本地访问URL（回退方案）
                base_url = AppConfig.app_host if AppConfig.app_host != '0.0.0.0' else 'localhost'
                port_suffix = f':{AppConfig.app_port}' if AppConfig.app_port != 80 else ''

                # 修改：使用完整的公网访问URL格式
                if AppConfig.app_host == '0.0.0.0':
                    # 如果是本地开发环境，使用localhost
                    report_page_url = f"http://localhost{port_suffix}{AppConfig.app_root_path}/public/report/view/{page_id}"
                else:
                    # 如果是生产环境，使用配置的域名
                    report_page_url = f"http://{base_url}{port_suffix}{AppConfig.app_root_path}/public/report/view/{page_id}"

                logger.warning(f'使用本地访问URL（OSS上传失败回退）: {report_page_url}')

            logger.info(f'报告页面生成成功: {report_page_url}')

            # 生成移动端友好的备用URL
            mobile_friendly_url = await cls._generate_mobile_friendly_url(report_page_url, page_id)

            # 验证报告URL是否可访问
            url_accessible = await cls._validate_report_url(report_page_url)
            if not url_accessible:
                logger.warning(f'主URL验证失败，尝试使用备用URL: {mobile_friendly_url}')
                # 如果主URL不可访问，使用备用URL
                if mobile_friendly_url != report_page_url:
                    backup_accessible = await cls._validate_report_url(mobile_friendly_url)
                    if backup_accessible:
                        report_page_url = mobile_friendly_url
                        logger.info(f'使用备用URL: {report_page_url}')
                    else:
                        logger.warning(f'备用URL也不可访问，继续使用原URL: {report_page_url}')

            # 保存推送地址到数据库（如果有requirement_id）
            if push_request.requirement_id:
                try:
                    # 导入OpinionTaskService（避免循环导入）
                    from module_opinion.service.opinion_task_service import OpinionTaskService

                    # 通过requirement_id查找对应的任务
                    tasks = await OpinionTaskService.get_tasks_by_requirement_services(
                        query_db, push_request.requirement_id, user_id
                    )

                    # 如果找到任务，保存推送地址和其他信息到第一个任务（通常是最新的）
                    if tasks and len(tasks) > 0:
                        task_id = tasks[0].get('id')
                        if task_id:
                            # 优先保存推送URL到数据库
                            try:
                                await OpinionTaskService.save_push_url_services(
                                    query_db, task_id, push_request.target_url
                                )
                                logger.info(f'✅ 推送URL已保存到任务 {task_id}: {push_request.target_url}')
                            except Exception as push_url_error:
                                logger.error(f'❌ 保存推送URL失败: {str(push_url_error)}')

                            # 如果是OSS URL，也保存OSS URL
                            if report_page_url.startswith('https://'):
                                try:
                                    await OpinionTaskService.save_report_oss_url_services(
                                        query_db, task_id, report_page_url
                                    )
                                    logger.info(f'✅ 报告OSS URL已保存到任务 {task_id}: {report_page_url}')
                                except Exception as oss_url_error:
                                    logger.error(f'❌ 保存报告OSS URL失败: {str(oss_url_error)}')

                            # 提取并保存情感统计数据
                            sentiment_data = push_request.report_data.get('sentiment', {})
                            if sentiment_data:
                                # 从百分比数据中提取数量（如果有的话）
                                positive_count = sentiment_data.get('positiveCount', 0)
                                negative_count = sentiment_data.get('negativeCount', 0)
                                neutral_count = sentiment_data.get('neutralCount', 0)

                                # 如果没有数量数据，尝试从百分比和总数计算
                                if positive_count == 0 and negative_count == 0 and neutral_count == 0:
                                    total_articles = push_request.report_data.get('totalArticles', 0)
                                    if total_articles > 0:
                                        positive_pct = sentiment_data.get('positive', 0)
                                        negative_pct = sentiment_data.get('negative', 0)
                                        neutral_pct = sentiment_data.get('neutral', 0)

                                        positive_count = int(total_articles * positive_pct / 100)
                                        negative_count = int(total_articles * negative_pct / 100)
                                        neutral_count = int(total_articles * neutral_pct / 100)

                                # 更新情感统计数据
                                if positive_count > 0 or negative_count > 0 or neutral_count > 0:
                                    await OpinionTaskService.update_sentiment_counts_services(
                                        query_db, task_id, positive_count, negative_count, neutral_count
                                    )
                                    logger.info(f'情感统计数据已保存到任务 {task_id}: 正面={positive_count}, 负面={negative_count}, 中性={neutral_count}')
                        else:
                            logger.warning('未找到有效的任务ID，跳过OSS URL和情感统计数据保存')
                    else:
                        logger.warning(f'未找到requirement_id {push_request.requirement_id} 对应的任务，跳过OSS URL和情感统计数据保存')

                except Exception as e:
                    logger.error(f'保存OSS URL和情感统计数据到数据库失败: {str(e)}')
                    # 不影响主流程，继续执行推送

            # 检查机器人类型
            is_dingtalk = cls._is_dingtalk_url(push_request.target_url)
            is_wechat_work = cls._is_wechat_work_url(push_request.target_url)
            is_feishu = cls._is_feishu_url(push_request.target_url)

            # 准备推送数据
            if is_dingtalk:
                push_data = cls._convert_to_dingtalk_format(
                    push_request.report_data,
                    push_request.analysis_results,
                    report_page_url
                )
            elif is_wechat_work:
                push_data = cls._convert_to_wechat_work_format(
                    push_request.report_data,
                    push_request.analysis_results,
                    report_page_url
                )
            elif is_feishu:
                push_data = cls._convert_to_feishu_format(
                    push_request.report_data,
                    push_request.analysis_results,
                    report_page_url
                )
            else:
                # 普通HTTP接口推送，发送页面链接
                push_data = {
                    'type': 'opinion_analysis_report',
                    'title': '舆情分析报告',
                    'reportPageUrl': report_page_url,
                    'summary': {
                        'requirementName': requirement_name,
                        'entityKeyword': entity_keyword,
                        'totalArticles': push_request.report_data.get('totalArticles', 0),
                        'sentiment': push_request.report_data.get('sentiment', {}),
                        'dataSources': push_request.report_data.get('dataSources', 0)
                    },
                    'pushTime': push_time.isoformat(),
                    'pushId': push_id,
                    'message': f'舆情分析报告已生成，请点击链接查看：{report_page_url}'
                }
            
            # 发送HTTP请求
            response_status, response_data, error_message = await cls._send_http_request(
                push_request.target_url,
                push_data
            )
            
            # 判断推送是否成功
            success = cls._is_push_successful(
                response_status,
                response_data,
                is_dingtalk,
                is_wechat_work,
                is_feishu
            )
            
            # 记录推送日志
            await cls._log_push_result(
                query_db,
                push_request,
                push_id,
                success,
                response_status,
                response_data,
                error_message,
                push_time,
                user_id
            )
            
            # 构建响应
            if success:
                message = '报告推送成功'
                if is_dingtalk and response_data and response_data.get('errmsg'):
                    message += f"，钉钉响应: {response_data.get('errmsg')}"
                
                return PushReportResponseModel(
                    success=True,
                    message=message,
                    push_id=push_id,
                    target_url=push_request.target_url,
                    push_time=push_time,
                    response_status=response_status,
                    response_data=response_data
                )
            else:
                error_details = error_message or f"HTTP状态码: {response_status}"
                if is_dingtalk and response_data and response_data.get('errmsg'):
                    error_details += f"，钉钉错误: {response_data.get('errmsg')}"
                
                return PushReportResponseModel(
                    success=False,
                    message='报告推送失败',
                    push_id=push_id,
                    target_url=push_request.target_url,
                    push_time=push_time,
                    response_status=response_status,
                    response_data=response_data,
                    error_details=error_details
                )
                
        except Exception as e:
            logger.error(f'推送报告失败: {str(e)}')
            
            # 记录错误日志
            await cls._log_push_result(
                query_db,
                push_request,
                push_id,
                False,
                None,
                None,
                str(e),
                push_time,
                user_id
            )
            
            return PushReportResponseModel(
                success=False,
                message='推送失败',
                push_id=push_id,
                target_url=push_request.target_url,
                push_time=push_time,
                error_details=str(e)
            )

    @classmethod
    def _is_dingtalk_url(cls, url: str) -> bool:
        """
        检查是否为钉钉机器人URL
        """
        return 'oapi.dingtalk.com/robot/send' in url

    @classmethod
    def _is_wechat_work_url(cls, url: str) -> bool:
        """
        检查是否为企业微信机器人URL
        """
        return 'qyapi.weixin.qq.com/cgi-bin/webhook/send' in url

    @classmethod
    def _is_feishu_url(cls, url: str) -> bool:
        """
        检查是否为飞书机器人URL
        """
        return 'open.feishu.cn/open-apis/bot/v2/hook/' in url

    @classmethod
    def _convert_to_dingtalk_format(cls, report_data: Dict[str, Any], analysis_results: Dict[str, Any], report_page_url: str) -> Dict[str, Any]:
        """
        将报告数据转换为钉钉机器人消息格式
        """
        # 构建markdown格式的报告内容，包含页面链接
        markdown_content = cls._build_markdown_report(report_data, analysis_results, report_page_url)
        
        return {
            "msgtype": "markdown",
            "markdown": {
                "title": "舆情分析报告",
                "text": markdown_content
            }
        }

    @classmethod
    def _build_markdown_report(cls, report_data: Dict[str, Any], analysis_results: Dict[str, Any], report_page_url: str = None) -> str:
        """
        构建markdown格式的报告内容
        """
        content = "# 📊 舆情分析报告\n\n"

        # 报告页面链接（最重要的内容，放在最前面）
        if report_page_url:
            content += f"## 🔗 查看完整报告\n\n"
            content += f"**[📱 点击查看详细分析报告]({report_page_url})**\n\n"
            content += f"> 💡 提示：建议在浏览器中打开以获得最佳体验\n\n"
            content += "---\n\n"

        # 基本信息摘要
        content += "## 📋 报告摘要\n\n"

        if report_data.get('requirementName'):
            content += f"**需求名称**: {report_data['requirementName']}\n\n"

        if report_data.get('entityKeyword'):
            content += f"**关键词**: {report_data['entityKeyword']}\n\n"

        # 核心数据统计
        if report_data.get('totalArticles'):
            content += f"**📈 数据统计**: 共分析 {report_data['totalArticles']} 篇相关文章\n\n"

        # 情感分析摘要
        if report_data.get('sentiment'):
            sentiment = report_data['sentiment']
            content += "**💭 情感倾向**:\n"
            content += f"- 正面: {sentiment.get('positive', 0)}%"
            content += f" | 中性: {sentiment.get('neutral', 0)}%"
            content += f" | 负面: {sentiment.get('negative', 0)}%\n\n"

        # 数据来源简述
        source_count = 0
        if report_data.get('onlineSearchCount'):
            source_count += 1
        if report_data.get('customSourceCounts'):
            source_count += len(report_data['customSourceCounts']) if isinstance(report_data['customSourceCounts'], dict) else 1

        if source_count > 0:
            content += f"**📊 数据来源**: {source_count} 个数据源\n\n"

        # 生成时间
        content += f"**⏰ 生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        # 再次强调链接
        if report_page_url:
            content += "---\n\n"
            content += f"💡 **[点击此处查看完整的交互式报告]({report_page_url})**\n\n"

        content += "*本报告由舆情分析系统自动生成*"

        return content

    @classmethod
    def _convert_to_wechat_work_format(cls, report_data: Dict[str, Any], analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        将报告数据转换为企业微信机器人消息格式
        """
        # 构建markdown格式的报告内容
        markdown_content = cls._build_markdown_report(report_data, analysis_results)

        return {
            "msgtype": "markdown",
            "markdown": {
                "content": markdown_content
            }
        }

    @classmethod
    def _convert_to_feishu_format(cls, report_data: Dict[str, Any], analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        将报告数据转换为飞书机器人消息格式
        """
        # 构建文本格式的报告内容（飞书机器人支持富文本）
        text_content = cls._build_text_report(report_data, analysis_results)

        return {
            "msg_type": "text",
            "content": {
                "text": text_content
            }
        }

    @classmethod
    def _build_text_report(cls, report_data: Dict[str, Any], analysis_results: Dict[str, Any]) -> str:
        """
        构建文本格式的报告内容（用于飞书等）
        """
        content = "📊 舆情分析报告\n\n"

        # 报告页面链接
        if report_data.get('reportPageUrl'):
            content += f"🔗 查看完整报告: {report_data['reportPageUrl']}\n\n"

        # 基本信息摘要
        if report_data.get('requirementName'):
            content += f"需求名称: {report_data['requirementName']}\n"

        if report_data.get('entityKeyword'):
            content += f"关键词: {report_data['entityKeyword']}\n"

        # 数据统计
        if report_data.get('totalArticles'):
            content += f"文章总数: {report_data['totalArticles']}篇\n"

        # 情感分析
        if report_data.get('sentiment'):
            sentiment = report_data['sentiment']
            content += f"情感倾向: 正面{sentiment.get('positive', 0)}% | 中性{sentiment.get('neutral', 0)}% | 负面{sentiment.get('negative', 0)}%\n"

        # 生成时间
        content += f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        # 再次强调链接
        if report_data.get('reportPageUrl'):
            content += f"点击查看完整报告: {report_data['reportPageUrl']}"

        return content

    @classmethod
    async def _send_http_request(cls, url: str, data: Dict[str, Any]) -> tuple:
        """
        发送HTTP请求
        
        :return: (status_code, response_data, error_message)
        """
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    url,
                    json=data,
                    headers={'Content-Type': 'application/json'}
                )
                
                try:
                    response_data = response.json()
                except:
                    response_data = {'text': response.text}
                
                return response.status_code, response_data, None
                
        except httpx.TimeoutException:
            return None, None, "请求超时"
        except httpx.RequestError as e:
            return None, None, f"网络请求错误: {str(e)}"
        except Exception as e:
            return None, None, f"未知错误: {str(e)}"

    @classmethod
    def _is_push_successful(
        cls,
        status_code: Optional[int],
        response_data: Optional[Dict],
        is_dingtalk: bool = False,
        is_wechat_work: bool = False,
        is_feishu: bool = False
    ) -> bool:
        """
        判断推送是否成功
        """
        if status_code is None:
            return False

        # HTTP状态码检查
        if not (200 <= status_code < 300):
            return False

        # 钉钉机器人特殊检查
        if is_dingtalk and response_data:
            return response_data.get('errcode') == 0

        # 企业微信机器人特殊检查
        if is_wechat_work and response_data:
            return response_data.get('errcode') == 0

        # 飞书机器人特殊检查
        if is_feishu and response_data:
            return response_data.get('code') == 0

        # 其他情况，HTTP状态码200-299即为成功
        return True

    @classmethod
    async def _log_push_result(
        cls,
        query_db: AsyncSession,
        push_request: PushReportRequestModel,
        push_id: str,
        success: bool,
        response_status: Optional[int],
        response_data: Optional[Dict],
        error_message: Optional[str],
        push_time: datetime,
        user_id: Optional[int]
    ):
        """
        记录推送结果到日志
        """
        try:
            # 这里可以将日志保存到数据库
            # 暂时只记录到系统日志
            log_data = {
                'push_id': push_id,
                'target_url': push_request.target_url,
                'success': success,
                'response_status': response_status,
                'response_data': response_data,
                'error_message': error_message,
                'push_time': push_time.isoformat(),
                'user_id': user_id
            }
            
            if success:
                logger.info(f'推送成功: {json.dumps(log_data, ensure_ascii=False)}')
            else:
                logger.error(f'推送失败: {json.dumps(log_data, ensure_ascii=False)}')
                
        except Exception as e:
            logger.error(f'记录推送日志失败: {str(e)}')

    @classmethod
    async def _validate_report_url(cls, report_url: str) -> bool:
        """
        验证报告URL是否可访问

        :param report_url: 报告URL
        :return: 是否可访问
        """
        try:
            import aiohttp
            import asyncio

            # 设置较短的超时时间，避免影响推送性能
            timeout = aiohttp.ClientTimeout(total=5)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(report_url) as response:
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '')
                        if 'text/html' in content_type:
                            logger.info(f'报告URL验证成功: {report_url}')
                            return True
                        else:
                            logger.warning(f'报告URL返回非HTML内容: {content_type}')
                            return False
                    else:
                        logger.warning(f'报告URL访问失败，状态码: {response.status}')
                        return False

        except Exception as e:
            logger.error(f'验证报告URL失败: {str(e)}')
            return False

    @classmethod
    async def _generate_mobile_friendly_url(cls, original_url: str, page_id: str) -> str:
        """
        生成移动端友好的备用URL

        :param original_url: 原始URL
        :param page_id: 页面ID
        :return: 移动端友好的URL
        """
        try:
            from config.env import AppConfig

            # 如果原始URL是OSS URL，尝试生成本地备用URL
            if original_url.startswith('https://oss.jingangai.cn'):
                # 生成本地访问URL作为备用
                base_url = AppConfig.app_host if AppConfig.app_host != '0.0.0.0' else 'localhost'
                port_suffix = f':{AppConfig.app_port}' if AppConfig.app_port != 80 else ''

                backup_url = f"http://{base_url}{port_suffix}{AppConfig.app_root_path}/public/report/view/{page_id}"
                logger.info(f'生成移动端备用URL: {backup_url}')
                return backup_url

            # 如果原始URL是本地URL，尝试生成不同的格式
            elif 'localhost' in original_url or '127.0.0.1' in original_url:
                # 尝试使用IP地址或其他域名
                modified_url = original_url.replace('localhost', '127.0.0.1')
                logger.info(f'生成移动端备用URL: {modified_url}')
                return modified_url

            # 其他情况返回原URL
            return original_url

        except Exception as e:
            logger.error(f'生成移动端友好URL失败: {str(e)}')
            return original_url
