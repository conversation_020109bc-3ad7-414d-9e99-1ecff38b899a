from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from exceptions.exception import ServiceException
from module_opinion.dao.opinion_requirement_dao import OpinionRequirementDao
from module_opinion.entity.vo.opinion_requirement_vo import (
    OpinionRequirementModel,
    OpinionRequirementPageQueryModel,
    CreateOpinionRequirementModel,
    UpdateOpinionRequirementModel,
    DeleteOpinionRequirementModel,
    OpinionRequirementListModel
)
from module_admin.entity.vo.common_vo import CrudResponseModel
from utils.common_util import CamelCaseUtil


class OpinionRequirementService:
    """
    舆情需求管理模块服务层
    """

    @classmethod
    async def get_opinion_requirement_list_services(
        cls, query_db: AsyncSession, query_object: OpinionRequirementPageQueryModel, is_page: bool = False
    ):
        """
        获取舆情需求列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 舆情需求列表信息对象
        """
        requirement_list_result = await OpinionRequirementDao.get_opinion_requirement_list(query_db, query_object, is_page)

        if is_page:
            return requirement_list_result
        else:
            return CamelCaseUtil.transform_result(requirement_list_result)

    @classmethod
    async def get_opinion_requirement_detail_services(cls, query_db: AsyncSession, requirement_id: int):
        """
        获取舆情需求详细信息service

        :param query_db: orm对象
        :param requirement_id: 需求ID
        :return: 舆情需求详细信息对象
        """
        requirement_detail_result = await OpinionRequirementDao.get_opinion_requirement_by_id(query_db, requirement_id)

        if not requirement_detail_result:
            raise ServiceException(message='舆情需求不存在')

        return CamelCaseUtil.transform_result(requirement_detail_result)

    @classmethod
    async def add_opinion_requirement_services(cls, query_db: AsyncSession, add_requirement: CreateOpinionRequirementModel, current_user_id: int):
        """
        新增舆情需求信息service

        :param query_db: orm对象
        :param add_requirement: 新增舆情需求对象
        :param current_user_id: 当前用户ID
        :return: 新增舆情需求校验结果
        """
        # 检查需求名称是否唯一
        if not await OpinionRequirementDao.check_requirement_name_unique(query_db, add_requirement.requirement_name):
            raise ServiceException(message=f'需求名称"{add_requirement.requirement_name}"已存在')

        # 构建需求对象
        requirement_data = OpinionRequirementModel(
            user_id=current_user_id,
            requirement_name=add_requirement.requirement_name,
            entity_keyword=add_requirement.entity_keyword,
            specific_requirement=add_requirement.specific_requirement,
            priority=add_requirement.priority,
            max_keywords_limit=add_requirement.max_keywords_limit,
            status=1,
            analysis_status=0,
            current_step=1,
            create_by=str(current_user_id),
            remark=add_requirement.remark
        )

        try:
            new_requirement = await OpinionRequirementDao.add_opinion_requirement_dao(query_db, requirement_data)
            await query_db.flush()  # 先flush获取ID
            requirement_id = new_requirement.id  # 获取ID
            await query_db.commit()
            return {'id': requirement_id, 'message': '新增成功'}
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_opinion_requirement_services(cls, query_db: AsyncSession, edit_requirement: UpdateOpinionRequirementModel, current_user_id: int):
        """
        编辑舆情需求信息service

        :param query_db: orm对象
        :param edit_requirement: 编辑舆情需求对象
        :param current_user_id: 当前用户ID
        :return: 编辑舆情需求校验结果
        """
        # 检查需求是否存在
        existing_requirement = await OpinionRequirementDao.get_opinion_requirement_by_id(query_db, edit_requirement.id)
        if not existing_requirement:
            raise ServiceException(message='舆情需求不存在')

        # 检查需求名称是否唯一（排除自己）
        if edit_requirement.requirement_name and not await OpinionRequirementDao.check_requirement_name_unique(
            query_db, edit_requirement.requirement_name, edit_requirement.id
        ):
            raise ServiceException(message=f'需求名称"{edit_requirement.requirement_name}"已存在')

        # 构建更新数据
        update_data = {
            'id': edit_requirement.id,
            'update_time': datetime.now(),
            'update_by': str(current_user_id)
        }

        # 只更新非空字段
        for field, value in edit_requirement.model_dump(exclude={'id'}, exclude_none=True).items():
            update_data[field] = value

        try:
            await OpinionRequirementDao.edit_opinion_requirement_dao(query_db, update_data)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def delete_opinion_requirement_services(cls, query_db: AsyncSession, delete_requirement: DeleteOpinionRequirementModel):
        """
        删除舆情需求信息service

        :param query_db: orm对象
        :param delete_requirement: 删除舆情需求对象
        :return: 删除舆情需求校验结果
        """
        if not delete_requirement.ids:
            raise ServiceException(message='请选择要删除的需求')

        try:
            await OpinionRequirementDao.delete_opinion_requirement_dao(query_db, delete_requirement.ids)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='删除成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def get_requirement_list_for_select_services(cls, query_db: AsyncSession, current_user_id: Optional[int] = None):
        """
        获取需求列表用于下拉选择service

        :param query_db: orm对象
        :param current_user_id: 当前用户ID
        :return: 需求列表
        """
        requirement_list = await OpinionRequirementDao.get_requirement_list_for_select(query_db, current_user_id)

        # 转换为响应模型
        result = []
        for requirement in requirement_list:
            result.append(OpinionRequirementListModel(
                id=requirement.id,
                requirement_name=requirement.requirement_name,
                entity_keyword=requirement.entity_keyword,
                status=requirement.status,
                analysis_status=requirement.analysis_status,
                create_time=requirement.create_time
            ))

        return result

    @classmethod
    async def update_requirement_step_services(cls, query_db: AsyncSession, requirement_id: int, step: int):
        """
        更新需求当前步骤service

        :param query_db: orm对象
        :param requirement_id: 需求ID
        :param step: 步骤号
        :return: 更新结果
        """
        # 检查需求是否存在
        existing_requirement = await OpinionRequirementDao.get_opinion_requirement_by_id(query_db, requirement_id)
        if not existing_requirement:
            raise ServiceException(message='舆情需求不存在')

        try:
            await OpinionRequirementDao.update_requirement_step(query_db, requirement_id, step)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='步骤更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def update_keywords_count_services(cls, query_db: AsyncSession, requirement_id: int, count: int):
        """
        更新已选择关键词数量service

        :param query_db: orm对象
        :param requirement_id: 需求ID
        :param count: 关键词数量
        :return: 更新结果
        """
        try:
            await OpinionRequirementDao.update_keywords_count(query_db, requirement_id, count)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='关键词数量更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e
