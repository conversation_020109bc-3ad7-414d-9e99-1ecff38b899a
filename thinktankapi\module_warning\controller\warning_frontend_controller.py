from datetime import datetime
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.service.login_service import LoginService
from module_warning.service.warning_record_service import WarningRecordService
from module_warning.service.warning_scheme_service import WarningSchemeService
from module_warning.service.warning_settings_service import WarningSettingsService
from module_warning.entity.vo.warning_vo import (
    WarningRecordPageQueryModel,
    WarningFrontendDataModel,
    WarningStatisticsModel
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from utils.log_util import logger
from utils.response_util import ResponseUtil


warningFrontendController = APIRouter(prefix='/warning/frontend', dependencies=[Depends(LoginService.get_current_user)])


@warningFrontendController.get(
    '/data', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:list'))]
)
async def get_warning_frontend_data(
    request: Request,
    schemeId: int = None,  # 修复：使用驼峰命名匹配模型别名
    pageNum: int = 1,      # 修复：使用驼峰命名匹配模型别名
    pageSize: int = 10,    # 修复：使用驼峰命名匹配模型别名
    searchText: str = None, # 修复：使用驼峰命名匹配模型别名
    id: int = None,        # ID筛选参数（无别名）
    status: int = None,    # 状态筛选参数（无别名）
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取预警中心前端页面所需的完整数据
    包括：方案列表、记录列表、统计信息、设置信息
    """
    try:
        # 添加详细的参数日志
        logger.info(f'收到前端数据请求 - 方案ID: {schemeId}, 页码: {pageNum}, 页大小: {pageSize}, 搜索文本: {searchText}, ID筛选: {id}, 状态筛选: {status}')

        # 1. 获取所有启用的预警方案（用于侧边栏）
        schemes = await WarningSchemeService.get_active_warning_schemes_services(query_db)
        logger.info(f'获取到 {len(schemes)} 个启用的预警方案')

        # 2. 构建记录查询参数（使用驼峰命名匹配模型别名）
        record_query = WarningRecordPageQueryModel(
            schemeId=schemeId,
            pageNum=pageNum,
            pageSize=pageSize,
            searchText=searchText,
            id=id,  # 添加ID筛选参数
            status=status  # 添加状态筛选参数
        )
        logger.info(f'构建查询参数: schemeId={schemeId}, pageNum={pageNum}, pageSize={pageSize}')

        # 3. 获取预警记录列表（分页）
        records_result = await WarningRecordService.get_warning_record_list_services(
            query_db, record_query, is_page=True
        )

        # 记录查询结果
        if hasattr(records_result, 'total'):
            logger.info(f'查询到预警记录: 总数={records_result.total}, 当前页记录数={len(records_result.rows) if hasattr(records_result, "rows") else 0}')
        else:
            logger.info(f'查询到预警记录: {len(records_result) if records_result else 0} 条')

        # 4. 获取统计信息
        statistics = await WarningRecordService.get_warning_statistics_services(query_db, schemeId)
        logger.info(f'获取统计信息: {statistics}')

        # 5. 获取预警设置（如果指定了方案ID）
        settings = None
        if schemeId:
            try:
                settings = await WarningSettingsService.get_warning_settings_services(query_db, schemeId)
                logger.info(f'获取方案 {schemeId} 的预警设置成功')
            except Exception as e:
                logger.warning(f'获取方案 {schemeId} 的预警设置失败: {str(e)}，使用默认设置')
                try:
                    settings = await WarningSettingsService.get_default_settings_services()
                    logger.info('使用默认预警设置')
                except Exception as default_error:
                    logger.error(f'获取默认预警设置也失败: {str(default_error)}，跳过设置获取')
                    settings = None

        # 6. 组装前端数据
        frontend_data = {
            'schemes': schemes,
            'records': records_result,
            'statistics': statistics,
            'settings': settings
        }

        logger.info(f'获取预警中心前端数据成功，方案ID: {schemeId}, 返回数据结构: schemes={len(schemes)}, records={type(records_result).__name__}, statistics={bool(statistics)}, settings={bool(settings)}')
        return ResponseUtil.success(data=frontend_data)
    except Exception as e:
        logger.error(f'获取预警中心前端数据失败: {str(e)}', exc_info=True)
        return ResponseUtil.error(msg=f'获取预警中心前端数据失败: {str(e)}')


@warningFrontendController.get(
    '/dashboard', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:list'))]
)
async def get_warning_dashboard_data(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取预警中心仪表板数据
    """
    try:
        # 1. 获取总体统计
        total_statistics = await WarningRecordService.get_warning_statistics_services(query_db)
        
        # 2. 获取所有启用的方案
        active_schemes = await WarningSchemeService.get_active_warning_schemes_services(query_db)
        
        # 3. 获取每个方案的统计数据
        scheme_statistics = []
        for scheme in active_schemes:
            scheme_stats = await WarningRecordService.get_warning_statistics_services(query_db, scheme.id)
            scheme_statistics.append({
                'scheme_id': scheme.id,
                'scheme_name': scheme.scheme_name,
                'statistics': scheme_stats
            })
        
        # 4. 获取最近的预警记录（最新10条）
        recent_query = WarningRecordPageQueryModel(page_num=1, page_size=10)
        recent_records = await WarningRecordService.get_warning_record_list_services(
            query_db, recent_query, is_page=True
        )
        
        dashboard_data = {
            'total_statistics': total_statistics,
            'active_schemes_count': len(active_schemes),
            'scheme_statistics': scheme_statistics,
            'recent_records': recent_records
        }
        
        logger.info('获取预警中心仪表板数据成功')
        return ResponseUtil.success(data=dashboard_data)
    except Exception as e:
        logger.error(f'获取预警中心仪表板数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警中心仪表板数据失败: {str(e)}')


@warningFrontendController.get(
    '/init/{scheme_id}', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:list'))]
)
async def init_warning_scheme_data(
    request: Request,
    scheme_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    初始化指定方案的预警数据（用于前端切换方案时）
    """
    try:
        # 1. 获取方案详情
        scheme_detail = await WarningSchemeService.get_warning_scheme_detail_services(query_db, scheme_id)
        
        # 2. 获取该方案的预警记录（第一页）
        record_query = WarningRecordPageQueryModel(
            scheme_id=scheme_id,
            page_num=1,
            page_size=10
        )
        records = await WarningRecordService.get_warning_record_list_services(
            query_db, record_query, is_page=True
        )
        
        # 3. 获取该方案的统计信息
        statistics = await WarningRecordService.get_warning_statistics_services(query_db, scheme_id)
        
        # 4. 获取该方案的设置信息
        settings = await WarningSettingsService.get_warning_settings_services(query_db, scheme_id)
        
        init_data = {
            'scheme': scheme_detail,
            'records': records,
            'statistics': statistics,
            'settings': settings
        }
        
        logger.info(f'初始化预警方案数据成功，方案ID: {scheme_id}')
        return ResponseUtil.success(data=init_data)
    except Exception as e:
        logger.error(f'初始化预警方案数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'初始化预警方案数据失败: {str(e)}')


@warningFrontendController.get(
    '/quick-stats', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:list'))]
)
async def get_quick_statistics(
    request: Request,
    scheme_id: int = None,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取快速统计信息（用于实时更新）
    """
    try:
        statistics = await WarningRecordService.get_warning_statistics_services(query_db, scheme_id)
        
        logger.info(f'获取快速统计信息成功，方案ID: {scheme_id}')
        return ResponseUtil.success(data=statistics)
    except Exception as e:
        logger.error(f'获取快速统计信息失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取快速统计信息失败: {str(e)}')


@warningFrontendController.post(
    '/batch-process', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:edit'))]
)
async def batch_process_records(
    request: Request,
    record_ids: list[int],
    action: str,  # 'approve', 'reject', 'delete'
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    批量处理预警记录
    """
    try:
        if not record_ids:
            return ResponseUtil.failure(msg='请选择要处理的记录')
        
        success_count = 0
        failed_count = 0
        
        for record_id in record_ids:
            try:
                if action == 'approve':
                    await WarningRecordService.update_warning_record_status_services(
                        query_db, record_id, 1, current_user.user.user_name
                    )
                elif action == 'reject':
                    await WarningRecordService.update_warning_record_status_services(
                        query_db, record_id, 2, current_user.user.user_name
                    )
                elif action == 'delete':
                    from module_warning.entity.vo.warning_vo import DeleteWarningRecordModel
                    delete_model = DeleteWarningRecordModel(record_ids=[record_id])
                    await WarningRecordService.delete_warning_record_services(query_db, delete_model)
                
                success_count += 1
            except Exception as e:
                logger.error(f'处理记录{record_id}失败: {str(e)}')
                failed_count += 1
        
        result_msg = f'批量处理完成，成功: {success_count}，失败: {failed_count}'
        logger.info(result_msg)
        return ResponseUtil.success(msg=result_msg, data={
            'success_count': success_count,
            'failed_count': failed_count
        })
    except Exception as e:
        logger.error(f'批量处理预警记录失败: {str(e)}')
        return ResponseUtil.error(msg=f'批量处理预警记录失败: {str(e)}')
