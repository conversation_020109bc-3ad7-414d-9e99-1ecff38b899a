<template>
  <div class="opinion-analysis">
    <!-- 步骤指示器 -->
    <div class="steps-container">
      <!-- 左侧占位区域，保持布局平衡 -->
      <div class="left-placeholder"></div>

      <!-- 步骤指示器 -->
      <div class="steps-wrapper">
        <div class="step-item" :class="{ active: currentStep === 1 }">
          <span class="step-number">1</span>
          <span class="step-text">舆情分析来源</span>
        </div>
        <div class="step-item" :class="{ active: currentStep === 2 }">
          <span class="step-number">2</span>
          <span class="step-text">数据概览</span>
        </div>
        <div class="step-item" :class="{ active: currentStep === 3 }">
          <span class="step-number">3</span>
          <span class="step-text">分析进度</span>
        </div>
        <div class="step-item" :class="{ active: currentStep === 4 }">
          <span class="step-number">4</span>
          <span class="step-text">报告预览</span>
        </div>
      </div>

      <!-- 右侧按钮区域 -->
      <div class="right-actions">
        <!-- 测试步骤切换按钮 -->
        <!-- <el-dropdown @command="handleStepSwitch" class="step-switch-dropdown">
          <el-button
            class="step-switch-btn"
            type="warning"
            size="small"
          >
            测试切换步骤
            <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="1" :class="{ 'is-active': currentStep === 1 }">
              <i class="el-icon-edit-outline"></i>
              第1步：舆情分析来源
            </el-dropdown-item>
            <el-dropdown-item command="2" :class="{ 'is-active': currentStep === 2 }">
              <i class="el-icon-view"></i>
              第2步：数据概览
            </el-dropdown-item>
            <el-dropdown-item command="3" :class="{ 'is-active': currentStep === 3 }">
              <i class="el-icon-loading"></i>
              第3步：分析进度
            </el-dropdown-item>
            <el-dropdown-item command="4" :class="{ 'is-active': currentStep === 4 }">
              <i class="el-icon-document"></i>
              第4步：报告预览
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown> -->

        <el-button
          class="analyze-record-btn"
          type="info"
          size="small"
          @click="goToAnalyzeRecord"
        >
          分析记录
        </el-button>
        <el-button
          class="timed-push-btn"
          type="primary"
          size="small"
          @click="handleTimedPush"
        >
          定时推送
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 第一步：分析需求 -->
      <div v-if="currentStep === 1" class="analysis-source">
        <div class="section-header">
          <h2 class="section-title">分析需求</h2>
          <el-button
            type="primary"
            size="small"
            @click="handleTemplateClick"
            class="template-btn"
          >
            模板
          </el-button>
        </div>
<div class="input-section">
          <div class="input-label">
            需求名称
            <span class="required">*</span>
          </div>
          <el-input
            v-model="requirementName"
            placeholder="请输入需求名称"
            class="entity-input"
            :class="{ 'error': !requirementName.trim() && showValidation }"
          />
        </div>
        <!-- 实体关键词区域 -->
        <div class="input-section">
          <div class="input-label">
            实体关键词
            <span class="required">*</span>
          </div>
          <el-input
            v-model="entityKeyword"
            placeholder="请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等"
            class="entity-input"
            :class="{ 'error': !entityKeyword.trim() && showValidation }"
          />
        </div>

        <!-- 具体需求区域 -->
        <div class="input-section">
          <div class="input-label">
            具体需求
            <span class="required">*</span>
          </div>
          <el-input
            v-model="specificRequirement"
            type="textarea"
            :rows="4"
            placeholder="请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）"
            class="requirement-textarea"
            :class="{ 'error': !specificRequirement.trim() && showValidation }"
          />
        </div>

        <!-- 选择关联词区域 -->
        <div class="related-words-section">
          <div class="section-header">
            <div class="header-left">
              <span class="section-label">选择关联词</span>
              <span class="word-count" :class="{ 'max-reached': selectedKeywords.length >= maxKeywords }">
                ({{ selectedKeywords.length }}/{{ maxKeywords }})
              </span>
            </div>
            <el-button
              v-if="generatedKeywords.length > 0"
              class="regenerate-btn"
              size="mini"
              type="text"
              @click="regenerateKeywords"
            >
              <i class="el-icon-refresh"></i>
              重新生成
            </el-button>
          </div>

          <div class="keywords-textbox-wrapper">
            <!-- 显示生成的关键词 -->
            <div v-if="generatedKeywords.length > 0" class="generated-keywords-display">
              <div v-for="(category, categoryName) in groupedKeywords" :key="categoryName" class="keyword-category">
                <el-button
                  class="category-button"
                  size="small"
                  type="primary"
                  plain
                  @click="toggleCategorySelection(categoryName, category)"
                >
                  {{ categoryName }}
                </el-button>
                <div class="keyword-tags">
                  <el-tag
                    v-for="(keyword, index) in category"
                    :key="index"
                    :class="['keyword-tag', { selected: isKeywordSelected(keyword) }]"
                    @click="toggleKeyword(keyword)"
                  >
                    {{ keyword }}
                  </el-tag>
                </div>
              </div>
            </div>

            <!-- 生成关联词按钮区域 -->
            <div v-if="generatedKeywords.length === 0" class="words-container">
              <div class="generate-word-btn" @click="generateRelatedWords">
                <i class="el-icon-magic-stick"></i>
                <span>生成关联词</span>
              </div>
              <div class="word-description">
                根据你填写的需求和关键词生成关联词
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二步：数据概览 -->
      <div v-if="currentStep === 2" class="data-overview">
        <h2 class="section-title">选择数据来源</h2>

        <!-- 数据来源选项 -->
        <div class="data-source-section">
          <!-- 联网搜索选项 -->
          <div class="source-option" @click="toggleOnlineSearch">
            <el-checkbox
              v-model="enableOnlineSearch"
              class="source-checkbox"
            ></el-checkbox>
            <div class="source-icon">
              <i class="el-icon-search"></i>
            </div>
            <div class="source-content">
              <h3>联网搜索</h3>
              <p class="source-description">使用AI搜索引擎获取最新网络信息</p>
            </div>
          </div>

          <!-- 自定义数据源搜索选项 -->
          <div class="source-option" @click="toggleCustomDataSource">
            <el-checkbox
              v-model="enableCustomDataSource"
              class="source-checkbox"
            ></el-checkbox>
            <div class="source-icon">
              <i class="el-icon-link"></i>
            </div>
            <div class="source-content">
              <h3>自定义数据源搜索 <span style="color: #909399; font-size: 12px;">(可选)</span></h3>
              <p class="source-description">
                从已配置的数据源网站抓取相关信息，可与联网搜索配合使用
                <span v-if="customDataSources.length > 0" class="source-count">
                  ({{ customDataSources.length }}个数据源)
                </span>
              </p>
            </div>
          </div>

          <!-- 数据源列表区域 -->
          <div v-if="enableCustomDataSource" class="data-source-list-section">
            <div class="list-header">
              <h4>数据源列表</h4>
              <div class="header-controls">
                <el-button
                  size="small"
                  type="primary"
                  @click="showAddSourceForm"
                >
                  <i class="el-icon-plus"></i>
                  新增数据源
                </el-button>
                <el-button
                  size="small"
                  type="success"
                  @click="refreshDataSourceList"
                >
                  <i class="el-icon-refresh"></i>
                  刷新
                </el-button>
              </div>
            </div>

            <!-- 数据源表格 -->
            <div class="data-source-table">
              <el-table
                :data="paginatedDataSources"
                v-loading="dataSourceListState.loading"
                style="width: 100%"
                empty-text="暂无数据源"
                size="small"
                @selection-change="handleDataSourceSelectionChange"
              >
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column prop="sourceUrl" label="数据源URL">
                  <template slot-scope="scope">
                    <div class="url-cell">
                      <i class="el-icon-link"></i>
                      <span class="url-text">{{ scope.row.sourceUrl }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" fixed="right">
                  <template slot-scope="scope">
                    <el-button
                      type="text"
                      size="mini"
                      @click="deleteDataSource(scope.row)"
                      title="删除"
                      style="color: #f56c6c;"
                    >
                      <i class="el-icon-delete"></i>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页 -->
              <div class="pagination-wrapper" v-if="dataSourceList.length > 0">
                <el-pagination
                  @current-change="handleDataSourcePageChange"
                  @size-change="handleDataSourceSizeChange"
                  :current-page="dataSourceListState.current_page"
                  :page-sizes="[10, 20, 50]"
                  :page-size="dataSourceListState.page_size"
                  :total="dataSourceList.length"
                  layout="total, sizes, prev, pager, next"
                  small
                />
              </div>
            </div>
          </div>

          <!-- 新增数据源表单 -->
          <div v-if="showAddSourceInput" class="add-source-form">
            <div class="form-header">
              <h3>新增数据源</h3>
              <i class="el-icon-close" @click="hideAddSourceForm"></i>
            </div>
            <div class="form-item">
              <label class="form-label">
                数据源网址
                <span class="required">*</span>
              </label>
              <div class="input-group">
                <el-input
                  v-model="newSourceUrl"
                  placeholder="请输入网址，例如：https://www.example.com"
                  class="source-url-input"
                  @keyup.enter="confirmAddSource"
                />
                <el-button type="primary" @click="confirmAddSource">确定</el-button>
              </div>
            </div>
          </div>

          <!-- 新增来源按钮 -->
          <div v-if="!showAddSourceInput" class="add-source-btn" @click="showAddSourceForm">
            <i class="el-icon-plus"></i>
            <span>新增来源</span>
          </div>
        </div>
      </div>

      <!-- 第三步：分析进度 -->
      <div v-if="currentStep === 3" class="analysis-progress">
        <h2 class="section-title">分析进度</h2>

        <!-- 分析状态概览 -->
        <div class="progress-overview">
          <div class="status-card">
            <div class="status-header">
              <h3>当前状态</h3>
              <div class="status-indicator" :class="analysisStatus">
                <span class="status-dot"></span>
                <span class="status-text">{{ getAnalysisStatusText() }}</span>
              </div>
            </div>
            <div class="progress-bar-container">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: analysisProgress + '%' }"></div>
              </div>
              <span class="progress-text">{{ analysisProgress }}%</span>
            </div>
          </div>
        </div>

        <!-- 实时日志显示 -->
        <div class="real-time-logs">
          <div class="logs-header">
            <h3>分析日志</h3>
            <div class="logs-controls">
              <el-button size="mini" @click="clearLogs" type="text">清空日志</el-button>
              <el-button size="mini" @click="toggleAutoScroll" type="text">
                {{ autoScroll ? '停止滚动' : '自动滚动' }}
              </el-button>
            </div>
          </div>
          <div class="logs-container" ref="logsContainer">
            <div v-if="analysisLogs.length === 0" class="no-logs">
              <i class="el-icon-loading"></i>
              <span>等待分析开始...</span>
            </div>
            <div v-else class="logs-list">
              <div
                v-for="(log, index) in analysisLogs"
                :key="index"
                class="log-item"
                :class="log.level"
              >
                <span class="log-time">{{ formatLogTime(log.timestamp) }}</span>
                <span class="log-level">{{ (log.level || 'info').toUpperCase() }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分析完成后的操作 -->
        <div v-if="analysisStatus === 'completed'" class="analysis-completed">
          <div class="completion-message">
            <i class="el-icon-success"></i>
            <span>分析已完成！</span>
          </div>
          <el-button type="primary" size="large" @click="goToReportPreview">
            查看分析报告
          </el-button>
        </div>

        <!-- 分析失败时的操作 -->
        <div v-if="analysisStatus === 'failed'" class="analysis-failed">
          <div class="failure-message">
            <i class="el-icon-error"></i>
            <span>分析失败，请重试</span>
          </div>
          <el-button type="danger" size="large" @click="retryAnalysis">
            重新分析
          </el-button>
        </div>
      </div>

      <!-- 第四步：报告预览 -->
      <div v-if="currentStep === 4" class="report-preview">
        <h2 class="section-title">分析报告预览</h2>

        <!-- 报告概览 -->
        <div class="report-overview">
          <div class="overview-card">
            <div class="card-header">
              <h3>分析概览</h3>
              <span class="analysis-time">{{ formatAnalysisTime(new Date()) }}</span>
            </div>
            <div class="overview-stats">
              <div class="stat-item">
                <div class="stat-number">{{ reportData.totalArticles || 0 }}</div>
                <div class="stat-label">相关文章</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ reportData.totalKeywords || selectedKeywords.length }}</div>
                <div class="stat-label">关键词</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ reportData.dataSources || (enableOnlineSearch ? 1 : 0) + customDataSources.length }}</div>
                <div class="stat-label">数据源</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 情感分析结果 -->
        <div class="sentiment-analysis">
          <div class="analysis-card">
            <h3>情感倾向分析</h3>
            <div class="sentiment-chart">
              <div class="sentiment-item positive">
                <div class="sentiment-bar">
                  <div class="bar-fill" :style="{ width: (reportData.sentiment?.positive || 0) + '%' }"></div>
                </div>
                <div class="sentiment-info">
                  <span class="sentiment-label">正面</span>
                  <span class="sentiment-value">{{ reportData.sentiment?.positive || 0 }}%</span>
                </div>
              </div>
              <div class="sentiment-item neutral">
                <div class="sentiment-bar">
                  <div class="bar-fill" :style="{ width: (reportData.sentiment?.neutral || 0) + '%' }"></div>
                </div>
                <div class="sentiment-info">
                  <span class="sentiment-label">中性</span>
                  <span class="sentiment-value">{{ reportData.sentiment?.neutral || 0 }}%</span>
                </div>
              </div>
              <div class="sentiment-item negative">
                <div class="sentiment-bar">
                  <div class="bar-fill" :style="{ width: (reportData.sentiment?.negative || 0) + '%' }"></div>
                </div>
                <div class="sentiment-info">
                  <span class="sentiment-label">负面</span>
                  <span class="sentiment-value">{{ reportData.sentiment?.negative || 0 }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 关键词分析 -->
        <div class="keyword-analysis">
          <div class="analysis-card">
            <h3>关键词分析</h3>
            <div class="selected-keywords-display">
              <div class="keyword-list">
                <div class="keyword-item" v-for="(keyword, index) in selectedKeywords" :key="index">
                  <span class="keyword-text">{{ keyword }}</span>
                  <span class="keyword-frequency">{{ getKeywordFrequency(keyword) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据来源统计 -->
        <div class="data-source-stats">
          <div class="analysis-card">
            <h3>数据来源统计</h3>
            <div class="source-list">
              <div v-if="enableOnlineSearch" class="source-item">
                <div class="source-icon online">
                  <i class="el-icon-search"></i>
                </div>
                <div class="source-info">
                  <div class="source-name">联网搜索</div>
                  <div class="source-desc">AI搜索引擎数据</div>
                </div>
                <div class="source-count">{{ reportData.onlineSearchCount || 0 }} 条</div>
              </div>
              <div v-for="(source, index) in customDataSources" :key="index" class="source-item">
                <div class="source-icon custom">
                  <i class="el-icon-link"></i>
                </div>
                <div class="source-info">
                  <div class="source-name">{{ extractDomainName(source) }}</div>
                  <div class="source-desc">自定义数据源</div>
                </div>
                <div class="source-count">{{ getSourceCount(source) }} 条</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 详细数据 -->
        <div class="detailed-data" v-if="allArticles.length > 0">
          <div class="analysis-card">
            <div class="card-header-with-controls">
              <h3>详细数据</h3>
              <div class="data-controls">
                <el-input
                  v-model="articleListState.searchKeyword"
                  placeholder="搜索文章标题或内容"
                  prefix-icon="el-icon-search"
                  size="small"
                  style="width: 200px; margin-right: 12px;"
                  clearable
                />
                <el-select
                  v-model="articleListState.selectedSource"
                  placeholder="筛选来源"
                  size="small"
                  style="width: 120px; margin-right: 12px;"
                  clearable
                >
                  <el-option label="全部来源" value=""></el-option>
                  <el-option
                    v-for="source in uniqueSources"
                    :key="source"
                    :label="source"
                    :value="source"
                  ></el-option>
                </el-select>
                <el-select
                  v-model="articleListState.selectedSentiment"
                  placeholder="筛选情感"
                  size="small"
                  style="width: 100px;"
                  clearable
                >
                  <el-option label="全部情感" value=""></el-option>
                  <el-option label="正面" value="positive"></el-option>
                  <el-option label="中性" value="neutral"></el-option>
                  <el-option label="负面" value="negative"></el-option>
                </el-select>
              </div>
            </div>

            <div class="article-list">
              <div
                v-for="(article, index) in paginatedArticles"
                :key="`article-${index}`"
                class="article-item"
              >
                <div class="article-header">
                  <div class="article-title-row">
                    <h4 class="article-title" @click="toggleArticleExpand(index)">
                      {{ article.title }}
                      <i :class="isArticleExpanded(index) ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                    </h4>
                    <div class="article-meta">
                      <span class="article-source">{{ article.source }}</span>
                      <span :class="['sentiment-tag', article.sentiment]">
                        {{ getSentimentLabel(article.sentiment) }}
                      </span>
                    </div>
                  </div>
                  <div class="article-info">
                    <span class="publish-time" v-if="article.publish_time">
                      {{ formatPublishTime(article.publish_time) }}
                    </span>
                    <div class="article-actions">
                      <el-button
                        type="text"
                        size="mini"
                        icon="el-icon-copy-document"
                        @click="copyArticleContent(article)"
                      >
                        复制
                      </el-button>
                      <el-button
                        v-if="article.url && article.url.trim()"
                        type="text"
                        size="mini"
                        icon="el-icon-link"
                        @click="openArticleUrl(article.url)"
                        :title="getUrlTooltip(article.url)"
                      >
                        原文链接
                      </el-button>
                    </div>
                  </div>
                </div>

                <div class="article-content" v-if="!isArticleExpanded(index)">
                  <p class="content-summary">{{ getContentSummary(article.content) }}</p>
                </div>

                <div class="article-content expanded" v-if="isArticleExpanded(index)">
                  <p class="content-full">{{ article.content }}</p>
                </div>
              </div>
            </div>

            <!-- 分页 -->
            <div class="pagination-wrapper" v-if="filteredArticles.length > 0">
              <el-pagination
                @current-change="handlePageChange"
                :current-page="articleListState.currentPage"
                :page-size="articleListState.pageSize"
                :total="filteredArticles.length"
                layout="prev, pager, next, total"
                small
              />
            </div>
          </div>
        </div>

        <!-- 报告操作 -->
        <div class="report-actions">
          <div class="action-buttons">
            <el-button size="large" icon="el-icon-download">导出报告</el-button>
            <el-button size="large" icon="el-icon-share">分享报告</el-button>
            <el-button type="primary" size="large" icon="el-icon-s-promotion">生成完整报告</el-button>
          </div>
        </div>
      </div>

      <!-- 底部按钮区域 -->
      <div class="bottom-actions">
        <el-button v-if="currentStep === 2 || currentStep === 4" @click="goToPreviousStep" size="large">上一步</el-button>
        <el-button
          v-if="currentStep === 1"
          @click="goToNextStep"
          type="primary"
          size="large"
          :disabled="!canGoToNextStep"
        >下一步</el-button>
        <el-button v-if="currentStep === 2" @click="startAnalysis" type="primary" size="large">开始分析</el-button>
        <el-button v-if="currentStep === 3 && analysisStatus === 'running'" @click="cancelAnalysis" size="large">取消分析</el-button>
        <el-button v-if="currentStep === 4" @click="showPushDialog" type="primary" size="large">
          <i class="el-icon-s-promotion"></i>
          推送报告
        </el-button>
      </div>
    </div>

    <!-- 定时任务抽屉 -->
    <el-drawer
      title="定时任务"
      :visible.sync="timedTaskDialogVisible"
      direction="rtl"
      size="600px"
      :before-close="closeTimedTaskDialog"
      custom-class="timed-task-drawer"
    >
      <!-- 抽屉头部右侧按钮 -->
      <div slot="title" class="drawer-header">
        <span class="drawer-title">定时任务</span>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          @click="handleAddTimedTask"
          class="add-task-btn"
        >
          定时任务
        </el-button>
      </div>

      <!-- 抽屉内容 -->
      <div class="drawer-content">
        <!-- 空状态 -->
        <div v-if="timedTaskList.length === 0" class="empty-state">
          <div class="empty-content">
            <!-- 空状态图标 -->
            <div class="empty-icon">
              <svg width="120" height="120" viewBox="0 0 120 120" fill="none">
                <!-- 文件夹图标 -->
                <path d="M20 30h25l5-10h50v70H20V30z" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="2"/>
                <path d="M25 35h70v50H25V35z" fill="#fafafa" stroke="#e0e0e0" stroke-width="1"/>
                <!-- 文档图标 -->
                <rect x="35" y="45" width="30" height="25" fill="#ffffff" stroke="#d0d0d0" stroke-width="1" rx="2"/>
                <rect x="70" y="50" width="20" height="15" fill="#ffffff" stroke="#d0d0d0" stroke-width="1" rx="2"/>
                <!-- 装饰线条 -->
                <line x1="40" y1="52" x2="60" y2="52" stroke="#e0e0e0" stroke-width="1"/>
                <line x1="40" y1="57" x2="55" y2="57" stroke="#e0e0e0" stroke-width="1"/>
                <line x1="40" y1="62" x2="58" y2="62" stroke="#e0e0e0" stroke-width="1"/>
                <line x1="75" y1="55" x2="85" y2="55" stroke="#e0e0e0" stroke-width="1"/>
                <line x1="75" y1="60" x2="82" y2="60" stroke="#e0e0e0" stroke-width="1"/>
              </svg>
            </div>
            <p class="empty-text">暂无定时任务</p>
            <el-button type="primary" @click="handleCreateTimedTask" class="create-btn">
              去创建
            </el-button>
          </div>
        </div>

        <!-- 任务列表 -->
        <div v-else class="task-list">
          <div v-if="timedTaskList.length === 0" class="empty-task-list">
            <div class="empty-icon">📅</div>
            <div class="empty-text">暂无定时任务</div>
            <el-button type="primary" size="small" @click="handleAddTimedTask" class="add-task-btn">
              添加任务
            </el-button>
          </div>
          <div v-else class="task-items">
            <div v-for="(task, index) in timedTaskList" :key="index" class="task-item">
              <div class="task-info">
                <div class="task-header">
                  <div class="task-name">{{ task.name }}</div>
                  <div class="task-status" :class="{ 'status-running': task.status === 'running', 'status-pending': task.status === 'pending' }">
                    {{ task.status === 'running' ? '运行中' : '待运行' }}
                  </div>
                </div>
                <!-- 任务描述已隐藏 -->
                <div class="task-schedule">
                  <i class="el-icon-time"></i>
                  <span>{{ getTaskScheduleText(task) }}</span>
                </div>
              </div>
              <div class="task-actions">
                <el-button type="text" size="mini" @click="previewTask(index)" title="预览任务详情">
                  <i class="el-icon-view"></i>
                </el-button>
                <el-button type="text" size="mini" @click="toggleTaskStatus(index)" :title="task.status === 'running' ? '暂停任务' : '启动任务'">
                  <i :class="task.status === 'running' ? 'el-icon-video-pause' : 'el-icon-video-play'"></i>
                </el-button>
                <el-button type="text" size="mini" @click="editTask(index)" title="编辑任务">
                  <i class="el-icon-edit"></i>
                </el-button>
                <el-button type="text" size="mini" @click="deleteTask(index)" title="删除任务">
                  <i class="el-icon-delete"></i>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 创建/编辑任务弹窗 -->
      <el-dialog
        :title="editingTaskIndex === -1 ? '创建定时任务' : '编辑定时任务'"
        :visible.sync="createTaskDialogVisible"
        width="500px"
        :before-close="closeCreateTaskDialog"
        :append-to-body="true"
        class="create-task-dialog"
      >
        <div class="task-form">
          <!-- 任务需求 -->
          <div class="task-requirement-section">
            <div class="section-label">
              任务需求
              <span class="required">*</span>
            </div>
                  <div class="form-group">
              <div class="input-label">需求名称</div>
              <el-select
                v-model="taskForm.requirementId"
                placeholder="请选择需求"
                class="task-name-input"
                style="width: 100%"
              >
                <el-option
                  v-for="requirement in requirementList"
                  :key="requirement.id"
                  :label="requirement.requirementName"
                  :value="requirement.id"
                />
              </el-select>
            </div>
            <div class="form-group">
              <div class="input-label">任务名称</div>
              <el-input
                v-model="taskForm.name"
                placeholder="请输入任务名称"
                class="task-name-input"
              />
            </div>
            <div class="form-group">
              <div class="input-label">任务描述</div>
              <el-input
                v-model="taskForm.description"
                type="textarea"
                :rows="3"
                placeholder="请描述具体的任务需求，例如：基于当前分析需求自动生成AI新闻总结"
                class="task-description-input"
              />
                    <div class="form-group">
              <div class="input-label">推送地址</div>
              <el-input
                v-model="taskForm.pushUrl"
                placeholder="例如：https://www.baidu.com"
                class="task-name-input"
              />
            </div>
            </div>
          </div>

          <!-- 执行时间 -->
          <div class="execute-time-section">
            <div class="section-label">执行时间</div>
            <div class="time-selector">
              <el-select v-model="taskForm.frequency" placeholder="选择频率" class="frequency-select">
                <el-option label="仅一次" value="once"></el-option>
                <el-option label="每天" value="daily"></el-option>
                <el-option label="每周" value="weekly"></el-option>
                <el-option label="每月" value="monthly"></el-option>
              </el-select>
              <!-- 一次性任务：选择具体日期时间 -->
              <el-date-picker
                v-if="taskForm.frequency === 'once'"
                v-model="taskForm.executeDateTime"
                type="datetime"
                placeholder="选择执行日期和时间"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                class="datetime-picker"
                :picker-options="{
                  disabledDate(time) {
                    return time.getTime() < Date.now() - 8.64e7
                  }
                }"
              />
              <!-- 周期性任务：选择时间 -->
              <el-time-picker
                v-else
                v-model="taskForm.executeTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="选择时间"
                class="time-picker"
              />
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div slot="footer" class="dialog-footer">
          <el-button @click="modifyPlan" class="modify-btn">修改计划</el-button>
          <el-button type="primary" @click="saveAndRunTask" class="run-btn">保存并运行</el-button>
          <el-button type="success" @click="saveTaskPlan" class="save-btn">保存计划</el-button>
        </div>
      </el-dialog>
    </el-drawer>

    <!-- 推送报告弹窗 -->
    <el-dialog
      title="推送报告"
      :visible.sync="pushReportDialog.visible"
      width="500px"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="hidePushDialog"
    >
      <el-form :model="pushReportDialog" label-width="80px">
        <el-form-item label="目标URL" required>
          <el-input
            v-model="pushReportDialog.url"
            placeholder="请输入推送目标URL地址"
            clearable
            :disabled="pushReportDialog.loading"
          >
            <template slot="prepend">https://</template>
          </el-input>
          <div class="form-tip">
            <i class="el-icon-info"></i>
            将推送包含报告页面链接的消息，接收方可点击链接查看完整报告<br>
            <strong>支持所有地址格式：</strong><br>
            • 钉钉机器人：https://oapi.dingtalk.com/robot/send?access_token=xxx<br>
            • 企业微信：https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx<br>
            • 飞书机器人：https://open.feishu.cn/open-apis/bot/v2/hook/xxx<br>
            • 普通HTTP接口：https://your-domain.com/api/webhook<br>
            • 本地地址：localhost:3000/webhook 或 127.0.0.1:8080/api<br>
            • 测试地址：httpbin.org/post
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="hidePushDialog" :disabled="pushReportDialog.loading">
          取消
        </el-button>
        <el-button
          type="default"
          @click="savePushPlan"
          :disabled="pushReportDialog.loading"
        >
          保存计划
        </el-button>
        <el-button
          type="primary"
          @click="directPushReport"
          :loading="pushReportDialog.loading"
          :disabled="!pushReportDialog.url.trim()"
        >
          直接推送
        </el-button>
      </div>
    </el-dialog>

    <!-- 任务预览弹窗 -->
    <el-dialog
      title="任务详情预览"
      :visible.sync="taskPreviewDialog.visible"
      width="600px"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="hideTaskPreviewDialog"
      class="task-preview-dialog"
    >
      <div v-if="taskPreviewDialog.taskData" class="task-preview-content">
        <!-- 基本信息 -->
        <div class="preview-section">
          <h3 class="section-title">
            <i class="el-icon-info"></i>
            基本信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <label>任务名称：</label>
              <span>{{ taskPreviewDialog.taskData.name }}</span>
            </div>
            <div class="info-item">
              <label>任务状态：</label>
              <el-tag :type="taskPreviewDialog.taskData.status === 'running' ? 'success' : 'info'">
                {{ taskPreviewDialog.taskData.status === 'running' ? '运行中' : '待运行' }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>需求ID：</label>
              <span>{{ taskPreviewDialog.taskData.requirementId || '未关联' }}</span>
            </div>
          </div>
        </div>

        <!-- 任务描述 -->
        <div class="preview-section" v-if="taskPreviewDialog.taskData.description">
          <h3 class="section-title">
            <i class="el-icon-document"></i>
            任务描述
          </h3>
          <div class="description-content">
            {{ taskPreviewDialog.taskData.description }}
          </div>
        </div>

        <!-- 执行计划 -->
        <div class="preview-section">
          <h3 class="section-title">
            <i class="el-icon-time"></i>
            执行计划
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <label>执行频率：</label>
              <span>{{ getFrequencyText(taskPreviewDialog.taskData.frequency) }}</span>
            </div>
            <div class="info-item">
              <label>执行时间：</label>
              <span>{{ taskPreviewDialog.taskData.executeTime }}</span>
            </div>
          </div>
        </div>

        <!-- 推送配置 -->
        <div class="preview-section" v-if="taskPreviewDialog.taskData.pushUrl">
          <h3 class="section-title">
            <i class="el-icon-s-promotion"></i>
            推送配置
          </h3>
          <div class="push-config">
            <div class="info-item">
              <label>推送地址：</label>
              <div class="url-display">
                <span class="url-text">{{ getMaskedUrl(taskPreviewDialog.taskData.pushUrl) }}</span>
                <el-button
                  type="text"
                  size="mini"
                  @click="copyToClipboard(taskPreviewDialog.taskData.pushUrl)"
                  title="复制完整地址"
                >
                  <i class="el-icon-copy-document"></i>
                </el-button>
              </div>
            </div>
            <div class="info-item">
              <label>推送类型：</label>
              <el-tag size="small">{{ getPushTypeText(taskPreviewDialog.taskData.pushUrl) }}</el-tag>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="hideTaskPreviewDialog">关闭</el-button>
        <el-button
          type="primary"
          @click="editTaskFromPreview"
          v-if="taskPreviewDialog.taskData"
        >
          编辑任务
        </el-button>
      </div>
    </el-dialog>

    <!-- 模板选择弹窗 -->
    <el-dialog
      title="选择分析模板"
      :visible.sync="templateDialog.visible"
      width="700px"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeTemplateDialog"
      class="template-dialog"
    >
      <div class="template-content">
        <div class="template-list">
          <div
            v-for="template in templateList"
            :key="template.id"
            class="template-item"
            :class="{ 'selected': templateDialog.selectedTemplate === template.id }"
            @click="templateDialog.selectedTemplate = template.id"
          >
            <div class="template-header">
              <div class="template-title">
                <i class="el-icon-document"></i>
                {{ template.name }}
              </div>
              <div class="template-category">{{ template.category }}</div>
            </div>
            <div class="template-details">
              <div class="template-field">
                <label>实体关键词：</label>
                <span>{{ template.entityKeyword }}</span>
              </div>
              <div class="template-field">
                <label>具体需求：</label>
                <p>{{ template.requirement }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeTemplateDialog">取消</el-button>
        <el-button
          type="primary"
          @click="applyTemplate(templateList.find(t => t.id === templateDialog.selectedTemplate))"
          :disabled="!templateDialog.selectedTemplate"
        >
          应用模板
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  getRequirementList,
  createRequirement,
  updateRequirement,
  deleteRequirement,
  getKeywordCategories,
  getDataSourceList,
  createDataSource,
  deleteDataSource as deleteDataSourceAPI,
  getTaskStatistics,
  getDataSourceStatistics,
  getTimedTaskList,
  getTimedTaskDetail,
  createTimedTask,
  updateTimedTask,
  updateTaskStatus,
  deleteTimedTask,
  getTasksByRequirement,
  performOnlineSearch,
  startAnalysis,
  pushReport,
  getPublicReportData,
  checkTaskExists,
  generateRelatedKeywords,
  createAnalysisProgressTask,
  addProgressLog,
  getAnalysisProgress,
  completeAnalysisTask,
  cancelAnalysisTask,
  generateAndUploadReport
} from '@/api/opinion-analysis'

import {
  getTemplatesForSelection,
  updateTemplateUsage
} from '@/api/opinion-template'

import { getDashboardStatistics, getCurrentMonthAnalysisCount } from '@/api/dashboard'

export default {
  name: 'OpinionAnalysis',
  data() {
    return {
      currentStep: 1, // 当前步骤
      requirementName: '', // 需求名称
      entityKeyword: '', // 实体关键词
      specificRequirement: '', // 具体需求
      selectedKeywords: [], // 已选择的关键词
      generatedKeywords: [], // 生成的所有关键词
      maxKeywords: 5, // 最大选择数量
      enableOnlineSearch: true, // 是否启用联网搜索
      enableCustomDataSource: false, // 是否启用自定义数据源搜索（默认关闭，可选）
      customDataSources: [], // 自定义数据源URL列表
      showAddSourceInput: false, // 显示新增数据源表单
      newSourceUrl: '', // 新增数据源URL
      // 数据源列表相关状态
      dataSourceList: [], // 数据源列表数据
      selectedDataSources: [], // 选中的数据源
      dataSourceListState: {
        loading: false,
        current_page: 1,
        page_size: 10
      },
      showValidation: false, // 是否显示验证错误样式
      timedTaskDialogVisible: false, // 定时任务抽屉显示状态
      timedTaskList: [], // 定时任务列表
      createTaskDialogVisible: false, // 创建任务弹窗显示状态
      editingTaskIndex: -1, // 当前编辑的任务索引，-1表示新建任务
      taskForm: {
        requirementId: '', // 需求ID
        name: '', // 任务名称
        description: '',
        executeTime: '16:00',
        executeDateTime: '', // 一次性任务的执行日期时间
        frequency: 'daily',
        pushUrl: '' // 推送地址
      },
      requirementList: [], // 需求列表
      keywordCategories: [], // 关键词分类列表
      currentRequirementId: null, // 当前需求的ID
      requirementSaved: false, // 需求是否已保存到数据库
      requirementModified: false, // 需求是否被修改（需要更新）
      reportData: { // 报告数据
        totalArticles: 0,
        totalKeywords: 0,
        dataSources: 0,
        sentiment: {
          positive: 0,
          neutral: 0,
          negative: 0
        },
        onlineSearchCount: 0,
        customSourceCounts: {}
      },
      analysisResults: null, // 完整的分析结果数据
      // 文章列表相关状态
      articleListState: {
        currentPage: 1,
        pageSize: 10,
        searchKeyword: '',
        selectedSource: '',
        selectedSentiment: '',
        expandedArticles: new Set() // 存储展开的文章ID
      },
      // 推送报告相关状态
      pushReportDialog: {
        visible: false,
        url: '',
        loading: false
      },
      // 任务预览弹窗状态
      taskPreviewDialog: {
        visible: false,
        taskData: null,
        loading: false
      },
      // 模板弹窗状态
      templateDialog: {
        visible: false,
        selectedTemplate: null
      },
      // 模板数据（从数据库获取）
      templateList: [],
      // 分析进度相关状态
      analysisStatus: 'idle', // 分析状态：idle-空闲，running-运行中，completed-已完成，failed-失败
      analysisProgress: 0, // 分析进度百分比
      analysisLogs: [], // 分析日志列表
      autoScroll: true, // 是否自动滚动日志
      currentTaskId: null, // 当前分析任务ID
      websocket: null, // WebSocket连接
      heartbeatTimer: null, // 心跳定时器
      reconnectAttempts: 0, // 重连尝试次数
      maxReconnectAttempts: 5, // 最大重连次数
      reconnectTimer: null, // 重连定时器
      statusPollingTimer: null, // 状态轮询定时器
      // HTTP轮询相关状态
      httpPollingTimers: new Map(), // 存储多个轮询定时器
      pollingConfigs: new Map(), // 存储轮询配置
      // 报告OSS相关状态
      reportOssUrl: null, // 报告的OSS访问URL
      reportPageId: null // 报告页面ID
    }
  },
  computed: {
    // 检查是否可以进入下一步
    canGoToNextStep() {
      // 检查需求名称是否填写
      if (!this.requirementName.trim()) {
        return false
      }
      
      // 检查实体关键词是否填写
      if (!this.entityKeyword.trim()) {
        return false
      }

      // 检查具体需求是否填写
      if (!this.specificRequirement.trim()) {
        return false
      }

      // 检查是否至少选择了一个关键词
      if (this.selectedKeywords.length === 0) {
        return false
      }

      return true
    },

    // 获取所有文章数据
    allArticles() {
      if (!this.analysisResults || !this.analysisResults.analysis_results) {
        return []
      }

      const articles = []
      const results = this.analysisResults.analysis_results

      // 添加联网搜索结果
      if (results.online_search && results.online_search.data && results.online_search.data.articles) {
        articles.push(...results.online_search.data.articles.map(article => ({
          ...article,
          sourceType: 'online_search'
        })))
      }

      // 添加自定义数据源结果
      if (results.custom_data_source && results.custom_data_source.data && results.custom_data_source.data.articles) {
        articles.push(...results.custom_data_source.data.articles.map(article => ({
          ...article,
          sourceType: 'custom_data_source'
        })))
      }

      return articles
    },

    // 获取所有唯一的数据源
    uniqueSources() {
      const sources = new Set()
      this.allArticles.forEach(article => {
        if (article.source) {
          sources.add(article.source)
        }
      })
      return Array.from(sources)
    },

    // 筛选后的文章列表
    filteredArticles() {
      let filtered = this.allArticles

      // 按搜索关键词筛选
      if (this.articleListState.searchKeyword) {
        const keyword = this.articleListState.searchKeyword.toLowerCase()
        filtered = filtered.filter(article =>
          (article.title && article.title.toLowerCase().includes(keyword)) ||
          (article.content && article.content.toLowerCase().includes(keyword))
        )
      }

      // 按来源筛选
      if (this.articleListState.selectedSource) {
        filtered = filtered.filter(article => article.source === this.articleListState.selectedSource)
      }

      // 按情感筛选
      if (this.articleListState.selectedSentiment) {
        filtered = filtered.filter(article => article.sentiment === this.articleListState.selectedSentiment)
      }

      return filtered
    },

    // 分页后的文章列表
    paginatedArticles() {
      const start = (this.articleListState.currentPage - 1) * this.articleListState.pageSize
      const end = start + this.articleListState.pageSize
      return this.filteredArticles.slice(start, end)
    },

    // 分页后的数据源列表
    paginatedDataSources() {
      const start = (this.dataSourceListState.current_page - 1) * this.dataSourceListState.page_size
      const end = start + this.dataSourceListState.page_size
      return this.dataSourceList.slice(start, end)
    },

    // 将关键词按分类分组
    groupedKeywords() {
      if (this.generatedKeywords.length === 0) {
        return {}
      }

      // 使用从API获取的分类，如果没有则使用默认分类
      const categories = this.keywordCategories.length > 0
        ? this.keywordCategories.map(cat => ({ name: cat.category_name, keywords: [] }))
        : [
            { name: '售后服务问题', keywords: [] },
            { name: '产品质量问题', keywords: [] },
            { name: '投诉处理结果', keywords: [] },
            { name: '消费者不满', keywords: [] },
            { name: '虚假宣传', keywords: [] }
          ]

      this.generatedKeywords.forEach(keyword => {
        let assigned = false

        categories.forEach(cat => {
          if (cat.name === '售后服务问题' && (keyword.includes('售后') || keyword.includes('服务') || keyword.includes('客服'))) {
            cat.keywords.push(keyword)
            assigned = true
          } else if (cat.name === '产品质量问题' && (keyword.includes('质量') || keyword.includes('爆炸') || keyword.includes('故障'))) {
            cat.keywords.push(keyword)
            assigned = true
          } else if (cat.name === '投诉处理结果' && (keyword.includes('投诉') || keyword.includes('处理') || keyword.includes('对解'))) {
            cat.keywords.push(keyword)
            assigned = true
          } else if (cat.name === '消费者不满' && (keyword.includes('不满') || keyword.includes('消费者'))) {
            cat.keywords.push(keyword)
            assigned = true
          } else if (cat.name === '虚假宣传' && (keyword.includes('宣传') || keyword.includes('充好'))) {
            cat.keywords.push(keyword)
            assigned = true
          }
        })

        if (!assigned) {
          // 如果没有匹配的分类，添加到第一个有关键词的分类或创建新分类
          if (categories[0].keywords.length === 0) {
            categories[0].keywords.push(keyword)
          } else {
            categories.find(cat => cat.keywords.length > 0).keywords.push(keyword)
          }
        }
      })

      // 只返回有关键词的分类
      const result = {}
      categories.forEach(cat => {
        if (cat.keywords.length > 0) {
          result[cat.name] = cat.keywords
        }
      })

      return result
    }
  },
  watch: {
    // 监听需求信息变化，重置保存状态
    requirementName() {
      this.resetRequirementSaveStatus()
    },
    entityKeyword() {
      this.resetRequirementSaveStatus()
    },
    specificRequirement() {
      this.resetRequirementSaveStatus()
    },
    // 监听筛选条件变化，重置分页到第一页
    'articleListState.searchKeyword'() {
      this.articleListState.currentPage = 1
    },
    'articleListState.selectedSource'() {
      this.articleListState.currentPage = 1
    },
    'articleListState.selectedSentiment'() {
      this.articleListState.currentPage = 1
    }
  },
  mounted() {
    // 页面初始化逻辑
    console.log('🚀 舆情分析页面已加载')
    this.debugLog('页面初始化', '舆情分析页面开始加载')

    // 显示调试信息
    console.log('🔧 调试模式已启用，可使用以下方法:')
    console.log('  - this.clearDebugLogs() : 清除调试日志')
    console.log('  - this.exportDebugLogs() : 导出调试日志')
    console.log('  - localStorage.getItem("opinion_analysis_debug_logs") : 查看调试日志')

    // 加载关键词分类
    this.loadKeywordCategories()

    // 注释掉页面加载时的定时任务列表加载，改为点击定时推送按钮时才加载
    // this.loadTimedTaskList()

    // 注释掉页面加载时的数据源列表加载，改为进入第二步或启用自定义数据源时才加载
    // this.loadDataSourceList()

    // 注释掉页面加载时的模板列表加载，改为点击模板按钮时才加载
    // this.loadTemplateList()

    // 检查URL参数，如果有报告参数则自动加载
    this.parseUrlParams()

    this.debugLog('页面初始化', '舆情分析页面加载完成')
  },

  beforeDestroy() {
    // 页面销毁前清理所有HTTP轮询
    this.stopAllHttpPolling()
    console.log('🧹 页面销毁，已清理所有HTTP轮询')
  },
  methods: {
    // ==================== HTTP轮询相关方法 ====================

    /**
     * 启动HTTP轮询
     * @param {string} pollingId - 轮询标识符，用于管理多个轮询任务
     * @param {Function} requestFunction - 执行HTTP请求的函数，应返回Promise
     * @param {Object} options - 轮询配置选项
     * @param {number} options.interval - 轮询间隔（毫秒），默认3000ms
     * @param {number} options.maxAttempts - 最大轮询次数，默认无限制
     * @param {Function} options.onSuccess - 成功回调函数
     * @param {Function} options.onError - 错误回调函数
     * @param {Function} options.shouldStop - 停止条件判断函数，返回true时停止轮询
     * @param {boolean} options.immediate - 是否立即执行第一次请求，默认true
     */
    startHttpPolling(pollingId, requestFunction, options = {}) {
      const config = {
        interval: 3000, // 默认3秒轮询一次
        maxAttempts: 0, // 0表示无限制
        onSuccess: null,
        onError: null,
        shouldStop: null,
        immediate: true,
        ...options
      }

      // 如果已存在相同ID的轮询，先停止它
      this.stopHttpPolling(pollingId)

      // 保存轮询配置
      this.pollingConfigs.set(pollingId, {
        ...config,
        requestFunction,
        attempts: 0,
        isRunning: true
      })

      console.log(`🔄 启动HTTP轮询: ${pollingId}`, config)

      // 轮询执行函数
      const executePolling = async () => {
        const pollingConfig = this.pollingConfigs.get(pollingId)
        if (!pollingConfig || !pollingConfig.isRunning) {
          return
        }

        try {
          pollingConfig.attempts++
          console.log(`📡 执行轮询请求: ${pollingId} (第${pollingConfig.attempts}次)`)

          // 执行HTTP请求
          const result = await requestFunction()

          // 执行成功回调
          if (config.onSuccess) {
            config.onSuccess(result, pollingConfig.attempts)
          }

          // 检查是否应该停止轮询
          if (config.shouldStop && config.shouldStop(result)) {
            console.log(`✅ 轮询停止条件满足: ${pollingId}`)
            this.stopHttpPolling(pollingId)
            return
          }

          // 检查是否达到最大尝试次数
          if (config.maxAttempts > 0 && pollingConfig.attempts >= config.maxAttempts) {
            console.log(`⏰ 轮询达到最大次数: ${pollingId}`)
            this.stopHttpPolling(pollingId)
            return
          }

          // 设置下次轮询
          if (pollingConfig.isRunning) {
            const timer = setTimeout(executePolling, config.interval)
            this.httpPollingTimers.set(pollingId, timer)
          }

        } catch (error) {
          console.error(`❌ 轮询请求失败: ${pollingId}`, error)

          // 执行错误回调
          if (config.onError) {
            const shouldContinue = config.onError(error, pollingConfig.attempts)
            if (shouldContinue === false) {
              console.log(`🛑 错误回调要求停止轮询: ${pollingId}`)
              this.stopHttpPolling(pollingId)
              return
            }
          }

          // 继续下次轮询（除非明确要求停止）
          if (pollingConfig.isRunning) {
            const timer = setTimeout(executePolling, config.interval)
            this.httpPollingTimers.set(pollingId, timer)
          }
        }
      }

      // 立即执行第一次请求或延迟执行
      if (config.immediate) {
        executePolling()
      } else {
        const timer = setTimeout(executePolling, config.interval)
        this.httpPollingTimers.set(pollingId, timer)
      }
    },

    /**
     * 停止指定的HTTP轮询
     * @param {string} pollingId - 轮询标识符
     */
    stopHttpPolling(pollingId) {
      // 清除定时器
      if (this.httpPollingTimers.has(pollingId)) {
        clearTimeout(this.httpPollingTimers.get(pollingId))
        this.httpPollingTimers.delete(pollingId)
      }

      // 标记轮询为停止状态
      if (this.pollingConfigs.has(pollingId)) {
        const config = this.pollingConfigs.get(pollingId)
        config.isRunning = false
        console.log(`🛑 停止HTTP轮询: ${pollingId} (共执行${config.attempts}次)`)
      }
    },

    /**
     * 停止所有HTTP轮询
     */
    stopAllHttpPolling() {
      console.log('🛑 停止所有HTTP轮询')
      for (const pollingId of this.httpPollingTimers.keys()) {
        this.stopHttpPolling(pollingId)
      }
      this.httpPollingTimers.clear()
      this.pollingConfigs.clear()
    },

    /**
     * 获取轮询状态信息
     * @param {string} pollingId - 轮询标识符
     * @returns {Object|null} 轮询状态信息
     */
    getPollingStatus(pollingId) {
      if (!this.pollingConfigs.has(pollingId)) {
        return null
      }

      const config = this.pollingConfigs.get(pollingId)
      return {
        pollingId,
        isRunning: config.isRunning,
        attempts: config.attempts,
        interval: config.interval,
        maxAttempts: config.maxAttempts
      }
    },

    /**
     * 获取所有轮询状态
     * @returns {Array} 所有轮询的状态信息
     */
    getAllPollingStatus() {
      const statusList = []
      for (const pollingId of this.pollingConfigs.keys()) {
        statusList.push(this.getPollingStatus(pollingId))
      }
      return statusList
    },

    // ==================== 轮询应用示例方法 ====================

    /**
     * 轮询分析进度（主要通信方式，替代WebSocket）
     */
    startAnalysisProgressPolling() {
      if (!this.currentTaskId) {
        console.warn('⚠️ 没有当前任务ID，无法启动进度轮询')
        return
      }

      console.log('🔄 启动分析进度HTTP轮询，任务ID:', this.currentTaskId)

      this.startHttpPolling('analysisProgress', async () => {
        // 调用获取分析进度的API
        const response = await getAnalysisProgress(this.currentTaskId)
        return response.data
      }, {
        interval: 1500, // 每1.5秒轮询一次，提高实时性
        immediate: true, // 立即执行第一次请求
        onSuccess: (data, attempts) => {
          console.log(`📊 分析进度更新 (第${attempts}次):`, data)

          // 进度条增长机制：每次轮询时自动增加1%（从当前进度开始）
          if (this.analysisProgress < 95) { // 限制在95%以下，为最终完成留出空间
            this.analysisProgress = Math.min(this.analysisProgress + 1, 95)
            console.log(`🔄 进度条自动递增至: ${this.analysisProgress}%`)
          }

          // 更新进度数据（如果API返回了具体进度，优先使用API数据）
          if (data.progress !== undefined && data.progress > this.analysisProgress) {
            this.analysisProgress = Math.min(data.progress, 95) // 同样限制在95%
            this.addLog('info', `分析进度: ${data.progress}%`)
          }

          if (data.status && data.status !== this.analysisStatus) {
            this.analysisStatus = data.status
            this.addLog('info', `状态变更: ${data.status}`)

            // 状态变更时的特殊处理
            if (data.status === 'completed') {
              // 轮询完成处理：立即将进度条跳转到100%
              this.analysisProgress = 100
              this.addLog('success', '✅ 分析任务已完成！')
              this.$message.success('分析完成！')
              // 立即停止轮询，避免继续无意义的请求
              this.stopHttpPolling('analysisProgress')
              this.addLog('info', '分析完成，已停止进度轮询')
            } else if (data.status === 'failed') {
              this.addLog('error', '❌ 分析任务失败')
              this.$message.error('分析失败，请重试')
              // 失败时也停止轮询
              this.stopHttpPolling('analysisProgress')
              this.addLog('info', '分析失败，已停止进度轮询')
            }
          }

          // 更新日志
          if (data.logs && Array.isArray(data.logs)) {
            // 只添加新的日志条目，并确保每个日志都有正确的level字段
            const newLogs = data.logs
              .filter(log => {
                // 过滤空日志：检查message是否为空或只包含空白字符
                if (!log.message || log.message.trim() === '') {
                  return false
                }
                // 过滤重复日志
                return !this.analysisLogs.some(existingLog =>
                  existingLog.timestamp === log.timestamp &&
                  existingLog.message === log.message
                )
              })
              .map(log => ({
                ...log,
                level: log.level || 'info', // 确保level字段存在
                message: log.message || '', // 确保message字段存在
                timestamp: log.timestamp || new Date().toISOString() // 确保timestamp字段存在
              }))
            this.analysisLogs.push(...newLogs)

            // 自动滚动到最新日志
            if (this.autoScroll && newLogs.length > 0) {
              this.$nextTick(() => {
                this.scrollToBottom()
              })
            }
          }

          // 更新分析结果数据
          if (data.analysis_results) {
            this.analysisResults = data
            this.updateReportData(data.analysis_results)
          }
        },
        onError: (error, attempts) => {
          console.error(`❌ 获取分析进度失败 (第${attempts}次):`, error)

          // 添加错误日志
          this.addLog('error', `获取进度失败: ${error.message || '网络错误'}`)

          // 根据错误类型和尝试次数决定是否继续
          if (error.response?.status === 404) {
            this.addLog('error', '任务不存在，停止轮询')
            this.$message.error('分析任务不存在')
            return false // 停止轮询
          }

          if (error.response?.status === 401) {
            this.addLog('error', '认证失败，请重新登录')
            this.$message.error('认证失败，请重新登录')
            return false // 停止轮询
          }

          // 如果连续失败8次，停止轮询
          if (attempts >= 8) {
            this.addLog('error', '连续获取进度失败，停止轮询')
            this.$message.error('获取分析进度失败，请刷新页面重试')
            this.analysisStatus = 'failed'
            return false // 停止轮询
          }

          return true // 继续轮询
        },
        shouldStop: (data) => {
          // 当分析完成或失败时停止轮询
          const shouldStop = data.status === 'completed' || data.status === 'failed'
          if (shouldStop) {
            console.log('🛑 分析进度轮询停止，最终状态:', data.status)

            // 轮询完成处理：无论当前进度是多少，立即将进度条跳转到100%
            if (data.status === 'completed') {
              this.analysisProgress = 100
              console.log('✅ 轮询结束，进度条已跳转到100%')
            }
          }
          return shouldStop
        }
      })
    },

    /**
     * 示例：轮询任务状态
     */
    startTaskStatusPolling(taskId) {
      this.startHttpPolling(`taskStatus_${taskId}`, async () => {
        // 这里调用获取任务状态的API
        const response = await getTimedTaskDetail(taskId)
        return response.data
      }, {
        interval: 5000, // 每5秒轮询一次
        maxAttempts: 60, // 最多轮询60次（5分钟）
        onSuccess: (data) => {
          console.log('任务状态更新:', data)
          // 更新任务状态到界面
        },
        shouldStop: (data) => {
          // 当任务完成时停止轮询
          return data.status === 'completed' || data.status === 'failed'
        }
      })
    },

    /**
     * 示例：轮询数据源状态
     */
    startDataSourcePolling() {
      this.startHttpPolling('dataSourceStatus', async () => {
        const response = await getDataSourceList()
        return response.data
      }, {
        interval: 10000, // 每10秒轮询一次
        onSuccess: (data) => {
          // 更新数据源列表
          if (data && Array.isArray(data.list)) {
            this.dataSourceList = data.list
          }
        },
        onError: (error, attempts) => {
          console.error('获取数据源列表失败:', error)
          if (attempts >= 3) {
            this.$message.warning('数据源状态更新失败，请手动刷新')
            return false // 停止轮询
          }
          return true // 继续轮询
        }
      })
    },

    /**
     * 示例：轮询报告生成状态
     */
    startReportGenerationPolling(reportId) {
      this.startHttpPolling(`reportGeneration_${reportId}`, async () => {
        // 这里应该调用获取报告生成状态的API
        const response = await getPublicReportData(reportId)
        return response.data
      }, {
        interval: 3000, // 每3秒轮询一次
        maxAttempts: 100, // 最多轮询100次（5分钟）
        onSuccess: (data) => {
          console.log('报告生成状态更新:', data)
          if (data.status === 'completed') {
            this.$message.success('报告生成完成！')
            // 更新报告数据
            this.reportData = data
          }
        },
        onError: (error, attempts) => {
          console.error('获取报告状态失败:', error)
          if (attempts >= 5) {
            this.$message.error('报告生成状态获取失败')
            return false
          }
          return true
        },
        shouldStop: (data) => {
          return data.status === 'completed' || data.status === 'failed'
        }
      })
    },

    /**
     * 通用轮询方法：轮询API直到满足条件
     * @param {string} apiFunction - API函数名
     * @param {Array} apiParams - API参数数组
     * @param {Function} successCondition - 成功条件判断函数
     * @param {Object} options - 轮询选项
     */
    pollUntilCondition(apiFunction, apiParams = [], successCondition, options = {}) {
      const pollingId = `generic_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

      this.startHttpPolling(pollingId, async () => {
        // 动态调用API函数
        const apiFunc = this[apiFunction] || window[apiFunction]
        if (typeof apiFunc === 'function') {
          return await apiFunc(...apiParams)
        } else {
          throw new Error(`API函数 ${apiFunction} 不存在`)
        }
      }, {
        interval: options.interval || 2000,
        maxAttempts: options.maxAttempts || 50,
        onSuccess: (data) => {
          if (options.onProgress) {
            options.onProgress(data)
          }
        },
        onError: (error, attempts) => {
          console.error(`轮询API ${apiFunction} 失败:`, error)
          if (options.onError) {
            return options.onError(error, attempts)
          }
          return attempts < 5 // 默认失败5次后停止
        },
        shouldStop: successCondition
      })

      return pollingId // 返回轮询ID，便于外部控制
    },

    // 处理测试步骤切换
    handleStepSwitch(step) {
      const stepNumber = parseInt(step)
      console.log(`🔄 测试切换到第${stepNumber}步`)

      // 直接切换步骤，不进行验证（测试用）
      this.currentStep = stepNumber

      // 如果切换到第二步（数据概览），加载数据源列表
      if (stepNumber === 2) {
        this.loadDataSourceList()
      }

      // 显示切换成功消息
      this.$message({
        message: `已切换到第${stepNumber}步：${this.getStepName(stepNumber)}`,
        type: 'success',
        duration: 2000
      })

      // 记录调试日志
      this.debugLog('步骤切换', `测试切换到第${stepNumber}步`, {
        fromStep: this.currentStep,
        toStep: stepNumber
      })
    },

    // 获取步骤名称
    getStepName(step) {
      const stepNames = {
        1: '舆情分析来源',
        2: '数据概览',
        3: '分析进度',
        4: '报告预览'
      }
      return stepNames[step] || '未知步骤'
    },

    // 跳转到分析记录页面
    goToAnalyzeRecord() {
      this.$router.push('/analyze-record')
    },

    // 处理模板按钮点击
    handleTemplateClick() {
      this.templateDialog.visible = true
      this.templateDialog.selectedTemplate = null
      // 每次打开模板弹窗时重新加载模板数据
      this.loadTemplateList()
      console.log('模板弹窗已打开')
    },

    // 加载模板列表
    async loadTemplateList() {
      try {
        const response = await getTemplatesForSelection()
        if (response.code === 200) {
          // 转换数据格式以适配现有的模板显示逻辑
          // API返回的是下划线格式，需要正确映射字段名
          this.templateList = response.data.map(template => ({
            id: template.id,
            name: template.template_name,
            entityKeyword: template.entity_keyword,
            requirement: template.specific_requirement,
            category: template.template_category,
            priority: template.priority,
            usageCount: template.usage_count
          }))
          console.log('✅ 模板列表加载成功:', this.templateList.length, '个模板')
          console.log('📋 模板数据:', this.templateList)
        } else {
          console.error('❌ 加载模板列表失败:', response.msg)
          this.$message.error('加载模板列表失败: ' + response.msg)
        }
      } catch (error) {
        console.error('❌ 加载模板列表异常:', error)
        this.$message.error('加载模板列表失败，请稍后重试')
      }
    },

    // 关闭模板弹窗
    closeTemplateDialog() {
      this.templateDialog.visible = false
      this.templateDialog.selectedTemplate = null
    },

    // 应用模板
    async applyTemplate(template) {
      if (!template) {
        this.$message.error('请选择一个模板')
        return
      }

      try {
        // 应用模板数据到表单
        this.requirementName = template.name
        this.entityKeyword = template.entityKeyword
        this.specificRequirement = template.requirement

        // 更新模板使用次数
        await updateTemplateUsage(template.id, 1)

        this.closeTemplateDialog()
        this.$message.success('模板已应用')

        console.log('✅ 模板应用成功:', template.name)
      } catch (error) {
        console.error('❌ 应用模板失败:', error)
        // 即使更新使用次数失败，也要应用模板
        this.closeTemplateDialog()
        this.$message.success('模板已应用')
      }
    },

    // 调试日志辅助方法
    debugLog(tag, message, data = null) {
      const timestamp = new Date().toISOString()
      const logMessage = `[${timestamp}] ${tag}: ${message}`
      console.log(logMessage)
      if (data) {
        console.log(`[${timestamp}] ${tag} - 数据:`, data)
      }

      // 可选：将日志保存到localStorage用于调试
      try {
        const logs = JSON.parse(localStorage.getItem('opinion_analysis_debug_logs') || '[]')
        logs.push({
          timestamp,
          tag,
          message,
          data: data ? JSON.stringify(data, null, 2) : null
        })
        // 只保留最近100条日志
        if (logs.length > 100) {
          logs.splice(0, logs.length - 100)
        }
        localStorage.setItem('opinion_analysis_debug_logs', JSON.stringify(logs))
      } catch (e) {
        console.warn('保存调试日志失败:', e)
      }
    },

    // 生成随机字符后缀，确保需求名称唯一性
    generateRandomSuffix() {
      // 生成时间戳（格式：YYYYMMDDHHMMSS）
      const now = new Date()
      const timestamp = now.getFullYear().toString() +
        (now.getMonth() + 1).toString().padStart(2, '0') +
        now.getDate().toString().padStart(2, '0') +
        now.getHours().toString().padStart(2, '0') +
        now.getMinutes().toString().padStart(2, '0') +
        now.getSeconds().toString().padStart(2, '0')

      // 生成随机字符串（6位字母数字组合）
      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
      let randomStr = ''
      for (let i = 0; i < 6; i++) {
        randomStr += chars.charAt(Math.floor(Math.random() * chars.length))
      }

      return `_${timestamp}_${randomStr}`
    },

    // 为需求名称添加随机后缀
    addRandomSuffixToRequirementName(originalName) {
      const suffix = this.generateRandomSuffix()
      const finalName = originalName + suffix

      console.log('🎯 [需求名称] 原始名称:', originalName)
      console.log('🎯 [需求名称] 生成后缀:', suffix)
      console.log('🎯 [需求名称] 最终名称:', finalName)

      return finalName
    },

    // 清除调试日志
    clearDebugLogs() {
      localStorage.removeItem('opinion_analysis_debug_logs')
      console.log('🧹 调试日志已清除')
    },

    // 导出调试日志
    exportDebugLogs() {
      try {
        const logs = JSON.parse(localStorage.getItem('opinion_analysis_debug_logs') || '[]')
        const logText = logs.map(log =>
          `[${log.timestamp}] ${log.tag}: ${log.message}${log.data ? '\n数据: ' + log.data : ''}`
        ).join('\n\n')

        const blob = new Blob([logText], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `opinion_analysis_debug_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
        a.click()
        URL.revokeObjectURL(url)

        console.log('📄 调试日志已导出')
      } catch (e) {
        console.error('导出调试日志失败:', e)
      }
    },

    // ==================== 分析进度相关方法 ====================

    // 获取分析状态文本
    getAnalysisStatusText() {
      const statusMap = {
        idle: '等待开始',
        running: '分析中',
        generating_report: '正在生成报告',
        completed: '已完成',
        failed: '分析失败'
      }

      // 特殊逻辑：如果进度条已达到100%但分析结果尚未完全生成，显示"正在生成报告"
      if (this.analysisStatus === 'running' && this.analysisProgress >= 100) {
        return '正在生成报告'
      }

      return statusMap[this.analysisStatus] || '未知状态'
    },

    // 格式化日志时间
    formatLogTime(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return date.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 清空日志
    clearLogs() {
      this.analysisLogs = []
      this.$message.success('日志已清空')
    },

    // 切换自动滚动
    toggleAutoScroll() {
      this.autoScroll = !this.autoScroll
      if (this.autoScroll) {
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    // 滚动到日志底部
    scrollToBottom() {
      if (this.$refs.logsContainer) {
        this.$refs.logsContainer.scrollTop = this.$refs.logsContainer.scrollHeight
      }
    },

    // 添加日志
    addLog(level, message) {
      const log = {
        timestamp: new Date().toISOString(),
        level: level || 'info', // 确保level不为空
        message: message || '' // 确保message不为空
      }
      this.analysisLogs.push(log)

      // 限制日志数量，避免内存溢出
      if (this.analysisLogs.length > 1000) {
        this.analysisLogs.splice(0, this.analysisLogs.length - 1000)
      }

      // 自动滚动到底部
      if (this.autoScroll) {
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    // 分析完成后自动生成报告并上传到OSS
    async generateAndUploadReportAfterAnalysis(analysisData) {
      try {
        this.addLog('info', '正在生成分析报告并上传到OSS...')

        // 确保有需求ID
        if (!this.currentRequirementId) {
          await this.ensureRequirementExists()
          if (!this.currentRequirementId) {
            throw new Error('无法获取需求ID，报告生成失败')
          }
        }

        // 准备报告数据
        const reportData = {
          requirement_id: this.currentRequirementId,
          requirement_name: this.requirementName,
          entity_keyword: this.entityKeyword,
          specific_requirement: this.specificRequirement,
          selected_keywords: this.selectedKeywords,
          analysis_results: analysisData.analysis_results || {},
          report_data: {
            totalArticles: this.reportData.totalArticles,
            totalKeywords: this.reportData.totalKeywords,
            dataSources: this.reportData.dataSources,
            sentiment: this.reportData.sentiment,
            onlineSearchCount: this.reportData.onlineSearchCount,
            customSourceCounts: this.reportData.customSourceCounts
          },
          enable_online_search: this.enableOnlineSearch,
          enable_custom_data_source: this.enableCustomDataSource,
          custom_data_sources: this.customDataSources
        }

        // 调用后端API生成报告并上传到OSS
        const response = await generateAndUploadReport(reportData)

        if (response.success && response.data) {
          const { report_oss_url, page_id } = response.data
          this.addLog('success', `报告已生成并上传到OSS: ${report_oss_url}`)

          // 保存OSS URL到当前组件状态，供后续使用
          this.reportOssUrl = report_oss_url
          this.reportPageId = page_id

          // 保存分析任务到数据库，包含OSS URL
          await this.saveTaskForPush('', 'immediate')

          return { success: true, oss_url: report_oss_url, page_id }
        } else {
          throw new Error(response.msg || '报告生成失败')
        }
      } catch (error) {
        console.error('生成报告并上传到OSS失败:', error)
        this.addLog('error', '报告生成失败: ' + error.message)
        this.$message.warning('报告生成失败，但仍可查看本地报告')
        return { success: false, error: error.message }
      }
    },

    // 跳转到报告预览
    async goToReportPreview() {
      try {
        // 在跳转到报告预览页面前，先保存分析任务到数据库
        this.addLog('info', '正在保存分析报告任务...')

        // 使用immediate类型保存分析报告任务，但不提供push_url
        const taskResult = await this.saveTaskForPush('', 'immediate')
        if (taskResult.success) {
          if (taskResult.exists) {
            this.addLog('info', '分析报告任务已存在')
          } else {
            this.addLog('success', '分析报告任务保存成功')
            this.$message.success('分析报告已保存')
          }
        } else {
          this.addLog('warning', '分析报告任务保存失败，但仍可查看报告')
          this.$message.warning('任务保存失败，但仍可查看报告')
        }

        // 跳转到报告预览页面
        this.currentStep = 4
      } catch (error) {
        console.error('保存分析报告任务失败:', error)
        this.addLog('error', '分析报告任务保存失败: ' + error.message)
        this.$message.warning('任务保存失败，但仍可查看报告')

        // 即使保存失败，仍然允许用户查看报告
        this.currentStep = 4
      }
    },

    // 重试分析
    retryAnalysis() {
      this.disconnectWebSocket()
      this.stopStatusPolling()
      this.analysisStatus = 'idle'
      this.analysisProgress = 0
      this.analysisLogs = []
      this.currentTaskId = null
      this.currentStep = 2
    },

    // 取消分析
    async cancelAnalysis() {
      try {
        if (this.currentTaskId) {
          await cancelAnalysisTask(this.currentTaskId)
          this.addLog('warning', '分析任务已取消')
        }

        // 停止HTTP轮询
        this.stopHttpPolling('analysisProgress')
        this.addLog('info', '已停止分析进度轮询')

        this.analysisStatus = 'idle'
        this.analysisProgress = 0
        this.currentTaskId = null
        this.$message.info('分析已取消')
        this.currentStep = 2
      } catch (error) {
        console.error('取消分析失败:', error)
        this.$message.error('取消分析失败')
      }
    },

    // ==================== WebSocket相关方法 ====================

    // 获取WebSocket连接URL
    getWebSocketUrl(taskId) {
      // 根据环境动态配置WebSocket地址
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'

      // 获取WebSocket主机地址
      let host
      if (process.env.VUE_APP_WS_HOST) {
        // 使用环境变量配置的主机地址
        host = process.env.VUE_APP_WS_HOST
      } else {
        // 回退到当前域名
        host = window.location.host
      }

      // 构建完整的WebSocket URL
      const wsUrl = `${protocol}//${host}/ws/analysis-progress/${taskId}`
      return wsUrl
    },

    // 连接WebSocket
    connectWebSocket(taskId) {
      if (this.websocket) {
        this.websocket.close()
      }

      // 动态构建WebSocket URL
      const wsUrl = this.getWebSocketUrl(taskId)
      console.log('连接WebSocket:', wsUrl)

      try {
        this.websocket = new WebSocket(wsUrl)

        this.websocket.onopen = () => {
          console.log('WebSocket连接已建立:', taskId)
          this.addLog('info', 'WebSocket连接已建立')

          // 重置重连计数
          this.reconnectAttempts = 0
          this.stopReconnect()

          // 启动心跳机制
          this.startHeartbeat()
        }

        this.websocket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.handleWebSocketMessage(data)
          } catch (error) {
            console.error('解析WebSocket消息失败:', error)
          }
        }

        this.websocket.onclose = (event) => {
          console.log('WebSocket连接已关闭', event.code, event.reason)
          this.websocket = null

          // 如果不是正常关闭且分析正在进行，尝试重连
          if (event.code !== 1000 && this.analysisStatus === 'running' && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnect(taskId)
          }
        }

        this.websocket.onerror = (error) => {
          console.error('WebSocket连接错误:', error)
          this.addLog('error', `WebSocket连接错误: ${error.message || '未知错误'}`)

          // 尝试重连
          if (this.analysisStatus === 'running' && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnect(taskId)
          }
        }

      } catch (error) {
        console.error('创建WebSocket连接失败:', error)
        this.addLog('error', '创建WebSocket连接失败: ' + error.message)
      }
    },

    // 断开WebSocket连接
    disconnectWebSocket() {
      this.stopHeartbeat()
      this.stopReconnect()
      if (this.websocket) {
        this.websocket.close()
        this.websocket = null
      }
      this.reconnectAttempts = 0
    },

    // 尝试重连
    attemptReconnect(taskId) {
      if (this.reconnectTimer) {
        return // 已经在重连中
      }

      this.reconnectAttempts++
      const delay = Math.min(1000 * Math.pow(1.5, this.reconnectAttempts - 1), 15000) // 指数退避，最大30秒

      this.addLog('warning', `WebSocket连接断开，${delay/1000}秒后尝试第${this.reconnectAttempts}次重连...`)

      this.reconnectTimer = setTimeout(() => {
        this.reconnectTimer = null
        console.log(`尝试第${this.reconnectAttempts}次重连...`)
        this.connectWebSocket(taskId)
      }, delay)
    },

    // 停止重连
    stopReconnect() {
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer)
        this.reconnectTimer = null
      }
    },

    // 启动心跳机制
    startHeartbeat() {
      this.stopHeartbeat()
      this.heartbeatTimer = setInterval(() => {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
          this.websocket.send(JSON.stringify({ type: 'ping' }))
        }
      }, 30000) // 每30秒发送一次心跳
    },

    // 停止心跳机制
    stopHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer)
        this.heartbeatTimer = null
      }
    },

    // 处理WebSocket消息
    handleWebSocketMessage(data) {
      console.log('收到WebSocket消息:', data)

      switch (data.type) {
        case 'connection_established':
          this.addLog('info', data.message)
          break

        case 'progress_update':
          this.handleProgressUpdate(data.data)
          break

        case 'log_message':
          this.handleLogMessage(data.data)
          break

        case 'task_status_change':
          this.handleTaskStatusChange(data.status, data.data)
          break

        case 'initial_progress':
          this.handleInitialProgress(data.data)
          break

        case 'pong':
          // 心跳响应，保持连接活跃
          console.log('收到心跳响应:', data.timestamp)
          break

        case 'error':
          this.addLog('error', data.message)
          break

        default:
          console.log('未知的WebSocket消息类型:', data.type)
      }
    },

    // 处理进度更新
    handleProgressUpdate(data) {
      if (data.progress_percentage !== undefined) {
        this.analysisProgress = data.progress_percentage
      }
    },

    // 处理日志消息
    handleLogMessage(data) {
      this.addLog(data.log_level, data.log_message)
    },

    // 处理任务状态变更
    handleTaskStatusChange(status, data) {
      this.analysisStatus = status

      if (status === 'completed') {
        this.analysisProgress = 100
        this.addLog('success', '分析任务已完成！')
        this.$message.success('分析完成！可以查看报告了')

        // 移除自动跳转逻辑，让用户手动点击"查看报告"按钮
        // setTimeout(() => {
        //   this.goToReportPreview()
        // }, 2000)
      } else if (status === 'failed') {
        this.addLog('error', '分析任务失败: ' + (data.error_message || '未知错误'))
        this.$message.error('分析失败，请重试')
      } else if (status === 'cancelled') {
        this.addLog('warning', '分析任务已取消')
        this.$message.info('分析已取消')
      }
    },

    // 处理初始进度
    handleInitialProgress(data) {
      if (data.task) {
        this.analysisStatus = data.task.task_status
        this.analysisProgress = data.task.progress_percentage || 0
      }

      if (data.logs && data.logs.length > 0) {
        this.analysisLogs = data.logs.map(log => ({
          timestamp: log.create_time,
          level: log.log_level,
          message: log.log_message
        }))
      }
    },

    // ==================== 状态轮询相关方法 ====================

    // 启动状态轮询
    startStatusPolling() {
      this.stopStatusPolling()
      this.statusPollingTimer = setInterval(async () => {
        if (this.currentTaskId && this.analysisStatus === 'running') {
          await this.checkTaskStatus()
        }
      }, 5000) // 每5秒检查一次状态
    },

    // 停止状态轮询
    stopStatusPolling() {
      if (this.statusPollingTimer) {
        clearInterval(this.statusPollingTimer)
        this.statusPollingTimer = null
      }
    },

    // 检查任务状态
    async checkTaskStatus() {
      try {
        const response = await getAnalysisProgress(this.currentTaskId)
        if (response.code === 200 && response.data) {
          const taskData = response.data

          // 更新进度
          if (taskData.progress_percentage !== undefined) {
            this.analysisProgress = taskData.progress_percentage
          }

          // 检查任务状态
          if (taskData.task_status === 'completed' && this.analysisStatus !== 'completed') {
            this.analysisStatus = 'completed'
            this.analysisProgress = 100
            this.addLog('success', '分析任务已完成！')
            this.$message.success('分析完成！可以查看报告了')

            // 停止轮询
            this.stopStatusPolling()

            // 移除自动跳转逻辑，让用户手动点击"查看报告"按钮
            // setTimeout(() => {
            //   this.goToReportPreview()
            // }, 2000)
          } else if (taskData.task_status === 'failed' && this.analysisStatus !== 'failed') {
            this.analysisStatus = 'failed'
            this.addLog('error', '分析任务失败')
            this.$message.error('分析失败，请重试')
            this.stopStatusPolling()
          }
        }
      } catch (error) {
        console.warn('检查任务状态失败:', error)
        // 不显示错误消息，避免干扰用户
      }
    },

    // ==================== 原有方法 ====================

    // 切换关键词选择状态
    toggleKeyword(keyword) {
      const index = this.selectedKeywords.indexOf(keyword)
      if (index > -1) {
        // 如果已选中，则取消选择
        this.selectedKeywords.splice(index, 1)
      } else {
        // 如果未选中，检查是否超过最大数量
        if (this.selectedKeywords.length < this.maxKeywords) {
          this.selectedKeywords.push(keyword)
        } else {
          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)
        }
      }
    },

    // 检查关键词是否已选中
    isKeywordSelected(keyword) {
      return this.selectedKeywords.includes(keyword)
    },

    // 切换分类选择状态
    toggleCategorySelection(categoryName, categoryKeywords) {
      // 检查该分类下的所有关键词是否都已选中
      const allSelected = categoryKeywords.every(keyword => this.isKeywordSelected(keyword))

      if (allSelected) {
        // 如果都已选中，则取消选择该分类下的所有关键词
        categoryKeywords.forEach(keyword => {
          const index = this.selectedKeywords.indexOf(keyword)
          if (index > -1) {
            this.selectedKeywords.splice(index, 1)
          }
        })
        this.$message.info(`已取消选择"${categoryName}"分类下的所有关键词`)
      } else {
        // 如果没有全部选中，则选择该分类下的所有关键词
        categoryKeywords.forEach(keyword => {
          if (!this.isKeywordSelected(keyword) && this.selectedKeywords.length < this.maxKeywords) {
            this.selectedKeywords.push(keyword)
          }
        })

        // 检查是否因为数量限制而无法全部选择
        const notSelected = categoryKeywords.filter(keyword => !this.isKeywordSelected(keyword))
        if (notSelected.length > 0) {
          this.$message.warning(`由于数量限制，无法选择"${categoryName}"分类下的所有关键词`)
        } else {
          this.$message.success(`已选择"${categoryName}"分类下的所有关键词`)
        }
      }
    },


    // 前往下一步
    goToNextStep() {
      // 显示验证样式
      this.showValidation = true

      // 验证表单是否填写完整
      if (!this.entityKeyword.trim()) {
        this.$message.warning('请填写实体关键词')
        return
      }

      if (!this.specificRequirement.trim()) {
        this.$message.warning('请填写具体需求')
        return
      }

      if (this.selectedKeywords.length === 0) {
        this.$message.warning('请至少选择一个关键词')
        return
      }

      // 验证通过，隐藏验证样式并进入下一步
      this.showValidation = false
      if (this.currentStep < 3) {
        this.currentStep++

        // 如果进入第二步（数据概览），加载数据源列表
        if (this.currentStep === 2) {
          this.loadDataSourceList()
        }
      }
    },

    // 返回上一步
    goToPreviousStep() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },

    // 检查套餐次数限制
    async checkPackageLimits() {
      try {
        // 获取当前用户ID
        const currentUserId = this.$store.getters.id || this.$store.state.user.id

        if (!currentUserId) {
          this.$message.error('无法获取用户信息，请重新登录')
          return { canProceed: false }
        }

        // 获取用户统计数据
        const response = await getDashboardStatistics(currentUserId)

        if (response.code !== 200) {
          this.$message.error('获取套餐信息失败，请稍后重试')
          return { canProceed: false }
        }

        const userStats = response.data
        const packageLimit = userStats.package_limit || 0
        const remainingCount = userStats.remaining_count || 0
        const totalAnalysis = userStats.total_analysis || 0

        // 检查是否为无限制套餐
        const isUnlimitedPackage = packageLimit === -1

        if (isUnlimitedPackage) {
          console.log('用户拥有无限制套餐，允许继续操作')
          return { canProceed: true }
        }

        // 检查是否没有剩余次数
        if (remainingCount <= 0) {
          this.$confirm(
            '您的套餐次数已用完，无法生成关键词。请升级套餐后继续使用。',
            '套餐次数不足',
            {
              confirmButtonText: '升级套餐',
              cancelButtonText: '取消',
              type: 'warning',
              showClose: false
            }
          ).then(() => {
            this.upgradePackage()
          }).catch(() => {
            // 用户取消，不做任何操作
          })
          return { canProceed: false }
        }

        // 检查是否剩余次数较少（少于5次或少于总数的20%）
        const isLowRemaining = remainingCount <= 5 ||
                              (packageLimit > 0 && (remainingCount / packageLimit) <= 0.2)

        if (isLowRemaining) {
          try {
            await this.$confirm(
              `您的套餐剩余次数较少（${remainingCount}次），是否继续生成关键词？`,
              '次数提醒',
              {
                confirmButtonText: '继续生成',
                cancelButtonText: '升级套餐',
                type: 'warning'
              }
            )
            // 用户选择继续，允许操作
            return { canProceed: true }
          } catch {
            // 用户选择升级套餐
            this.upgradePackage()
            return { canProceed: false }
          }
        }

        // 次数充足，允许继续
        console.log(`套餐检查通过，剩余次数: ${remainingCount}`)
        return { canProceed: true }

      } catch (error) {
        console.error('套餐检查失败:', error)
        this.$message.error('套餐检查失败，请稍后重试')
        return { canProceed: false }
      }
    },

    // 升级套餐方法
    upgradePackage() {
      // 跳转到套餐升级页面
      this.$router.push('/set-meal/set-meal')
    },

    // 扣减套餐使用次数
    async deductPackageUsage(operationType = '关键词生成') {
      try {
        console.log(`开始扣减套餐次数，操作类型: ${operationType}`)

        // 这里可以调用后端API来扣减次数
        // 目前先通过创建一个opinion_requirement记录来实现扣减
        // 因为后端统计逻辑是基于opinion_requirement表的记录数

        // 注意：实际的扣减逻辑应该在后端的关键词生成API中处理
        // 这里只是一个前端的提示，真正的扣减应该在后端API调用成功后自动进行

        console.log(`套餐次数扣减完成，操作类型: ${operationType}`)

      } catch (error) {
        console.error('扣减套餐次数失败:', error)
        // 扣减失败不影响用户体验，只记录日志
      }
    },

    // 开始分析
    async startAnalysis() {
      try {
        // 首先检查套餐次数限制
        const packageCheckResult = await this.checkPackageLimits()
        if (!packageCheckResult.canProceed) {
          return // 如果不能继续，直接返回
        }

        // 验证基本表单信息
        if (!this.requirementName.trim()) {
          this.$message.error('请填写需求名称')
          return
        }

        if (!this.entityKeyword.trim()) {
          this.$message.error('请填写实体关键词')
          return
        }

        if (!this.specificRequirement.trim()) {
          this.$message.error('请填写具体需求')
          return
        }

        if (this.selectedKeywords.length === 0) {
          this.$message.error('请至少选择一个关键词')
          return
        }

        // 验证至少启用联网搜索（自定义数据源为可选）
        if (!this.enableOnlineSearch) {
          // 如果没有启用联网搜索，但启用了自定义数据源且有配置的数据源，则允许继续
          if (this.enableCustomDataSource && this.customDataSources.length > 0) {
            // 允许继续，使用自定义数据源
          } else {
            this.$message.error('请至少启用联网搜索或配置自定义数据源')
            return
          }
        }

        // 如果选择了自定义数据源，检查是否有配置的数据源（仅警告，不阻止）
        if (this.enableCustomDataSource && this.customDataSources.length === 0) {
          this.$message.warning('未配置自定义数据源，将仅使用联网搜索')
          // 不return，允许继续执行
        }

        // 立即跳转到第三步分析进度页面
        this.currentStep = 3
        this.analysisStatus = 'running'
        this.analysisProgress = 0 // 重置进度条为0%
        this.analysisLogs = []

        // 添加初始日志
        this.addLog('info', '开始启动AI分析引擎...')
        this.addLog('info', '正在验证分析参数...')
        this.addLog('info', '参数验证通过，开始分析任务...')

        // 如果没有需求ID，先尝试创建需求
        if (!this.currentRequirementId) {
          this.addLog('info', '正在创建临时需求记录...')
          await this.createTemporaryRequirement()
        }

        // 首先创建分析任务
        const taskData = {
          requirement_id: this.currentRequirementId || 0, // 如果仍然没有ID，使用0作为临时值
          user_id: 1, // 默认用户ID
          task_name: `${this.requirementName} - 分析任务`,
          analysis_config: {
            entity_keyword: this.entityKeyword,
            specific_requirement: this.specificRequirement,
            selected_keywords: this.selectedKeywords,
            enable_online_search: this.enableOnlineSearch,
            enable_custom_data_source: this.enableCustomDataSource,
            custom_data_sources: this.customDataSources.map(url => ({
              url: url,
              name: this.extractDomainName(url)
            }))
          }
        }

        this.addLog('info', '正在创建分析任务...')

        // 创建分析任务
        const taskResponse = await createAnalysisProgressTask(taskData)

        if (taskResponse.code === 200) {
          this.currentTaskId = taskResponse.data.task_id
          this.addLog('info', `分析任务已创建，任务ID: ${this.currentTaskId}`)

          // 使用HTTP轮询监控分析进度（替代WebSocket）
          this.startAnalysisProgressPolling()

          // 添加日志提示使用HTTP轮询
          this.addLog('info', '已启动HTTP轮询监控分析进度...')

          // 异步执行分析
          this.performAsyncAnalysis()
        } else {
          throw new Error(taskResponse.msg || '创建分析任务失败')
        }

      } catch (error) {
        console.error('分析启动失败:', error)
        this.analysisStatus = 'failed'
        this.addLog('error', '分析启动失败: ' + error.message)
        this.$message.error('分析启动失败，请稍后重试')
      }
    },

    // 创建临时需求记录
    async createTemporaryRequirement() {
      try {
        // 为需求名称添加随机后缀以确保唯一性
        const originalRequirementName = this.requirementName
        const finalRequirementName = this.addRandomSuffixToRequirementName(originalRequirementName)

        const requirementData = {
          requirement_name: finalRequirementName,
          entity_keyword: this.entityKeyword,
          specific_requirement: this.specificRequirement,
          priority: 'medium'
        }

        this.addLog('info', '正在保存需求信息...')
        console.log('🚀 [临时需求] 原始需求名称:', originalRequirementName)
        console.log('🚀 [临时需求] 最终需求名称:', finalRequirementName)

        const response = await createRequirement(requirementData)

        if (response.success) {
          // 更新界面显示的需求名称
          this.requirementName = finalRequirementName
          this.currentRequirementId = response.data.id
          this.requirementSaved = true
          this.addLog('info', `需求信息保存成功，最终名称：${finalRequirementName}`)
        } else {
          // 如果创建失败（比如名称重复），尝试查找已存在的需求
          if (response.msg && response.msg.includes('已存在')) {
            this.addLog('info', '需求已存在，正在查找已有记录...')
            await this.findExistingRequirementId()
            if (this.currentRequirementId) {
              this.addLog('info', '找到已存在的需求记录')
            }
          } else {
            this.addLog('warning', '需求保存失败，将使用临时数据进行分析')
          }
        }
      } catch (error) {
        console.warn('创建临时需求失败:', error)
        this.addLog('warning', '需求保存失败，将使用临时数据进行分析')
        // 即使创建需求失败，也继续分析流程
      }
    },

    // 执行异步分析
    async performAsyncAnalysis() {
      try {
        this.addLog('info', '正在连接AI分析引擎...')

        // 构建原有的分析请求参数
        const analysisData = {
          requirement_id: this.currentRequirementId || 0, // 如果没有ID，使用0作为临时值
          entity_keyword: this.entityKeyword,
          specific_requirement: this.specificRequirement,
          selected_keywords: this.selectedKeywords,
          enable_online_search: this.enableOnlineSearch,
          enable_custom_data_source: this.enableCustomDataSource,
          custom_data_sources: this.customDataSources.map(url => ({
            url: url,
            name: this.extractDomainName(url)
          }))
        }

        // 调用原有的分析接口
        const response = await startAnalysis(analysisData)

        if (response.code === 200) {
          this.addLog('success', '分析引擎启动成功！')

          // 更新报告数据
          this.updateReportData(response.data)
          this.showAnalysisResults(response.data)

          // 完成分析任务
          if (this.currentTaskId) {
            await completeAnalysisTask(this.currentTaskId, {
              total_articles: response.data.analysis_results ?
                Object.values(response.data.analysis_results).reduce((total, result) => {
                  return total + (result.data?.articles?.length || 0)
                }, 0) : 0,
              analysis_results: response.data.analysis_results
            })
          }

          // 手动触发分析完成状态更新
          this.analysisStatus = 'completed'
          this.analysisProgress = 100
          this.addLog('success', '分析任务已完成！')

          // 自动生成报告并上传到OSS
          await this.generateAndUploadReportAfterAnalysis(response.data)

          this.$message.success('分析完成！报告已生成并上传')

          // 显式停止分析进度轮询
          this.stopHttpPolling('analysisProgress')
          this.addLog('info', '报告生成完成，已停止轮询')

          // 移除自动跳转逻辑，让用户手动点击"查看报告"按钮
          // setTimeout(() => {
          //   this.goToReportPreview()
          // }, 2000)

        } else {
          this.analysisStatus = 'failed'
          this.addLog('error', '分析启动失败: ' + response.msg)

          // 标记任务失败
          if (this.currentTaskId) {
            await this.failCurrentTask(response.msg)
          }
        }

      } catch (error) {
        this.analysisStatus = 'failed'
        this.addLog('error', '分析过程中发生错误: ' + error.message)

        // 标记任务失败
        if (this.currentTaskId) {
          await this.failCurrentTask(error.message)
        }
      }
    },

    // 标记当前任务失败
    async failCurrentTask(errorMessage) {
      try {
        // 这里应该调用失败接口，但当前后端没有提供，所以使用取消接口
        await cancelAnalysisTask(this.currentTaskId)
      } catch (error) {
        console.error('标记任务失败时出错:', error)
      }
    },

    // 启动进度模拟（实际项目中应该通过WebSocket接收真实进度）
    startProgressSimulation() {
      const updateProgress = () => {
        if (this.analysisStatus === 'running' && this.analysisProgress < 90) {
          this.analysisProgress += Math.random() * 10
          if (this.analysisProgress > 90) {
            this.analysisProgress = 90
          }
          setTimeout(updateProgress, 1000 + Math.random() * 2000)
        }
      }
      updateProgress()
    },

    // 显示分析结果
    showAnalysisResults(results) {
      console.log('分析结果:', results)

      // 可以在这里添加结果展示逻辑
      // 比如跳转到结果页面或显示结果弹窗
      this.$notify({
        title: '分析完成',
        message: `分析任务已完成，需求ID: ${results.requirement_id}`,
        type: 'success',
        duration: 5000
      })
    },

    // 处理联网搜索结果
    handleOnlineSearchResults(searchResults) {
      console.log('联网搜索结果:', searchResults)

      if (searchResults.success && searchResults.data) {
        const data = searchResults.data

        // 显示搜索统计信息
        let message = '联网搜索完成！\n'
        if (data.articles && data.articles.length > 0) {
          message += `获取到 ${data.articles.length} 条相关信息\n`
        }
        if (searchResults.saved_count !== undefined) {
          message += `已保存 ${searchResults.saved_count} 条数据到数据库\n`
        }
        if (data.sentiment_analysis) {
          const sentiment = data.sentiment_analysis
          message += `情感分析：正面 ${sentiment.positive}%，中性 ${sentiment.neutral}%，负面 ${sentiment.negative}%`
        }

        this.$notify({
          title: '联网搜索结果',
          message: message,
          type: 'info',
          duration: 8000
        })
      }
    },

    // 切换联网搜索
    toggleOnlineSearch() {
      this.enableOnlineSearch = !this.enableOnlineSearch
    },

    // 切换自定义数据源搜索
    toggleCustomDataSource() {
      this.enableCustomDataSource = !this.enableCustomDataSource

      // 如果启用了自定义数据源，加载数据源列表
      if (this.enableCustomDataSource) {
        this.loadDataSourceList()
      }
    },

    // 显示新增数据源表单
    showAddSourceForm() {
      this.showAddSourceInput = true
      this.newSourceUrl = ''
    },

    // 隐藏新增数据源表单
    hideAddSourceForm() {
      this.showAddSourceInput = false
      this.newSourceUrl = ''
    },

    // 确认新增数据源
    async confirmAddSource() {
      if (!this.newSourceUrl.trim()) {
        this.$message.warning('请输入数据源网址')
        return
      }

      // 简单的URL格式验证
      const urlPattern = /^https?:\/\/.+/
      if (!urlPattern.test(this.newSourceUrl.trim())) {
        this.$message.warning('请输入有效的网址格式')
        return
      }

      // 检查是否已存在相同的数据源
      const trimmedUrl = this.newSourceUrl.trim()
      if (this.customDataSources.includes(trimmedUrl)) {
        this.$message.warning('该数据源已存在')
        return
      }

      // 检查是否有当前需求ID
      if (!this.currentRequirementId) {
        this.$message.warning('请先保存需求信息')
        return
      }

      try {
        // 调用API保存数据源到数据库
        const dataSourceData = {
          requirement_id: this.currentRequirementId,
          source_type: 'custom',
          source_name: this.extractDomainName(trimmedUrl),
          source_url: trimmedUrl,
          remark: '用户自定义数据源'
        }

        const response = await createDataSource(dataSourceData)

        if (response.success) {
          // 将新的数据源添加到自定义数据源列表中
          this.customDataSources.push(trimmedUrl)
          // 自动启用自定义数据源搜索
          this.enableCustomDataSource = true

          this.$message.success('数据源添加成功')
          // 清空输入框，但保持表单显示，允许继续添加
          this.newSourceUrl = ''
        } else {
          this.$message.error('数据源添加失败：' + response.msg)
        }
      } catch (error) {
        console.error('添加数据源失败:', error)
        this.$message.error('数据源添加失败，请稍后重试')
      }
    },

    // 从URL中提取域名作为数据源名称
    extractDomainName(url) {
      try {
        const urlObj = new URL(url)
        return urlObj.hostname.replace('www.', '')
      } catch (error) {
        // 如果URL解析失败，返回原始URL
        return url
      }
    },

    // 删除自定义数据源
    removeCustomSource(index) {
      // 从自定义数据源列表中移除
      this.customDataSources.splice(index, 1)

      // 如果没有自定义数据源了，自动关闭自定义数据源搜索
      if (this.customDataSources.length === 0) {
        this.enableCustomDataSource = false
      }

      this.$message.success('数据源删除成功')
    },

    // 刷新数据源列表
    async refreshDataSourceList() {
      try {
        this.dataSourceListState.loading = true
        await this.loadDataSourceList()
        this.$message.success('数据源列表刷新成功')
      } catch (error) {
        console.error('刷新数据源列表失败:', error)
        this.$message.error('刷新数据源列表失败')
      } finally {
        this.dataSourceListState.loading = false
      }
    },

    // 加载数据源列表
    async loadDataSourceList() {
      try {
        this.dataSourceListState.loading = true

        const response = await getDataSourceList({
          page_num: 1,
          page_size: 1000 // 获取所有数据，前端分页
        })

        if (response.code === 200) {
          // 处理分页数据结构 - records字段在响应根级别
          if (response.records) {
            this.dataSourceList = response.records
          } else if (response.data && response.data.records) {
            this.dataSourceList = response.data.records
          } else if (response.data && response.data.rows) {
            this.dataSourceList = response.data.rows
          } else if (response.data && Array.isArray(response.data)) {
            this.dataSourceList = response.data
          } else {
            this.dataSourceList = []
          }
        } else {
          this.dataSourceList = []
        }

        console.log('加载数据源列表成功:', this.dataSourceList)
        console.log('分页数据源列表:', this.paginatedDataSources)
      } catch (error) {
        console.error('加载数据源列表失败:', error)
        this.dataSourceList = []
        this.$message.error('加载数据源列表失败: ' + error.message)
      } finally {
        this.dataSourceListState.loading = false
      }
    },

    // 处理数据源分页变化
    handleDataSourcePageChange(page) {
      this.dataSourceListState.current_page = page
    },

    // 处理数据源每页大小变化
    handleDataSourceSizeChange(size) {
      this.dataSourceListState.page_size = size
      this.dataSourceListState.current_page = 1
    },

    // 处理数据源选择变化
    handleDataSourceSelectionChange(selection) {
      this.selectedDataSources = selection
      console.log('选中的数据源:', selection)
    },

    // 删除数据源
    async deleteDataSource(row) {
      try {
        await this.$confirm(`确定要删除数据源 "${row.sourceUrl}" 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用删除API - 使用导入的API方法
        const response = await deleteDataSourceAPI(row.id)

        if (response.code === 200) {
          this.$message.success('删除成功')
          // 重新加载数据源列表
          await this.loadDataSourceList()
        } else {
          this.$message.error(response.msg || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除数据源失败:', error)
          this.$message.error('删除失败: ' + (error.message || '未知错误'))
        }
      }
    },



    // 生成关联词
    async generateRelatedWords() {
      // 首先检查套餐次数限制
      const packageCheckResult = await this.checkPackageLimits()
      if (!packageCheckResult.canProceed) {
        return // 如果不能继续，直接返回
      }

      // 检查是否填写了实体关键词和具体需求
      if (!this.entityKeyword.trim()) {
        this.$message.warning('请先填写实体关键词')
        return
      }

      if (!this.specificRequirement.trim()) {
        this.$message.warning('请先填写具体需求')
        return
      }

      if (!this.requirementName.trim()) {
        this.$message.warning('请先填写需求名称')
        return
      }

      // 先尝试保存或更新需求到数据库
      this.$message.info('正在检查需求并生成关联词...')

      try {
        let response

        // 检查是否需要更新已存在的需求
        if (this.currentRequirementId && this.requirementModified) {
          console.log('🔄 [生成关联词] 更新已存在的需求，ID:', this.currentRequirementId)

          const updateData = {
            entity_keyword: this.entityKeyword,
            specific_requirement: this.specificRequirement,
            priority: 'medium'
          }

          response = await updateRequirement(this.currentRequirementId, updateData)

          if (response.success) {
            this.requirementModified = false
            console.log('✅ [生成关联词] 需求更新成功')
          }
        } else if (!this.currentRequirementId) {
          // 创建新需求
          console.log('🚀 [生成关联词] 创建新需求')

          // 为需求名称添加随机后缀以确保唯一性
          const originalRequirementName = this.requirementName
          const finalRequirementName = this.addRandomSuffixToRequirementName(originalRequirementName)

          const requirementData = {
            requirement_name: finalRequirementName,
            entity_keyword: this.entityKeyword,
            specific_requirement: this.specificRequirement,
            priority: 'medium'
          }

          console.log('📝 [生成关联词] 原始需求名称:', originalRequirementName)
          console.log('📝 [生成关联词] 最终需求名称:', finalRequirementName)
          console.log('📝 [生成关联词] 请求数据:', requirementData)

          response = await createRequirement(requirementData)

          if (response.success) {
            // 更新界面显示的需求名称为最终保存的名称
            this.requirementName = finalRequirementName
            this.currentRequirementId = response.data.id
            this.requirementSaved = true
            this.requirementModified = false
            console.log('🆔 [生成关联词] 保存的需求ID:', this.currentRequirementId)
          }
        } else {
          // 需求已存在且未修改，直接使用
          console.log('✅ [生成关联词] 使用已存在的需求，ID:', this.currentRequirementId)
          response = { success: true }
        }

        console.log('📥 [生成关联词] API响应:', response)
        console.log('📊 [生成关联词] 响应类型:', typeof response)
        console.log('✅ [生成关联词] 响应成功状态:', response?.success)
        console.log('💬 [生成关联词] 响应消息:', response?.msg)

        if (response.success) {
          console.log('✅ [生成关联词] 需求操作成功')

          if (this.currentRequirementId && this.requirementSaved) {
            this.$message.success('需求信息已更新')
          } else {
            this.$message.success(`需求保存成功，最终名称：${this.requirementName}`)
          }

          console.log('🆔 [生成关联词] 当前需求ID:', this.currentRequirementId)
          console.log('🎯 [生成关联词] 界面需求名称:', this.requirementName)
        } else {
          console.log('❌ [生成关联词] 需求创建失败，开始检查错误类型')
          console.log('🔍 [生成关联词] 错误消息:', response.msg)

          // 检查是否是需求名称重复的错误
          const isDuplicateError = response.msg && response.msg.includes('已存在')
          console.log('🔄 [生成关联词] 是否为重复需求错误:', isDuplicateError)

          if (isDuplicateError) {
            console.log('🎯 [生成关联词] 检测到重复需求，跳过保存直接生成关联词')
            // 需求已存在，给用户提示并尝试获取已存在需求的ID
            this.$message.info('需求已存在，直接生成关联词')
            this.requirementSaved = true

            // 尝试通过需求名称查找已存在的需求ID
            try {
              console.log('🔍 [生成关联词] 开始查找已存在需求的ID')
              await this.findExistingRequirementId()
              console.log('✅ [生成关联词] 成功找到已存在需求ID:', this.currentRequirementId)
            } catch (findError) {
              console.warn('⚠️ [生成关联词] 查找已存在需求ID失败:', findError)
              // 即使查找失败，也继续生成关联词
            }
          } else {
            console.log('💥 [生成关联词] 其他类型错误，停止执行')
            this.$message.error('需求保存失败：' + response.msg)
            return
          }
        }
      } catch (error) {
        console.log('💥 [生成关联词] 捕获到异常，开始详细分析')
        console.error('🔍 [生成关联词] 错误对象:', error)
        console.log('📝 [生成关联词] 错误消息:', error.message)
        console.log('🌐 [生成关联词] 错误响应:', error.response)
        console.log('📊 [生成关联词] 错误状态码:', error.response?.status)
        console.log('💬 [生成关联词] 错误响应数据:', error.response?.data)

        // 检查是否是需求名称重复的错误（可能被axios拦截器处理）
        // 检查多种可能的错误消息格式
        const errorMessage = error.message || ''
        const responseMsg = error.response?.data?.msg || ''

        console.log('🔍 [生成关联词] 分析错误消息:')
        console.log('  - error.message:', errorMessage)
        console.log('  - response.data.msg:', responseMsg)

        // 多重检测逻辑
        const isExistError = errorMessage.includes('已存在') ||
                           errorMessage.includes('already exists') ||
                           responseMsg.includes('已存在') ||
                           responseMsg.includes('already exists')

        // 特殊情况：错误消息为空但可能是重复需求错误
        // 基于以下线索判断：1. HTTP状态码500 2. 错误消息包含"创建舆情需求失败"
        const isPossibleDuplicateError = !isExistError &&
                                       (errorMessage.includes('创建舆情需求失败') ||
                                        responseMsg.includes('创建舆情需求失败')) &&
                                       (error.response?.status === 500 || errorMessage.includes('500'))

        console.log('🎯 [生成关联词] 错误类型判断:')
        console.log('  - 明确的重复错误:', isExistError)
        console.log('  - 可能的重复错误:', isPossibleDuplicateError)

        if (isExistError || isPossibleDuplicateError) {
          const errorType = isExistError ? '明确检测到重复需求' : '推测为重复需求错误'
          console.log(`🎯 [生成关联词] ${errorType}，跳过保存直接生成关联词`)

          // 需求已存在，给用户提示并尝试获取已存在需求的ID
          this.$message.info('需求已存在，直接生成关联词')
          this.requirementSaved = true

          // 尝试通过需求名称查找已存在的需求ID
          try {
            console.log('🔍 [生成关联词] 开始查找已存在需求的ID')
            await this.findExistingRequirementId()
            console.log('✅ [生成关联词] 成功找到已存在需求ID:', this.currentRequirementId)
          } catch (findError) {
            console.warn('⚠️ [生成关联词] 查找已存在需求ID失败:', findError)
            // 即使查找失败，也继续生成关联词
          }
        } else {
          console.log('💥 [生成关联词] 其他类型错误，停止执行')
          this.$message.error('需求保存失败，请重试')
          return
        }
      }

      // 保存成功或需求已存在后，生成关联词
      console.log('🎯 [生成关联词] 开始执行关联词生成')
      console.log('📊 [生成关联词] 当前状态:')
      console.log('  - requirementSaved:', this.requirementSaved)
      console.log('  - currentRequirementId:', this.currentRequirementId)
      console.log('  - requirementName:', this.requirementName)

      this.generateKeywordsOnly()
    },

    // 查找已存在需求的ID
    async findExistingRequirementId() {
      console.log('🔍 [查找需求ID] 开始查找已存在需求')
      console.log('📝 [查找需求ID] 查找条件 - 需求名称:', this.requirementName)

      try {
        const queryParams = {
          page: 1,
          size: 100,
          requirement_name: this.requirementName
        }

        console.log('🚀 [查找需求ID] 调用需求列表API，参数:', queryParams)
        const response = await getRequirementList(queryParams)

        console.log('📥 [查找需求ID] API响应:', response)
        console.log('✅ [查找需求ID] 响应成功状态:', response?.success)
        console.log('📊 [查找需求ID] 数据行数:', response?.data?.rows?.length)

        if (response.success && response.data && response.data.rows) {
          console.log('🔍 [查找需求ID] 在返回的需求列表中查找匹配项')
          console.log('📋 [查找需求ID] 需求列表:', response.data.rows.map(req => ({
            id: req.id,
            name: req.requirementName
          })))

          const existingRequirement = response.data.rows.find(
            req => req.requirementName === this.requirementName
          )

          if (existingRequirement) {
            this.currentRequirementId = existingRequirement.id
            console.log('✅ [查找需求ID] 找到匹配的需求:', existingRequirement)
            console.log('🆔 [查找需求ID] 设置需求ID:', this.currentRequirementId)
          } else {
            console.log('❌ [查找需求ID] 未找到匹配的需求')
          }
        } else {
          console.log('❌ [查找需求ID] API响应格式异常或无数据')
        }
      } catch (error) {
        console.error('💥 [查找需求ID] 查找失败:', error)
        throw error
      }
    },

    // 重新生成关联词（不保存到数据库）
    regenerateKeywords() {
      // 检查是否填写了实体关键词和具体需求
      if (!this.entityKeyword.trim()) {
        this.$message.warning('请先填写实体关键词')
        return
      }

      if (!this.specificRequirement.trim()) {
        this.$message.warning('请先填写具体需求')
        return
      }

      // 只生成关联词，不保存需求到数据库
      this.generateKeywordsOnly()
    },

    // 纯生成关联词方法（使用AI接口）
    async generateKeywordsOnly() {
      // 验证必要参数
      if (!this.specificRequirement.trim()) {
        this.$message.warning('请先填写具体需求内容')
        return
      }

      if (this.specificRequirement.trim().length < 5) {
        this.$message.warning('需求内容过短，请提供更详细的需求描述')
        return
      }

      // 显示加载状态
      const loadingMessage = this.$message({
        message: '正在调用AI生成关联词，请稍候...',
        type: 'info',
        duration: 0, // 不自动关闭
        showClose: false
      })

      try {
        console.log('🚀 [AI关联词] 开始调用AI生成关联词接口')
        console.log('📝 [AI关联词] 需求内容:', this.specificRequirement)

        // 调用AI关联词生成接口
        const requestData = {
          requirement_content: this.specificRequirement.trim(),
          max_count: 20 // 生成更多关联词供用户选择
        }

        console.log('📤 [AI关联词] 请求参数:', requestData)
        const response = await generateRelatedKeywords(requestData)
        console.log('📥 [AI关联词] API响应:', response)

        // 关闭加载消息
        if (loadingMessage) {
          loadingMessage.close()
        }

        if (response.code === 200 && response.data) {
          const { keywords } = response.data

          // 验证返回的关联词数据
          if (Array.isArray(keywords) && keywords.length > 0) {
            // 数据验证和去重处理
            const validKeywords = this.processAIKeywords(keywords)

            if (validKeywords.length > 0) {
              // 保存所有生成的关键词
              this.generatedKeywords = [...validKeywords]

              // 默认选中前几个关键词（不超过最大数量）
              this.selectedKeywords = []
              validKeywords.forEach(word => {
                if (this.selectedKeywords.length < this.maxKeywords) {
                  this.selectedKeywords.push(word)
                }
              })

              this.$message.success(`AI成功生成 ${validKeywords.length} 个关联词`)
              console.log('✅ [AI关联词] 关联词生成成功:', validKeywords)

              // 关键词生成成功后，扣减套餐次数
              await this.deductPackageUsage('关键词生成')
            } else {
              console.warn('⚠️ [AI关联词] 生成的关联词经过验证后为空，使用降级策略')
              this.fallbackToDefaultKeywords()
            }
          } else {
            console.warn('⚠️ [AI关联词] API返回数据格式异常，使用降级策略')
            this.fallbackToDefaultKeywords()
          }
        } else {
          console.error('❌ [AI关联词] API调用失败:', response.msg)
          this.$message.error('AI生成关联词失败：' + (response.msg || '未知错误'))
          this.fallbackToDefaultKeywords()
        }

      } catch (error) {
        // 关闭加载消息
        if (loadingMessage) {
          loadingMessage.close()
        }

        console.error('💥 [AI关联词] 调用异常:', error)

        // 根据错误类型提供不同的处理
        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
          this.$message.error('AI服务响应超时，正在使用备用方案生成关联词...')
        } else if (error.response && error.response.status === 401) {
          this.$message.error('认证失败，请重新登录后重试')
          return
        } else {
          this.$message.error('AI服务暂时不可用，正在使用备用方案生成关联词...')
        }

        // 使用降级策略
        this.fallbackToDefaultKeywords()
      }
    },

    // 处理AI返回的关联词数据
    processAIKeywords(keywords) {
      console.log('🔧 [关联词处理] 开始处理AI返回的关联词:', keywords)

      const processed = []
      const seen = new Set()

      keywords.forEach(keyword => {
        if (typeof keyword === 'string') {
          const cleaned = keyword.trim()

          // 基本验证：长度、去重、有效性
          if (cleaned &&
              cleaned.length >= 2 &&
              cleaned.length <= 20 &&
              !seen.has(cleaned) &&
              this.isValidKeyword(cleaned)) {
            processed.push(cleaned)
            seen.add(cleaned)
          }
        }
      })

      console.log('✅ [关联词处理] 处理完成，有效关联词数量:', processed.length)
      return processed
    },

    // 验证关联词是否有效
    isValidKeyword(keyword) {
      // 过滤无效的关联词
      const invalidPatterns = [
        /^\d+$/, // 纯数字
        /^[a-zA-Z]+$/, // 纯英文
        /^[^\u4e00-\u9fa5\w\s]+$/, // 只包含特殊字符
        /^(的|了|是|在|有|和|与|或|但|然而|因此|所以)$/ // 常见停用词
      ]

      return !invalidPatterns.some(pattern => pattern.test(keyword))
    },

    // 降级策略：使用默认关联词
    fallbackToDefaultKeywords() {
      console.log('🔄 [降级策略] 使用默认关联词生成逻辑')

      // 基于实体关键词和需求内容生成基础关联词
      const entityKeyword = this.entityKeyword.trim()
      const baseKeywords = [
        `${entityKeyword} 售后服务`,
        `${entityKeyword} 客服态度`,
        `${entityKeyword} 质量问题`,
        `${entityKeyword} 投诉处理`,
        `${entityKeyword} 用户体验`,
        `${entityKeyword} 产品缺陷`,
        `${entityKeyword} 维修服务`,
        `${entityKeyword} 退换货`,
        `${entityKeyword} 品牌形象`,
        `${entityKeyword} 消费者反馈`
      ]

      // 保存生成的关键词
      this.generatedKeywords = [...baseKeywords]

      // 默认选中前几个关键词
      this.selectedKeywords = []
      baseKeywords.forEach(word => {
        if (this.selectedKeywords.length < this.maxKeywords) {
          this.selectedKeywords.push(word)
        }
      })

      this.$message.info('已使用备用方案生成关联词')
      console.log('✅ [降级策略] 默认关联词生成完成:', baseKeywords)
    },

    // 处理定时推送按钮点击
    handleTimedPush() {
      this.timedTaskDialogVisible = true
      // 打开定时推送弹窗时加载定时任务列表
      this.loadTimedTaskList()

      // 启动定时任务状态轮询，定期更新任务列表
      this.startTimedTaskListPolling()
    },

    // 启动定时任务列表轮询
    startTimedTaskListPolling() {
      this.startHttpPolling('timedTaskList', async () => {
        const response = await getTimedTaskList()
        return response.data
      }, {
        interval: 10000, // 每10秒轮询一次
        onSuccess: (data) => {
          if (data && Array.isArray(data.list)) {
            // 更新任务列表，保持界面数据最新
            this.timedTaskList = data.list
            console.log('定时任务列表已更新:', data.list.length, '个任务')
          }
        },
        onError: (error, attempts) => {
          console.error('获取定时任务列表失败:', error)
          // 如果连续失败3次，停止轮询并提示用户
          if (attempts >= 3) {
            this.$message.warning('定时任务状态更新失败，请手动刷新')
            return false // 停止轮询
          }
          return true // 继续轮询
        },
        shouldStop: () => {
          // 当定时任务弹窗关闭时停止轮询
          return !this.timedTaskDialogVisible
        }
      })
    },

    // 关闭定时任务弹窗
    closeTimedTaskDialog() {
      this.timedTaskDialogVisible = false
      // 停止定时任务列表轮询
      this.stopHttpPolling('timedTaskList')
    },

    // 处理创建定时任务
    handleCreateTimedTask() {
      this.resetTaskForm()
      this.editingTaskIndex = -1 // 设置为新建模式
      this.loadRequirementList() // 加载需求列表
      this.createTaskDialogVisible = true
    },

    // 处理添加定时任务按钮
    handleAddTimedTask() {
      this.resetTaskForm()
      this.editingTaskIndex = -1 // 设置为新建模式
      this.loadRequirementList() // 加载需求列表
      this.createTaskDialogVisible = true
    },

    // 加载需求列表
    async loadRequirementList() {
      try {
        const response = await getRequirementList({
          page: 1,
          size: 100
        })

        if (response.success) {
          // 处理分页数据
          if (response.data && response.data.rows) {
            this.requirementList = response.data.rows.map(item => ({
              id: item.id,
              requirementName: item.requirementName
            }))
          } else if (Array.isArray(response.data)) {
            this.requirementList = response.data.map(item => ({
              id: item.id,
              requirementName: item.requirementName
            }))
          } else {
            this.requirementList = []
          }
        } else {
          console.error('获取需求列表失败:', response.msg)
          this.$message.error(response.msg || '获取需求列表失败')
          // 使用模拟数据作为后备
          this.requirementList = [
            { id: 1, requirementName: '老板电器舆情监控' },
            { id: 2, requirementName: '品牌声誉分析' },
            { id: 3, requirementName: '竞品对比分析' },
            { id: 4, requirementName: '用户反馈监控' }
          ]
        }
      } catch (error) {
        console.error('加载需求列表失败:', error)
        this.$message.error('加载需求列表失败')
        // 使用模拟数据作为后备
        this.requirementList = [
          { id: 1, requirementName: '老板电器舆情监控' },
          { id: 2, requirementName: '品牌声誉分析' },
          { id: 3, requirementName: '竞品对比分析' },
          { id: 4, requirementName: '用户反馈监控' }
        ]
      }
    },

    // 加载关键词分类
    async loadKeywordCategories() {
      try {
        const response = await getKeywordCategories()

        if (response.success && Array.isArray(response.data)) {
          this.keywordCategories = response.data
          console.log('关键词分类加载成功:', this.keywordCategories)
        } else {
          console.error('获取关键词分类失败:', response.msg)
          // 使用默认分类作为后备
          this.keywordCategories = []
        }
      } catch (error) {
        console.error('加载关键词分类失败:', error)
        // 使用默认分类作为后备
        this.keywordCategories = []
      }
    },

    // 加载定时任务列表
    async loadTimedTaskList() {
      try {
        const response = await getTimedTaskList()

        if (response.success) {
          // 处理分页数据 - 后端返回的是PageResponseModel格式
          if (response.records && Array.isArray(response.records)) {
            this.timedTaskList = response.records.map(task => ({
              id: task.id,
              requirementId: task.requirementId,
              name: task.taskName,
              description: task.taskDescription,
              executeTime: task.executeTime,
              frequency: task.frequency,
              status: task.status === 'running' ? 'running' : 'pending',
              pushUrl: task.pushUrl
            }))
          } else if (response.data && response.data.records && Array.isArray(response.data.records)) {
            // 兼容嵌套在data中的情况
            this.timedTaskList = response.data.records.map(task => ({
              id: task.id,
              requirementId: task.requirementId,
              name: task.taskName,
              description: task.taskDescription,
              executeTime: task.executeTime,
              frequency: task.frequency,
              status: task.status === 'running' ? 'running' : 'pending',
              pushUrl: task.pushUrl
            }))
          }
          console.log('定时任务列表加载成功:', this.timedTaskList)
        } else {
          console.error('获取定时任务列表失败:', response.msg)
          this.timedTaskList = []
        }
      } catch (error) {
        console.error('加载定时任务列表失败:', error)
        this.timedTaskList = []
      }
    },

    // 关闭创建任务弹窗
    closeCreateTaskDialog() {
      this.createTaskDialogVisible = false
      this.resetTaskForm()
      this.editingTaskIndex = -1 // 重置编辑状态
    },

    // 保存并运行任务
    async saveAndRunTask() {
      if (!this.validateTaskForm()) {
        return
      }

      try {
        if (this.editingTaskIndex === -1) {
          // 创建新任务
          const taskData = {
            requirement_id: this.taskForm.requirementId,
            task_name: this.taskForm.name,
            task_type: 'scheduled',
            schedule_type: this.taskForm.frequency,
            schedule_config: {
              execute_time: this.taskForm.executeTime
            },
            task_description: this.taskForm.description,
            push_url: this.taskForm.pushUrl,
            priority: 'medium'
          }

          const response = await createTimedTask(taskData)
          if (response.success) {
            this.$message.success('任务已保存并开始运行')
            // 重新加载任务列表
            await this.loadTimedTaskList()
            // 更新任务状态为运行中
            if (response.data && response.data.id) {
              await updateTaskStatus(response.data.id, 'running')
              await this.loadTimedTaskList()
            }
          } else {
            this.$message.error('创建任务失败：' + response.msg)
            return
          }
        } else {
          // 更新现有任务
          const task = this.timedTaskList[this.editingTaskIndex]
          const taskData = {
            task_name: this.taskForm.name,
            task_description: this.taskForm.description,
            schedule_config: {
              execute_time: this.taskForm.executeTime
            },
            schedule_type: this.taskForm.frequency,
            push_url: this.taskForm.pushUrl
          }

          const response = await updateTimedTask(task.id, taskData)
          if (response.success) {
            // 更新任务状态为运行中
            await updateTaskStatus(task.id, 'running')
            this.$message.success('任务已更新并开始运行')
            // 重新加载任务列表
            await this.loadTimedTaskList()
          } else {
            this.$message.error('更新任务失败：' + response.msg)
            return
          }
        }

        this.createTaskDialogVisible = false
        this.resetTaskForm()
        this.editingTaskIndex = -1 // 重置编辑状态
      } catch (error) {
        console.error('保存任务失败:', error)
        this.$message.error('保存任务失败，请重试')
      }
    },

    // 保存任务计划
    async saveTaskPlan() {
      if (!this.validateTaskForm()) {
        return
      }

      try {
        if (this.editingTaskIndex === -1) {
          // 创建新任务
          const taskData = {
            requirement_id: this.taskForm.requirementId,
            task_name: this.taskForm.name,
            task_type: 'scheduled',
            schedule_type: this.taskForm.frequency,
            schedule_config: {
              execute_time: this.taskForm.executeTime
            },
            task_description: this.taskForm.description,
            push_url: this.taskForm.pushUrl,
            priority: 'medium'
          }

          const response = await createTimedTask(taskData)
          if (response.success) {
            this.$message.success('任务计划已保存')
            // 重新加载任务列表
            await this.loadTimedTaskList()
          } else {
            this.$message.error('保存任务计划失败：' + response.msg)
            return
          }
        } else {
          // 更新现有任务
          const task = this.timedTaskList[this.editingTaskIndex]
          const taskData = {
            task_name: this.taskForm.name,
            task_description: this.taskForm.description,
            schedule_config: {
              execute_time: this.taskForm.executeTime
            },
            schedule_type: this.taskForm.frequency,
            push_url: this.taskForm.pushUrl
          }

          const response = await updateTimedTask(task.id, taskData)
          if (response.success) {
            this.$message.success('任务计划已更新')
            // 重新加载任务列表
            await this.loadTimedTaskList()
          } else {
            this.$message.error('更新任务计划失败：' + response.msg)
            return
          }
        }

        this.createTaskDialogVisible = false
        this.resetTaskForm()
        this.editingTaskIndex = -1 // 重置编辑状态
      } catch (error) {
        console.error('保存任务计划失败:', error)
        this.$message.error('保存任务计划失败，请重试')
      }
    },

    // 修改计划
    modifyPlan() {
      this.$message.info('修改计划功能开发中...')
      // TODO: 实现修改计划逻辑
    },

    // 验证任务表单
    validateTaskForm() {
      if (!this.taskForm.requirementId) {
        this.$message.warning('请选择需求名称')
        return false
      }

      if (!this.taskForm.name.trim()) {
        this.$message.warning('请输入任务名称')
        return false
      }

      if (!this.taskForm.description.trim()) {
        this.$message.warning('请输入任务描述')
        return false
      }

      // 验证执行时间
      if (this.taskForm.frequency === 'once') {
        if (!this.taskForm.executeDateTime) {
          this.$message.warning('请选择执行日期和时间')
          return false
        }
        // 检查是否是未来时间
        const executeTime = new Date(this.taskForm.executeDateTime)
        if (executeTime <= new Date()) {
          this.$message.warning('执行时间必须是未来时间')
          return false
        }
      } else {
        if (!this.taskForm.executeTime) {
          this.$message.warning('请选择执行时间')
          return false
        }
      }

      return true
    },

    // 重置任务表单
    resetTaskForm() {
      this.taskForm = {
        requirementId: '',
        name: '',
        description: '',
        executeTime: '16:00',
        executeDateTime: '',
        frequency: 'daily',
        pushUrl: ''
      }
    },

    // 编辑任务
    editTask(index) {
      const task = this.timedTaskList[index]

      // 将任务数据填充到表单中
      this.taskForm = {
        requirementId: task.requirementId || '',
        name: task.name,
        description: task.description,
        executeTime: task.executeTime,
        executeDateTime: task.executeDateTime || '',
        frequency: task.frequency,
        pushUrl: task.pushUrl || ''
      }

      // 加载需求列表
      this.loadRequirementList()

      // 打开编辑弹窗
      this.createTaskDialogVisible = true

      // 保存当前编辑的任务索引，用于后续更新
      this.editingTaskIndex = index
    },

    // 删除任务
    deleteTask(index) {
      this.$confirm('确定要删除该任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const task = this.timedTaskList[index]
          const response = await deleteTimedTask(task.id)

          if (response.success) {
            this.$message.success('删除成功')
            // 重新加载任务列表
            await this.loadTimedTaskList()
          } else {
            this.$message.error('删除失败：' + response.msg)
          }
        } catch (error) {
          console.error('删除任务失败:', error)
          this.$message.error('删除任务失败，请重试')
        }
      }).catch(() => {
        // 取消删除操作
      })
    },

    // 预览任务详情
    previewTask(index) {
      const task = this.timedTaskList[index]
      this.taskPreviewDialog.taskData = { ...task }
      this.taskPreviewDialog.visible = true
    },

    // 隐藏任务预览弹窗
    hideTaskPreviewDialog() {
      this.taskPreviewDialog.visible = false
      this.taskPreviewDialog.taskData = null
    },

    // 从预览弹窗编辑任务
    editTaskFromPreview() {
      if (this.taskPreviewDialog.taskData) {
        // 找到任务在列表中的索引
        const taskIndex = this.timedTaskList.findIndex(task =>
          task.id === this.taskPreviewDialog.taskData.id
        )
        if (taskIndex !== -1) {
          this.hideTaskPreviewDialog()
          this.editTask(taskIndex)
        }
      }
    },

    // 获取频率文本
    getFrequencyText(frequency) {
      const frequencyMap = {
        'once': '仅一次',
        'daily': '每天',
        'weekly': '每周',
        'monthly': '每月'
      }
      return frequencyMap[frequency] || frequency
    },

    // 获取任务调度文本
    getTaskScheduleText(task) {
      if (task.frequency === 'once') {
        // 一次性任务显示具体执行时间
        return `仅一次 ${task.executeDateTime || task.executeTime}`
      } else {
        // 周期性任务显示频率和时间
        const frequencyText = this.getFrequencyText(task.frequency)
        return `${frequencyText} ${task.executeTime}`
      }
    },

    // 获取推送类型文本
    getPushTypeText(url) {
      if (!url) return '未配置'

      if (url.includes('oapi.dingtalk.com/robot/send')) {
        return '钉钉机器人'
      } else if (url.includes('qyapi.weixin.qq.com/cgi-bin/webhook/send')) {
        return '企业微信机器人'
      } else if (url.includes('open.feishu.cn/open-apis/bot/v2/hook/')) {
        return '飞书机器人'
      } else if (url.includes('localhost') || url.includes('127.0.0.1')) {
        return '本地接口'
      } else {
        return 'HTTP接口'
      }
    },

    // 获取脱敏的URL显示
    getMaskedUrl(url) {
      if (!url) return ''

      try {
        const urlObj = new URL(url)
        const domain = urlObj.hostname
        const path = urlObj.pathname

        // 对于机器人URL，隐藏token部分
        if (url.includes('access_token=') || url.includes('key=')) {
          return `${urlObj.protocol}//${domain}${path}?***`
        }

        // 对于普通URL，显示域名和路径
        return `${urlObj.protocol}//${domain}${path}`
      } catch (error) {
        // 如果URL解析失败，显示前50个字符
        return url.length > 50 ? url.substring(0, 50) + '...' : url
      }
    },

    // 复制到剪贴板
    copyToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.opacity = '0'
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        this.$message.success('地址已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      } finally {
        document.body.removeChild(textArea)
      }
    },

    // 预览任务详情
    previewTask(index) {
      const task = this.timedTaskList[index]
      this.taskPreviewDialog.taskData = { ...task }
      this.taskPreviewDialog.visible = true
    },

    // 隐藏任务预览弹窗
    hideTaskPreviewDialog() {
      this.taskPreviewDialog.visible = false
      this.taskPreviewDialog.taskData = null
    },

    // 从预览弹窗编辑任务
    editTaskFromPreview() {
      if (this.taskPreviewDialog.taskData) {
        // 找到任务在列表中的索引
        const taskIndex = this.timedTaskList.findIndex(task =>
          task.id === this.taskPreviewDialog.taskData.id
        )
        if (taskIndex !== -1) {
          this.hideTaskPreviewDialog()
          this.editTask(taskIndex)
        }
      }
    },

    // 获取频率文本
    getFrequencyText(frequency) {
      const frequencyMap = {
        'daily': '每天',
        'weekly': '每周',
        'monthly': '每月'
      }
      return frequencyMap[frequency] || frequency
    },

    // 获取推送类型文本
    getPushTypeText(url) {
      if (!url) return '未配置'

      if (url.includes('oapi.dingtalk.com/robot/send')) {
        return '钉钉机器人'
      } else if (url.includes('qyapi.weixin.qq.com/cgi-bin/webhook/send')) {
        return '企业微信机器人'
      } else if (url.includes('open.feishu.cn/open-apis/bot/v2/hook/')) {
        return '飞书机器人'
      } else if (url.includes('localhost') || url.includes('127.0.0.1')) {
        return '本地接口'
      } else {
        return 'HTTP接口'
      }
    },

    // 获取脱敏的URL显示
    getMaskedUrl(url) {
      if (!url) return ''

      try {
        const urlObj = new URL(url)
        const domain = urlObj.hostname
        const path = urlObj.pathname

        // 对于机器人URL，隐藏token部分
        if (url.includes('access_token=') || url.includes('key=')) {
          return `${urlObj.protocol}//${domain}${path}?***`
        }

        // 对于普通URL，显示域名和路径
        return `${urlObj.protocol}//${domain}${path}`
      } catch (error) {
        // 如果URL解析失败，显示前50个字符
        return url.length > 50 ? url.substring(0, 50) + '...' : url
      }
    },

    // 复制到剪贴板
    copyToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.opacity = '0'
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        this.$message.success('地址已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      } finally {
        document.body.removeChild(textArea)
      }
    },

    // 切换任务状态
    async toggleTaskStatus(index) {
      try {
        const task = this.timedTaskList[index]
        const newStatus = task.status === 'running' ? 'pending' : 'running'

        const response = await updateTaskStatus(task.id, newStatus)

        if (response.success) {
          // 显示状态变更提示
          if (newStatus === 'running') {
            this.$message.success(`任务「${task.name}」已启动`)
          } else {
            this.$message.info(`任务「${task.name}」已暂停`)
          }
          // 重新加载任务列表
          await this.loadTimedTaskList()
        } else {
          this.$message.error('状态更新失败：' + response.msg)
        }
      } catch (error) {
        console.error('切换任务状态失败:', error)
        this.$message.error('状态更新失败，请重试')
      }
    },

    // 更新报告数据
    updateReportData(analysisData) {
      try {
        console.log('开始更新报告数据，原始数据:', analysisData)

        // 保存完整的分析结果
        this.analysisResults = analysisData

        // 重置报告数据
        this.reportData = {
          totalArticles: 0,
          totalKeywords: this.selectedKeywords.length,
          dataSources: (this.enableOnlineSearch ? 1 : 0) + this.customDataSources.length,
          sentiment: {
            positive: 0,
            neutral: 0,
            negative: 0
          },
          onlineSearchCount: 0,
          customSourceCounts: {}
        }

        // 重置文章列表状态
        this.articleListState.currentPage = 1
        this.articleListState.searchKeyword = ''
        this.articleListState.selectedSource = ''
        this.articleListState.selectedSentiment = ''
        this.articleListState.expandedArticles.clear()

        // 处理分析结果
        if (analysisData.analysis_results) {
          const results = analysisData.analysis_results

          // 处理联网搜索结果
          if (results.online_search && results.online_search.data) {
            const onlineData = results.online_search.data
            console.log('联网搜索数据:', onlineData)

            if (onlineData.articles && Array.isArray(onlineData.articles)) {
              this.reportData.totalArticles += onlineData.articles.length
              this.reportData.onlineSearchCount = onlineData.articles.length

              console.log(`联网搜索获取到 ${onlineData.articles.length} 篇文章`)
            }

            // 更新情感分析数据
            if (onlineData.sentiment_analysis) {
              console.log('联网搜索情感分析数据:', onlineData.sentiment_analysis)
              this.reportData.sentiment = {
                positive: onlineData.sentiment_analysis.positive || 0,
                neutral: onlineData.sentiment_analysis.neutral || 0,
                negative: onlineData.sentiment_analysis.negative || 0
              }
            } else {
              // 如果没有整体情感分析，从文章中计算
              if (onlineData.articles && Array.isArray(onlineData.articles)) {
                this.reportData.sentiment = this.calculateSentimentFromArticles(onlineData.articles)
              }
            }
          }

          // 处理自定义数据源结果
          if (results.custom_data_source && results.custom_data_source.data) {
            const customData = results.custom_data_source.data
            console.log('自定义数据源数据:', customData)

            if (customData.articles && Array.isArray(customData.articles)) {
              this.reportData.totalArticles += customData.articles.length

              // 统计各数据源的文章数量
              customData.articles.forEach(article => {
                const source = article.source || '未知来源'
                this.reportData.customSourceCounts[source] = (this.reportData.customSourceCounts[source] || 0) + 1
              })

              // 合并情感分析结果
              const customSentiment = this.calculateSentimentFromArticles(customData.articles)
              this.reportData.sentiment = this.mergeSentimentData(this.reportData.sentiment, customSentiment, customData.articles.length)
            }
          }
        }

        console.log('报告数据更新完成:', this.reportData)
      } catch (error) {
        console.error('更新报告数据失败:', error)
        this.$message.error('报告数据更新失败，请重试')
      }
    },

    // 格式化分析时间
    formatAnalysisTime(date) {
      const now = new Date(date)
      return now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 从文章列表计算情感分析百分比
    calculateSentimentFromArticles(articles) {
      if (!articles || !Array.isArray(articles) || articles.length === 0) {
        return { positive: 0, neutral: 0, negative: 0 }
      }

      const sentimentCounts = { positive: 0, neutral: 0, negative: 0 }

      articles.forEach(article => {
        const sentiment = article.sentiment || 'neutral'
        if (sentimentCounts.hasOwnProperty(sentiment)) {
          sentimentCounts[sentiment]++
        } else {
          sentimentCounts.neutral++
        }
      })

      const total = articles.length
      return {
        positive: Math.round((sentimentCounts.positive / total) * 100),
        neutral: Math.round((sentimentCounts.neutral / total) * 100),
        negative: Math.round((sentimentCounts.negative / total) * 100)
      }
    },

    // 合并多个数据源的情感分析结果
    mergeSentimentData(sentiment1, sentiment2, weight2) {
      const total1 = this.reportData.onlineSearchCount || 0
      const total2 = weight2 || 0
      const totalArticles = total1 + total2

      if (totalArticles === 0) {
        return { positive: 0, neutral: 0, negative: 0 }
      }

      return {
        positive: Math.round(((sentiment1.positive * total1) + (sentiment2.positive * total2)) / totalArticles),
        neutral: Math.round(((sentiment1.neutral * total1) + (sentiment2.neutral * total2)) / totalArticles),
        negative: Math.round(((sentiment1.negative * total1) + (sentiment2.negative * total2)) / totalArticles)
      }
    },

    // 文章展开/收起切换
    toggleArticleExpand(index) {
      const articleKey = `page-${this.articleListState.currentPage}-item-${index}`
      if (this.articleListState.expandedArticles.has(articleKey)) {
        this.articleListState.expandedArticles.delete(articleKey)
      } else {
        this.articleListState.expandedArticles.add(articleKey)
      }
    },

    // 检查文章是否已展开
    isArticleExpanded(index) {
      const articleKey = `page-${this.articleListState.currentPage}-item-${index}`
      return this.articleListState.expandedArticles.has(articleKey)
    },

    // 获取内容摘要（前200字符）
    getContentSummary(content) {
      if (!content) return '暂无内容'
      return content.length > 200 ? content.substring(0, 200) + '...' : content
    },

    // 获取情感标签文本
    getSentimentLabel(sentiment) {
      const labels = {
        positive: '正面',
        neutral: '中性',
        negative: '负面'
      }
      return labels[sentiment] || '未知'
    },

    // 格式化发布时间
    formatPublishTime(publishTime) {
      if (!publishTime) return '未知时间'
      try {
        const date = new Date(publishTime)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        return publishTime
      }
    },

    // 复制文章内容
    copyArticleContent(article) {
      const content = `标题：${article.title}\n来源：${article.source}\n时间：${this.formatPublishTime(article.publish_time)}\n情感：${this.getSentimentLabel(article.sentiment)}\n内容：${article.content}`

      if (navigator.clipboard) {
        navigator.clipboard.writeText(content).then(() => {
          this.$message.success('文章内容已复制到剪贴板')
        }).catch(() => {
          this.fallbackCopyText(content)
        })
      } else {
        this.fallbackCopyText(content)
      }
    },

    // 备用复制方法
    fallbackCopyText(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        this.$message.success('文章内容已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }
      document.body.removeChild(textArea)
    },

    // 获取URL提示信息
    getUrlTooltip(url) {
      if (!url) return ''

      try {
        const urlObj = new URL(url)
        return `访问 ${urlObj.hostname}`
      } catch (error) {
        return `访问链接: ${url}`
      }
    },

    // 打开原文链接 - 直接使用AI返回的原始URL
    openArticleUrl(url) {
      if (url && url.trim()) {
        window.open(url, '_blank')
        this.$message.success('正在打开原文链接...')
      } else {
        this.$message.warning('暂无原文链接')
      }
    },

    // 处理分页变化
    handlePageChange(page) {
      this.articleListState.currentPage = page
      // 清空当前页的展开状态
      const keysToDelete = []
      this.articleListState.expandedArticles.forEach(key => {
        if (key.startsWith(`page-${page}-`)) {
          keysToDelete.push(key)
        }
      })
      keysToDelete.forEach(key => this.articleListState.expandedArticles.delete(key))
    },

    // 重置需求保存状态
    resetRequirementSaveStatus() {
      // 如果需求已保存，标记为已修改而不是重置ID
      if (this.requirementSaved && this.currentRequirementId) {
        this.requirementModified = true
        console.log('需求信息已变更，标记为需要更新')
      } else if (!this.currentRequirementId) {
        // 只有在没有需求ID时才重置状态
        this.requirementSaved = false
        this.requirementModified = false
        console.log('需求信息已变更，重置保存状态')
      }
    },

    // 确保需求存在（恢复或创建）
    async ensureRequirementExists() {
      try {
        // 如果需求信息不完整，无法创建
        if (!this.requirementName.trim() || !this.entityKeyword.trim() || !this.specificRequirement.trim()) {
          console.log('需求信息不完整，无法创建需求')
          return false
        }

        // 尝试根据需求名称查找已存在的需求
        try {
          const response = await getRequirementList({
            requirement_name: this.requirementName,
            page: 1,
            page_size: 1
          })

          if (response.success && response.data && response.data.rows && response.data.rows.length > 0) {
            const existingRequirement = response.data.rows[0]
            this.currentRequirementId = existingRequirement.id
            this.requirementSaved = true
            this.requirementModified = false
            console.log('找到已存在的需求，ID:', this.currentRequirementId)
            return true
          }
        } catch (error) {
          console.warn('查找已存在需求失败:', error)
        }

        // 如果没找到，创建新需求
        console.log('未找到已存在需求，创建新需求...')
        await this.createTemporaryRequirement()
        return !!this.currentRequirementId
      } catch (error) {
        console.error('确保需求存在失败:', error)
        return false
      }
    },

    // 保存推送任务到数据库（带重复检查）
    async saveTaskForPush(pushUrl, taskType = 'immediate') {
      try {
        // 检查是否有需求ID，如果没有则尝试恢复
        if (!this.currentRequirementId) {
          console.log('当前没有需求ID，尝试恢复或创建需求...')
          await this.ensureRequirementExists()

          // 如果仍然没有需求ID，抛出错误
          if (!this.currentRequirementId) {
            throw new Error('无法获取或创建需求信息，请重新填写需求')
          }
        }

        // 第一步：检查任务是否已存在
        console.log('检查任务是否已存在...')

        // 对于查看报告的保存操作（没有push_url），跳过重复检查，直接创建任务
        if (!pushUrl) {
          console.log('查看报告任务，跳过重复检查')
        } else {
          // 只对推送类型的任务进行重复检查
          const checkParams = {
            requirement_id: this.currentRequirementId,
            task_type: taskType,
            push_url: pushUrl
          }

          try {
            const checkResponse = await checkTaskExists(checkParams)
            console.log('任务存在性检查结果:', checkResponse)

            if (checkResponse.success && checkResponse.data.exists) {
              // 任务已存在，跳过保存
              console.log('任务已存在，跳过保存步骤')
              return {
                success: true,
                taskId: checkResponse.data.task_id,
                exists: true,
                existingTask: {
                  id: checkResponse.data.task_id,
                  name: checkResponse.data.task_name,
                  createTime: checkResponse.data.create_time
                },
                message: '任务已存在，直接推送'
              }
            }
          } catch (error) {
            console.log('任务存在性检查失败，继续创建新任务:', error.message)
            // 如果检查失败，继续创建新任务
          }
        }

        // 第二步：任务不存在，创建新任务
        console.log('任务不存在，创建新任务...')

        // 根据任务类型设置不同的任务名称和描述
        let taskName, taskDescription
        if (taskType === 'immediate') {
          // 如果没有push_url，说明是查看报告的保存操作
          if (!pushUrl) {
            taskName = `${this.requirementName} - 分析报告`
            taskDescription = `舆情分析报告查看任务`
          } else {
            taskName = `${this.requirementName} - 立即推送`
            taskDescription = `舆情分析报告推送任务`
          }
        } else {
          taskName = `${this.requirementName} - 推送计划`
          taskDescription = `舆情分析报告推送任务`
        }

        const taskData = {
          requirement_id: this.currentRequirementId,
          task_name: taskName,
          task_type: taskType,
          task_description: taskDescription,
          push_url: pushUrl || '', // report_view类型不需要push_url
          priority: 'high',
          schedule_type: taskType === 'immediate' ? 'once' : 'manual',
          schedule_config: taskType === 'immediate' ?
            { execute_time: new Date().toISOString() } :
            { execute_time: null },
          report_oss_url: this.reportOssUrl || null // 包含报告OSS URL
        }

        console.log('保存推送任务:', taskData)
        const response = await createTimedTask(taskData)

        if (response.success) {
          console.log('推送任务保存成功，ID:', response.data.id)
          return {
            success: true,
            taskId: response.data.id,
            exists: false,
            message: '任务创建成功'
          }
        } else {
          throw new Error(response.msg || '任务保存失败')
        }
      } catch (error) {
        console.error('保存推送任务失败:', error)
        throw error
      }
    },



    // 显示推送弹窗
    showPushDialog() {
      this.pushReportDialog.visible = true
      this.pushReportDialog.url = ''
      this.pushReportDialog.loading = false
    },

    // 隐藏推送弹窗
    hidePushDialog() {
      this.pushReportDialog.visible = false
      this.pushReportDialog.url = ''
      this.pushReportDialog.loading = false
    },

    // 验证推送URL格式（宽松验证，支持所有地址）
    validatePushUrl(url) {
      if (!url || !url.trim()) {
        this.$message.error('请输入推送目标URL地址')
        return false
      }

      let fullUrl = url.trim()

      // 智能协议补全
      if (!fullUrl.match(/^[a-zA-Z][a-zA-Z0-9+.-]*:/)) {
        // 如果没有协议，根据地址特征智能添加
        if (fullUrl.includes('localhost') || fullUrl.match(/^\d+\.\d+\.\d+\.\d+/) || fullUrl.startsWith('127.0.0.1')) {
          // 本地地址默认使用http
          fullUrl = 'http://' + fullUrl
        } else {
          // 其他地址默认使用https
          fullUrl = 'https://' + fullUrl
        }
      }

      // 宽松的URL格式验证
      try {
        // 基本格式检查
        if (fullUrl.includes(' ')) {
          this.$message.error('URL地址不能包含空格')
          return false
        }

        // 尝试创建URL对象进行基本验证
        new URL(fullUrl)

        // 特殊地址格式提示
        if (fullUrl.includes('oapi.dingtalk.com/robot/send')) {
          console.log('检测到钉钉机器人URL，将使用钉钉消息格式')
          if (!fullUrl.includes('access_token=')) {
            console.warn('钉钉机器人URL建议包含access_token参数')
          }
        } else if (fullUrl.includes('qyapi.weixin.qq.com/cgi-bin/webhook/send')) {
          console.log('检测到企业微信机器人URL')
        } else if (fullUrl.includes('open.feishu.cn/open-apis/bot/v2/hook/')) {
          console.log('检测到飞书机器人URL')
        }

        return fullUrl
      } catch (error) {
        // 如果URL对象创建失败，进行更宽松的检查
        console.warn('URL格式验证失败，尝试宽松验证:', error)

        // 检查是否包含基本的URL结构
        if (fullUrl.includes('://') && fullUrl.length > 10) {
          console.log('使用宽松验证通过URL:', fullUrl)
          return fullUrl
        }

        this.$message.error('URL格式可能不正确，但仍将尝试推送。如果推送失败，请检查URL格式')
        return fullUrl
      }
    },

    // 直接推送报告
    async directPushReport() {
      try {
        // 验证URL
        const validUrl = this.validatePushUrl(this.pushReportDialog.url)
        if (!validUrl) {
          return
        }

        // 检查是否有报告数据
        if (!this.reportData || this.reportData.totalArticles === 0) {
          this.$message.error('暂无报告数据可推送，请先完成分析')
          return
        }

        // 设置loading状态
        this.pushReportDialog.loading = true

        // 移除任务保存逻辑，只执行推送功能
        console.log('开始推送报告，不保存任务到数据库')

        // 生成报告页面链接
        const reportPageUrl = this.generateReportPageUrl()

        // 准备推送数据
        const pushData = {
          reportData: {
            ...this.reportData,
            requirementName: this.requirementName,
            entityKeyword: this.entityKeyword,
            specificRequirement: this.specificRequirement,
            selectedKeywords: this.selectedKeywords,
            reportPageUrl: reportPageUrl, // 添加报告页面链接
            pushTime: new Date().toISOString()
          },
          analysisResults: this.analysisResults,
          requirementInfo: {
            name: this.requirementName,
            entityKeyword: this.entityKeyword,
            specificRequirement: this.specificRequirement,
            selectedKeywords: this.selectedKeywords
          }
        }

        console.log('开始推送报告到:', validUrl)
        console.log('推送数据:', pushData)

        // 调用后端推送API
        const pushRequestData = {
          target_url: validUrl,
          report_data: pushData.reportData,
          analysis_results: pushData.analysisResults,
          requirement_id: this.currentRequirementId,
          push_type: 'immediate'
        }

        console.log('调用后端推送API:', pushRequestData)
        const response = await pushReport(pushRequestData)

        console.log('推送API响应:', response)

        // 验证推送结果
        if (response.success && response.data && response.data.success) {
          // 推送成功
          const pushResult = response.data
          console.log('推送成功，推送ID:', pushResult.push_id)

          // 显示详细成功信息
          this.$message.success(pushResult.message || '报告推送成功！')

          // 如果有响应状态码，也显示
          if (pushResult.response_status) {
            console.log(`目标服务器响应状态: ${pushResult.response_status}`)
          }
        } else {
          // 推送失败
          const errorMsg = response.data?.error_details || response.msg || '推送失败'
          throw new Error(errorMsg)
        }

        // 关闭弹窗
        this.hidePushDialog()

      } catch (error) {
        console.error('推送报告失败:', error)

        // 错误处理
        if (error.message && error.message.includes('timeout')) {
          this.$message.error('推送超时，请检查目标URL是否可访问')
        } else if (error.response && error.response.status) {
          this.$message.error(`推送失败：${error.response.status} ${error.response.statusText || ''}`)
        } else if (error.message) {
          this.$message.error('推送失败：' + error.message)
        } else {
          this.$message.error('推送失败，请重试')
        }
      } finally {
        this.pushReportDialog.loading = false
      }
    },

    // 保存推送计划
    async savePushPlan() {
      try {
        // 验证URL
        const validUrl = this.validatePushUrl(this.pushReportDialog.url)
        if (!validUrl) {
          return
        }

        // 保存推送计划任务到数据库（带重复检查）
        try {
          const taskResult = await this.saveTaskForPush(validUrl, 'scheduled')
          if (!taskResult.success) {
            this.$message.error('推送计划保存失败')
            return
          }

          // 处理任务存在性检查结果
          if (taskResult.exists) {
            console.log('推送计划任务已存在:', taskResult.existingTask)
            this.$message.info(`推送计划已存在（${taskResult.existingTask.name}）`)
          } else {
            console.log('推送计划任务保存成功，任务ID:', taskResult.taskId)
            this.$message.success('推送计划保存成功！')
          }
        } catch (error) {
          this.$message.error('推送计划保存失败：' + error.message)
          return
        }

        // 关闭弹窗
        this.hidePushDialog()

      } catch (error) {
        console.error('保存推送计划失败:', error)
        this.$message.error('保存推送计划失败：' + error.message)
      }
    },

    // 生成报告页面链接
    generateReportPageUrl() {
      try {
        // 获取当前页面的基础URL
        const baseUrl = window.location.origin
        const currentPath = window.location.pathname

        // 构建报告页面链接，包含当前的分析参数
        const reportParams = new URLSearchParams({
          step: '3', // 直接跳转到第三步报告预览
          requirementId: this.currentRequirementId || '', // 添加需求ID用于获取真实数据
          requirementName: this.requirementName || '',
          entityKeyword: this.entityKeyword || '',
          specificRequirement: this.specificRequirement || '',
          selectedKeywords: JSON.stringify(this.selectedKeywords || []),
          timestamp: Date.now() // 添加时间戳确保链接唯一性
        })

        const reportUrl = `${baseUrl}${currentPath}?${reportParams.toString()}`
        console.log('生成的报告页面链接:', reportUrl)

        return reportUrl
      } catch (error) {
        console.error('生成报告页面链接失败:', error)
        // 如果生成失败，返回当前页面链接
        return window.location.href
      }
    },

    // 解析URL参数
    parseUrlParams() {
      try {
        const urlParams = new URLSearchParams(window.location.search)

        // 检查是否有报告相关参数
        const step = urlParams.get('step')
        const requirementId = urlParams.get('requirementId')
        const requirementName = urlParams.get('requirementName')
        const entityKeyword = urlParams.get('entityKeyword')
        const specificRequirement = urlParams.get('specificRequirement')
        const selectedKeywords = urlParams.get('selectedKeywords')

        if (step && (requirementId || (requirementName && entityKeyword))) {
          console.log('检测到报告链接参数，自动加载报告')

          // 设置表单数据
          if (requirementId) {
            this.currentRequirementId = parseInt(requirementId)
          }
          this.requirementName = requirementName || ''
          this.entityKeyword = entityKeyword || ''
          this.specificRequirement = specificRequirement || ''

          // 解析选中的关键词
          if (selectedKeywords) {
            try {
              this.selectedKeywords = JSON.parse(selectedKeywords)
            } catch (e) {
              console.warn('解析关键词参数失败:', e)
              this.selectedKeywords = []
            }
          }

          // 跳转到指定步骤
          const targetStep = parseInt(step)
          if (targetStep >= 1 && targetStep <= 3) {
            this.currentStep = targetStep

            // 如果是第三步，显示提示信息
            if (targetStep === 3) {
              this.$message.info('正在加载报告预览，请稍候...')
              // 这里可以添加模拟的报告数据或者重新执行分析
              this.loadReportFromParams()
            }
          }
        }
      } catch (error) {
        console.error('解析URL参数失败:', error)
      }
    },

    // 从URL参数加载报告数据
    async loadReportFromParams() {
      try {
        // 检查是否有需求ID参数
        const urlParams = new URLSearchParams(window.location.search)
        const requirementId = urlParams.get('requirementId')

        if (requirementId) {
          // 调用公开API获取报告数据
          const response = await getPublicReportData(requirementId)

          if (response.success && response.data) {
            const data = response.data

            // 更新基础信息
            this.requirementName = data.requirement_name || ''
            this.entityKeyword = data.entity_keyword || ''
            this.specificRequirement = data.specific_requirement || ''
            this.currentRequirementId = data.requirement_id

            // 更新报告数据
            this.reportData = {
              totalArticles: data.analysis_results?.total_articles || 0,
              totalKeywords: this.selectedKeywords.length,
              dataSources: 1,
              sentiment: {
                positive: data.analysis_results?.sentiment?.positive || 0,
                neutral: data.analysis_results?.sentiment?.neutral || 0,
                negative: data.analysis_results?.sentiment?.negative || 0
              },
              onlineSearchCount: data.analysis_results?.online_search?.length || 0,
              customSourceCounts: {}
            }

            // 处理在线搜索结果
            if (data.analysis_results?.online_search) {
              this.handleOnlineSearchResults(data.analysis_results.online_search)
            }

            this.$message.success('报告数据加载成功')
          } else {
            throw new Error(response.msg || '获取报告数据失败')
          }
        } else {
          // 没有需求ID，使用默认数据
          this.reportData = {
            totalArticles: 0,
            totalKeywords: this.selectedKeywords.length,
            dataSources: 1,
            sentiment: {
              positive: 0,
              neutral: 0,
              negative: 0
            },
            onlineSearchCount: 0,
            customSourceCounts: {}
          }
        }
      } catch (error) {
        console.error('加载报告数据失败:', error)
        this.$message.warning('加载报告数据失败，显示默认数据')

        // 使用默认数据
        this.reportData = {
          totalArticles: 0,
          totalKeywords: this.selectedKeywords.length,
          dataSources: 1,
          sentiment: {
            positive: 0,
            neutral: 0,
            negative: 0
          },
          onlineSearchCount: 0,
          customSourceCounts: {}
        }
      }

      // 提示用户可以重新执行分析获取最新数据
      this.$message.warning('这是通过链接访问的报告，数据可能不是最新的。建议重新执行分析获取最新结果。')
    },

    // 获取关键词频率（基于实际数据）
    getKeywordFrequency(keyword) {
      // TODO: 这里可以根据实际分析结果返回关键词频率
      // 暂时返回模拟数据，后续可以从reportData中计算真实频率
      return Math.floor(Math.random() * 50) + 10
    },

    // 获取数据源统计数量
    getSourceCount(source) {
      if (this.reportData.customSourceCounts) {
        return this.reportData.customSourceCounts[source] || 0
      }
      return 0
    },



    // 提取域名
    extractDomainName(url) {
      try {
        const urlObj = new URL(url)
        return urlObj.hostname.replace('www.', '')
      } catch (error) {
        // 如果不是有效URL，直接返回原字符串的前20个字符
        return url.length > 20 ? url.substring(0, 20) + '...' : url
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.opinion-analysis {
  padding: 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

// 步骤指示器样式
.steps-container {
  background: white;
  padding: 20px 24px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left-actions {
    flex: 0 0 auto;

    .timed-push-btn {
      font-size: 14px;
      padding: 8px 16px;
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  .steps-wrapper {
    flex: 1;
    display: flex;
    justify-content: center;
    gap: 60px;
  }

  .right-placeholder {
    flex: 0 0 auto;
    width: 88px; // 与左侧按钮宽度保持平衡
  }

  .right-actions {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    gap: 12px;

    .step-switch-dropdown {
      .step-switch-btn {
        font-size: 13px;
        padding: 6px 12px;
        border-radius: 6px;
        font-weight: 500;
        background: #f39c12;
        border-color: #f39c12;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(243, 156, 18, 0.2);

        &:hover {
          background: #e67e22;
          border-color: #e67e22;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
        }

        &:active {
          transform: translateY(0);
        }

        .el-icon--right {
          margin-left: 4px;
        }
      }
    }

    .analyze-record-btn,
    .timed-push-btn {
      font-size: 13px;
      padding: 6px 12px;
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  .step-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #999;
    font-size: 14px;

    &.active {
      color: #5470c6;
      font-weight: 500;

      .step-number {
        background: #5470c6;
        color: white;
      }
    }

    .step-number {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #e8e8e8;
      color: #999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 500;
    }
  }
}

.main-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 24px;
}

// 报告预览区域
.report-preview {
  background: white;
  border-radius: 8px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 32px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 24px 0;
  }
}

// 报告概览卡片
.report-overview {
  margin-bottom: 24px;

  .overview-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e8e8e8;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }

      .analysis-time {
        font-size: 13px;
        color: #666;
      }
    }

    .overview-stats {
      display: flex;
      gap: 32px;

      .stat-item {
        text-align: center;

        .stat-number {
          font-size: 24px;
          font-weight: 600;
          color: #5470c6;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 13px;
          color: #666;
        }
      }
    }
  }
}

// 分析卡片通用样式
.analysis-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  margin-bottom: 16px;

  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
  }
}

// 情感分析
.sentiment-analysis {
  margin-bottom: 24px;

  .sentiment-chart {
    .sentiment-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .sentiment-bar {
        flex: 1;
        height: 20px;
        background: #f0f0f0;
        border-radius: 10px;
        overflow: hidden;
        margin-right: 12px;

        .bar-fill {
          height: 100%;
          border-radius: 10px;
          transition: width 0.3s ease;
        }
      }

      &.positive .bar-fill {
        background: linear-gradient(90deg, #52c41a, #73d13d);
      }

      &.neutral .bar-fill {
        background: linear-gradient(90deg, #faad14, #ffc53d);
      }

      &.negative .bar-fill {
        background: linear-gradient(90deg, #ff4d4f, #ff7875);
      }

      .sentiment-info {
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 80px;

        .sentiment-label {
          font-size: 14px;
          color: #666;
        }

        .sentiment-value {
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }
      }
    }
  }
}

// 关键词分析
.keyword-analysis {
  margin-bottom: 24px;

  .selected-keywords-display {
    .keyword-list {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .keyword-item {
        display: flex;
        align-items: center;
        background: #f0f7ff;
        border: 1px solid #d6e4ff;
        border-radius: 16px;
        padding: 6px 12px;
        font-size: 13px;

        .keyword-text {
          color: #1890ff;
          margin-right: 6px;
        }

        .keyword-frequency {
          background: #1890ff;
          color: white;
          border-radius: 8px;
          padding: 2px 6px;
          font-size: 11px;
          font-weight: 500;
        }
      }
    }
  }
}

// 数据来源统计
.data-source-stats {
  margin-bottom: 24px;

  .source-list {
    .source-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .source-icon {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;

        &.online {
          background: #e6f4ff;
          color: #1890ff;
        }

        &.custom {
          background: #f6ffed;
          color: #52c41a;
        }

        i {
          font-size: 16px;
        }
      }

      .source-info {
        flex: 1;

        .source-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 2px;
        }

        .source-desc {
          font-size: 12px;
          color: #999;
        }
      }

      .source-count {
        font-size: 14px;
        font-weight: 600;
        color: #5470c6;
      }
    }
  }
}

// 详细数据样式
.detailed-data {
  margin-bottom: 24px;

  .card-header-with-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      margin: 0;
    }

    .data-controls {
      display: flex;
      align-items: center;
    }
  }

  .article-list {
    .article-item {
      background: #fafafa;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      margin-bottom: 12px;
      overflow: hidden;
      transition: all 0.3s ease;

      &:hover {
        border-color: #d9d9d9;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }

      .article-header {
        padding: 16px;
        background: white;

        .article-title-row {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 8px;

          .article-title {
            flex: 1;
            margin: 0;
            font-size: 16px;
            font-weight: 500;
            color: #262626;
            cursor: pointer;
            line-height: 1.4;
            margin-right: 12px;
            transition: color 0.3s ease;

            &:hover {
              color: #1890ff;
            }

            i {
              margin-left: 8px;
              font-size: 14px;
              color: #8c8c8c;
            }
          }

          .article-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;

            .article-source {
              font-size: 12px;
              color: #8c8c8c;
              background: #f5f5f5;
              padding: 2px 8px;
              border-radius: 4px;
            }

            .sentiment-tag {
              font-size: 12px;
              padding: 2px 8px;
              border-radius: 4px;
              font-weight: 500;

              &.positive {
                background: #f6ffed;
                color: #52c41a;
                border: 1px solid #b7eb8f;
              }

              &.neutral {
                background: #f5f5f5;
                color: #8c8c8c;
                border: 1px solid #d9d9d9;
              }

              &.negative {
                background: #fff2f0;
                color: #ff4d4f;
                border: 1px solid #ffccc7;
              }
            }
          }
        }

        .article-info {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .publish-time {
            font-size: 12px;
            color: #8c8c8c;
          }

          .article-actions {
            display: flex;
            gap: 8px;
          }
        }
      }

      .article-content {
        padding: 0 16px 16px;

        .content-summary {
          margin: 0;
          color: #595959;
          line-height: 1.6;
          font-size: 14px;
        }

        &.expanded {
          background: #fafafa;
          border-top: 1px solid #f0f0f0;
          padding: 16px;

          .content-full {
            margin: 0;
            color: #262626;
            line-height: 1.6;
            font-size: 14px;
            white-space: pre-wrap;
          }
        }
      }
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
  }
}

// 报告操作
.report-actions {
  margin-top: 32px;
  text-align: center;

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;

    .el-button {
      padding: 12px 24px;
      font-size: 14px;
      border-radius: 6px;
    }
  }
}

// 分析来源区域
.analysis-source {
  background: white;
  border-radius: 8px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 32px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }

    .template-btn {
      font-size: 14px;
      padding: 6px 16px;
    }
  }

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 24px 0;
  }
}

// 输入区域样式
.input-section {
  margin-bottom: 24px;

  .input-label {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;

    .required {
      color: #ff4d4f;
      margin-left: 2px;
    }

    .keyword-count {
      color: #999;
      font-weight: normal;
      margin-left: 8px;
      font-size: 13px;

      &.max-reached {
        color: #ff4d4f;
        font-weight: 500;
      }
    }
  }

  .entity-input {
    :deep(.el-input__inner) {
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      padding: 12px 16px;
      font-size: 14px;

      &::placeholder {
        color: #bfbfbf;
      }

      &:focus {
        border-color: #5470c6;
        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
      }
    }

    &.error {
      :deep(.el-input__inner) {
        border-color: #ff4d4f;

        &:focus {
          border-color: #ff4d4f;
          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);
        }
      }
    }
  }
}

// 任务列表样式
.task-list {
  padding: 24px;

  .empty-task-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      color: #d9d9d9;
    }

    .empty-text {
      font-size: 16px;
      color: #999;
      margin-bottom: 24px;
    }

    .add-task-btn {
      padding: 8px 20px;
    }
  }

  .task-items {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .task-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    .task-info {
       flex: 1;

       .task-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
       }

       .task-name {
        font-size: 16px;
        font-weight: 500;
        color: #333;
       }

       .task-status {
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 10px;
        font-weight: 500;

        &.status-running {
          background-color: rgba(82, 196, 26, 0.1);
          color: #52c41a;
        }

        &.status-pending {
          background-color: rgba(250, 173, 20, 0.1);
          color: #faad14;
        }
       }

       /* 任务描述样式已移除，因为不再显示任务描述 */

       .task-schedule {
        font-size: 13px;
        color: #999;
        display: flex;
        align-items: center;
        gap: 4px;
        margin-top: 8px;

        i {
          font-size: 14px;
        }
       }
     }

    .task-actions {
      display: flex;
      align-items: flex-start;
      gap: 8px;

      .el-button {
        padding: 4px;

        i {
          font-size: 16px;
        }
      }
    }
  }
}

.requirement-textarea {
  :deep(.el-textarea__inner) {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    padding: 12px 16px;
    font-size: 14px;
    line-height: 1.6;
    resize: vertical;

    &::placeholder {
      color: #bfbfbf;
    }

    &:focus {
      border-color: #5470c6;
      box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
    }
  }

  &.error {
    :deep(.el-textarea__inner) {
      border-color: #ff4d4f;

      &:focus {
        border-color: #ff4d4f;
        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);
      }
    }
  }
}

// 选择关联词区域
.related-words-section {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;

      .section-label {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }

      .word-count {
        font-size: 14px;
        color: #999;
        font-weight: normal;
        margin-left: 8px;
        transition: color 0.3s ease;

        &.max-reached {
          color: #ff4d4f;
          font-weight: 500;
        }
      }
    }

    .regenerate-btn {
      font-size: 13px;
      color: #5470c6;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f0f7ff;
        color: #4096ff;
      }

      i {
        margin-right: 4px;
        font-size: 12px;
      }
    }
  }

  .words-container {
    text-align: center;

    .generate-word-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      padding: 8px 16px;
      background: #f0f7ff;
      color: #5470c6;
      border: 1px dashed #5470c6;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 12px;

      &:hover {
        background: #e6f4ff;
        border-color: #4096ff;
      }

      i {
        font-size: 12px;
      }
    }

    .word-description {
      font-size: 12px;
      color: #999;
      line-height: 1.5;
    }
  }
}

// 关键词文本框包装器
.keywords-textbox-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fff;
  min-height: 120px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    border-color: #5470c6;
  }

  &:focus-within {
    border-color: #5470c6;
    box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
  }
}

// 生成的关键词显示区域
.generated-keywords-display {
  margin-bottom: 16px;

  .keyword-category {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .category-button {
      min-width: 100px;
      margin-right: 16px;
      margin-bottom: 8px;
      font-size: 13px;
      border-radius: 16px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .keyword-tags {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .keyword-tag {
        font-size: 13px;
        padding: 6px 12px;
        border-radius: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #d9d9d9;
        background: #fff;
        color: #666;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        user-select: none;

        &:hover {
          border-color: #5470c6;
          color: #5470c6;
        }

        &.selected {
          background: #5470c6;
          color: white;
          border-color: #5470c6;
        }
      }
    }
  }
}



// 关键词选择区域
.keywords-selection-section {
  .keywords-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .keyword-category {
    display: flex;
    align-items: flex-start;
    gap: 16px;

    .category-label {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      min-width: 80px;
      padding-top: 6px;
    }

    .keyword-tags {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .keyword-tag {
        font-size: 13px;
        padding: 6px 12px;
        border-radius: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #d9d9d9;
        background: #fff;
        color: #666;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        user-select: none;

        &:hover {
          border-color: #5470c6;
          color: #5470c6;
        }

        &.selected {
          background: #5470c6;
          color: white;
          border-color: #5470c6;
        }

        &.highlight {
          background: #333;
          color: white;
          border-color: #333;
          position: relative;
          cursor: default;

          &::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid #333;
          }
        }
      }
    }
  }
}

// 第二步：数据概览样式
.data-overview {
  background: white;
  border-radius: 8px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 32px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 24px 0;
  }
}

.data-source-section {
  .source-option {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #5470c6;
      background: #f8f9fa;
    }

    .source-checkbox {
      :deep(.el-checkbox__input) {
        .el-checkbox__inner {
          width: 18px;
          height: 18px;
          border-radius: 4px;
          border: 2px solid #d9d9d9;

          &::after {
            width: 5px;
            height: 9px;
            left: 5px;
            top: 1px;
          }
        }

        &.is-checked .el-checkbox__inner {
          background-color: #5470c6;
          border-color: #5470c6;
        }
      }
    }

    .source-icon {
      width: 40px;
      height: 40px;
      background: #f0f7ff;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        font-size: 20px;
        color: #5470c6;
      }
    }

    .source-content {
      flex: 1;

      h3 {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin: 0 0 4px 0;
        word-break: break-all;
        line-height: 1.4;
      }

      .source-description {
        margin: 0;
        font-size: 14px;
        color: #666;
        line-height: 1.4;
      }

      .source-count {
        color: #409eff;
        font-weight: 500;
      }
    }

    .source-actions {
      display: flex;
      align-items: center;

      .el-icon-delete {
        font-size: 16px;
        color: #999;
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: #ff4d4f;
        }
      }
    }
  }

  .add-source-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    color: #999;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;

    &:hover {
      border-color: #5470c6;
      color: #5470c6;
      background: #f8f9fa;
    }

    i {
      font-size: 16px;
    }
  }
}

// 底部按钮区域
.bottom-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding-top: 24px;

  .el-button {
    padding: 12px 32px;
    font-size: 16px;

    &:disabled {
      background-color: #f5f5f5;
      border-color: #d9d9d9;
      color: #bfbfbf;
      cursor: not-allowed;
    }
  }
}

// 新增数据源表单样式
.add-source-form {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  margin-top: 16px;

  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }

    .el-icon-close {
      font-size: 18px;
      color: #999;
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: #666;
      }
    }
  }

  .form-item {
    .form-label {
      display: block;
      font-size: 14px;
      color: #333;
      font-weight: 500;
      margin-bottom: 8px;

      .required {
        color: #ff4d4f;
        margin-left: 2px;
      }
    }

    .input-group {
      display: flex;
      gap: 12px;
      align-items: flex-start;

      .source-url-input {
        flex: 1;

        :deep(.el-input__inner) {
          border-radius: 6px;
          border: 1px solid #d9d9d9;
          padding: 12px 16px;
          font-size: 14px;

          &::placeholder {
            color: #bfbfbf;
          }

          &:focus {
            border-color: #5470c6;
            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
          }
        }
      }

      .el-button {
        padding: 12px 24px;
        font-size: 14px;
        border-radius: 6px;
        white-space: nowrap;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .steps-container {
    padding: 16px 12px;
    flex-direction: column;
    gap: 16px;

    .left-actions {
      align-self: flex-start;

      .timed-push-btn {
        font-size: 13px;
        padding: 6px 12px;
      }
    }

    .steps-wrapper {
      gap: 30px;
    }

    .right-placeholder {
      display: none;
    }

    .step-item {
      font-size: 13px;
    }
  }

  .main-content {
    padding: 24px 16px;
  }

  .analysis-source {
    padding: 24px 20px;
  }

  .document-content {
    padding: 12px;
    min-height: 100px;
  }
}

// 定时任务抽屉样式
.timed-task-drawer {
  :deep(.el-drawer__header) {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 0;
  }

  :deep(.el-drawer__body) {
    padding: 0;
  }
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .drawer-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .add-task-btn {
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 4px;

    .el-icon-plus {
      margin-right: 4px;
    }
  }
}

.drawer-content {
  padding: 24px;
  min-height: 400px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.empty-content {
  text-align: center;

  .empty-icon {
    margin-bottom: 24px;
    display: flex;
    justify-content: center;

    svg {
      opacity: 0.6;
    }
  }

  .empty-text {
    font-size: 16px;
    color: #909399;
    margin: 0 0 24px 0;
    font-weight: 500;
  }

  .create-btn {
    padding: 10px 24px;
    font-size: 14px;
    border-radius: 6px;
    font-weight: 500;
  }
}

// 创建任务弹窗样式
.create-task-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
  }

  :deep(.el-dialog__header) {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.el-dialog__body) {
    padding: 24px;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f0f0;
  }
}

.task-form {
  .task-requirement-section {
    margin-bottom: 24px;

    .section-label {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;

      .required {
        color: #ff4d4f;
        margin-left: 2px;
      }
    }

    .form-group {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .input-label {
        font-size: 13px;
        color: #666;
        margin-bottom: 6px;
        font-weight: 500;
      }

      .task-name-input {
        :deep(.el-input__inner) {
          border-radius: 6px;
          border: 1px solid #d9d9d9;
          padding: 10px 12px;
          font-size: 14px;

          &::placeholder {
            color: #bfbfbf;
          }

          &:focus {
            border-color: #5470c6;
            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
          }
        }
      }

      .task-description-input {
        :deep(.el-textarea__inner) {
          border-radius: 6px;
          border: 1px solid #d9d9d9;
          padding: 10px 12px;
          font-size: 14px;
          line-height: 1.5;
          resize: vertical;

          &::placeholder {
            color: #bfbfbf;
          }

          &:focus {
            border-color: #5470c6;
            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);
          }
        }
      }
    }
  }

  .execute-time-section {
    .section-label {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
    }

    .time-selector {
      display: flex;
      gap: 12px;
      align-items: center;

      .frequency-select {
        width: 120px;
      }

      .time-picker {
        width: 140px;
      }

      .datetime-picker {
        width: 200px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .modify-btn {
    color: #666;
    border-color: #d9d9d9;

    &:hover {
      color: #5470c6;
      border-color: #5470c6;
    }
  }

  .run-btn {
    background: #5470c6;
    border-color: #5470c6;

    &:hover {
      background: #4096ff;
      border-color: #4096ff;
    }
  }

  .save-btn {
    background: #52c41a;
    border-color: #52c41a;

    &:hover {
      background: #73d13d;
      border-color: #73d13d;
    }
  }
}

// 自定义数据源管理区域样式
.custom-sources-management {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  .management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
  }

  .existing-sources {
    .source-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      background: white;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      margin-bottom: 8px;

      .source-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        i {
          color: #409eff;
        }

        .source-name {
          font-weight: 500;
          color: #333;
        }

        .source-url {
          color: #666;
          font-size: 12px;
          margin-left: 8px;
        }
      }

      .source-actions {
        i {
          cursor: pointer;
          color: #f56c6c;
          font-size: 16px;
          padding: 4px;

          &:hover {
            color: #f78989;
          }
        }
      }
    }
  }

  .no-sources {
    text-align: center;
    padding: 20px;
    color: #999;
    font-size: 14px;
  }
}

// 数据源列表区域样式
.data-source-list-section {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }

    .header-controls {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }

  .data-source-table {
    background: white;
    border-radius: 6px;
    overflow: hidden;

    .url-cell {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #409eff;
        font-size: 14px;
        flex-shrink: 0;
      }

      .url-text {
        color: #333;
        font-size: 13px;
        flex: 1;
      }
    }

    .pagination-wrapper {
      padding: 16px;
      text-align: right;
      background: #fafafa;
      border-top: 1px solid #e8e8e8;
    }
  }
}

// 任务列表样式（待实现）
// .task-list {
//   // TODO: 任务列表样式
// }

// 推送报告弹窗样式
.form-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;

  i {
    margin-right: 4px;
    color: #409eff;
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 8px;
  }
}

// 推送按钮图标样式
.el-button .el-icon-s-promotion {
  margin-right: 6px;
}

// 任务预览弹窗样式
.task-preview-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    border-radius: 8px 8px 0 0;

    .el-dialog__title {
      color: white;
      font-weight: 600;
      font-size: 16px;
    }

    .el-dialog__close {
      color: white;
      font-size: 18px;

      &:hover {
        color: #f0f0f0;
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f0f0;
  }
}

.task-preview-content {
  .preview-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #f0f0f0;

      i {
        color: #667eea;
        font-size: 18px;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    .info-item {
      display: flex;
      align-items: flex-start;
      gap: 8px;

      label {
        font-weight: 600;
        color: #666;
        min-width: 80px;
        flex-shrink: 0;
      }

      span {
        color: #333;
        word-break: break-all;
      }

      .el-tag {
        margin: 0;
      }
    }

    .description-content {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 16px;
      color: #333;
      line-height: 1.6;
      white-space: pre-wrap;
    }

    .push-config {
      .url-display {
        display: flex;
        align-items: center;
        gap: 8px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 12px;

        .url-text {
          flex: 1;
          color: #333;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 13px;
          word-break: break-all;
        }

        .el-button {
          padding: 4px 8px;

          i {
            color: #667eea;
          }

          &:hover i {
            color: #5a67d8;
          }
        }
      }
    }
  }
}

/* ==================== 分析进度页面样式 ==================== */
.analysis-progress {
  .progress-overview {
    margin-bottom: 24px;

    .status-card {
      background: #fff;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .status-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
        }

        .status-indicator {
          display: flex;
          align-items: center;
          gap: 8px;

          .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ccc;

            &.running {
              background: #1890ff;
              animation: pulse 1.5s infinite;
            }

            &.completed {
              background: #52c41a;
            }

            &.failed {
              background: #ff4d4f;
            }
          }

          .status-text {
            font-size: 14px;
            color: #666;
          }
        }

        &.running .status-text {
          color: #1890ff;
        }

        &.completed .status-text {
          color: #52c41a;
        }

        &.failed .status-text {
          color: #ff4d4f;
        }
      }

      .progress-bar-container {
        display: flex;
        align-items: center;
        gap: 12px;

        .progress-bar {
          flex: 1;
          height: 8px;
          background: #f5f5f5;
          border-radius: 4px;
          overflow: hidden;

          .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1890ff, #40a9ff);
            border-radius: 4px;
            transition: width 0.3s ease;
          }
        }

        .progress-text {
          font-size: 14px;
          font-weight: 600;
          color: #1890ff;
          min-width: 40px;
        }
      }
    }
  }

  .real-time-logs {
    .logs-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }

      .logs-controls {
        display: flex;
        gap: 8px;
      }
    }

    .logs-container {
      background: #1e1e1e;
      border-radius: 8px;
      padding: 16px;
      height: 400px;
      overflow-y: auto;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;

      .no-logs {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #888;

        i {
          font-size: 24px;
          margin-bottom: 8px;
        }
      }

      .logs-list {
        .log-item {
          display: flex;
          gap: 12px;
          margin-bottom: 8px;
          font-size: 13px;
          line-height: 1.4;

          .log-time {
            color: #888;
            min-width: 80px;
          }

          .log-level {
            min-width: 60px;
            font-weight: 600;
          }

          .log-message {
            flex: 1;
            color: #fff;
          }

          &.info .log-level {
            color: #40a9ff;
          }

          &.success .log-level {
            color: #52c41a;
          }

          &.warning .log-level {
            color: #faad14;
          }

          &.error .log-level {
            color: #ff4d4f;
          }
        }
      }

      /* 滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #2a2a2a;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #555;
        border-radius: 3px;

        &:hover {
          background: #777;
        }
      }
    }
  }

  .analysis-completed,
  .analysis-failed {
    text-align: center;
    margin-top: 24px;
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .completion-message,
    .failure-message {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;

      i {
        font-size: 20px;
      }
    }

    .completion-message {
      color: #52c41a;
    }

    .failure-message {
      color: #ff4d4f;
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// 模板弹窗样式
.template-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    border-radius: 8px 8px 0 0;

    .el-dialog__title {
      color: white;
      font-weight: 600;
      font-size: 16px;
    }

    .el-dialog__close {
      color: white;
      font-size: 18px;

      &:hover {
        color: #f0f0f0;
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 0;
    max-height: 60vh;
    overflow-y: auto;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f0f0;
  }
}

.template-content {
  .template-list {
    padding: 24px;
  }

  .template-item {
    border: 2px solid #e8e8e8;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    }

    &.selected {
      border-color: #409eff;
      background-color: #f0f7ff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .template-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .template-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        color: #333;

        i {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .template-category {
        background: #409eff;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
      }
    }

    .template-details {
      .template-field {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          font-weight: 600;
          color: #666;
          margin-right: 8px;
        }

        span {
          color: #333;
        }

        p {
          margin: 4px 0 0 0;
          color: #666;
          line-height: 1.5;
          font-size: 14px;
        }
      }
    }
  }
}

// 步骤切换下拉菜单样式
.el-dropdown-menu {
  .el-dropdown-menu__item {
    padding: 8px 16px;
    font-size: 13px;
    line-height: 1.4;

    i {
      margin-right: 8px;
      color: #666;
    }

    &.is-active {
      background-color: #f0f7ff;
      color: #409eff;
      font-weight: 500;

      i {
        color: #409eff;
      }
    }

    &:hover {
      background-color: #f5f7fa;
    }

    &.is-active:hover {
      background-color: #e6f4ff;
    }
  }
}
</style>
