from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank, Size
from typing import Literal, Optional, List, Dict, Any
from module_admin.annotation.pydantic_annotation import as_query


class WarningRecordModel(BaseModel):
    """
    预警记录表对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='预警记录ID')
    scheme_id: Optional[int] = Field(default=None, description='预警方案ID')
    warning_type: Optional[str] = Field(default=None, description='预警类型')
    content: Optional[str] = Field(default=None, description='预警内容')
    keywords: Optional[str] = Field(default=None, description='关键词')
    status: Optional[int] = Field(default=0, description='状态')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default='', description='创建者')
    update_by: Optional[str] = Field(default='', description='更新者')
    remark: Optional[str] = Field(default=None, description='备注')


class WarningRecordQueryModel(WarningRecordModel):
    """
    预警记录不分页查询模型
    """

    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')
    search_text: Optional[str] = Field(default=None, description='搜索关键词')


@as_query
class WarningRecordPageQueryModel(WarningRecordQueryModel):
    """
    预警记录分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class WarningSchemeModel(BaseModel):
    """
    预警方案表对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='方案ID')
    scheme_name: Optional[str] = Field(default=None, description='方案名称')
    scheme_type: Optional[str] = Field(default='default', description='方案类型')
    description: Optional[str] = Field(default=None, description='方案描述')
    is_active: Optional[bool] = Field(default=True, description='是否启用')
    create_by: Optional[str] = Field(default='', description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[str] = Field(default='', description='更新者')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')

    @NotBlank(field_name='scheme_name', message='方案名称不能为空')
    @Size(field_name='scheme_name', min_length=1, max_length=100, message='方案名称长度不能超过100个字符')
    def get_scheme_name(self):
        return self.scheme_name

    def validate_fields(self):
        self.get_scheme_name()


class WarningSchemeQueryModel(WarningSchemeModel):
    """
    预警方案不分页查询模型
    """

    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


@as_query
class WarningSchemePageQueryModel(WarningSchemeQueryModel):
    """
    预警方案分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class WarningSettingsModel(BaseModel):
    """
    预警设置表对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='设置ID')
    scheme_id: Optional[int] = Field(default=None, description='方案ID')
    platform_types: Optional[List[str]] = Field(default=None, description='平台类型配置')
    content_property: Optional[str] = Field(default='all', description='内容属性')
    info_type: Optional[str] = Field(default='noncomment', description='信息类型')
    match_objects: Optional[List[str]] = Field(default=None, description='匹配对象配置')
    match_method: Optional[str] = Field(default='exact', description='匹配方式')
    publish_regions: Optional[List[str]] = Field(default=None, description='发布地区配置')
    ip_areas: Optional[List[str]] = Field(default=None, description='IP属地配置')
    media_categories: Optional[List[str]] = Field(default=None, description='媒体类别配置')
    article_categories: Optional[List[str]] = Field(default=None, description='文章类别配置')
    create_by: Optional[str] = Field(default='', description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[str] = Field(default='', description='更新者')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')


class WarningSettingsQueryModel(WarningSettingsModel):
    """
    预警设置不分页查询模型
    """

    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


@as_query
class WarningSettingsPageQueryModel(WarningSettingsQueryModel):
    """
    预警设置分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class DeleteWarningRecordModel(BaseModel):
    """
    删除预警记录模型
    """

    record_ids: List[int] = Field(description='需要删除的预警记录ID')


class DeleteWarningSchemeModel(BaseModel):
    """
    删除预警方案模型
    """

    scheme_ids: List[int] = Field(description='需要删除的预警方案ID')


class DeleteWarningSettingsModel(BaseModel):
    """
    删除预警设置模型
    """

    setting_ids: List[int] = Field(description='需要删除的预警设置ID')


class WarningSettingsConfigModel(BaseModel):
    """
    预警设置配置模型（用于前端设置界面）
    """

    scheme_id: Optional[int] = Field(default=None, description='方案ID')
    platform_types: Optional[List[str]] = Field(default=['all'], description='平台类型')
    content_property: Optional[str] = Field(default='all', description='内容属性')
    info_type: Optional[str] = Field(default='noncomment', description='信息类型')
    match_objects: Optional[List[str]] = Field(default=None, description='匹配对象')
    match_method: Optional[str] = Field(default='exact', description='匹配方式')
    publish_regions: Optional[List[str]] = Field(default=None, description='发布地区')
    ip_areas: Optional[List[str]] = Field(default=None, description='IP属地')
    media_categories: Optional[List[str]] = Field(default=None, description='媒体类别')
    article_categories: Optional[List[str]] = Field(default=None, description='文章类别')
    allow_words: Optional[str] = Field(default='', description='允许词')
    reject_words: Optional[str] = Field(default='', description='拒绝词')
    auto_warning_settings: Optional[Dict[str, Any]] = Field(default=None, description='自动预警设置')


class WarningStatisticsModel(BaseModel):
    """
    预警统计信息模型
    """

    total_count: int = Field(default=0, description='总数量')
    pending_count: int = Field(default=0, description='待处理数量')
    processed_count: int = Field(default=0, description='已处理数量')
    urgent_count: int = Field(default=0, description='紧急预警数量')
    negative_count: int = Field(default=0, description='负面预警数量')
    positive_count: int = Field(default=0, description='正面预警数量')


class WarningFrontendDataModel(BaseModel):
    """
    前端页面数据模型
    """

    schemes: List[WarningSchemeModel] = Field(default=[], description='预警方案列表')
    records: List[WarningRecordModel] = Field(default=[], description='预警记录列表')
    statistics: WarningStatisticsModel = Field(default=None, description='统计信息')
    settings: Optional[WarningSettingsConfigModel] = Field(default=None, description='预警设置')
