"""
套餐配置管理
"""

from typing import Dict, Any
import os
from utils.log_util import logger


class PackageConfig:
    """套餐配置类"""
    
    # VIP等级对应的套餐配置（统一使用sys_user.vip_level字段）
    VIP_LEVEL_PACKAGES = {
        0: {
            'name': '基础版',
            'limit': int(os.getenv('DEFAULT_BASIC_LIMIT', '20')),
            'description': '基础用户套餐',
            'membership_status': '基础版会员'
        },
        1: {
            'name': 'VIP1',
            'limit': int(os.getenv('VIP1_LIMIT', '100')),
            'description': 'VIP1会员套餐',
            'membership_status': 'VIP1会员'
        },
        2: {
            'name': 'VIP2',
            'limit': int(os.getenv('VIP2_LIMIT', '500')),
            'description': 'VIP2会员套餐',
            'membership_status': 'VIP2会员'
        },
        3: {
            'name': 'VIP3',
            'limit': int(os.getenv('VIP3_LIMIT', '-1')),  # -1表示无限制
            'description': 'VIP3会员套餐（无限制）',
            'membership_status': 'VIP3会员'
        },
        4: {
            'name': '企业版',
            'limit': int(os.getenv('ENTERPRISE_LIMIT', '-1')),
            'description': '企业版套餐（无限制）',
            'membership_status': '企业版会员'
        }
    }
    
    # 默认套餐配置
    DEFAULT_PACKAGE = {
        'name': '基础版',
        'limit': int(os.getenv('DEFAULT_BASIC_LIMIT', '20')),
        'description': '默认基础套餐',
        'membership_status': '基础版会员'
    }
    
    @classmethod
    def get_package_by_vip_level(cls, vip_level: int) -> Dict[str, Any]:
        """
        根据VIP等级获取套餐配置
        
        :param vip_level: VIP等级
        :return: 套餐配置字典
        """
        package = cls.VIP_LEVEL_PACKAGES.get(vip_level, cls.DEFAULT_PACKAGE)
        logger.info(f"VIP等级 {vip_level} 对应套餐: {package}")
        return package
    
    @classmethod
    def get_default_package(cls) -> Dict[str, Any]:
        """
        获取默认套餐配置
        
        :return: 默认套餐配置
        """
        return cls.DEFAULT_PACKAGE.copy()
    
    @classmethod
    def is_unlimited_package(cls, limit: int) -> bool:
        """
        判断是否为无限制套餐
        
        :param limit: 限制次数
        :return: 是否无限制
        """
        return limit == -1
    
    @classmethod
    def get_membership_status(cls, vip_level: int) -> str:
        """
        根据VIP等级获取会员状态显示

        :param vip_level: VIP等级
        :return: 会员状态文本
        """
        package = cls.VIP_LEVEL_PACKAGES.get(vip_level, cls.DEFAULT_PACKAGE)
        return package.get('membership_status', '基础版会员')

    @classmethod
    def format_limit_display(cls, limit: int) -> str:
        """
        格式化限制次数显示

        :param limit: 限制次数
        :return: 格式化后的显示文本
        """
        if cls.is_unlimited_package(limit):
            return "无限制"
        return f"{limit}次"
