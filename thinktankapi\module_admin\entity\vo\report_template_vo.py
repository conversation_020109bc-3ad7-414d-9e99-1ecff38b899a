from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel


class ReportTemplateModel(BaseModel):
    """
    报告模板模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='模板ID')
    name: str = Field(description='模板名称')
    template_type: str = Field(description='模板类型：normal-普通模板，competitor-竞对模板')
    description: Optional[str] = Field(default=None, description='模板描述')
    template_content: Optional[str] = Field(default=None, description='模板内容（JSON格式）')
    is_active: Optional[bool] = Field(default=True, description='是否启用')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default=None, description='创建者')
    update_by: Optional[str] = Field(default=None, description='更新者')
    remark: Optional[str] = Field(default=None, description='备注')


class ReportTemplatePageQueryModel(BaseModel):
    """
    报告模板分页查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    page_num: int = Field(default=1, description='页码')
    page_size: int = Field(default=10, description='每页数量')
    name: Optional[str] = Field(default=None, description='模板名称')
    template_type: Optional[str] = Field(default=None, description='模板类型')
    is_active: Optional[bool] = Field(default=None, description='是否启用')

    @classmethod
    def as_query(cls, 
                 page_num: int = 1,
                 page_size: int = 10,
                 name: Optional[str] = None,
                 template_type: Optional[str] = None,
                 is_active: Optional[bool] = None):
        return cls(
            page_num=page_num,
            page_size=page_size,
            name=name,
            template_type=template_type,
            is_active=is_active
        )


class CreateReportTemplateModel(BaseModel):
    """
    创建报告模板模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    name: str = Field(description='模板名称')
    template_type: str = Field(description='模板类型：normal-普通模板，competitor-竞对模板')
    description: Optional[str] = Field(default=None, description='模板描述')
    template_content: Optional[str] = Field(default=None, description='模板内容（JSON格式）')
    remark: Optional[str] = Field(default=None, description='备注')


class DeleteReportTemplateModel(BaseModel):
    """
    删除报告模板模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    template_ids: str = Field(description='模板ID列表，逗号分隔')


class ReportTemplateListModel(BaseModel):
    """
    报告模板列表模型（简化版）
    """
    model_config = ConfigDict(alias_generator=to_camel)

    id: int = Field(description='模板ID')
    name: str = Field(description='模板名称')
    template_type: str = Field(description='模板类型')
    create_time: datetime = Field(description='创建时间')
    is_active: bool = Field(description='是否启用')
