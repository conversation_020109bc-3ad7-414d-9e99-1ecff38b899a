import request from '@/utils/request'

// 报告中心API接口

// ==================== 方案管理接口 ====================

/**
 * 获取方案列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getSchemeList(params) {
  return request({
    url: '/report/scheme/list',
    method: 'get',
    params
  })
}

/**
 * 获取方案详情
 * @param {number} schemeId 方案ID
 * @returns {Promise}
 */
export function getSchemeDetail(schemeId) {
  return request({
    url: `/report/scheme/${schemeId}`,
    method: 'get'
  })
}

/**
 * 获取方案类型列表
 * @returns {Promise}
 */
export function getSchemeTypeList() {
  return request({
    url: '/report/scheme-type/list',
    method: 'get'
  })
}

/**
 * 获取侧边栏菜单分类
 * @returns {Promise}
 */
export function getMenuCategories() {
  return request({
    url: '/report/menu/categories',
    method: 'get'
  })
}

/**
 * 新增方案
 * @param {Object} data 方案数据
 * @returns {Promise}
 */
export function addScheme(data) {
  return request({
    url: '/report/scheme',
    method: 'post',
    data
  })
}

/**
 * 编辑方案
 * @param {Object} data 方案数据
 * @returns {Promise}
 */
export function editScheme(data) {
  return request({
    url: '/report/scheme',
    method: 'put',
    data
  })
}

/**
 * 删除方案
 * @param {Object} data 删除参数
 * @returns {Promise}
 */
export function deleteScheme(data) {
  return request({
    url: '/report/scheme',
    method: 'delete',
    data
  })
}

// ==================== 报告模板管理接口 ====================

/**
 * 获取模板列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getTemplateList(params) {
  return request({
    url: '/report/template/list',
    method: 'get',
    params
  })
}

/**
 * 获取模板详情
 * @param {number} templateId 模板ID
 * @returns {Promise}
 */
export function getTemplateDetail(templateId) {
  return request({
    url: `/report/template/${templateId}`,
    method: 'get'
  })
}

/**
 * 根据类型获取模板列表
 * @param {string} templateType 模板类型
 * @returns {Promise}
 */
export function getTemplateByType(templateType) {
  return request({
    url: `/report/template/type/${templateType}`,
    method: 'get'
  })
}

/**
 * 新增模板
 * @param {Object} data 模板数据
 * @returns {Promise}
 */
export function addTemplate(data) {
  return request({
    url: '/report/template',
    method: 'post',
    data
  })
}

/**
 * 编辑模板
 * @param {Object} data 模板数据
 * @returns {Promise}
 */
export function editTemplate(data) {
  return request({
    url: '/report/template',
    method: 'put',
    data
  })
}

/**
 * 删除模板
 * @param {Object} data 删除参数
 * @returns {Promise}
 */
export function deleteTemplate(data) {
  return request({
    url: '/report/template',
    method: 'delete',
    data
  })
}

// ==================== 关键词设置接口 ====================

/**
 * 获取方案关键词设置
 * @param {number} schemeId 方案ID
 * @returns {Promise}
 */
export function getSchemeKeywords(schemeId) {
  return request({
    url: `/report/scheme/${schemeId}/keywords`,
    method: 'get'
  })
}

/**
 * 更新方案关键词设置
 * @param {number} schemeId 方案ID
 * @param {Object} data 关键词数据
 * @returns {Promise}
 */
export function updateSchemeKeywords(schemeId, data) {
  return request({
    url: `/report/scheme/${schemeId}/keywords`,
    method: 'put',
    data
  })
}
