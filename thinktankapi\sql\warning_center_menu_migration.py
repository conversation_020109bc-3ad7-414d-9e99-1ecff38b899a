#!/usr/bin/env python3
"""
预警中心菜单数据库迁移脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from config.database import AsyncSessionLocal
from module_admin.entity.do.menu_do import SysMenu
from datetime import datetime


async def add_warning_center_menu():
    """添加预警中心菜单"""
    async with AsyncSessionLocal() as db:
        try:
            # 检查是否已存在预警中心菜单
            existing_menu = await db.execute(
                text("SELECT menu_id FROM sys_menu WHERE menu_name = '预警中心' OR path = 'warning-center'")
            )
            if existing_menu.fetchone():
                print("预警中心菜单已存在，跳过添加")
                return

            # 获取当前最大菜单ID
            max_id_result = await db.execute(text("SELECT MAX(menu_id) as max_id FROM sys_menu"))
            max_id = max_id_result.fetchone()[0] or 1000
            new_menu_id = max_id + 1

            # 创建预警中心菜单记录
            warning_menu = SysMenu(
                menu_id=new_menu_id,
                menu_name='预警中心',
                parent_id=0,  # 一级菜单
                order_num=5,  # 排序
                path='warning-center',
                component='warning-center/index',
                query='',
                route_name='WarningCenter',
                is_frame=1,  # 不是外链
                is_cache=0,  # 缓存
                menu_type='C',  # 菜单类型
                visible='0',  # 显示
                status='0',  # 正常
                perms='warning:center:view',  # 权限标识
                icon='warning',  # 图标
                create_by='admin',
                create_time=datetime.now(),
                update_by='admin',
                update_time=datetime.now(),
                remark='预警中心菜单'
            )

            # 添加到数据库
            db.add(warning_menu)
            await db.commit()
            
            print(f"成功添加预警中心菜单，菜单ID: {new_menu_id}")

        except Exception as e:
            await db.rollback()
            print(f"添加预警中心菜单失败: {str(e)}")
            raise


async def main():
    """主函数"""
    print("开始添加预警中心菜单...")
    await add_warning_center_menu()
    print("预警中心菜单添加完成")


if __name__ == "__main__":
    asyncio.run(main())
