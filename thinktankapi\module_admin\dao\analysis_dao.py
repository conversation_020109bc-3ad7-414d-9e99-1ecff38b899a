from sqlalchemy import and_, or_, desc, asc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from module_admin.entity.do.analysis_do import EmotionAnalysisSummaryDO, PlatformAnalysisSummaryDO
from module_admin.entity.do.scheme_do import SchemeSummaryStatisticDO
from module_admin.entity.vo.analysis_vo import AnalysisPageQueryModel
from utils.page_util import PageUtil


class EmotionAnalysisDao:
    """
    情感分析数据访问层
    """

    @classmethod
    async def get_emotion_analysis_list(
        cls,
        query_db: AsyncSession,
        query_object: AnalysisPageQueryModel,
        is_page: bool = False
    ) -> List[EmotionAnalysisSummaryDO]:
        """
        根据查询参数获取情感分析列表
        """
        query = select(EmotionAnalysisSummaryDO)
        
        # 构建查询条件
        conditions = []
        
        if query_object.scheme_id:
            conditions.append(EmotionAnalysisSummaryDO.scheme_id == query_object.scheme_id)
        
        if query_object.start_time:
            conditions.append(EmotionAnalysisSummaryDO.date_range_start >= query_object.start_time)
        
        if query_object.end_time:
            conditions.append(EmotionAnalysisSummaryDO.date_range_end <= query_object.end_time)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 排序
        query = query.order_by(desc(EmotionAnalysisSummaryDO.create_time))
        
        # 分页
        if is_page:
            page_obj = PageUtil(query_object.page_num, query_object.page_size)
            query = query.offset(page_obj.start).limit(page_obj.limit)
        
        result = await query_db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_emotion_analysis_count(
        cls,
        query_db: AsyncSession,
        query_object: AnalysisPageQueryModel
    ) -> int:
        """
        根据查询参数获取情感分析总数
        """
        query = select(func.count(EmotionAnalysisSummaryDO.id))
        
        # 构建查询条件
        conditions = []
        
        if query_object.scheme_id:
            conditions.append(EmotionAnalysisSummaryDO.scheme_id == query_object.scheme_id)
        
        if query_object.start_time:
            conditions.append(EmotionAnalysisSummaryDO.date_range_start >= query_object.start_time)
        
        if query_object.end_time:
            conditions.append(EmotionAnalysisSummaryDO.date_range_end <= query_object.end_time)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await query_db.execute(query)
        return result.scalar()

    @classmethod
    async def get_emotion_statistics_by_scheme(
        cls,
        query_db: AsyncSession,
        scheme_id: int,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        根据方案ID获取情感统计数据
        """
        query = select(
            EmotionAnalysisSummaryDO.emotion_id,
            func.sum(EmotionAnalysisSummaryDO.count).label('total_count')
        ).where(EmotionAnalysisSummaryDO.scheme_id == scheme_id)
        
        if start_time:
            query = query.where(EmotionAnalysisSummaryDO.date_range_start >= start_time)
        
        if end_time:
            query = query.where(EmotionAnalysisSummaryDO.date_range_end <= end_time)
        
        query = query.group_by(EmotionAnalysisSummaryDO.emotion_id)
        query = query.order_by(desc('total_count'))
        
        result = await query_db.execute(query)
        return [{'emotion_id': row.emotion_id, 'count': row.total_count} for row in result]


class PlatformAnalysisDao:
    """
    平台分析数据访问层
    """

    @classmethod
    async def get_platform_analysis_list(
        cls,
        query_db: AsyncSession,
        query_object: AnalysisPageQueryModel,
        is_page: bool = False
    ) -> List[PlatformAnalysisSummaryDO]:
        """
        根据查询参数获取平台分析列表
        """
        query = select(PlatformAnalysisSummaryDO)
        
        # 构建查询条件
        conditions = []
        
        if query_object.scheme_id:
            conditions.append(PlatformAnalysisSummaryDO.scheme_id == query_object.scheme_id)
        
        if query_object.start_time:
            conditions.append(PlatformAnalysisSummaryDO.date_range_start >= query_object.start_time)
        
        if query_object.end_time:
            conditions.append(PlatformAnalysisSummaryDO.date_range_end <= query_object.end_time)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 排序
        query = query.order_by(desc(PlatformAnalysisSummaryDO.create_time))
        
        # 分页
        if is_page:
            page_obj = PageUtil(query_object.page_num, query_object.page_size)
            query = query.offset(page_obj.start).limit(page_obj.limit)
        
        result = await query_db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_platform_analysis_count(
        cls,
        query_db: AsyncSession,
        query_object: AnalysisPageQueryModel
    ) -> int:
        """
        根据查询参数获取平台分析总数
        """
        query = select(func.count(PlatformAnalysisSummaryDO.id))
        
        # 构建查询条件
        conditions = []
        
        if query_object.scheme_id:
            conditions.append(PlatformAnalysisSummaryDO.scheme_id == query_object.scheme_id)
        
        if query_object.start_time:
            conditions.append(PlatformAnalysisSummaryDO.date_range_start >= query_object.start_time)
        
        if query_object.end_time:
            conditions.append(PlatformAnalysisSummaryDO.date_range_end <= query_object.end_time)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await query_db.execute(query)
        return result.scalar()

    @classmethod
    async def get_platform_statistics_by_scheme(
        cls,
        query_db: AsyncSession,
        scheme_id: int,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        根据方案ID获取平台统计数据
        """
        query = select(
            PlatformAnalysisSummaryDO.platform_id,
            func.sum(PlatformAnalysisSummaryDO.count).label('total_count')
        ).where(PlatformAnalysisSummaryDO.scheme_id == scheme_id)
        
        if start_time:
            query = query.where(PlatformAnalysisSummaryDO.date_range_start >= start_time)
        
        if end_time:
            query = query.where(PlatformAnalysisSummaryDO.date_range_end <= end_time)
        
        query = query.group_by(PlatformAnalysisSummaryDO.platform_id)
        query = query.order_by(desc('total_count'))
        
        result = await query_db.execute(query)
        return [{'platform_id': row.platform_id, 'count': row.total_count} for row in result]


class SummaryStatisticDao:
    """
    汇总统计数据访问层
    """

    @classmethod
    async def get_scheme_statistics(
        cls,
        query_db: AsyncSession,
        scheme_id: int
    ) -> Optional[SchemeSummaryStatisticDO]:
        """
        根据方案ID获取汇总统计数据
        """
        query = select(SchemeSummaryStatisticDO).where(
            SchemeSummaryStatisticDO.scheme_id == scheme_id
        ).order_by(desc(SchemeSummaryStatisticDO.refresh_time))
        
        result = await query_db.execute(query)
        return result.scalar_one_or_none()

    @classmethod
    async def get_trend_data_by_scheme(
        cls,
        query_db: AsyncSession,
        scheme_id: int,
        days: int = 7
    ) -> List[Dict[str, Any]]:
        """
        根据方案ID获取趋势数据
        """
        # 这里需要根据实际的数据结构来实现
        # 暂时返回空列表，实际实现时需要查询相关的趋势数据表
        return []
