from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, DECIMAL, ForeignKey
from sqlalchemy.orm import relationship
from config.database import Base


class ReportEmotionStatisticsDO(Base):
    """
    报告情感统计数据对象
    用于存储每个分析报告的情感统计信息
    """
    __tablename__ = 'report_emotion_statistics'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='统计ID')
    report_id = Column(String(100), unique=True, nullable=False, comment='报告ID（关联到具体的分析报告实例）')
    scheme_id = Column(Integer, ForeignKey('scheme.id'), nullable=True, comment='关联的方案ID（可选，用于关联到方案）')
    positive_count = Column(Integer, default=0, comment='正面情感数量')
    neutral_count = Column(Integer, default=0, comment='中性情感数量')
    negative_count = Column(Integer, default=0, comment='负面情感数量')
    total_count = Column(Integer, default=0, comment='总数量（正面+中性+负面）')
    positive_percentage = Column(DECIMAL(5, 2), default=0.00, comment='正面情感占比（%）')
    neutral_percentage = Column(DECIMAL(5, 2), default=0.00, comment='中性情感占比（%）')
    negative_percentage = Column(DECIMAL(5, 2), default=0.00, comment='负面情感占比（%）')
    statistics_start_time = Column(DateTime, nullable=True, comment='统计时间范围开始')
    statistics_end_time = Column(DateTime, nullable=True, comment='统计时间范围结束')
    data_source = Column(String(100), nullable=True, comment='数据来源（如：在线搜索、自定义数据源等）')
    analysis_keywords = Column(Text, nullable=True, comment='分析关键词（JSON格式）')
    report_type = Column(String(50), default='normal', comment='报告类型（normal-普通报告，competitor-竞对报告）')
    status = Column(String(1), default='0', comment='状态（0正常 1停用）')
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')

    # 关联关系（可选，如果需要与方案表建立关联）
    # scheme = relationship("SchemeDO", back_populates="report_emotion_statistics")

    def __repr__(self):
        return f"<ReportEmotionStatistics(id={self.id}, report_id='{self.report_id}', " \
               f"positive={self.positive_count}, neutral={self.neutral_count}, negative={self.negative_count})>"

    def to_dict(self):
        """
        转换为字典格式
        """
        return {
            'id': self.id,
            'report_id': self.report_id,
            'scheme_id': self.scheme_id,
            'positive_count': self.positive_count,
            'neutral_count': self.neutral_count,
            'negative_count': self.negative_count,
            'total_count': self.total_count,
            'positive_percentage': float(self.positive_percentage) if self.positive_percentage else 0.0,
            'neutral_percentage': float(self.neutral_percentage) if self.neutral_percentage else 0.0,
            'negative_percentage': float(self.negative_percentage) if self.negative_percentage else 0.0,
            'statistics_start_time': self.statistics_start_time.isoformat() if self.statistics_start_time else None,
            'statistics_end_time': self.statistics_end_time.isoformat() if self.statistics_end_time else None,
            'data_source': self.data_source,
            'analysis_keywords': self.analysis_keywords,
            'report_type': self.report_type,
            'status': self.status,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None,
            'create_by': self.create_by,
            'update_by': self.update_by,
            'remark': self.remark
        }

    @classmethod
    def calculate_percentages(cls, positive_count: int, neutral_count: int, negative_count: int) -> tuple:
        """
        计算情感占比
        
        :param positive_count: 正面情感数量
        :param neutral_count: 中性情感数量
        :param negative_count: 负面情感数量
        :return: (positive_percentage, neutral_percentage, negative_percentage)
        """
        total = positive_count + neutral_count + negative_count
        if total == 0:
            return 0.0, 0.0, 0.0
        
        positive_pct = round((positive_count / total) * 100, 2)
        neutral_pct = round((neutral_count / total) * 100, 2)
        negative_pct = round((negative_count / total) * 100, 2)
        
        return positive_pct, neutral_pct, negative_pct

    def update_statistics(self, positive_count: int, neutral_count: int, negative_count: int):
        """
        更新统计数据并自动计算占比
        
        :param positive_count: 正面情感数量
        :param neutral_count: 中性情感数量
        :param negative_count: 负面情感数量
        """
        self.positive_count = positive_count
        self.neutral_count = neutral_count
        self.negative_count = negative_count
        self.total_count = positive_count + neutral_count + negative_count
        
        # 计算占比
        positive_pct, neutral_pct, negative_pct = self.calculate_percentages(
            positive_count, neutral_count, negative_count
        )
        self.positive_percentage = positive_pct
        self.neutral_percentage = neutral_pct
        self.negative_percentage = negative_pct
        
        # 更新时间
        self.update_time = datetime.now()

    def get_emotion_summary(self) -> dict:
        """
        获取情感统计摘要
        
        :return: 情感统计摘要字典
        """
        return {
            'total_count': self.total_count,
            'positive': {
                'count': self.positive_count,
                'percentage': float(self.positive_percentage) if self.positive_percentage else 0.0
            },
            'neutral': {
                'count': self.neutral_count,
                'percentage': float(self.neutral_percentage) if self.neutral_percentage else 0.0
            },
            'negative': {
                'count': self.negative_count,
                'percentage': float(self.negative_percentage) if self.negative_percentage else 0.0
            },
            'dominant_emotion': self.get_dominant_emotion()
        }

    def get_dominant_emotion(self) -> str:
        """
        获取主导情感类型
        
        :return: 主导情感类型（positive/neutral/negative）
        """
        if self.total_count == 0:
            return 'neutral'
        
        max_count = max(self.positive_count, self.neutral_count, self.negative_count)
        
        if max_count == self.positive_count:
            return 'positive'
        elif max_count == self.negative_count:
            return 'negative'
        else:
            return 'neutral'

    def is_positive_dominant(self) -> bool:
        """
        判断是否正面情感占主导
        
        :return: 是否正面情感占主导
        """
        return self.get_dominant_emotion() == 'positive'

    def is_negative_dominant(self) -> bool:
        """
        判断是否负面情感占主导
        
        :return: 是否负面情感占主导
        """
        return self.get_dominant_emotion() == 'negative'
