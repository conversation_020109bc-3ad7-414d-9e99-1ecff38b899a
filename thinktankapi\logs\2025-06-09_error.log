2025-06-09 08:48:01.235 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-09 08:48:01.236 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-09 08:48:02.875 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-09 08:48:02.876 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-09 08:48:02.885 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-09 08:48:03.503 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-09 08:48:04.351 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-09 08:48:04.351 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-09 08:48:28.538 | 34cd9a1001194774864c0e847181c1c7 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为bd194a24-334c-4339-a78d-f3ba696c2bc0的会话获取图片验证码成功
2025-06-09 08:48:39.927 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-09 08:48:39.927 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-09 08:48:41.195 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-09 08:48:41.196 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-09 08:48:41.199 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-09 08:48:41.601 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-09 08:48:42.175 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-09 08:48:42.176 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-09 08:49:30.753 | 95ba4ecdbfc349e0a7488db0e0fee7e0 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-09 08:49:31.037 | a2e20c4dbef24ea3983a9f2c0708688b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 08:49:31.261 | 5678863da418470e99d7310de7e8553d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 08:49:37.572 | 10dbbf717607420e85bdd3d1fc90cd66 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 08:49:37.799 | adb59f8d2db04ae6b9edb2b9a2a025d8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 08:49:42.134 | 9b55bb8f94204592ace46482a627e181 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:49:42.134 | 9b55bb8f94204592ace46482a627e181 | INFO     | module_warning.controller.warning_scheme_controller:get_active_warning_schemes:60 - 获取启用预警方案列表成功
2025-06-09 08:49:42.390 | 38f5dfc852434fcd92c4b286a7d8267c | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:49:42.419 | 38f5dfc852434fcd92c4b286a7d8267c | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 08:49:42.557 | 38f5dfc852434fcd92c4b286a7d8267c | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 08:49:42.558 | 38f5dfc852434fcd92c4b286a7d8267c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 08:51:44.799 | a7aee87325844710bb06e9376803f1ef | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为d84c8fbd-9c8f-4d38-8b21-c50d8c60f8b8的会话获取图片验证码成功
2025-06-09 08:51:50.648 | 93f6cc9d2a754f5ebadf4d46ec29a8f3 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为d8702c17-ee8a-476f-a009-2cece5049ed9的会话获取图片验证码成功
2025-06-09 08:52:02.926 | 6b7ad6e079154db9ba43d103004d0249 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为cdd887c9-31da-4a42-9574-5fde7c254e68的会话获取图片验证码成功
2025-06-09 08:53:54.050 | b4ae39a4df3e44039a0d5102734be386 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 08:53:54.241 | add1efd9e8174a54bfa19c6fb668a2ef | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 08:54:30.776 | 946c94b6116d4735b29522967515e1c1 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:54:30.776 | 946c94b6116d4735b29522967515e1c1 | INFO     | module_warning.controller.warning_scheme_controller:get_active_warning_schemes:60 - 获取启用预警方案列表成功
2025-06-09 08:54:31.405 | 18eef6a463eb4e3e91ce29288e0f4541 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:54:31.432 | 18eef6a463eb4e3e91ce29288e0f4541 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 08:54:31.568 | 18eef6a463eb4e3e91ce29288e0f4541 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 08:54:31.568 | 18eef6a463eb4e3e91ce29288e0f4541 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 08:55:30.233 | 2fb9bcb5b6c146cdb17f071221c372c4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 08:55:30.791 | 7cc57eec30204193b3596ddd95b00f75 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 08:55:31.608 | 49d524c68a0c4b128d7f4f81253854d8 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:55:31.608 | 49d524c68a0c4b128d7f4f81253854d8 | INFO     | module_warning.controller.warning_scheme_controller:get_active_warning_schemes:60 - 获取启用预警方案列表成功
2025-06-09 08:55:32.135 | e5c5c1006e764b74933aacb6bb3b12cc | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:55:32.163 | e5c5c1006e764b74933aacb6bb3b12cc | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 08:55:32.295 | e5c5c1006e764b74933aacb6bb3b12cc | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 08:55:32.296 | e5c5c1006e764b74933aacb6bb3b12cc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 08:55:44.575 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-09 08:55:44.577 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-09 08:55:53.292 | 8899847de95d451d90629b68fe78123f | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:55:53.292 | 8899847de95d451d90629b68fe78123f | INFO     | module_warning.controller.warning_scheme_controller:get_active_warning_schemes:60 - 获取启用预警方案列表成功
2025-06-09 08:55:53.888 | 6462b92395e24efbbd3b151befd86cdb | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:55:53.916 | 6462b92395e24efbbd3b151befd86cdb | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 08:55:54.817 | 6462b92395e24efbbd3b151befd86cdb | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 08:55:54.817 | 6462b92395e24efbbd3b151befd86cdb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 08:55:56.611 | 21da2f9bf08640b698c7b75af5f53ca4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 08:55:57.094 | 14d11bb510df4d35bd673fcaf65dfd36 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 08:55:58.055 | 9e3e295253a54b7a9a49a6f68d3c6458 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:55:58.056 | 9e3e295253a54b7a9a49a6f68d3c6458 | INFO     | module_warning.controller.warning_scheme_controller:get_active_warning_schemes:60 - 获取启用预警方案列表成功
2025-06-09 08:55:58.313 | 72f599468f0c41929d35ca9e43f2eb3c | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:55:58.341 | 72f599468f0c41929d35ca9e43f2eb3c | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 08:55:58.477 | 72f599468f0c41929d35ca9e43f2eb3c | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 08:55:58.477 | 72f599468f0c41929d35ca9e43f2eb3c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 08:55:59.399 | 4e910144e3af4d4cb4bf995950011d4e | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:55:59.427 | 4e910144e3af4d4cb4bf995950011d4e | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 08:55:59.558 | 5383e4834dcf4ca8ad3db0ac31999ab6 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:55:59.568 | 4e910144e3af4d4cb4bf995950011d4e | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 08:55:59.569 | 4e910144e3af4d4cb4bf995950011d4e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 08:55:59.588 | 5383e4834dcf4ca8ad3db0ac31999ab6 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 08:55:59.727 | 5383e4834dcf4ca8ad3db0ac31999ab6 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 08:55:59.727 | 5383e4834dcf4ca8ad3db0ac31999ab6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 08:56:00.086 | 39860162fea34bd0aadf38fce34ac91a | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:56:00.114 | 39860162fea34bd0aadf38fce34ac91a | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 08:56:00.253 | 39860162fea34bd0aadf38fce34ac91a | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 08:56:00.253 | 39860162fea34bd0aadf38fce34ac91a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 08:56:00.945 | 111c1e75a1e8457ea638fbb1b1309269 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:56:00.971 | 111c1e75a1e8457ea638fbb1b1309269 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 08:56:01.180 | 7cb0f99e3cc94451b9f6234ee6af8abb | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:56:01.208 | 7cb0f99e3cc94451b9f6234ee6af8abb | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 08:56:01.577 | 7cb0f99e3cc94451b9f6234ee6af8abb | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 08:56:01.577 | 7cb0f99e3cc94451b9f6234ee6af8abb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 08:56:01.584 | 111c1e75a1e8457ea638fbb1b1309269 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 08:56:01.585 | 111c1e75a1e8457ea638fbb1b1309269 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 08:56:02.648 | 25efbd7351ab435c9fd1398ef9bdda68 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 08:56:02.895 | 18172ec1f9aa4e2a9e8475829a9f6c26 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 08:56:03.683 | 8250c2bcfa45460fa393fa3e432f80d3 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:56:03.684 | 8250c2bcfa45460fa393fa3e432f80d3 | INFO     | module_warning.controller.warning_scheme_controller:get_active_warning_schemes:60 - 获取启用预警方案列表成功
2025-06-09 08:56:03.940 | c6b18df161ad4c5199bacaad3ca5d450 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:56:03.967 | c6b18df161ad4c5199bacaad3ca5d450 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 08:56:04.104 | c6b18df161ad4c5199bacaad3ca5d450 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 08:56:04.105 | c6b18df161ad4c5199bacaad3ca5d450 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 08:56:50.268 | 36436406bdf04508b09fe8ee470950c0 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 保存预警设置失败: "WarningSettingsConfigModel" object has no field "create_by"
2025-06-09 08:57:06.812 | 366b7131a34546e08c75fcfac4110db8 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 08:57:07.047 | 58c6ea6b21b542cfb117599eae88e4b6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 08:57:07.568 | 2efea68c9eb3449ca27f96e46a00b154 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:57:07.569 | 2efea68c9eb3449ca27f96e46a00b154 | INFO     | module_warning.controller.warning_scheme_controller:get_active_warning_schemes:60 - 获取启用预警方案列表成功
2025-06-09 08:57:08.113 | 9c7fafc576f94a92bc56c43b58333613 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 08:57:08.142 | 9c7fafc576f94a92bc56c43b58333613 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 08:57:08.759 | 9c7fafc576f94a92bc56c43b58333613 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 08:57:08.760 | 9c7fafc576f94a92bc56c43b58333613 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 08:57:46.488 | 30982865bfc4423c919be39796e57fad | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 保存预警设置失败: "WarningSettingsConfigModel" object has no field "create_by"
2025-06-09 09:00:09.578 | c909d3e96f6040ffa71b8d4c36f0a6a9 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:00:09.606 | c909d3e96f6040ffa71b8d4c36f0a6a9 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:00:09.978 | c909d3e96f6040ffa71b8d4c36f0a6a9 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:00:09.978 | c909d3e96f6040ffa71b8d4c36f0a6a9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:00:11.252 | 7b7911afc06b43868fba50c619865c59 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:00:11.280 | 7b7911afc06b43868fba50c619865c59 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:00:11.650 | 7b7911afc06b43868fba50c619865c59 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:00:11.650 | 7b7911afc06b43868fba50c619865c59 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:01:46.324 | 8e360b06dbca4c1c91d7491188256545 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:01:46.355 | 8e360b06dbca4c1c91d7491188256545 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:01:46.502 | 8e360b06dbca4c1c91d7491188256545 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:01:46.502 | 8e360b06dbca4c1c91d7491188256545 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:01:47.105 | 94e6792ea87d458c90e572e136a1a167 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:01:47.135 | 94e6792ea87d458c90e572e136a1a167 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:01:47.270 | 94e6792ea87d458c90e572e136a1a167 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:01:47.271 | 94e6792ea87d458c90e572e136a1a167 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:02:50.088 | b65707efe1044fdb8c5cbdd2848db82d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 09:02:50.335 | dc9fc6203d8948038537f65428950b12 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 09:02:50.848 | 2811ce8428684665a7660e411e51c4ba | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:02:50.975 | 2811ce8428684665a7660e411e51c4ba | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:02:51.593 | 2811ce8428684665a7660e411e51c4ba | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:02:51.593 | 2811ce8428684665a7660e411e51c4ba | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:02:51.857 | 36df14ae52ed4e899ab1eccbcd587032 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:02:51.886 | 36df14ae52ed4e899ab1eccbcd587032 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:02:52.023 | 36df14ae52ed4e899ab1eccbcd587032 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:02:52.024 | 36df14ae52ed4e899ab1eccbcd587032 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:03:31.421 | 1f07379093ce49d1bd24e0bf27481958 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:03:31.451 | 1f07379093ce49d1bd24e0bf27481958 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:03:31.825 | 1f07379093ce49d1bd24e0bf27481958 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:03:31.825 | 1f07379093ce49d1bd24e0bf27481958 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:03:31.891 | b748906f934d41d5ac1a040d93309790 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:03:31.917 | b748906f934d41d5ac1a040d93309790 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:03:32.054 | b748906f934d41d5ac1a040d93309790 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:03:32.055 | b748906f934d41d5ac1a040d93309790 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:03:32.907 | 658a9c3d9f5e41d5b1945373555ab8d2 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:03:32.935 | 658a9c3d9f5e41d5b1945373555ab8d2 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:03:33.074 | 658a9c3d9f5e41d5b1945373555ab8d2 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:03:33.074 | 658a9c3d9f5e41d5b1945373555ab8d2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:15:59.549 | f94ea8704444403ca52ecd055ca41460 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:15:59.575 | f94ea8704444403ca52ecd055ca41460 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:15:59.703 | f94ea8704444403ca52ecd055ca41460 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:15:59.704 | f94ea8704444403ca52ecd055ca41460 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:16:00.004 | 975b4bcb3641400c9c111e8a36d7a956 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:16:00.031 | 975b4bcb3641400c9c111e8a36d7a956 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:16:00.164 | 975b4bcb3641400c9c111e8a36d7a956 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:16:00.164 | 975b4bcb3641400c9c111e8a36d7a956 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:16:01.300 | c884c0f7270a4fbdb38397fe802599c5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 09:16:01.525 | 63a1a103ea514373b949e0e15f50f0cd | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 09:17:06.827 | c8ed1288b7b8450785c8530cfca60ee3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 09:17:07.045 | d53bd32e9f754d8d98bfbe9c3ae6735f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 09:17:52.384 | 1223d77cc5a64b8eb92378f4cb311f40 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:17:52.410 | 1223d77cc5a64b8eb92378f4cb311f40 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:17:52.549 | 1223d77cc5a64b8eb92378f4cb311f40 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:17:52.549 | 1223d77cc5a64b8eb92378f4cb311f40 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:17:53.118 | 3f34ba9d5d6c452e9858f83c3adcfae6 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:17:53.146 | 3f34ba9d5d6c452e9858f83c3adcfae6 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:17:53.280 | 3f34ba9d5d6c452e9858f83c3adcfae6 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:17:53.281 | 3f34ba9d5d6c452e9858f83c3adcfae6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:19:07.094 | 9a789cda23e04727b24f64a7c17f34e5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 09:19:07.310 | 608b73deb329499d9287e46efb4329f5 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 09:19:07.774 | c784b614976147a794a295fa134ffb79 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:19:07.800 | c784b614976147a794a295fa134ffb79 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:19:07.928 | c784b614976147a794a295fa134ffb79 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:19:07.929 | c784b614976147a794a295fa134ffb79 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:19:08.159 | b3354bcf40f741f9b1ef9568a7c4e204 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:19:08.187 | b3354bcf40f741f9b1ef9568a7c4e204 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:19:08.320 | b3354bcf40f741f9b1ef9568a7c4e204 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:19:08.320 | b3354bcf40f741f9b1ef9568a7c4e204 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:23:42.669 | 3fa989ec727745bbb8586a5e87801747 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:23:42.695 | 3fa989ec727745bbb8586a5e87801747 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:23:42.827 | 3fa989ec727745bbb8586a5e87801747 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:23:42.827 | 3fa989ec727745bbb8586a5e87801747 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:23:43.057 | ab9440312781419fbee184acd9da672d | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:23:43.084 | ab9440312781419fbee184acd9da672d | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:23:43.217 | ab9440312781419fbee184acd9da672d | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:23:43.217 | ab9440312781419fbee184acd9da672d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:28:23.209 | 531e31e6ee924119981bd6ce4f4b4009 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:28:23.237 | 531e31e6ee924119981bd6ce4f4b4009 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:28:23.369 | 531e31e6ee924119981bd6ce4f4b4009 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:28:23.369 | 531e31e6ee924119981bd6ce4f4b4009 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:28:23.597 | 4421563f91024dd19e4137f47da6015b | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:28:23.624 | 4421563f91024dd19e4137f47da6015b | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:28:23.753 | 4421563f91024dd19e4137f47da6015b | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:28:23.753 | 4421563f91024dd19e4137f47da6015b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:28:25.236 | 6b84f56475a841b7bd3ebd038f6f5597 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 09:28:25.432 | 9fa26f98171944a48b99a1b30cd7756a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 09:28:25.882 | 8c9a99c3490e4ead96b68a14ba501acf | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:28:25.909 | 8c9a99c3490e4ead96b68a14ba501acf | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:28:26.044 | 8c9a99c3490e4ead96b68a14ba501acf | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:28:26.044 | 8c9a99c3490e4ead96b68a14ba501acf | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:28:26.593 | c078b4b3fde341c2b8153d17f2a254f1 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:28:26.619 | c078b4b3fde341c2b8153d17f2a254f1 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:28:26.752 | c078b4b3fde341c2b8153d17f2a254f1 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:28:26.753 | c078b4b3fde341c2b8153d17f2a254f1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:28:33.450 | 1fe13a41376245e7b17013dab2ec081c | ERROR    | module_warning.service.warning_record_service:edit_warning_record_services:116 - 编辑预警记录失败: (asyncmy.errors.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`jgthinktanks`.`warning_record`, CONSTRAINT `warning_record_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `scheme` (`id`) ON DELETE CASCADE)')
[SQL: UPDATE warning_record SET scheme_id=%s, warning_type=%s, content=%s, status=%s, update_time=%s, update_by=%s, remark=%s WHERE warning_record.id = %s]
[parameters: (7, '负面', '方太品牌在某平台出现负面评论，用户反映1', 0, datetime.datetime(2025, 6, 9, 9, 28, 33, 369578), 'admin', '自动检测到的负面舆情', 27)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-09 09:28:33.450 | 1fe13a41376245e7b17013dab2ec081c | ERROR    | module_warning.controller.warning_record_controller:edit_warning_record:112 - 编辑预警记录失败: 
2025-06-09 09:28:35.555 | 54b1a30487174c1495560235add78155 | ERROR    | module_warning.service.warning_record_service:edit_warning_record_services:116 - 编辑预警记录失败: (asyncmy.errors.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`jgthinktanks`.`warning_record`, CONSTRAINT `warning_record_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `scheme` (`id`) ON DELETE CASCADE)')
[SQL: UPDATE warning_record SET scheme_id=%s, warning_type=%s, content=%s, status=%s, update_time=%s, update_by=%s, remark=%s WHERE warning_record.id = %s]
[parameters: (7, '负面', '方太品牌在某平台出现负面评论，用户反映1', 0, datetime.datetime(2025, 6, 9, 9, 28, 35, 475110), 'admin', '自动检测到的负面舆情', 27)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-09 09:28:35.555 | 54b1a30487174c1495560235add78155 | ERROR    | module_warning.controller.warning_record_controller:edit_warning_record:112 - 编辑预警记录失败: 
2025-06-09 09:28:36.287 | 87f4e61e4dff4fc8b60e2879e90318c9 | ERROR    | module_warning.service.warning_record_service:edit_warning_record_services:116 - 编辑预警记录失败: (asyncmy.errors.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`jgthinktanks`.`warning_record`, CONSTRAINT `warning_record_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `scheme` (`id`) ON DELETE CASCADE)')
[SQL: UPDATE warning_record SET scheme_id=%s, warning_type=%s, content=%s, status=%s, update_time=%s, update_by=%s, remark=%s WHERE warning_record.id = %s]
[parameters: (7, '负面', '方太品牌在某平台出现负面评论，用户反映1', 0, datetime.datetime(2025, 6, 9, 9, 28, 36, 206775), 'admin', '自动检测到的负面舆情', 27)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-09 09:28:36.287 | 87f4e61e4dff4fc8b60e2879e90318c9 | ERROR    | module_warning.controller.warning_record_controller:edit_warning_record:112 - 编辑预警记录失败: 
2025-06-09 09:28:45.666 | d534d4fa611a4b0293a7844dc99b3347 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 09:28:45.870 | 4a158c0d6c094c2db1d4bef89b224e8a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 09:28:46.843 | af04b64b28b842b1bf1b6a2ca183a24d | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:28:46.872 | af04b64b28b842b1bf1b6a2ca183a24d | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:28:47.008 | af04b64b28b842b1bf1b6a2ca183a24d | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:28:47.008 | af04b64b28b842b1bf1b6a2ca183a24d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:28:47.394 | ce393808f6954622a56a24ccd70e2875 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:28:47.423 | ce393808f6954622a56a24ccd70e2875 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:28:47.559 | ce393808f6954622a56a24ccd70e2875 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:28:47.559 | ce393808f6954622a56a24ccd70e2875 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:28:59.019 | a25f6733914645a79a838a15dfc243f4 | ERROR    | module_warning.service.warning_record_service:edit_warning_record_services:116 - 编辑预警记录失败: (asyncmy.errors.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`jgthinktanks`.`warning_record`, CONSTRAINT `warning_record_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `scheme` (`id`) ON DELETE CASCADE)')
[SQL: UPDATE warning_record SET scheme_id=%s, warning_type=%s, content=%s, status=%s, update_time=%s, update_by=%s, remark=%s WHERE warning_record.id = %s]
[parameters: (7, '负面', '方太品牌在某平台出现负面评论，用户反映产品质1', 0, datetime.datetime(2025, 6, 9, 9, 28, 58, 941583), 'admin', '自动检测到的负面舆情', 27)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-09 09:28:59.020 | a25f6733914645a79a838a15dfc243f4 | ERROR    | module_warning.controller.warning_record_controller:edit_warning_record:112 - 编辑预警记录失败: 
2025-06-09 09:31:16.565 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-09 09:31:16.566 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-09 09:31:21.002 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-09 09:31:21.003 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-09 09:31:21.835 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-09 09:31:21.835 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-09 09:31:21.837 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-09 09:31:22.261 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-09 09:31:22.806 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-09 09:31:22.806 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-09 09:34:46.275 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-09 09:34:46.275 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-09 09:34:48.924 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-09 09:34:48.925 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-09 09:34:49.770 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-09 09:34:49.770 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-09 09:34:49.772 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-09 09:34:49.845 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-09 09:34:49.846 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-09 09:34:50.203 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-09 09:34:50.614 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-09 09:34:50.614 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-09 09:34:50.616 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-09 09:34:50.704 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-09 09:34:50.704 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-09 09:34:51.017 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-09 09:34:51.548 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-09 09:34:51.549 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-09 09:35:23.428 | c2f350d34a2c44afa3e5534429a7e561 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 09:35:23.596 | a129dde902104cea98b23d2fea27e3fd | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 09:35:24.029 | 059c118f8bb042b1aad8aa43839f0ea1 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:35:24.052 | 059c118f8bb042b1aad8aa43839f0ea1 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:35:24.165 | 059c118f8bb042b1aad8aa43839f0ea1 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:35:24.166 | 059c118f8bb042b1aad8aa43839f0ea1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:35:24.674 | d420de3fd7854e6ea256538ee6a1c14d | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:35:24.696 | d420de3fd7854e6ea256538ee6a1c14d | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:35:24.808 | d420de3fd7854e6ea256538ee6a1c14d | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:35:24.808 | d420de3fd7854e6ea256538ee6a1c14d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:35:31.956 | fc0fe8b8fef2412187ae407dc8b1b7d9 | ERROR    | module_warning.service.warning_record_service:edit_warning_record_services:116 - 编辑预警记录失败: (asyncmy.errors.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`jgthinktanks`.`warning_record`, CONSTRAINT `warning_record_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `scheme` (`id`) ON DELETE CASCADE)')
[SQL: UPDATE warning_record SET scheme_id=%s, warning_type=%s, content=%s, status=%s, update_time=%s, update_by=%s, remark=%s WHERE warning_record.id = %s]
[parameters: (7, '负面', '方太品牌在某平台出现负面评论，用户反映产品1', 0, datetime.datetime(2025, 6, 9, 9, 35, 31, 887381), 'admin', '自动检测到的负面舆情', 27)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-09 09:35:31.957 | fc0fe8b8fef2412187ae407dc8b1b7d9 | ERROR    | module_warning.controller.warning_record_controller:edit_warning_record:112 - 编辑预警记录失败: 
2025-06-09 09:35:53.289 | 6ba3f1aa7df4484b91b12de80e4b08ac | ERROR    | module_warning.service.warning_record_service:edit_warning_record_services:116 - 编辑预警记录失败: (asyncmy.errors.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`jgthinktanks`.`warning_record`, CONSTRAINT `warning_record_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `scheme` (`id`) ON DELETE CASCADE)')
[SQL: UPDATE warning_record SET scheme_id=%s, warning_type=%s, content=%s, status=%s, update_time=%s, update_by=%s, remark=%s WHERE warning_record.id = %s]
[parameters: (7, '负面', '方太品牌在某平台出现负面评论，用户反映产品质量问题', 0, datetime.datetime(2025, 6, 9, 9, 35, 53, 225850), 'admin', '自动检测到的负面舆情', 27)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-09 09:35:53.290 | 6ba3f1aa7df4484b91b12de80e4b08ac | ERROR    | module_warning.controller.warning_record_controller:edit_warning_record:112 - 编辑预警记录失败: 
2025-06-09 09:37:20.131 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-09 09:37:20.133 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-09 09:37:22.758 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-09 09:37:22.758 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-09 09:37:23.638 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-09 09:37:23.638 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-09 09:37:23.640 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-09 09:37:24.092 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-09 09:37:24.613 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-09 09:37:24.613 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-09 09:39:43.428 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-09 09:39:43.429 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-09 09:39:46.707 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-09 09:39:46.707 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-09 09:39:47.478 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-09 09:39:47.479 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-09 09:39:47.481 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-09 09:39:47.881 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-09 09:39:47.973 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-09 09:39:47.973 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-09 09:39:48.464 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-09 09:39:48.464 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-09 09:39:51.367 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-09 09:39:51.368 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-09 09:39:52.198 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-09 09:39:52.198 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-09 09:39:52.201 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-09 09:39:52.629 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-09 09:39:53.246 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-09 09:39:53.247 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-09 09:49:36.006 | 1a97800d08694d93a425efac6e04b65c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-09 09:49:36.189 | 7cd7aa9e24e64b4cb0a7039be4b8d164 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-09 09:49:36.638 | c4631d7352c1458fa4d89f75beaaa8a9 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:49:36.666 | c4631d7352c1458fa4d89f75beaaa8a9 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:49:36.793 | c4631d7352c1458fa4d89f75beaaa8a9 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:49:36.793 | c4631d7352c1458fa4d89f75beaaa8a9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:49:37.011 | e213997a16e94814a691ef84024c3863 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-09 09:49:37.036 | e213997a16e94814a691ef84024c3863 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-09 09:49:37.158 | e213997a16e94814a691ef84024c3863 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-09 09:49:37.159 | e213997a16e94814a691ef84024c3863 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-09 09:49:40.800 | 112873deca6c44cc97f2896812c77b68 | ERROR    | module_warning.service.warning_record_service:edit_warning_record_services:116 - 编辑预警记录失败: (asyncmy.errors.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`jgthinktanks`.`warning_record`, CONSTRAINT `warning_record_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `scheme` (`id`) ON DELETE CASCADE)')
[SQL: UPDATE warning_record SET scheme_id=%s, warning_type=%s, content=%s, status=%s, update_time=%s, update_by=%s, remark=%s WHERE warning_record.id = %s]
[parameters: (7, '负面', '方太品牌在某平台出现负面评论，用户反映产品1', 0, datetime.datetime(2025, 6, 9, 9, 49, 40, 729200), 'admin', '自动检测到的负面舆情', 27)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-09 09:49:40.800 | 112873deca6c44cc97f2896812c77b68 | ERROR    | module_warning.controller.warning_record_controller:edit_warning_record:112 - 编辑预警记录失败: 
2025-06-09 09:49:48.954 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-09 09:49:48.955 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
