2025-06-13 08:29:03.265 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-13 08:29:03.265 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 08:29:04.133 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 08:29:04.133 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 08:29:04.136 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 08:29:04.576 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 08:29:05.071 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 08:29:05.071 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-13 08:29:32.992 | 9d0aae3813d04a7a929858ddc65b4499 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为c89cdd93-4b30-4aab-8731-616f3735c68e的会话获取图片验证码成功
2025-06-13 08:29:41.066 | d912624223eb41079236f240869ea508 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-13 08:29:41.284 | b7927aaf288948449ea3b2c9cf1e1265 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 08:29:41.776 | 3c22c543e74540f49e582867d7f03be6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 08:29:45.604 | 62227e349d704314a5561d29d170253a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:29:45.631 | 62227e349d704314a5561d29d170253a | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:29:45.631 | 62227e349d704314a5561d29d170253a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:29:45.631 | 62227e349d704314a5561d29d170253a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 08:29:45.632 | 62227e349d704314a5561d29d170253a | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None
2025-06-13 08:29:45.660 | 62227e349d704314a5561d29d170253a | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:29:45.660 | 62227e349d704314a5561d29d170253a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 5 条
2025-06-13 08:29:45.815 | 62227e349d704314a5561d29d170253a | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 5, 已处理: 0, 紧急: 0, 负面: 4, 正面: 1
2025-06-13 08:29:45.815 | 62227e349d704314a5561d29d170253a | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 08:29:45.815 | 62227e349d704314a5561d29d170253a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=5 pending_count=5 processed_count=0 urgent_count=0 negative_count=4 positive_count=1
2025-06-13 08:29:45.815 | 62227e349d704314a5561d29d170253a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 08:29:46.020 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:29:46.046 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:29:46.046 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:29:46.047 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 08:29:46.047 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None
2025-06-13 08:29:46.047 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 08:29:46.074 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:29:46.074 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 1 条
2025-06-13 08:29:46.233 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 08:29:46.234 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 08:29:46.235 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 08:29:46.288 | 6f55ea6b9ed7463f818006570237cec9 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:29:46.289 | 6f55ea6b9ed7463f818006570237cec9 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:29:46.289 | 6f55ea6b9ed7463f818006570237cec9 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:29:46.289 | 6f55ea6b9ed7463f818006570237cec9 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:29:46.289 | 6f55ea6b9ed7463f818006570237cec9 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:29:46.289 | 6f55ea6b9ed7463f818006570237cec9 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:29:46.289 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 08:29:46.289 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 15 的预警设置成功
2025-06-13 08:29:46.289 | 6f55ea6b9ed7463f818006570237cec9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:29:52.766 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:29:52.794 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:29:52.794 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:29:52.795 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 08:29:52.795 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 08:29:52.795 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 08:29:52.821 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:29:52.821 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 4 条
2025-06-13 08:29:52.970 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 4, 已处理: 0, 紧急: 0, 负面: 3, 正面: 1
2025-06-13 08:29:52.971 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 08:29:52.971 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=4 processed_count=0 urgent_count=0 negative_count=3 positive_count=1
2025-06-13 08:29:53.022 | e1bf9225e03b4544bbd79b3a34a0f56e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:29:53.022 | e1bf9225e03b4544bbd79b3a34a0f56e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:29:53.023 | e1bf9225e03b4544bbd79b3a34a0f56e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:29:53.023 | e1bf9225e03b4544bbd79b3a34a0f56e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:29:53.023 | e1bf9225e03b4544bbd79b3a34a0f56e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:29:53.023 | e1bf9225e03b4544bbd79b3a34a0f56e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:29:53.023 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 08:29:53.024 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 08:29:53.024 | e1bf9225e03b4544bbd79b3a34a0f56e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:29:54.902 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 18, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:29:54.928 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:29:54.928 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:29:54.929 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=18, pageNum=1, pageSize=10
2025-06-13 08:29:54.929 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 18, 记录ID: None, 搜索文本: None
2025-06-13 08:29:54.930 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 18
2025-06-13 08:29:54.956 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:29:54.956 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 0 条
2025-06-13 08:29:55.107 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 18, 总计: 0, 待处理: 0, 已处理: 0, 紧急: 0, 负面: 0, 正面: 0
2025-06-13 08:29:55.108 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 18
2025-06-13 08:29:55.109 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=0 pending_count=0 processed_count=0 urgent_count=0 negative_count=0 positive_count=0
2025-06-13 08:29:55.159 | 3b9134de833d46639a1f02f0b10929eb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:29:55.159 | 3b9134de833d46639a1f02f0b10929eb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:29:55.159 | 3b9134de833d46639a1f02f0b10929eb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:29:55.159 | 3b9134de833d46639a1f02f0b10929eb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:29:55.160 | 3b9134de833d46639a1f02f0b10929eb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:29:55.160 | 3b9134de833d46639a1f02f0b10929eb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:29:55.160 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 18
2025-06-13 08:29:55.161 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 18 的预警设置成功
2025-06-13 08:29:55.161 | 3b9134de833d46639a1f02f0b10929eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 18, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:29:56.919 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 16, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:29:56.944 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:29:56.944 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:29:56.945 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=16, pageNum=1, pageSize=10
2025-06-13 08:29:56.945 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 16, 记录ID: None, 搜索文本: None
2025-06-13 08:29:56.945 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 16
2025-06-13 08:29:56.970 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:29:56.970 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 0 条
2025-06-13 08:29:57.118 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 16, 总计: 0, 待处理: 0, 已处理: 0, 紧急: 0, 负面: 0, 正面: 0
2025-06-13 08:29:57.119 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 16
2025-06-13 08:29:57.120 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=0 pending_count=0 processed_count=0 urgent_count=0 negative_count=0 positive_count=0
2025-06-13 08:29:57.170 | fd2981e82816414592668abedd2cf986 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:29:57.171 | fd2981e82816414592668abedd2cf986 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:29:57.171 | fd2981e82816414592668abedd2cf986 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:29:57.171 | fd2981e82816414592668abedd2cf986 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:29:57.172 | fd2981e82816414592668abedd2cf986 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:29:57.172 | fd2981e82816414592668abedd2cf986 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:29:57.172 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 16
2025-06-13 08:29:57.172 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 16 的预警设置成功
2025-06-13 08:29:57.172 | fd2981e82816414592668abedd2cf986 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 16, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:29:58.333 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 17, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:29:58.358 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:29:58.358 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:29:58.359 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=17, pageNum=1, pageSize=10
2025-06-13 08:29:58.359 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 17, 记录ID: None, 搜索文本: None
2025-06-13 08:29:58.359 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 17
2025-06-13 08:29:58.384 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:29:58.384 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 0 条
2025-06-13 08:29:58.539 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 17, 总计: 0, 待处理: 0, 已处理: 0, 紧急: 0, 负面: 0, 正面: 0
2025-06-13 08:29:58.539 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 17
2025-06-13 08:29:58.539 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=0 pending_count=0 processed_count=0 urgent_count=0 negative_count=0 positive_count=0
2025-06-13 08:29:58.591 | 37049fa222df4ba38b3b107bc4c4c654 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:29:58.591 | 37049fa222df4ba38b3b107bc4c4c654 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:29:58.591 | 37049fa222df4ba38b3b107bc4c4c654 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:29:58.592 | 37049fa222df4ba38b3b107bc4c4c654 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:29:58.592 | 37049fa222df4ba38b3b107bc4c4c654 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:29:58.592 | 37049fa222df4ba38b3b107bc4c4c654 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:29:58.593 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 17
2025-06-13 08:29:58.593 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 17 的预警设置成功
2025-06-13 08:29:58.593 | 37049fa222df4ba38b3b107bc4c4c654 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 17, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:30:20.501 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:30:20.527 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:30:20.528 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:30:20.528 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 08:30:20.528 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None
2025-06-13 08:30:20.529 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 08:30:20.555 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:30:20.555 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 1 条
2025-06-13 08:30:20.708 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 08:30:20.709 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 08:30:20.709 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 08:30:20.761 | cf3a93a7efc14caa992cda9fc1a899f0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:30:20.762 | cf3a93a7efc14caa992cda9fc1a899f0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:30:20.762 | cf3a93a7efc14caa992cda9fc1a899f0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:30:20.762 | cf3a93a7efc14caa992cda9fc1a899f0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:30:20.762 | cf3a93a7efc14caa992cda9fc1a899f0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:30:20.763 | cf3a93a7efc14caa992cda9fc1a899f0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:30:20.763 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 08:30:20.763 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 15 的预警设置成功
2025-06-13 08:30:20.763 | cf3a93a7efc14caa992cda9fc1a899f0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:42:04.023 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:42:04.050 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:42:04.050 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:42:04.050 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 08:42:04.050 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 08:42:04.051 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 08:42:04.076 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:42:04.076 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 4 条
2025-06-13 08:42:04.225 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 4, 已处理: 0, 紧急: 0, 负面: 3, 正面: 1
2025-06-13 08:42:04.226 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 08:42:04.226 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=4 processed_count=0 urgent_count=0 negative_count=3 positive_count=1
2025-06-13 08:42:04.277 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:42:04.278 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:42:04.278 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:42:04.279 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:42:04.279 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:42:04.279 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:42:04.280 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 08:42:04.280 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 08:42:04.281 | 8d9f87b80f334fe8957e7ab4b1e6ef4e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:42:05.905 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:42:05.932 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:42:05.932 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:42:05.933 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 08:42:05.933 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 08:42:05.933 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 08:42:05.959 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:42:05.959 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 4 条
2025-06-13 08:42:06.109 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 4, 已处理: 0, 紧急: 0, 负面: 3, 正面: 1
2025-06-13 08:42:06.109 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 08:42:06.109 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=4 processed_count=0 urgent_count=0 negative_count=3 positive_count=1
2025-06-13 08:42:06.160 | 762a8225a89b4133a4435f8cd50f98e1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:42:06.161 | 762a8225a89b4133a4435f8cd50f98e1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:42:06.161 | 762a8225a89b4133a4435f8cd50f98e1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:42:06.161 | 762a8225a89b4133a4435f8cd50f98e1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:42:06.161 | 762a8225a89b4133a4435f8cd50f98e1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:42:06.162 | 762a8225a89b4133a4435f8cd50f98e1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:42:06.162 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 08:42:06.162 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 08:42:06.162 | 762a8225a89b4133a4435f8cd50f98e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:42:09.432 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:42:09.457 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:42:09.457 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:42:09.457 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 08:42:09.458 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 08:42:09.458 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 08:42:09.483 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:42:09.483 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 4 条
2025-06-13 08:42:09.632 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 4, 已处理: 0, 紧急: 0, 负面: 3, 正面: 1
2025-06-13 08:42:09.632 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 08:42:09.632 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=4 processed_count=0 urgent_count=0 negative_count=3 positive_count=1
2025-06-13 08:42:09.682 | 64449802431e4ab1bbd666aa0fa9f734 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:42:09.682 | 64449802431e4ab1bbd666aa0fa9f734 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:42:09.682 | 64449802431e4ab1bbd666aa0fa9f734 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:42:09.683 | 64449802431e4ab1bbd666aa0fa9f734 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:42:09.683 | 64449802431e4ab1bbd666aa0fa9f734 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:42:09.683 | 64449802431e4ab1bbd666aa0fa9f734 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:42:09.683 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 08:42:09.683 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 08:42:09.683 | 64449802431e4ab1bbd666aa0fa9f734 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:42:11.316 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:42:11.342 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:42:11.342 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:42:11.342 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 08:42:11.343 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 08:42:11.343 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 08:42:11.368 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:42:11.368 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 4 条
2025-06-13 08:42:11.516 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 4, 已处理: 0, 紧急: 0, 负面: 3, 正面: 1
2025-06-13 08:42:11.517 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 08:42:11.517 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=4 processed_count=0 urgent_count=0 negative_count=3 positive_count=1
2025-06-13 08:42:11.568 | a64f8be5e06f4bf1aa7b88471b156153 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:42:11.568 | a64f8be5e06f4bf1aa7b88471b156153 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:42:11.568 | a64f8be5e06f4bf1aa7b88471b156153 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:42:11.569 | a64f8be5e06f4bf1aa7b88471b156153 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:42:11.569 | a64f8be5e06f4bf1aa7b88471b156153 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:42:11.569 | a64f8be5e06f4bf1aa7b88471b156153 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:42:11.570 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 08:42:11.570 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 08:42:11.570 | a64f8be5e06f4bf1aa7b88471b156153 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:42:15.817 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:42:15.844 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:42:15.844 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:42:15.845 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 08:42:15.845 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 08:42:15.845 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 08:42:15.871 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:42:15.871 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 4 条
2025-06-13 08:42:16.027 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 4, 已处理: 0, 紧急: 0, 负面: 3, 正面: 1
2025-06-13 08:42:16.028 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 08:42:16.028 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=4 processed_count=0 urgent_count=0 negative_count=3 positive_count=1
2025-06-13 08:42:16.080 | 679ccd4e9d51470c94051bdaef1dbd64 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:42:16.080 | 679ccd4e9d51470c94051bdaef1dbd64 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:42:16.080 | 679ccd4e9d51470c94051bdaef1dbd64 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:42:16.081 | 679ccd4e9d51470c94051bdaef1dbd64 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:42:16.081 | 679ccd4e9d51470c94051bdaef1dbd64 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:42:16.082 | 679ccd4e9d51470c94051bdaef1dbd64 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:42:16.082 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 08:42:16.083 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 08:42:16.083 | 679ccd4e9d51470c94051bdaef1dbd64 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:51:19.155 | 2ffa774580e14b398557af07cb65473d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 08:51:19.346 | d6139747ee8e4c10916ad75e638d14c8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 08:51:19.839 | 748e5d2b3da741ca82924e5ad26d38e8 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:51:19.865 | 748e5d2b3da741ca82924e5ad26d38e8 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:51:19.866 | 748e5d2b3da741ca82924e5ad26d38e8 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:51:19.866 | 748e5d2b3da741ca82924e5ad26d38e8 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 08:51:19.866 | 748e5d2b3da741ca82924e5ad26d38e8 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None
2025-06-13 08:51:19.893 | 748e5d2b3da741ca82924e5ad26d38e8 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:51:19.893 | 748e5d2b3da741ca82924e5ad26d38e8 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 5 条
2025-06-13 08:51:20.047 | 748e5d2b3da741ca82924e5ad26d38e8 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 5, 已处理: 0, 紧急: 0, 负面: 4, 正面: 1
2025-06-13 08:51:20.047 | 748e5d2b3da741ca82924e5ad26d38e8 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 08:51:20.048 | 748e5d2b3da741ca82924e5ad26d38e8 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=5 pending_count=5 processed_count=0 urgent_count=0 negative_count=4 positive_count=1
2025-06-13 08:51:20.048 | 748e5d2b3da741ca82924e5ad26d38e8 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 08:51:20.271 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:51:20.298 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:51:20.299 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:51:20.299 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 08:51:20.300 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None
2025-06-13 08:51:20.301 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 08:51:20.327 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:51:20.327 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 1 条
2025-06-13 08:51:20.480 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 08:51:20.481 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 08:51:20.482 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 08:51:20.534 | 58a6868c26c94c6e9e3ff240e93633ca | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:51:20.535 | 58a6868c26c94c6e9e3ff240e93633ca | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:51:20.535 | 58a6868c26c94c6e9e3ff240e93633ca | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:51:20.536 | 58a6868c26c94c6e9e3ff240e93633ca | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:51:20.536 | 58a6868c26c94c6e9e3ff240e93633ca | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:51:20.536 | 58a6868c26c94c6e9e3ff240e93633ca | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:51:20.536 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 08:51:20.537 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 15 的预警设置成功
2025-06-13 08:51:20.537 | 58a6868c26c94c6e9e3ff240e93633ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:51:23.007 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:51:23.034 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:51:23.035 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:51:23.036 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 08:51:23.036 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None
2025-06-13 08:51:23.037 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 08:51:23.062 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:51:23.062 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 1 条
2025-06-13 08:51:23.211 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 08:51:23.212 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 08:51:23.213 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 08:51:23.263 | 26123f1f5abd4c2ebff3b1b127a4d2ff | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:51:23.263 | 26123f1f5abd4c2ebff3b1b127a4d2ff | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:51:23.264 | 26123f1f5abd4c2ebff3b1b127a4d2ff | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:51:23.264 | 26123f1f5abd4c2ebff3b1b127a4d2ff | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:51:23.264 | 26123f1f5abd4c2ebff3b1b127a4d2ff | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:51:23.264 | 26123f1f5abd4c2ebff3b1b127a4d2ff | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:51:23.265 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 08:51:23.265 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 15 的预警设置成功
2025-06-13 08:51:23.265 | 26123f1f5abd4c2ebff3b1b127a4d2ff | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:59:22.521 | b46f929bc8fc48739a77beb1198f2bc7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:59:22.547 | b46f929bc8fc48739a77beb1198f2bc7 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:59:22.547 | b46f929bc8fc48739a77beb1198f2bc7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:59:22.547 | b46f929bc8fc48739a77beb1198f2bc7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 08:59:22.548 | b46f929bc8fc48739a77beb1198f2bc7 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None
2025-06-13 08:59:22.573 | b46f929bc8fc48739a77beb1198f2bc7 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:59:22.573 | b46f929bc8fc48739a77beb1198f2bc7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 5 条
2025-06-13 08:59:22.723 | b46f929bc8fc48739a77beb1198f2bc7 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 5, 已处理: 0, 紧急: 0, 负面: 4, 正面: 1
2025-06-13 08:59:22.724 | b46f929bc8fc48739a77beb1198f2bc7 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 08:59:22.724 | b46f929bc8fc48739a77beb1198f2bc7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=5 pending_count=5 processed_count=0 urgent_count=0 negative_count=4 positive_count=1
2025-06-13 08:59:22.724 | b46f929bc8fc48739a77beb1198f2bc7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 08:59:22.921 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:59:22.946 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:59:22.946 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:59:22.946 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 08:59:22.947 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None
2025-06-13 08:59:22.947 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 08:59:22.973 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:59:22.973 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 1 条
2025-06-13 08:59:23.120 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 08:59:23.120 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 08:59:23.121 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 08:59:23.170 | 6794ce9b93df48078d7e4ce7fc1e8c29 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:59:23.170 | 6794ce9b93df48078d7e4ce7fc1e8c29 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:59:23.170 | 6794ce9b93df48078d7e4ce7fc1e8c29 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:59:23.170 | 6794ce9b93df48078d7e4ce7fc1e8c29 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:59:23.170 | 6794ce9b93df48078d7e4ce7fc1e8c29 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:59:23.170 | 6794ce9b93df48078d7e4ce7fc1e8c29 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:59:23.171 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 08:59:23.171 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 15 的预警设置成功
2025-06-13 08:59:23.171 | 6794ce9b93df48078d7e4ce7fc1e8c29 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:59:25.369 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:59:25.397 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:59:25.398 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:59:25.398 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 08:59:25.398 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None
2025-06-13 08:59:25.399 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 08:59:25.424 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:59:25.424 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 1 条
2025-06-13 08:59:25.584 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 08:59:25.585 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 08:59:25.585 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 08:59:25.635 | 472860dcb5c64159b7c71308eae5e6f5 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:59:25.635 | 472860dcb5c64159b7c71308eae5e6f5 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:59:25.636 | 472860dcb5c64159b7c71308eae5e6f5 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:59:25.636 | 472860dcb5c64159b7c71308eae5e6f5 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:59:25.636 | 472860dcb5c64159b7c71308eae5e6f5 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:59:25.636 | 472860dcb5c64159b7c71308eae5e6f5 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:59:25.636 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 08:59:25.636 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 15 的预警设置成功
2025-06-13 08:59:25.636 | 472860dcb5c64159b7c71308eae5e6f5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:59:27.432 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:59:27.458 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:59:27.459 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:59:27.459 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 08:59:27.460 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None
2025-06-13 08:59:27.460 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 08:59:27.486 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:59:27.486 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 1 条
2025-06-13 08:59:27.637 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 08:59:27.639 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 08:59:27.639 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 08:59:27.693 | ad8c03666b4346ebbf5be8f32ab5c15e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:59:27.693 | ad8c03666b4346ebbf5be8f32ab5c15e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:59:27.694 | ad8c03666b4346ebbf5be8f32ab5c15e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:59:27.694 | ad8c03666b4346ebbf5be8f32ab5c15e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:59:27.694 | ad8c03666b4346ebbf5be8f32ab5c15e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:59:27.694 | ad8c03666b4346ebbf5be8f32ab5c15e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:59:27.694 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 08:59:27.694 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 15 的预警设置成功
2025-06-13 08:59:27.694 | ad8c03666b4346ebbf5be8f32ab5c15e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:59:31.399 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:59:31.424 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:59:31.425 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:59:31.425 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 08:59:31.425 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None
2025-06-13 08:59:31.425 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 08:59:31.451 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:59:31.452 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 1 条
2025-06-13 08:59:31.604 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 08:59:31.604 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 08:59:31.605 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 08:59:31.656 | 9afa889f334d4f5a9dda25d8b6378f9c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:59:31.656 | 9afa889f334d4f5a9dda25d8b6378f9c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:59:31.657 | 9afa889f334d4f5a9dda25d8b6378f9c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:59:31.657 | 9afa889f334d4f5a9dda25d8b6378f9c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:59:31.657 | 9afa889f334d4f5a9dda25d8b6378f9c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:59:31.657 | 9afa889f334d4f5a9dda25d8b6378f9c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:59:31.657 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 08:59:31.657 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 15 的预警设置成功
2025-06-13 08:59:31.657 | 9afa889f334d4f5a9dda25d8b6378f9c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:59:38.424 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:59:38.449 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:59:38.450 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:59:38.450 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 08:59:38.450 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 08:59:38.451 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 08:59:38.476 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:59:38.477 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 4 条
2025-06-13 08:59:38.628 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 4, 已处理: 0, 紧急: 0, 负面: 3, 正面: 1
2025-06-13 08:59:38.629 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 08:59:38.629 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=4 processed_count=0 urgent_count=0 negative_count=3 positive_count=1
2025-06-13 08:59:38.680 | c88c4e6fae2d42b48607796a10ce0800 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:59:38.681 | c88c4e6fae2d42b48607796a10ce0800 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:59:38.681 | c88c4e6fae2d42b48607796a10ce0800 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:59:38.682 | c88c4e6fae2d42b48607796a10ce0800 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:59:38.682 | c88c4e6fae2d42b48607796a10ce0800 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:59:38.682 | c88c4e6fae2d42b48607796a10ce0800 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:59:38.682 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 08:59:38.682 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 08:59:38.683 | c88c4e6fae2d42b48607796a10ce0800 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:59:41.969 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:59:41.994 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:59:41.995 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:59:41.996 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 08:59:41.996 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 08:59:41.997 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 08:59:42.023 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:59:42.024 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 4 条
2025-06-13 08:59:42.172 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 4, 已处理: 0, 紧急: 0, 负面: 3, 正面: 1
2025-06-13 08:59:42.173 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 08:59:42.173 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=4 processed_count=0 urgent_count=0 negative_count=3 positive_count=1
2025-06-13 08:59:42.224 | 3c61484f314845c3a78f03a437698aa6 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:59:42.224 | 3c61484f314845c3a78f03a437698aa6 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:59:42.224 | 3c61484f314845c3a78f03a437698aa6 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:59:42.224 | 3c61484f314845c3a78f03a437698aa6 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:59:42.225 | 3c61484f314845c3a78f03a437698aa6 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:59:42.225 | 3c61484f314845c3a78f03a437698aa6 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:59:42.225 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 08:59:42.225 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 08:59:42.225 | 3c61484f314845c3a78f03a437698aa6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:59:53.664 | b8eb35c97e824980b6e981407696157f | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:100 - 开始编辑预警记录，输入数据: {'id': 4, 'scheme_id': None, 'warning_type': None, 'content': '行业媒体报道该品牌参加某展会，展示了最新技术成果', 'keywords': None, 'status': 1, 'create_time': None, 'update_time': datetime.datetime(2025, 6, 13, 8, 59, 53, 662913), 'create_by': '', 'update_by': 'admin', 'remark': ''}
2025-06-13 08:59:53.692 | b8eb35c97e824980b6e981407696157f | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:111 - 找到现有记录: ID=4, scheme_id=14
2025-06-13 08:59:53.693 | b8eb35c97e824980b6e981407696157f | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:118 - 准备更新的数据: {'id': 4, 'content': '行业媒体报道该品牌参加某展会，展示了最新技术成果', 'status': 1, 'update_time': datetime.datetime(2025, 6, 13, 8, 59, 53, 693295), 'update_by': 'admin', 'remark': ''}
2025-06-13 08:59:53.693 | b8eb35c97e824980b6e981407696157f | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:128 - DAO层更新数据: record_id=4, update_data={'content': '行业媒体报道该品牌参加某展会，展示了最新技术成果', 'status': 1, 'update_time': datetime.datetime(2025, 6, 13, 8, 59, 53, 693295), 'update_by': 'admin', 'remark': ''}
2025-06-13 08:59:53.721 | b8eb35c97e824980b6e981407696157f | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:136 - 数据库更新结果: 影响行数=1
2025-06-13 08:59:53.769 | b8eb35c97e824980b6e981407696157f | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:122 - 编辑预警记录成功，记录ID: 4
2025-06-13 08:59:53.770 | b8eb35c97e824980b6e981407696157f | INFO     | module_warning.controller.warning_record_controller:edit_warning_record:134 - 更新成功
2025-06-13 08:59:54.439 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:59:54.464 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:59:54.464 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:59:54.465 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 08:59:54.465 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 08:59:54.465 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 08:59:54.493 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:59:54.493 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 3 条
2025-06-13 08:59:54.646 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 3, 已处理: 1, 紧急: 0, 负面: 3, 正面: 1
2025-06-13 08:59:54.647 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 08:59:54.647 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=3 processed_count=1 urgent_count=0 negative_count=3 positive_count=1
2025-06-13 08:59:54.699 | f7cc6b543fdd4aa79405cfbcd880045a | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:59:54.700 | f7cc6b543fdd4aa79405cfbcd880045a | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:59:54.700 | f7cc6b543fdd4aa79405cfbcd880045a | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:59:54.700 | f7cc6b543fdd4aa79405cfbcd880045a | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:59:54.700 | f7cc6b543fdd4aa79405cfbcd880045a | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:59:54.700 | f7cc6b543fdd4aa79405cfbcd880045a | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:59:54.700 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 08:59:54.700 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 08:59:54.700 | f7cc6b543fdd4aa79405cfbcd880045a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 08:59:59.603 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 08:59:59.629 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 08:59:59.629 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 08:59:59.630 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 08:59:59.630 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 08:59:59.631 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 08:59:59.656 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 08:59:59.657 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 3 条
2025-06-13 08:59:59.808 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 3, 已处理: 1, 紧急: 0, 负面: 3, 正面: 1
2025-06-13 08:59:59.809 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 08:59:59.809 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=3 processed_count=1 urgent_count=0 negative_count=3 positive_count=1
2025-06-13 08:59:59.861 | 12784f8899834ce791055b1d959142eb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 08:59:59.861 | 12784f8899834ce791055b1d959142eb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 08:59:59.861 | 12784f8899834ce791055b1d959142eb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 08:59:59.861 | 12784f8899834ce791055b1d959142eb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 08:59:59.861 | 12784f8899834ce791055b1d959142eb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 08:59:59.862 | 12784f8899834ce791055b1d959142eb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 08:59:59.862 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 08:59:59.862 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 08:59:59.863 | 12784f8899834ce791055b1d959142eb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:00:02.748 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 09:00:02.773 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:00:02.774 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 09:00:02.774 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:00:02.775 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 09:00:02.776 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:00:02.802 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:00:02.803 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 3 条
2025-06-13 09:00:02.953 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 3, 已处理: 1, 紧急: 0, 负面: 3, 正面: 1
2025-06-13 09:00:02.954 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:00:02.954 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=3 processed_count=1 urgent_count=0 negative_count=3 positive_count=1
2025-06-13 09:00:03.005 | 5b99248ab728437e8df5c0217f2d83d3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:00:03.006 | 5b99248ab728437e8df5c0217f2d83d3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:00:03.006 | 5b99248ab728437e8df5c0217f2d83d3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:00:03.006 | 5b99248ab728437e8df5c0217f2d83d3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:00:03.006 | 5b99248ab728437e8df5c0217f2d83d3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:00:03.007 | 5b99248ab728437e8df5c0217f2d83d3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:00:03.007 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:00:03.007 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 09:00:03.007 | 5b99248ab728437e8df5c0217f2d83d3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:00:41.435 | 5091c9d1697f4d949e75a5fbed8370b4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 09:00:41.631 | 73dc8c840f3c4abaacc58848b3c3755b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 09:00:42.328 | 3b958e776b43407dbc0015f759a02cc9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 09:00:42.353 | 3b958e776b43407dbc0015f759a02cc9 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:00:42.354 | 3b958e776b43407dbc0015f759a02cc9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 09:00:42.354 | 3b958e776b43407dbc0015f759a02cc9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 09:00:42.355 | 3b958e776b43407dbc0015f759a02cc9 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None
2025-06-13 09:00:42.381 | 3b958e776b43407dbc0015f759a02cc9 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:00:42.381 | 3b958e776b43407dbc0015f759a02cc9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 4 条
2025-06-13 09:00:42.529 | 3b958e776b43407dbc0015f759a02cc9 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 4, 已处理: 1, 紧急: 0, 负面: 4, 正面: 1
2025-06-13 09:00:42.530 | 3b958e776b43407dbc0015f759a02cc9 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 09:00:42.530 | 3b958e776b43407dbc0015f759a02cc9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=5 pending_count=4 processed_count=1 urgent_count=0 negative_count=4 positive_count=1
2025-06-13 09:00:42.530 | 3b958e776b43407dbc0015f759a02cc9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 09:00:42.738 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 09:00:42.764 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:00:42.765 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 09:00:42.765 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 09:00:42.765 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None
2025-06-13 09:00:42.766 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 09:00:42.793 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:00:42.793 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 1 条
2025-06-13 09:00:42.945 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 09:00:42.946 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 09:00:42.946 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 09:00:42.999 | 08ccf02ab14f42f2bff5adf2cfee4a37 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:00:43.000 | 08ccf02ab14f42f2bff5adf2cfee4a37 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:00:43.000 | 08ccf02ab14f42f2bff5adf2cfee4a37 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:00:43.000 | 08ccf02ab14f42f2bff5adf2cfee4a37 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:00:43.000 | 08ccf02ab14f42f2bff5adf2cfee4a37 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:00:43.000 | 08ccf02ab14f42f2bff5adf2cfee4a37 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:00:43.000 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 09:00:43.000 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 15 的预警设置成功
2025-06-13 09:00:43.000 | 08ccf02ab14f42f2bff5adf2cfee4a37 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:00:43.860 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 09:00:43.886 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:00:43.886 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 09:00:43.886 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:00:43.886 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 09:00:43.886 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:00:43.913 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:00:43.913 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 3 条
2025-06-13 09:00:44.065 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 3, 已处理: 1, 紧急: 0, 负面: 3, 正面: 1
2025-06-13 09:00:44.065 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:00:44.066 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=3 processed_count=1 urgent_count=0 negative_count=3 positive_count=1
2025-06-13 09:00:44.118 | ad14f2dc0bc94881a4f081d10a795542 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:00:44.118 | ad14f2dc0bc94881a4f081d10a795542 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:00:44.118 | ad14f2dc0bc94881a4f081d10a795542 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:00:44.118 | ad14f2dc0bc94881a4f081d10a795542 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:00:44.118 | ad14f2dc0bc94881a4f081d10a795542 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:00:44.119 | ad14f2dc0bc94881a4f081d10a795542 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:00:44.119 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:00:44.119 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 09:00:44.119 | ad14f2dc0bc94881a4f081d10a795542 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:01:12.504 | da453d1b9edc490d9a6982898c6361ba | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 09:01:12.701 | be33f5afd30f421fa161ba361f423dc4 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 09:01:13.640 | dabb7b3dae814e6fa60a11ad96e003dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 09:01:13.667 | dabb7b3dae814e6fa60a11ad96e003dc | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:01:13.667 | dabb7b3dae814e6fa60a11ad96e003dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 09:01:13.668 | dabb7b3dae814e6fa60a11ad96e003dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 09:01:13.669 | dabb7b3dae814e6fa60a11ad96e003dc | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None
2025-06-13 09:01:13.695 | dabb7b3dae814e6fa60a11ad96e003dc | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:01:13.696 | dabb7b3dae814e6fa60a11ad96e003dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 5 条
2025-06-13 09:01:13.851 | dabb7b3dae814e6fa60a11ad96e003dc | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 5, 已处理: 0, 紧急: 0, 负面: 4, 正面: 1
2025-06-13 09:01:13.852 | dabb7b3dae814e6fa60a11ad96e003dc | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 09:01:13.852 | dabb7b3dae814e6fa60a11ad96e003dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=5 pending_count=5 processed_count=0 urgent_count=0 negative_count=4 positive_count=1
2025-06-13 09:01:13.852 | dabb7b3dae814e6fa60a11ad96e003dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 09:01:14.086 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 09:01:14.115 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:01:14.115 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 09:01:14.116 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 09:01:14.116 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None
2025-06-13 09:01:14.117 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 09:01:14.144 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:01:14.144 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 1 条
2025-06-13 09:01:14.300 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 09:01:14.301 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 09:01:14.301 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 09:01:14.352 | 19219f6950e64aa0af329432ddf75d69 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:01:14.353 | 19219f6950e64aa0af329432ddf75d69 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:01:14.353 | 19219f6950e64aa0af329432ddf75d69 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:01:14.353 | 19219f6950e64aa0af329432ddf75d69 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:01:14.354 | 19219f6950e64aa0af329432ddf75d69 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:01:14.354 | 19219f6950e64aa0af329432ddf75d69 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:01:14.354 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 09:01:14.355 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 15 的预警设置成功
2025-06-13 09:01:14.355 | 19219f6950e64aa0af329432ddf75d69 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:01:16.805 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 09:01:16.830 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:01:16.831 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 09:01:16.831 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:01:16.831 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 09:01:16.832 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:01:16.859 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:01:16.859 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 4 条
2025-06-13 09:01:17.015 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 4, 已处理: 0, 紧急: 0, 负面: 3, 正面: 1
2025-06-13 09:01:17.015 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:01:17.016 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=4 processed_count=0 urgent_count=0 negative_count=3 positive_count=1
2025-06-13 09:01:17.069 | a4bfb5f9f7c6404ab964d019d4416624 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:01:17.069 | a4bfb5f9f7c6404ab964d019d4416624 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:01:17.070 | a4bfb5f9f7c6404ab964d019d4416624 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:01:17.070 | a4bfb5f9f7c6404ab964d019d4416624 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:01:17.071 | a4bfb5f9f7c6404ab964d019d4416624 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:01:17.072 | a4bfb5f9f7c6404ab964d019d4416624 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:01:17.072 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:01:17.072 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 09:01:17.072 | a4bfb5f9f7c6404ab964d019d4416624 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:11:02.113 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:41 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None
2025-06-13 09:11:02.139 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:11:02.139 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:45 - 获取到 5 个启用的预警方案
2025-06-13 09:11:02.139 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:55 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:11:02.140 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None
2025-06-13 09:11:02.140 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:11:02.165 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:11:02.165 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:66 - 查询到预警记录: 2 条
2025-06-13 09:11:02.315 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:231 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:11:02.316 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:11:02.316 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:70 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:11:02.366 | e98f9ccb996e460397605b6d1becb56d | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:11:02.367 | e98f9ccb996e460397605b6d1becb56d | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:11:02.367 | e98f9ccb996e460397605b6d1becb56d | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:11:02.367 | e98f9ccb996e460397605b6d1becb56d | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:11:02.367 | e98f9ccb996e460397605b6d1becb56d | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:11:02.367 | e98f9ccb996e460397605b6d1becb56d | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:11:02.368 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:11:02.368 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:77 - 获取方案 14 的预警设置成功
2025-06-13 09:11:02.368 | e98f9ccb996e460397605b6d1becb56d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:95 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:11:02.501 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 09:11:02.502 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 09:11:06.556 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-13 09:11:06.556 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 09:11:07.381 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 09:11:07.382 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 09:11:07.385 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 09:11:07.949 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 09:11:08.500 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 09:11:08.500 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-13 09:11:08.872 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 1
2025-06-13 09:11:08.898 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:11:08.899 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:11:08.900 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:11:08.900 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 1
2025-06-13 09:11:08.900 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:11:08.900 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 1
2025-06-13 09:11:08.925 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:11:08.925 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:11:09.308 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:11:09.309 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:11:09.309 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:11:09.362 | 9ec1095e3c8a441290b6f5e2a71b7609 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:11:09.362 | 9ec1095e3c8a441290b6f5e2a71b7609 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:11:09.363 | 9ec1095e3c8a441290b6f5e2a71b7609 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:11:09.363 | 9ec1095e3c8a441290b6f5e2a71b7609 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:11:09.363 | 9ec1095e3c8a441290b6f5e2a71b7609 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:11:09.363 | 9ec1095e3c8a441290b6f5e2a71b7609 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:11:09.364 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:11:09.364 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:11:09.364 | 9ec1095e3c8a441290b6f5e2a71b7609 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:11:09.902 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 2
2025-06-13 09:11:10.015 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:11:10.015 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:11:10.016 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:11:10.018 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 2
2025-06-13 09:11:10.018 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:11:10.018 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 2
2025-06-13 09:11:10.043 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:11:10.044 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:11:10.189 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:11:10.190 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:11:10.191 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:11:10.240 | 7412a3e3d5634fba9920b355cf0c1629 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:11:10.241 | 7412a3e3d5634fba9920b355cf0c1629 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:11:10.241 | 7412a3e3d5634fba9920b355cf0c1629 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:11:10.241 | 7412a3e3d5634fba9920b355cf0c1629 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:11:10.242 | 7412a3e3d5634fba9920b355cf0c1629 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:11:10.242 | 7412a3e3d5634fba9920b355cf0c1629 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:11:10.242 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:11:10.242 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:11:10.242 | 7412a3e3d5634fba9920b355cf0c1629 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:11:39.444 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 0
2025-06-13 09:11:39.468 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:11:39.469 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:11:39.469 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:11:39.469 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 0
2025-06-13 09:11:39.469 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:11:39.469 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 0
2025-06-13 09:11:39.494 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:11:39.495 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 2 条
2025-06-13 09:11:39.642 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:11:39.643 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:11:39.643 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:11:39.693 | 4b4aa6064ced48aeab503fc92cdd11e4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:11:39.694 | 4b4aa6064ced48aeab503fc92cdd11e4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:11:39.694 | 4b4aa6064ced48aeab503fc92cdd11e4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:11:39.695 | 4b4aa6064ced48aeab503fc92cdd11e4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:11:39.695 | 4b4aa6064ced48aeab503fc92cdd11e4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:11:39.695 | 4b4aa6064ced48aeab503fc92cdd11e4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:11:39.696 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:11:39.696 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:11:39.696 | 4b4aa6064ced48aeab503fc92cdd11e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:11:42.046 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 1
2025-06-13 09:11:42.069 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:11:42.070 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:11:42.070 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:11:42.070 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 1
2025-06-13 09:11:42.070 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:11:42.070 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 1
2025-06-13 09:11:42.096 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:11:42.097 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:11:42.241 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:11:42.241 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:11:42.242 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:11:42.289 | a8ac8f0f7e2e4ce59ff2df39877e504a | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:11:42.289 | a8ac8f0f7e2e4ce59ff2df39877e504a | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:11:42.290 | a8ac8f0f7e2e4ce59ff2df39877e504a | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:11:42.290 | a8ac8f0f7e2e4ce59ff2df39877e504a | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:11:42.290 | a8ac8f0f7e2e4ce59ff2df39877e504a | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:11:42.291 | a8ac8f0f7e2e4ce59ff2df39877e504a | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:11:42.291 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:11:42.291 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:11:42.292 | a8ac8f0f7e2e4ce59ff2df39877e504a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:11:43.083 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 2
2025-06-13 09:11:43.106 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:11:43.107 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:11:43.107 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:11:43.108 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 2
2025-06-13 09:11:43.108 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:11:43.108 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 2
2025-06-13 09:11:43.133 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:11:43.133 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:11:43.273 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:11:43.273 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:11:43.274 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:11:43.321 | 8c82ee675dcb4182acc91711653fe71b | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:11:43.321 | 8c82ee675dcb4182acc91711653fe71b | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:11:43.321 | 8c82ee675dcb4182acc91711653fe71b | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:11:43.322 | 8c82ee675dcb4182acc91711653fe71b | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:11:43.322 | 8c82ee675dcb4182acc91711653fe71b | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:11:43.322 | 8c82ee675dcb4182acc91711653fe71b | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:11:43.322 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:11:43.322 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:11:43.322 | 8c82ee675dcb4182acc91711653fe71b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:11:50.341 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: aa, ID筛选: None, 状态筛选: 2
2025-06-13 09:11:50.367 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:11:50.367 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:11:50.367 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:11:50.367 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: aa, 状态筛选: 2
2025-06-13 09:11:50.367 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:11:50.368 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 2
2025-06-13 09:11:50.395 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:11:50.395 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 09:11:50.779 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:11:50.779 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:11:50.779 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:11:50.829 | 59f2e03ec7e44634a18cfc4ce2752733 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:11:50.829 | 59f2e03ec7e44634a18cfc4ce2752733 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:11:50.830 | 59f2e03ec7e44634a18cfc4ce2752733 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:11:50.830 | 59f2e03ec7e44634a18cfc4ce2752733 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:11:50.830 | 59f2e03ec7e44634a18cfc4ce2752733 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:11:50.830 | 59f2e03ec7e44634a18cfc4ce2752733 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:11:50.830 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:11:50.830 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:11:50.831 | 59f2e03ec7e44634a18cfc4ce2752733 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:11:51.146 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: aa, ID筛选: None, 状态筛选: None
2025-06-13 09:11:51.173 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:11:51.174 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:11:51.174 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:11:51.174 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: aa, 状态筛选: None
2025-06-13 09:11:51.174 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:11:51.201 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:11:51.201 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 09:11:51.578 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:11:51.578 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:11:51.578 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:11:51.629 | ffdb4f6771444e4a8a17307c57aa6fc0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:11:51.630 | ffdb4f6771444e4a8a17307c57aa6fc0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:11:51.630 | ffdb4f6771444e4a8a17307c57aa6fc0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:11:51.630 | ffdb4f6771444e4a8a17307c57aa6fc0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:11:51.630 | ffdb4f6771444e4a8a17307c57aa6fc0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:11:51.630 | ffdb4f6771444e4a8a17307c57aa6fc0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:11:51.630 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:11:51.631 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:11:51.631 | ffdb4f6771444e4a8a17307c57aa6fc0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:11:53.610 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:11:53.634 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:11:53.634 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:11:53.635 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:11:53.635 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:11:53.635 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:11:53.660 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:11:53.660 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-13 09:11:53.803 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:11:53.804 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:11:53.804 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:11:53.853 | b4afaf7122c0468aa2163a21556c401c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:11:53.853 | b4afaf7122c0468aa2163a21556c401c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:11:53.853 | b4afaf7122c0468aa2163a21556c401c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:11:53.854 | b4afaf7122c0468aa2163a21556c401c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:11:53.854 | b4afaf7122c0468aa2163a21556c401c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:11:53.854 | b4afaf7122c0468aa2163a21556c401c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:11:53.854 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:11:53.854 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:11:53.855 | b4afaf7122c0468aa2163a21556c401c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:11:56.628 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: a, ID筛选: None, 状态筛选: None
2025-06-13 09:11:56.654 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:11:56.654 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:11:56.655 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:11:56.655 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: a, 状态筛选: None
2025-06-13 09:11:56.655 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:11:56.683 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:11:56.683 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 09:11:56.831 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:11:56.831 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:11:56.832 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:11:56.880 | 75a560cd22714c5f986a7e422dc12020 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:11:56.880 | 75a560cd22714c5f986a7e422dc12020 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:11:56.881 | 75a560cd22714c5f986a7e422dc12020 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:11:56.881 | 75a560cd22714c5f986a7e422dc12020 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:11:56.881 | 75a560cd22714c5f986a7e422dc12020 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:11:56.881 | 75a560cd22714c5f986a7e422dc12020 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:11:56.881 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:11:56.881 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:11:56.882 | 75a560cd22714c5f986a7e422dc12020 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:11:57.856 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: A, ID筛选: None, 状态筛选: None
2025-06-13 09:11:57.885 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:11:57.885 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:11:57.886 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:11:57.886 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: A, 状态筛选: None
2025-06-13 09:11:57.886 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:11:57.912 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:11:57.912 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 09:11:58.057 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:11:58.057 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:11:58.058 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:11:58.105 | 3c9fda39679f42bca338879de2edf0d0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:11:58.106 | 3c9fda39679f42bca338879de2edf0d0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:11:58.106 | 3c9fda39679f42bca338879de2edf0d0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:11:58.106 | 3c9fda39679f42bca338879de2edf0d0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:11:58.106 | 3c9fda39679f42bca338879de2edf0d0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:11:58.106 | 3c9fda39679f42bca338879de2edf0d0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:11:58.106 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:11:58.107 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:11:58.107 | 3c9fda39679f42bca338879de2edf0d0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:12:01.177 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:12:01.203 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:12:01.204 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:12:01.204 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:12:01.204 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:12:01.204 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:12:01.230 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:12:01.231 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-13 09:12:01.384 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:12:01.384 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:12:01.384 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:12:01.669 | 179f6b5c6912477c89432a8036de1a21 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:12:01.669 | 179f6b5c6912477c89432a8036de1a21 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:12:01.669 | 179f6b5c6912477c89432a8036de1a21 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:12:01.670 | 179f6b5c6912477c89432a8036de1a21 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:12:01.670 | 179f6b5c6912477c89432a8036de1a21 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:12:01.670 | 179f6b5c6912477c89432a8036de1a21 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:12:01.671 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:12:01.671 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:12:01.671 | 179f6b5c6912477c89432a8036de1a21 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:14:33.934 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: 1, ID筛选: None, 状态筛选: None
2025-06-13 09:14:33.958 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:14:33.958 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:14:33.959 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:14:33.959 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: 1, 状态筛选: None
2025-06-13 09:14:33.959 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:14:33.984 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:14:33.984 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-13 09:14:34.127 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:14:34.128 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:14:34.128 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:14:34.261 | 1be75b1f8dc04e83a39f518b7afbdd3f | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:14:34.261 | 1be75b1f8dc04e83a39f518b7afbdd3f | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:14:34.261 | 1be75b1f8dc04e83a39f518b7afbdd3f | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:14:34.261 | 1be75b1f8dc04e83a39f518b7afbdd3f | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:14:34.261 | 1be75b1f8dc04e83a39f518b7afbdd3f | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:14:34.261 | 1be75b1f8dc04e83a39f518b7afbdd3f | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:14:34.262 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:14:34.262 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:14:34.262 | 1be75b1f8dc04e83a39f518b7afbdd3f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:14:37.668 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: 1, ID筛选: None, 状态筛选: 0
2025-06-13 09:14:37.694 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:14:37.694 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:14:37.695 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:14:37.695 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: 1, 状态筛选: 0
2025-06-13 09:14:37.695 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:14:37.696 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 0
2025-06-13 09:14:37.723 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:14:37.724 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 2 条
2025-06-13 09:14:37.872 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:14:37.873 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:14:37.873 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:14:37.925 | ad75d1da287b445f95c0e352ef026c8e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:14:37.926 | ad75d1da287b445f95c0e352ef026c8e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:14:37.926 | ad75d1da287b445f95c0e352ef026c8e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:14:37.926 | ad75d1da287b445f95c0e352ef026c8e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:14:37.926 | ad75d1da287b445f95c0e352ef026c8e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:14:37.926 | ad75d1da287b445f95c0e352ef026c8e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:14:37.927 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:14:37.927 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:14:37.927 | ad75d1da287b445f95c0e352ef026c8e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:14:40.033 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: 11, ID筛选: None, 状态筛选: 0
2025-06-13 09:14:40.058 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:14:40.059 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:14:40.060 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:14:40.060 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: 11, 状态筛选: 0
2025-06-13 09:14:40.061 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:14:40.061 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 0
2025-06-13 09:14:40.086 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:14:40.086 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 09:14:40.234 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:14:40.235 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:14:40.235 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:14:40.286 | c97a6cf59d44419ba18b5d3dbc9056cb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:14:40.286 | c97a6cf59d44419ba18b5d3dbc9056cb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:14:40.286 | c97a6cf59d44419ba18b5d3dbc9056cb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:14:40.286 | c97a6cf59d44419ba18b5d3dbc9056cb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:14:40.286 | c97a6cf59d44419ba18b5d3dbc9056cb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:14:40.286 | c97a6cf59d44419ba18b5d3dbc9056cb | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:14:40.286 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:14:40.286 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:14:40.287 | c97a6cf59d44419ba18b5d3dbc9056cb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:14:42.962 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 0
2025-06-13 09:14:42.987 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:14:42.988 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:14:42.988 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:14:42.989 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 0
2025-06-13 09:14:42.989 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:14:42.989 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 0
2025-06-13 09:14:43.013 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:14:43.014 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 2 条
2025-06-13 09:14:43.162 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:14:43.163 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:14:43.163 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:14:43.211 | 55058d6e0c5c4c5fa6954ec6259ef208 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:14:43.211 | 55058d6e0c5c4c5fa6954ec6259ef208 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:14:43.212 | 55058d6e0c5c4c5fa6954ec6259ef208 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:14:43.212 | 55058d6e0c5c4c5fa6954ec6259ef208 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:14:43.212 | 55058d6e0c5c4c5fa6954ec6259ef208 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:14:43.212 | 55058d6e0c5c4c5fa6954ec6259ef208 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:14:43.212 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:14:43.212 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:14:43.212 | 55058d6e0c5c4c5fa6954ec6259ef208 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:14:45.594 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: 2, ID筛选: None, 状态筛选: 0
2025-06-13 09:14:45.618 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:14:45.618 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:14:45.619 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:14:45.619 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: 2, 状态筛选: 0
2025-06-13 09:14:45.619 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:14:45.619 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 0
2025-06-13 09:14:45.643 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:14:45.643 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 09:14:46.023 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:14:46.023 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:14:46.023 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:14:46.074 | 43ef496337f04589ba80419859f9c616 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:14:46.075 | 43ef496337f04589ba80419859f9c616 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:14:46.075 | 43ef496337f04589ba80419859f9c616 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:14:46.075 | 43ef496337f04589ba80419859f9c616 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:14:46.075 | 43ef496337f04589ba80419859f9c616 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:14:46.076 | 43ef496337f04589ba80419859f9c616 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:14:46.077 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:14:46.077 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:14:46.077 | 43ef496337f04589ba80419859f9c616 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:14:48.758 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 0
2025-06-13 09:14:48.872 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:14:48.873 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:14:48.873 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:14:48.873 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 0
2025-06-13 09:14:48.874 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:14:48.874 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 0
2025-06-13 09:14:48.898 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:14:48.899 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 2 条
2025-06-13 09:14:49.047 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:14:49.048 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:14:49.048 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:14:49.097 | 642b62c7440c43e7a0e51526cf227649 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:14:49.097 | 642b62c7440c43e7a0e51526cf227649 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:14:49.098 | 642b62c7440c43e7a0e51526cf227649 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:14:49.098 | 642b62c7440c43e7a0e51526cf227649 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:14:49.098 | 642b62c7440c43e7a0e51526cf227649 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:14:49.098 | 642b62c7440c43e7a0e51526cf227649 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:14:49.099 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:14:49.099 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:14:49.099 | 642b62c7440c43e7a0e51526cf227649 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:15:37.717 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: yongh, ID筛选: None, 状态筛选: 0
2025-06-13 09:15:37.744 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:15:37.744 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:15:37.744 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:15:37.745 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: yongh, 状态筛选: 0
2025-06-13 09:15:37.745 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:15:37.745 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 0
2025-06-13 09:15:37.769 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:15:37.770 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 09:15:37.911 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:15:37.911 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:15:37.911 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:15:37.959 | 88ce7f92e1ef450bae9b051ae9f24de0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:15:37.960 | 88ce7f92e1ef450bae9b051ae9f24de0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:15:37.960 | 88ce7f92e1ef450bae9b051ae9f24de0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:15:37.960 | 88ce7f92e1ef450bae9b051ae9f24de0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:15:37.960 | 88ce7f92e1ef450bae9b051ae9f24de0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:15:37.960 | 88ce7f92e1ef450bae9b051ae9f24de0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:15:37.960 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:15:37.961 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:15:37.961 | 88ce7f92e1ef450bae9b051ae9f24de0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:15:38.746 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 0
2025-06-13 09:15:38.770 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:15:38.770 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:15:38.771 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:15:38.771 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 0
2025-06-13 09:15:38.771 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:15:38.771 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 0
2025-06-13 09:15:38.796 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:15:38.796 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 2 条
2025-06-13 09:15:38.942 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:15:38.942 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:15:38.942 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:15:38.993 | ec5941e0458e41619cd808e0eb71175e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:15:38.994 | ec5941e0458e41619cd808e0eb71175e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:15:38.994 | ec5941e0458e41619cd808e0eb71175e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:15:38.994 | ec5941e0458e41619cd808e0eb71175e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:15:38.994 | ec5941e0458e41619cd808e0eb71175e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:15:38.995 | ec5941e0458e41619cd808e0eb71175e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:15:38.995 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:15:38.995 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:15:38.995 | ec5941e0458e41619cd808e0eb71175e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:15:41.167 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: 用户, ID筛选: None, 状态筛选: 0
2025-06-13 09:15:41.192 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:15:41.192 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:15:41.192 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:15:41.193 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: 用户, 状态筛选: 0
2025-06-13 09:15:41.193 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:15:41.193 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 0
2025-06-13 09:15:41.218 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:15:41.218 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:15:41.593 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:15:41.594 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:15:41.594 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:15:41.641 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:15:41.641 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:15:41.642 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:15:41.642 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:15:41.642 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:15:41.642 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:15:41.642 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:15:41.642 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:15:41.642 | 0d3a93c46a3d4d258ee793fe01bb7cf3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:15:44.626 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: 用户, ID筛选: None, 状态筛选: None
2025-06-13 09:15:44.650 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:15:44.650 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:15:44.650 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:15:44.651 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: 用户, 状态筛选: None
2025-06-13 09:15:44.651 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:15:44.675 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:15:44.676 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 2 条
2025-06-13 09:15:45.050 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:15:45.050 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:15:45.050 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:15:45.098 | 818c0f10696241359b9f5e32a0cac7bf | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:15:45.098 | 818c0f10696241359b9f5e32a0cac7bf | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:15:45.098 | 818c0f10696241359b9f5e32a0cac7bf | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:15:45.098 | 818c0f10696241359b9f5e32a0cac7bf | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:15:45.098 | 818c0f10696241359b9f5e32a0cac7bf | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:15:45.099 | 818c0f10696241359b9f5e32a0cac7bf | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:15:45.099 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:15:45.099 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:15:45.099 | 818c0f10696241359b9f5e32a0cac7bf | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:15:47.173 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:15:47.200 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:15:47.200 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:15:47.201 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:15:47.201 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:15:47.202 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:15:47.226 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:15:47.226 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-13 09:15:47.368 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:15:47.369 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:15:47.369 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:15:47.419 | df9b1492aca947b0ae4975e0ca9b18e4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:15:47.419 | df9b1492aca947b0ae4975e0ca9b18e4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:15:47.419 | df9b1492aca947b0ae4975e0ca9b18e4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:15:47.419 | df9b1492aca947b0ae4975e0ca9b18e4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:15:47.419 | df9b1492aca947b0ae4975e0ca9b18e4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:15:47.419 | df9b1492aca947b0ae4975e0ca9b18e4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:15:47.420 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:15:47.420 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:15:47.420 | df9b1492aca947b0ae4975e0ca9b18e4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:20:32.085 | 8b722b63303a43dc8dd737afe74a3742 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:20:32.109 | 8b722b63303a43dc8dd737afe74a3742 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:20:32.109 | 8b722b63303a43dc8dd737afe74a3742 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:20:32.109 | 8b722b63303a43dc8dd737afe74a3742 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 09:20:32.109 | 8b722b63303a43dc8dd737afe74a3742 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:20:32.134 | 8b722b63303a43dc8dd737afe74a3742 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:20:32.134 | 8b722b63303a43dc8dd737afe74a3742 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-13 09:20:32.274 | 8b722b63303a43dc8dd737afe74a3742 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 3, 已处理: 1, 紧急: 1, 负面: 4, 正面: 1
2025-06-13 09:20:32.275 | 8b722b63303a43dc8dd737afe74a3742 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 09:20:32.275 | 8b722b63303a43dc8dd737afe74a3742 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=3 processed_count=1 urgent_count=1 negative_count=4 positive_count=1
2025-06-13 09:20:32.275 | 8b722b63303a43dc8dd737afe74a3742 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 09:20:32.499 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:20:32.522 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:20:32.523 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:20:32.523 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 09:20:32.523 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:20:32.524 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 09:20:32.548 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:20:32.548 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:20:32.686 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 09:20:32.686 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 09:20:32.686 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 09:20:32.733 | 60c9f730cc6e460d9b10504f941900c7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:20:32.733 | 60c9f730cc6e460d9b10504f941900c7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:20:32.733 | 60c9f730cc6e460d9b10504f941900c7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:20:32.733 | 60c9f730cc6e460d9b10504f941900c7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:20:32.734 | 60c9f730cc6e460d9b10504f941900c7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:20:32.734 | 60c9f730cc6e460d9b10504f941900c7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:20:32.734 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 09:20:32.734 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 09:20:32.734 | 60c9f730cc6e460d9b10504f941900c7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:20:35.367 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:20:35.391 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:20:35.391 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:20:35.391 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:20:35.392 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:20:35.392 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:20:35.416 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:20:35.416 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-13 09:20:35.557 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-13 09:20:35.558 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:20:35.558 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-13 09:20:35.605 | 0a65901d547a450c889b7ff1e55a6d3e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:20:35.606 | 0a65901d547a450c889b7ff1e55a6d3e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:20:35.606 | 0a65901d547a450c889b7ff1e55a6d3e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:20:35.606 | 0a65901d547a450c889b7ff1e55a6d3e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:20:35.606 | 0a65901d547a450c889b7ff1e55a6d3e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:20:35.606 | 0a65901d547a450c889b7ff1e55a6d3e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:20:35.607 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:20:35.607 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:20:35.607 | 0a65901d547a450c889b7ff1e55a6d3e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:20:46.981 | 7da1fdeba4a24c168375571e9592770f | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:100 - 开始编辑预警记录，输入数据: {'id': 4, 'scheme_id': None, 'warning_type': None, 'content': '行业媒体报道该品牌参加某展会，展示了最新技术成果', 'keywords': None, 'status': 2, 'create_time': None, 'update_time': datetime.datetime(2025, 6, 13, 9, 20, 46, 981718), 'create_by': '', 'update_by': 'admin', 'remark': ''}
2025-06-13 09:20:47.005 | 7da1fdeba4a24c168375571e9592770f | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:111 - 找到现有记录: ID=4, scheme_id=14
2025-06-13 09:20:47.006 | 7da1fdeba4a24c168375571e9592770f | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:118 - 准备更新的数据: {'id': 4, 'content': '行业媒体报道该品牌参加某展会，展示了最新技术成果', 'status': 2, 'update_time': datetime.datetime(2025, 6, 13, 9, 20, 47, 5996), 'update_by': 'admin', 'remark': ''}
2025-06-13 09:20:47.006 | 7da1fdeba4a24c168375571e9592770f | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:129 - DAO层更新数据: record_id=4, update_data={'content': '行业媒体报道该品牌参加某展会，展示了最新技术成果', 'status': 2, 'update_time': datetime.datetime(2025, 6, 13, 9, 20, 47, 5996), 'update_by': 'admin', 'remark': ''}
2025-06-13 09:20:47.031 | 7da1fdeba4a24c168375571e9592770f | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:137 - 数据库更新结果: 影响行数=1
2025-06-13 09:20:47.075 | 7da1fdeba4a24c168375571e9592770f | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:122 - 编辑预警记录成功，记录ID: 4
2025-06-13 09:20:47.076 | 7da1fdeba4a24c168375571e9592770f | INFO     | module_warning.controller.warning_record_controller:edit_warning_record:134 - 更新成功
2025-06-13 09:20:48.772 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:20:48.796 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:20:48.796 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:20:48.796 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:20:48.796 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:20:48.796 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:20:48.821 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:20:48.822 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-13 09:20:48.961 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-13 09:20:48.961 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:20:48.962 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-13 09:20:49.010 | b461d318763a487e8aa0a23477313690 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:20:49.010 | b461d318763a487e8aa0a23477313690 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:20:49.011 | b461d318763a487e8aa0a23477313690 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:20:49.011 | b461d318763a487e8aa0a23477313690 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:20:49.011 | b461d318763a487e8aa0a23477313690 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:20:49.011 | b461d318763a487e8aa0a23477313690 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:20:49.011 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:20:49.011 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:20:49.011 | b461d318763a487e8aa0a23477313690 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:21:03.239 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 0
2025-06-13 09:21:03.264 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:21:03.264 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:21:03.265 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:21:03.265 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 0
2025-06-13 09:21:03.265 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:21:03.265 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 0
2025-06-13 09:21:03.289 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:21:03.290 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:21:03.433 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-13 09:21:03.434 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:21:03.434 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-13 09:21:03.483 | fe4d76bd381848da86b35730644d6495 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:21:03.484 | fe4d76bd381848da86b35730644d6495 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:21:03.484 | fe4d76bd381848da86b35730644d6495 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:21:03.484 | fe4d76bd381848da86b35730644d6495 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:21:03.485 | fe4d76bd381848da86b35730644d6495 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:21:03.485 | fe4d76bd381848da86b35730644d6495 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:21:03.485 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:21:03.486 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:21:03.486 | fe4d76bd381848da86b35730644d6495 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:21:05.865 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 1
2025-06-13 09:21:05.890 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:21:05.890 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:21:05.891 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:21:05.891 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 1
2025-06-13 09:21:05.892 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:21:05.892 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 1
2025-06-13 09:21:05.917 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:21:05.917 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:21:06.059 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-13 09:21:06.059 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:21:06.059 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-13 09:21:06.107 | a1fad65bcf9c4b61bbebb03c6dccc601 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:21:06.108 | a1fad65bcf9c4b61bbebb03c6dccc601 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:21:06.108 | a1fad65bcf9c4b61bbebb03c6dccc601 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:21:06.109 | a1fad65bcf9c4b61bbebb03c6dccc601 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:21:06.109 | a1fad65bcf9c4b61bbebb03c6dccc601 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:21:06.109 | a1fad65bcf9c4b61bbebb03c6dccc601 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:21:06.109 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:21:06.110 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:21:06.110 | a1fad65bcf9c4b61bbebb03c6dccc601 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:21:08.626 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 2
2025-06-13 09:21:08.651 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:21:08.651 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:21:08.651 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:21:08.652 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 2
2025-06-13 09:21:08.652 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:21:08.653 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 2
2025-06-13 09:21:08.677 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:21:08.678 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 2 条
2025-06-13 09:21:08.821 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-13 09:21:08.821 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:21:08.822 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-13 09:21:08.871 | e038a2329ad34ee98beac329a5092356 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:21:08.871 | e038a2329ad34ee98beac329a5092356 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:21:08.872 | e038a2329ad34ee98beac329a5092356 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:21:08.872 | e038a2329ad34ee98beac329a5092356 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:21:08.872 | e038a2329ad34ee98beac329a5092356 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:21:08.872 | e038a2329ad34ee98beac329a5092356 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:21:08.872 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:21:08.873 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:21:08.873 | e038a2329ad34ee98beac329a5092356 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:21:35.789 | 4ddd7552581b4d97a382d1cd640b421c | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 保存预警设置失败: "WarningSettingsConfigModel" object has no field "create_by"
2025-06-13 09:21:46.629 | 7489ce8d72f94ca2a3dc57c7477c72d3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:21:46.655 | 7489ce8d72f94ca2a3dc57c7477c72d3 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:21:46.655 | 7489ce8d72f94ca2a3dc57c7477c72d3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:21:46.656 | 7489ce8d72f94ca2a3dc57c7477c72d3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 09:21:46.656 | 7489ce8d72f94ca2a3dc57c7477c72d3 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:21:46.684 | 7489ce8d72f94ca2a3dc57c7477c72d3 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:21:46.684 | 7489ce8d72f94ca2a3dc57c7477c72d3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-13 09:21:46.834 | 7489ce8d72f94ca2a3dc57c7477c72d3 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 2, 已处理: 1, 紧急: 2, 负面: 4, 正面: 1
2025-06-13 09:21:46.834 | 7489ce8d72f94ca2a3dc57c7477c72d3 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 09:21:46.835 | 7489ce8d72f94ca2a3dc57c7477c72d3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=2 processed_count=1 urgent_count=2 negative_count=4 positive_count=1
2025-06-13 09:21:46.835 | 7489ce8d72f94ca2a3dc57c7477c72d3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 09:21:47.352 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:21:47.377 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:21:47.378 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:21:47.378 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 09:21:47.379 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:21:47.379 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 09:21:47.403 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:21:47.404 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:21:47.545 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 09:21:47.545 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 09:21:47.546 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 09:21:47.593 | 2db472dfaa014ec88a99b5c95b4c9bd2 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:21:47.594 | 2db472dfaa014ec88a99b5c95b4c9bd2 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:21:47.594 | 2db472dfaa014ec88a99b5c95b4c9bd2 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:21:47.594 | 2db472dfaa014ec88a99b5c95b4c9bd2 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:21:47.595 | 2db472dfaa014ec88a99b5c95b4c9bd2 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:21:47.595 | 2db472dfaa014ec88a99b5c95b4c9bd2 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:21:47.595 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 09:21:47.595 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 09:21:47.596 | 2db472dfaa014ec88a99b5c95b4c9bd2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:23:51.466 | 78987df1113940d18fa3086347c16131 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:100 - 开始编辑预警记录，输入数据: {'id': 5, 'scheme_id': None, 'warning_type': None, 'content': '无内容', 'keywords': None, 'status': 1, 'create_time': None, 'update_time': datetime.datetime(2025, 6, 13, 9, 23, 51, 466158), 'create_by': '', 'update_by': 'admin', 'remark': ''}
2025-06-13 09:23:51.490 | 78987df1113940d18fa3086347c16131 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:111 - 找到现有记录: ID=5, scheme_id=15
2025-06-13 09:23:51.490 | 78987df1113940d18fa3086347c16131 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:118 - 准备更新的数据: {'id': 5, 'content': '无内容', 'status': 1, 'update_time': datetime.datetime(2025, 6, 13, 9, 23, 51, 490719), 'update_by': 'admin', 'remark': ''}
2025-06-13 09:23:51.491 | 78987df1113940d18fa3086347c16131 | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:129 - DAO层更新数据: record_id=5, update_data={'content': '无内容', 'status': 1, 'update_time': datetime.datetime(2025, 6, 13, 9, 23, 51, 490719), 'update_by': 'admin', 'remark': ''}
2025-06-13 09:23:51.516 | 78987df1113940d18fa3086347c16131 | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:137 - 数据库更新结果: 影响行数=1
2025-06-13 09:23:51.562 | 78987df1113940d18fa3086347c16131 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:122 - 编辑预警记录成功，记录ID: 5
2025-06-13 09:23:51.563 | 78987df1113940d18fa3086347c16131 | INFO     | module_warning.controller.warning_record_controller:edit_warning_record:134 - 更新成功
2025-06-13 09:23:52.945 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:23:52.969 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:23:52.969 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:23:52.969 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 09:23:52.969 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:23:52.970 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 09:23:52.993 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:23:52.994 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:23:53.142 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 09:23:53.143 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 09:23:53.143 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 09:23:53.191 | 13c5b469047248e391881e9998ee5d65 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:23:53.192 | 13c5b469047248e391881e9998ee5d65 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:23:53.192 | 13c5b469047248e391881e9998ee5d65 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:23:53.192 | 13c5b469047248e391881e9998ee5d65 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:23:53.192 | 13c5b469047248e391881e9998ee5d65 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:23:53.193 | 13c5b469047248e391881e9998ee5d65 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:23:53.193 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 09:23:53.193 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 09:23:53.193 | 13c5b469047248e391881e9998ee5d65 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:23:55.220 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:23:55.244 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:23:55.244 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:23:55.244 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:23:55.245 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:23:55.245 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:23:55.269 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:23:55.269 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-13 09:23:55.414 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-13 09:23:55.414 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:23:55.414 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-13 09:23:55.462 | e427469bbf9046d19378f1f945320ac0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:23:55.463 | e427469bbf9046d19378f1f945320ac0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:23:55.463 | e427469bbf9046d19378f1f945320ac0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:23:55.463 | e427469bbf9046d19378f1f945320ac0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:23:55.463 | e427469bbf9046d19378f1f945320ac0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:23:55.463 | e427469bbf9046d19378f1f945320ac0 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:23:55.464 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:23:55.464 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:23:55.464 | e427469bbf9046d19378f1f945320ac0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:24:01.181 | e818433e672248f99ef8df94b07b408d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:24:01.289 | e818433e672248f99ef8df94b07b408d | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:24:01.290 | e818433e672248f99ef8df94b07b408d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:24:01.290 | e818433e672248f99ef8df94b07b408d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 09:24:01.290 | e818433e672248f99ef8df94b07b408d | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:24:01.315 | e818433e672248f99ef8df94b07b408d | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:24:01.315 | e818433e672248f99ef8df94b07b408d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-13 09:24:01.689 | e818433e672248f99ef8df94b07b408d | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 1, 已处理: 2, 紧急: 2, 负面: 4, 正面: 1
2025-06-13 09:24:01.690 | e818433e672248f99ef8df94b07b408d | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 09:24:01.690 | e818433e672248f99ef8df94b07b408d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=1 processed_count=2 urgent_count=2 negative_count=4 positive_count=1
2025-06-13 09:24:01.691 | e818433e672248f99ef8df94b07b408d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 09:24:02.189 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:24:02.213 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:24:02.214 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:24:02.214 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 09:24:02.214 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:24:02.215 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 09:24:02.239 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:24:02.240 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:24:02.388 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 09:24:02.388 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 09:24:02.389 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 09:24:02.437 | f92a60b712d2433b92d8aa656d466606 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:24:02.437 | f92a60b712d2433b92d8aa656d466606 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:24:02.437 | f92a60b712d2433b92d8aa656d466606 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:24:02.438 | f92a60b712d2433b92d8aa656d466606 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:24:02.438 | f92a60b712d2433b92d8aa656d466606 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:24:02.438 | f92a60b712d2433b92d8aa656d466606 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:24:02.439 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 09:24:02.439 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 09:24:02.440 | f92a60b712d2433b92d8aa656d466606 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:24:10.532 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:24:10.556 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:24:10.557 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:24:10.557 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 09:24:10.557 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:24:10.557 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 09:24:10.584 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:24:10.585 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-13 09:24:10.734 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-13 09:24:10.735 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 09:24:10.735 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-13 09:24:10.788 | 77e2ebf4b4934f0dafc269a31a6181e1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:24:10.788 | 77e2ebf4b4934f0dafc269a31a6181e1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:24:10.789 | 77e2ebf4b4934f0dafc269a31a6181e1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:24:10.789 | 77e2ebf4b4934f0dafc269a31a6181e1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:24:10.790 | 77e2ebf4b4934f0dafc269a31a6181e1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:24:10.790 | 77e2ebf4b4934f0dafc269a31a6181e1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:24:10.790 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 09:24:10.791 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 09:24:10.791 | 77e2ebf4b4934f0dafc269a31a6181e1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:24:11.451 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 16, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:24:11.475 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:24:11.476 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:24:11.476 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=16, pageNum=1, pageSize=10
2025-06-13 09:24:11.476 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 16, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:24:11.477 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 16
2025-06-13 09:24:11.500 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:24:11.501 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 09:24:11.642 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 16, 总计: 0, 待处理: 0, 已处理: 0, 紧急: 0, 负面: 0, 正面: 0
2025-06-13 09:24:11.643 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 16
2025-06-13 09:24:11.643 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=0 pending_count=0 processed_count=0 urgent_count=0 negative_count=0 positive_count=0
2025-06-13 09:24:11.691 | 25d123e76ae94cceb1dbd05dca0311ca | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:24:11.692 | 25d123e76ae94cceb1dbd05dca0311ca | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:24:11.692 | 25d123e76ae94cceb1dbd05dca0311ca | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:24:11.692 | 25d123e76ae94cceb1dbd05dca0311ca | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:24:11.692 | 25d123e76ae94cceb1dbd05dca0311ca | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:24:11.692 | 25d123e76ae94cceb1dbd05dca0311ca | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:24:11.692 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 16
2025-06-13 09:24:11.692 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 16 的预警设置成功
2025-06-13 09:24:11.693 | 25d123e76ae94cceb1dbd05dca0311ca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 16, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:24:11.760 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 18, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:24:11.785 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:24:11.786 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:24:11.786 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=18, pageNum=1, pageSize=10
2025-06-13 09:24:11.787 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 18, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:24:11.787 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 18
2025-06-13 09:24:11.814 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:24:11.814 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 09:24:11.968 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 18, 总计: 0, 待处理: 0, 已处理: 0, 紧急: 0, 负面: 0, 正面: 0
2025-06-13 09:24:11.969 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 18
2025-06-13 09:24:11.969 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=0 pending_count=0 processed_count=0 urgent_count=0 negative_count=0 positive_count=0
2025-06-13 09:24:12.019 | 687dcdc787c249f1a06e09e88340aa04 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:24:12.020 | 687dcdc787c249f1a06e09e88340aa04 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:24:12.020 | 687dcdc787c249f1a06e09e88340aa04 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:24:12.020 | 687dcdc787c249f1a06e09e88340aa04 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:24:12.020 | 687dcdc787c249f1a06e09e88340aa04 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:24:12.020 | 687dcdc787c249f1a06e09e88340aa04 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:24:12.020 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 18
2025-06-13 09:24:12.020 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 18 的预警设置成功
2025-06-13 09:24:12.021 | 687dcdc787c249f1a06e09e88340aa04 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 18, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:24:12.182 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 16, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:24:12.206 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:24:12.206 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:24:12.207 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=16, pageNum=1, pageSize=10
2025-06-13 09:24:12.207 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 16, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:24:12.207 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 16
2025-06-13 09:24:12.230 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:24:12.231 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 09:24:12.376 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 16, 总计: 0, 待处理: 0, 已处理: 0, 紧急: 0, 负面: 0, 正面: 0
2025-06-13 09:24:12.377 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 16
2025-06-13 09:24:12.378 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=0 pending_count=0 processed_count=0 urgent_count=0 negative_count=0 positive_count=0
2025-06-13 09:24:12.429 | d3ded0ee3e5b4b298366902ac1f4a19e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:24:12.429 | d3ded0ee3e5b4b298366902ac1f4a19e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:24:12.430 | d3ded0ee3e5b4b298366902ac1f4a19e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:24:12.430 | d3ded0ee3e5b4b298366902ac1f4a19e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:24:12.430 | d3ded0ee3e5b4b298366902ac1f4a19e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:24:12.430 | d3ded0ee3e5b4b298366902ac1f4a19e | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:24:12.431 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 16
2025-06-13 09:24:12.431 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 16 的预警设置成功
2025-06-13 09:24:12.431 | d3ded0ee3e5b4b298366902ac1f4a19e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 16, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:24:13.223 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 17, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:24:13.248 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:24:13.249 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:24:13.249 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=17, pageNum=1, pageSize=10
2025-06-13 09:24:13.250 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 17, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:24:13.250 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 17
2025-06-13 09:24:13.273 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:24:13.274 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 09:24:13.394 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 16, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:24:13.420 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:24:13.420 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:24:13.421 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=16, pageNum=1, pageSize=10
2025-06-13 09:24:13.421 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 16, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:24:13.422 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 16
2025-06-13 09:24:13.424 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 17, 总计: 0, 待处理: 0, 已处理: 0, 紧急: 0, 负面: 0, 正面: 0
2025-06-13 09:24:13.424 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 17
2025-06-13 09:24:13.424 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=0 pending_count=0 processed_count=0 urgent_count=0 negative_count=0 positive_count=0
2025-06-13 09:24:13.446 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:24:13.446 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 09:24:13.472 | 1686929649a945aca4dcc6338204e8c3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:24:13.472 | 1686929649a945aca4dcc6338204e8c3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:24:13.472 | 1686929649a945aca4dcc6338204e8c3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:24:13.472 | 1686929649a945aca4dcc6338204e8c3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:24:13.472 | 1686929649a945aca4dcc6338204e8c3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:24:13.472 | 1686929649a945aca4dcc6338204e8c3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:24:13.472 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 17
2025-06-13 09:24:13.473 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 17 的预警设置成功
2025-06-13 09:24:13.473 | 1686929649a945aca4dcc6338204e8c3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 17, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:24:13.591 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 16, 总计: 0, 待处理: 0, 已处理: 0, 紧急: 0, 负面: 0, 正面: 0
2025-06-13 09:24:13.592 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 16
2025-06-13 09:24:13.592 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=0 pending_count=0 processed_count=0 urgent_count=0 negative_count=0 positive_count=0
2025-06-13 09:24:13.639 | d3775ef50d9e4b459ccb349501318c20 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:24:13.640 | d3775ef50d9e4b459ccb349501318c20 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:24:13.640 | d3775ef50d9e4b459ccb349501318c20 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:24:13.640 | d3775ef50d9e4b459ccb349501318c20 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:24:13.641 | d3775ef50d9e4b459ccb349501318c20 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:24:13.641 | d3775ef50d9e4b459ccb349501318c20 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:24:13.641 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 16
2025-06-13 09:24:13.642 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 16 的预警设置成功
2025-06-13 09:24:13.642 | d3775ef50d9e4b459ccb349501318c20 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 16, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:24:14.063 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 18, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:24:14.087 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:24:14.088 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:24:14.088 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=18, pageNum=1, pageSize=10
2025-06-13 09:24:14.089 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 18, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:24:14.089 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 18
2025-06-13 09:24:14.113 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:24:14.113 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 09:24:14.257 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 18, 总计: 0, 待处理: 0, 已处理: 0, 紧急: 0, 负面: 0, 正面: 0
2025-06-13 09:24:14.257 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 18
2025-06-13 09:24:14.257 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=0 pending_count=0 processed_count=0 urgent_count=0 negative_count=0 positive_count=0
2025-06-13 09:24:14.307 | dfda7c52e29841b594c0f98609ddacfc | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:24:14.307 | dfda7c52e29841b594c0f98609ddacfc | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:24:14.307 | dfda7c52e29841b594c0f98609ddacfc | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:24:14.308 | dfda7c52e29841b594c0f98609ddacfc | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:24:14.308 | dfda7c52e29841b594c0f98609ddacfc | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:24:14.308 | dfda7c52e29841b594c0f98609ddacfc | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:24:14.308 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 18
2025-06-13 09:24:14.308 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 18 的预警设置成功
2025-06-13 09:24:14.308 | dfda7c52e29841b594c0f98609ddacfc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 18, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:27:06.832 | a4fa6d1ab0b44c22bfc963d8aafedf0a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:27:06.855 | a4fa6d1ab0b44c22bfc963d8aafedf0a | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:27:06.856 | a4fa6d1ab0b44c22bfc963d8aafedf0a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:27:06.856 | a4fa6d1ab0b44c22bfc963d8aafedf0a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 09:27:06.856 | a4fa6d1ab0b44c22bfc963d8aafedf0a | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:27:06.880 | a4fa6d1ab0b44c22bfc963d8aafedf0a | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:27:06.880 | a4fa6d1ab0b44c22bfc963d8aafedf0a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-13 09:27:07.020 | a4fa6d1ab0b44c22bfc963d8aafedf0a | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 1, 已处理: 2, 紧急: 2, 负面: 4, 正面: 1
2025-06-13 09:27:07.020 | a4fa6d1ab0b44c22bfc963d8aafedf0a | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 09:27:07.021 | a4fa6d1ab0b44c22bfc963d8aafedf0a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=1 processed_count=2 urgent_count=2 negative_count=4 positive_count=1
2025-06-13 09:27:07.021 | a4fa6d1ab0b44c22bfc963d8aafedf0a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 09:27:07.196 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:27:07.221 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:27:07.221 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:27:07.221 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 09:27:07.221 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:27:07.222 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 09:27:07.246 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:27:07.246 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:27:07.387 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 09:27:07.387 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 09:27:07.388 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 09:27:07.435 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:27:07.435 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:27:07.435 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:27:07.435 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:27:07.436 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:27:07.436 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:27:07.436 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 09:27:07.436 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 09:27:07.436 | 2e6c2e16f3e94005a3f5d1f3fdb3601f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:27:53.130 | 9b88fe9793144989ab7cf0efcf4bc771 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:27:53.154 | 9b88fe9793144989ab7cf0efcf4bc771 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:27:53.154 | 9b88fe9793144989ab7cf0efcf4bc771 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:27:53.154 | 9b88fe9793144989ab7cf0efcf4bc771 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 09:27:53.155 | 9b88fe9793144989ab7cf0efcf4bc771 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:27:53.178 | 9b88fe9793144989ab7cf0efcf4bc771 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:27:53.179 | 9b88fe9793144989ab7cf0efcf4bc771 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-13 09:27:53.319 | 9b88fe9793144989ab7cf0efcf4bc771 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 1, 已处理: 2, 紧急: 2, 负面: 4, 正面: 1
2025-06-13 09:27:53.319 | 9b88fe9793144989ab7cf0efcf4bc771 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 09:27:53.320 | 9b88fe9793144989ab7cf0efcf4bc771 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=1 processed_count=2 urgent_count=2 negative_count=4 positive_count=1
2025-06-13 09:27:53.321 | 9b88fe9793144989ab7cf0efcf4bc771 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 09:27:53.507 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:27:53.531 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:27:53.531 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:27:53.531 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 09:27:53.532 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:27:53.532 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 09:27:53.555 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:27:53.555 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:27:53.693 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 09:27:53.694 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 09:27:53.694 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 09:27:53.742 | be219ce0d5ad44f5a433cbb88332f437 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:27:53.743 | be219ce0d5ad44f5a433cbb88332f437 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:27:53.743 | be219ce0d5ad44f5a433cbb88332f437 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:27:53.743 | be219ce0d5ad44f5a433cbb88332f437 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:27:53.744 | be219ce0d5ad44f5a433cbb88332f437 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:27:53.744 | be219ce0d5ad44f5a433cbb88332f437 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:27:53.744 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 09:27:53.744 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 09:27:53.745 | be219ce0d5ad44f5a433cbb88332f437 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 09:39:58.043 | 8b2aa3ed70d542ab930440a4abc98926 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 09:39:58.226 | c96c1c644ead429383fcd3a074694fae | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 09:39:58.644 | 15d749a136ad4843ac5e11d7662ff043 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:39:58.669 | 15d749a136ad4843ac5e11d7662ff043 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:39:58.670 | 15d749a136ad4843ac5e11d7662ff043 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:39:58.670 | 15d749a136ad4843ac5e11d7662ff043 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 09:39:58.671 | 15d749a136ad4843ac5e11d7662ff043 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:39:58.695 | 15d749a136ad4843ac5e11d7662ff043 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:39:58.696 | 15d749a136ad4843ac5e11d7662ff043 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-13 09:39:58.840 | 15d749a136ad4843ac5e11d7662ff043 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 1, 已处理: 2, 紧急: 2, 负面: 4, 正面: 1
2025-06-13 09:39:58.841 | 15d749a136ad4843ac5e11d7662ff043 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 09:39:58.842 | 15d749a136ad4843ac5e11d7662ff043 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=1 processed_count=2 urgent_count=2 negative_count=4 positive_count=1
2025-06-13 09:39:58.842 | 15d749a136ad4843ac5e11d7662ff043 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 09:39:59.036 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 09:39:59.061 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 09:39:59.062 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 09:39:59.062 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 09:39:59.063 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 09:39:59.063 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 09:39:59.088 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 09:39:59.088 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 09:39:59.230 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 09:39:59.231 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 09:39:59.232 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 09:39:59.281 | 46c464f9ec814a869713bdd49a708ca4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 09:39:59.282 | 46c464f9ec814a869713bdd49a708ca4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 09:39:59.282 | 46c464f9ec814a869713bdd49a708ca4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 09:39:59.282 | 46c464f9ec814a869713bdd49a708ca4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 09:39:59.282 | 46c464f9ec814a869713bdd49a708ca4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 09:39:59.283 | 46c464f9ec814a869713bdd49a708ca4 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 09:39:59.283 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 09:39:59.284 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 09:39:59.284 | 46c464f9ec814a869713bdd49a708ca4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 11:04:25.984 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-13 11:04:25.984 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 11:04:26.895 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 11:04:26.895 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 11:04:26.903 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 11:04:27.545 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 11:04:28.084 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 11:04:28.085 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-13 11:05:29.455 | 275786f13fba4529946abb8690921165 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为7d7bb54d-85f5-46a6-91c5-fa0fdac927d4的会话获取图片验证码成功
2025-06-13 11:05:41.180 | 59ac5be7df774f11b411f80ef4c0d4c4 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-13 11:05:41.531 | cd15cd6021b24077a02a06aa636608bb | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 11:05:42.040 | 75ac7f9c54764964b9aa03c7293b467b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 11:05:46.966 | 81d4ee4d2e7842e193b70c7fa1839d9e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 11:05:46.991 | 81d4ee4d2e7842e193b70c7fa1839d9e | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 11:05:46.991 | 81d4ee4d2e7842e193b70c7fa1839d9e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 11:05:46.992 | 81d4ee4d2e7842e193b70c7fa1839d9e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 11:05:46.992 | 81d4ee4d2e7842e193b70c7fa1839d9e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 11:05:47.016 | 81d4ee4d2e7842e193b70c7fa1839d9e | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 11:05:47.017 | 81d4ee4d2e7842e193b70c7fa1839d9e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-13 11:05:47.161 | 81d4ee4d2e7842e193b70c7fa1839d9e | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 1, 已处理: 2, 紧急: 2, 负面: 4, 正面: 1
2025-06-13 11:05:47.161 | 81d4ee4d2e7842e193b70c7fa1839d9e | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 11:05:47.162 | 81d4ee4d2e7842e193b70c7fa1839d9e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=1 processed_count=2 urgent_count=2 negative_count=4 positive_count=1
2025-06-13 11:05:47.162 | 81d4ee4d2e7842e193b70c7fa1839d9e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 11:05:47.364 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 11:05:47.391 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 11:05:47.391 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 11:05:47.391 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 11:05:47.392 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 11:05:47.392 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 11:05:47.418 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 11:05:47.418 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 11:05:47.576 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 11:05:47.576 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 11:05:47.577 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 11:05:47.630 | c6fb8d06219d4306ab7626ef1b2ab809 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 11:05:47.630 | c6fb8d06219d4306ab7626ef1b2ab809 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 11:05:47.630 | c6fb8d06219d4306ab7626ef1b2ab809 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 11:05:47.631 | c6fb8d06219d4306ab7626ef1b2ab809 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 11:05:47.631 | c6fb8d06219d4306ab7626ef1b2ab809 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 11:05:47.631 | c6fb8d06219d4306ab7626ef1b2ab809 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 11:05:47.631 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 11:05:47.632 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 11:05:47.632 | c6fb8d06219d4306ab7626ef1b2ab809 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 11:05:56.374 | 558adafd6f6a4557a7b4e0b027b7a739 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 保存预警设置失败: "WarningSettingsConfigModel" object has no field "create_by"
2025-06-13 11:06:27.836 | 2800f0db47724fc98b3f2d9b27f995e3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 11:06:28.021 | 80a0d9b66d6a47cda12ee4379a83e084 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 11:06:28.727 | 543f75419b8f4cb9846a63e126373fe0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 11:06:28.753 | 543f75419b8f4cb9846a63e126373fe0 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 11:06:28.753 | 543f75419b8f4cb9846a63e126373fe0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 11:06:28.754 | 543f75419b8f4cb9846a63e126373fe0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 11:06:28.754 | 543f75419b8f4cb9846a63e126373fe0 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 11:06:28.779 | 543f75419b8f4cb9846a63e126373fe0 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 11:06:28.780 | 543f75419b8f4cb9846a63e126373fe0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-13 11:06:28.932 | 543f75419b8f4cb9846a63e126373fe0 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 1, 已处理: 2, 紧急: 2, 负面: 4, 正面: 1
2025-06-13 11:06:28.933 | 543f75419b8f4cb9846a63e126373fe0 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 11:06:28.933 | 543f75419b8f4cb9846a63e126373fe0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=1 processed_count=2 urgent_count=2 negative_count=4 positive_count=1
2025-06-13 11:06:28.933 | 543f75419b8f4cb9846a63e126373fe0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 11:06:29.133 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 11:06:29.158 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 11:06:29.158 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 11:06:29.159 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 11:06:29.159 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 11:06:29.160 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 11:06:29.184 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 11:06:29.185 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 11:06:29.329 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 11:06:29.330 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 11:06:29.330 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 11:06:29.381 | 321e20678d334620a3abe4fd036ff5ce | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 11:06:29.382 | 321e20678d334620a3abe4fd036ff5ce | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 11:06:29.383 | 321e20678d334620a3abe4fd036ff5ce | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 11:06:29.383 | 321e20678d334620a3abe4fd036ff5ce | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 11:06:29.384 | 321e20678d334620a3abe4fd036ff5ce | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 11:06:29.384 | 321e20678d334620a3abe4fd036ff5ce | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 11:06:29.385 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 11:06:29.385 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 11:06:29.385 | 321e20678d334620a3abe4fd036ff5ce | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 11:06:30.977 | d56751c4654942f1897b551882f5bcee | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 保存预警设置失败: "WarningSettingsConfigModel" object has no field "create_by"
2025-06-13 11:09:21.502 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 11:09:21.503 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 11:09:24.544 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-13 11:09:24.545 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 11:09:25.367 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 11:09:25.367 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 11:09:25.369 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 11:09:25.794 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 11:09:26.340 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 11:09:26.341 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-13 11:13:36.577 | 25de77c374414f67b259c8c7f02fad06 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 保存预警设置失败: "WarningSettingsConfigModel" object has no field "create_by"
2025-06-13 11:14:21.557 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 11:14:21.557 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 11:14:24.536 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-13 11:14:24.536 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 11:14:25.362 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 11:14:25.362 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 11:14:25.376 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 11:14:25.791 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 11:14:26.388 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 11:14:26.389 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-13 11:16:24.914 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-13 11:16:24.915 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 11:16:25.707 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 11:16:25.707 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 11:16:25.709 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 11:16:26.119 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 11:16:26.655 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 11:16:26.655 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-13 11:18:21.920 | 9271e75509854c838014b69c7c9e81f4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 11:18:22.111 | cbf1126963a24c1e99ae168444815f05 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 11:18:22.545 | de9c1d72bf2140b49a9d4fca79c3385e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 11:18:22.572 | de9c1d72bf2140b49a9d4fca79c3385e | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 11:18:22.573 | de9c1d72bf2140b49a9d4fca79c3385e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 11:18:22.573 | de9c1d72bf2140b49a9d4fca79c3385e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 11:18:22.573 | de9c1d72bf2140b49a9d4fca79c3385e | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 11:18:22.600 | de9c1d72bf2140b49a9d4fca79c3385e | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 11:18:22.601 | de9c1d72bf2140b49a9d4fca79c3385e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-13 11:18:22.762 | de9c1d72bf2140b49a9d4fca79c3385e | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 1, 已处理: 2, 紧急: 2, 负面: 4, 正面: 1
2025-06-13 11:18:22.762 | de9c1d72bf2140b49a9d4fca79c3385e | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 11:18:22.763 | de9c1d72bf2140b49a9d4fca79c3385e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=1 processed_count=2 urgent_count=2 negative_count=4 positive_count=1
2025-06-13 11:18:22.763 | de9c1d72bf2140b49a9d4fca79c3385e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 11:18:22.966 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 11:18:22.991 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 11:18:22.992 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 11:18:22.992 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 11:18:22.992 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 11:18:22.992 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 11:18:23.019 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 11:18:23.020 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 11:18:23.172 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 11:18:23.172 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 11:18:23.173 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 11:18:23.222 | 9b10669d09774e4e95938209e5728786 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 11:18:23.222 | 9b10669d09774e4e95938209e5728786 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 11:18:23.222 | 9b10669d09774e4e95938209e5728786 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 11:18:23.223 | 9b10669d09774e4e95938209e5728786 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 11:18:23.223 | 9b10669d09774e4e95938209e5728786 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 11:18:23.223 | 9b10669d09774e4e95938209e5728786 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 11:18:23.223 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 11:18:23.223 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 11:18:23.223 | 9b10669d09774e4e95938209e5728786 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 11:18:27.120 | 4486c36499844603bed85140d95d8bd3 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 保存预警设置失败: "WarningSettingsConfigModel" object has no field "create_by"
2025-06-13 11:20:04.791 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 11:20:04.792 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 11:20:07.544 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-13 11:20:07.544 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 11:20:16.075 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-13 11:20:16.075 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 11:20:16.901 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 11:20:16.901 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 11:20:16.904 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 11:20:17.344 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 11:20:17.909 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 11:20:17.910 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-13 13:50:46.461 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-13 13:50:46.462 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 13:50:47.312 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 13:50:47.312 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 13:50:47.316 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 13:50:47.764 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 13:50:48.288 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 13:50:48.289 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-13 13:51:12.889 | b6236b043d9a43498157a70b9407506d | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为28e6b875-e166-4384-99c0-636e01d4b0d4的会话获取图片验证码成功
2025-06-13 13:51:15.069 | 9e9177d210844dcdb77e0bd59b9b0ea9 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-13 13:51:15.278 | c9ae7efc9e26469184e388bd9587c3ca | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 13:51:15.756 | 439590175cbd45cfadaeeec51176cb66 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 13:51:18.819 | 430e810c4619460a9b9835034d9af7f3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 13:51:18.846 | 430e810c4619460a9b9835034d9af7f3 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 13:51:18.846 | 430e810c4619460a9b9835034d9af7f3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 13:51:18.847 | 430e810c4619460a9b9835034d9af7f3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 13:51:18.847 | 430e810c4619460a9b9835034d9af7f3 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 13:51:18.875 | 430e810c4619460a9b9835034d9af7f3 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 13:51:18.875 | 430e810c4619460a9b9835034d9af7f3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-13 13:51:19.022 | 430e810c4619460a9b9835034d9af7f3 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 1, 已处理: 2, 紧急: 2, 负面: 4, 正面: 1
2025-06-13 13:51:19.023 | 430e810c4619460a9b9835034d9af7f3 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 13:51:19.023 | 430e810c4619460a9b9835034d9af7f3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=1 processed_count=2 urgent_count=2 negative_count=4 positive_count=1
2025-06-13 13:51:19.023 | 430e810c4619460a9b9835034d9af7f3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 13:51:19.221 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 13:51:19.245 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 13:51:19.246 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 13:51:19.246 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 13:51:19.247 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 13:51:19.247 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 13:51:19.271 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 13:51:19.272 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 13:51:19.416 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 13:51:19.416 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 13:51:19.417 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 13:51:19.468 | 26f23ad549d448e3a3244fe3fc7b0dd7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 13:51:19.468 | 26f23ad549d448e3a3244fe3fc7b0dd7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 13:51:19.469 | 26f23ad549d448e3a3244fe3fc7b0dd7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 13:51:19.469 | 26f23ad549d448e3a3244fe3fc7b0dd7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 13:51:19.470 | 26f23ad549d448e3a3244fe3fc7b0dd7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 13:51:19.470 | 26f23ad549d448e3a3244fe3fc7b0dd7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 13:51:19.470 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 13:51:19.471 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 13:51:19.471 | 26f23ad549d448e3a3244fe3fc7b0dd7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 13:55:59.950 | 94806b69174248878b4d75c7da569f7c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 13:56:00.126 | 11dd9cd3582c41c69dcabfb1fdd250f1 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:09:00.628 | 30e4cfeba25f422685f992910cf73aac | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 14:09:00.652 | 30e4cfeba25f422685f992910cf73aac | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 14:09:00.652 | 30e4cfeba25f422685f992910cf73aac | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 14:09:00.652 | 30e4cfeba25f422685f992910cf73aac | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 14:09:00.653 | 30e4cfeba25f422685f992910cf73aac | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 14:09:00.677 | 30e4cfeba25f422685f992910cf73aac | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 14:09:00.677 | 30e4cfeba25f422685f992910cf73aac | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-13 14:09:00.818 | 30e4cfeba25f422685f992910cf73aac | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 1, 已处理: 2, 紧急: 2, 负面: 4, 正面: 1
2025-06-13 14:09:00.818 | 30e4cfeba25f422685f992910cf73aac | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 14:09:00.818 | 30e4cfeba25f422685f992910cf73aac | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=1 processed_count=2 urgent_count=2 negative_count=4 positive_count=1
2025-06-13 14:09:00.818 | 30e4cfeba25f422685f992910cf73aac | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 14:09:00.966 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 14:09:00.967 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 14:09:04.669 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-13 14:09:04.670 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 14:09:05.470 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 14:09:05.470 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 14:09:05.472 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 14:09:05.994 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 14:09:06.495 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 14:09:06.496 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-13 14:09:06.624 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 14:09:06.648 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 14:09:06.648 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 14:09:06.648 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 14:09:06.649 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 14:09:06.649 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 14:09:06.673 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 14:09:06.673 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 14:09:06.811 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 14:09:06.811 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 14:09:06.811 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 14:09:06.858 | 65e5c8ccac0e4ab0a91a4af73c976058 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 14:09:06.859 | 65e5c8ccac0e4ab0a91a4af73c976058 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 14:09:06.859 | 65e5c8ccac0e4ab0a91a4af73c976058 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 14:09:06.859 | 65e5c8ccac0e4ab0a91a4af73c976058 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 14:09:06.859 | 65e5c8ccac0e4ab0a91a4af73c976058 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 14:09:06.859 | 65e5c8ccac0e4ab0a91a4af73c976058 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 14:09:06.859 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 14:09:06.860 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 14:09:06.860 | 65e5c8ccac0e4ab0a91a4af73c976058 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 14:09:09.292 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 14:09:09.316 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 14:09:09.316 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 14:09:09.316 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 14:09:09.317 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 14:09:09.317 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 14:09:09.341 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 14:09:09.341 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-13 14:09:09.480 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-13 14:09:09.481 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 14:09:09.481 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-13 14:09:09.529 | 648b98ef49f64375b60f81b862d91cea | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 14:09:09.529 | 648b98ef49f64375b60f81b862d91cea | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 14:09:09.530 | 648b98ef49f64375b60f81b862d91cea | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 14:09:09.530 | 648b98ef49f64375b60f81b862d91cea | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 14:09:09.530 | 648b98ef49f64375b60f81b862d91cea | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 14:09:09.531 | 648b98ef49f64375b60f81b862d91cea | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 14:09:09.531 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 14:09:09.532 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 14:09:09.533 | 648b98ef49f64375b60f81b862d91cea | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 14:09:09.641 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 14:09:09.642 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 14:09:12.639 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-13 14:09:12.639 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 14:09:13.466 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 14:09:13.467 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 14:09:13.468 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 14:09:13.896 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 14:09:14.422 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 14:09:14.422 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-13 14:09:14.556 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 18, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 14:09:14.581 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 14:09:14.582 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 14:09:14.582 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=18, pageNum=1, pageSize=10
2025-06-13 14:09:14.582 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 18, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 14:09:14.582 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 18
2025-06-13 14:09:14.607 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 14:09:14.608 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-13 14:09:14.752 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 18, 总计: 0, 待处理: 0, 已处理: 0, 紧急: 0, 负面: 0, 正面: 0
2025-06-13 14:09:14.752 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 18
2025-06-13 14:09:14.753 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=0 pending_count=0 processed_count=0 urgent_count=0 negative_count=0 positive_count=0
2025-06-13 14:09:14.802 | 430499287fef4d92930ff5165d11766c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 14:09:14.802 | 430499287fef4d92930ff5165d11766c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 14:09:14.802 | 430499287fef4d92930ff5165d11766c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 14:09:14.802 | 430499287fef4d92930ff5165d11766c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 14:09:14.802 | 430499287fef4d92930ff5165d11766c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 14:09:14.803 | 430499287fef4d92930ff5165d11766c | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 14:09:14.803 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 18
2025-06-13 14:09:14.803 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 18 的预警设置成功
2025-06-13 14:09:14.803 | 430499287fef4d92930ff5165d11766c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 18, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 14:09:16.644 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 14:09:16.669 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 14:09:16.670 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 14:09:16.670 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 14:09:16.670 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 14:09:16.671 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 14:09:16.695 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 14:09:16.696 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 14:09:16.838 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 14:09:16.839 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 14:09:16.839 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 14:09:16.889 | db30a935b467456eb08163a58eeb55dc | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 14:09:16.890 | db30a935b467456eb08163a58eeb55dc | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 14:09:16.891 | db30a935b467456eb08163a58eeb55dc | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 14:09:16.891 | db30a935b467456eb08163a58eeb55dc | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 14:09:16.891 | db30a935b467456eb08163a58eeb55dc | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 14:09:16.891 | db30a935b467456eb08163a58eeb55dc | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 14:09:16.892 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 14:09:16.892 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 14:09:16.892 | db30a935b467456eb08163a58eeb55dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 14:09:19.000 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 14:09:19.037 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 14:09:19.037 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 14:09:19.038 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 14:09:19.038 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 14:09:19.038 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 14:09:19.076 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 14:09:19.076 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-13 14:09:19.271 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-13 14:09:19.271 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 14:09:19.271 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-13 14:09:19.339 | 4923f30dfd334887aa58647ae1c920da | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 14:09:19.340 | 4923f30dfd334887aa58647ae1c920da | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 14:09:19.340 | 4923f30dfd334887aa58647ae1c920da | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 14:09:19.340 | 4923f30dfd334887aa58647ae1c920da | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 14:09:19.341 | 4923f30dfd334887aa58647ae1c920da | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 14:09:19.341 | 4923f30dfd334887aa58647ae1c920da | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 14:09:19.341 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 14:09:19.342 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 14:09:19.342 | 4923f30dfd334887aa58647ae1c920da | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 14:09:22.072 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 0
2025-06-13 14:09:22.097 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 14:09:22.098 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 14:09:22.098 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 14:09:22.099 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 0
2025-06-13 14:09:22.099 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 14:09:22.100 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 0
2025-06-13 14:09:22.125 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 14:09:22.126 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 14:09:22.271 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-13 14:09:22.272 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 14:09:22.272 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-13 14:09:22.322 | 6d922b3687f1452ebc8f4663498b0744 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 14:09:22.323 | 6d922b3687f1452ebc8f4663498b0744 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 14:09:22.323 | 6d922b3687f1452ebc8f4663498b0744 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 14:09:22.324 | 6d922b3687f1452ebc8f4663498b0744 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 14:09:22.324 | 6d922b3687f1452ebc8f4663498b0744 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 14:09:22.324 | 6d922b3687f1452ebc8f4663498b0744 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 14:09:22.325 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 14:09:22.325 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 14:09:22.326 | 6d922b3687f1452ebc8f4663498b0744 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 14:09:23.784 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 1
2025-06-13 14:09:23.809 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 14:09:23.810 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 14:09:23.810 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 14:09:23.811 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 1
2025-06-13 14:09:23.811 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 14:09:23.812 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 1
2025-06-13 14:09:23.837 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 14:09:23.837 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 14:09:23.983 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-13 14:09:23.984 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 14:09:23.984 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-13 14:09:24.033 | 322c74a9871f493ba6257a51f82abaa1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 14:09:24.034 | 322c74a9871f493ba6257a51f82abaa1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 14:09:24.034 | 322c74a9871f493ba6257a51f82abaa1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 14:09:24.034 | 322c74a9871f493ba6257a51f82abaa1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 14:09:24.035 | 322c74a9871f493ba6257a51f82abaa1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 14:09:24.035 | 322c74a9871f493ba6257a51f82abaa1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 14:09:24.035 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 14:09:24.036 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 14:09:24.036 | 322c74a9871f493ba6257a51f82abaa1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 14:09:25.294 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 2
2025-06-13 14:09:25.320 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 14:09:25.320 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 14:09:25.321 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 14:09:25.321 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 2
2025-06-13 14:09:25.322 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 14:09:25.322 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 2
2025-06-13 14:09:25.348 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 14:09:25.348 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 2 条
2025-06-13 14:09:25.495 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-13 14:09:25.496 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 14:09:25.496 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-13 14:09:25.546 | 872574e70cee4fe3a70d3eaaa105bbb7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 14:09:25.546 | 872574e70cee4fe3a70d3eaaa105bbb7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 14:09:25.546 | 872574e70cee4fe3a70d3eaaa105bbb7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 14:09:25.547 | 872574e70cee4fe3a70d3eaaa105bbb7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 14:09:25.547 | 872574e70cee4fe3a70d3eaaa105bbb7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 14:09:25.547 | 872574e70cee4fe3a70d3eaaa105bbb7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 14:09:25.548 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 14:09:25.548 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 14:09:25.549 | 872574e70cee4fe3a70d3eaaa105bbb7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 14:09:28.066 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 14:09:28.091 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 14:09:28.091 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 14:09:28.092 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-13 14:09:28.092 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 14:09:28.093 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-13 14:09:28.117 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 14:09:28.118 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-13 14:09:28.263 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-13 14:09:28.264 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-13 14:09:28.264 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-13 14:09:28.315 | f06e0c0d0c5743178aa1169638d0d091 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 14:09:28.315 | f06e0c0d0c5743178aa1169638d0d091 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 14:09:28.316 | f06e0c0d0c5743178aa1169638d0d091 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 14:09:28.316 | f06e0c0d0c5743178aa1169638d0d091 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 14:09:28.316 | f06e0c0d0c5743178aa1169638d0d091 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 14:09:28.317 | f06e0c0d0c5743178aa1169638d0d091 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 14:09:28.317 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-13 14:09:28.317 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-13 14:09:28.317 | f06e0c0d0c5743178aa1169638d0d091 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 14:13:23.793 | 741cb5cb47064b0b85e75a581ee666a6 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:13:23.934 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 14:13:23.934 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 14:13:27.433 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-13 14:13:27.433 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 14:13:28.459 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 14:13:28.459 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 14:13:28.461 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 14:13:28.919 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 14:13:29.460 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 14:13:29.461 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-13 14:13:29.598 | 683c34ef7da944abaa1e431ac460b8ed | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:13:59.801 | e53eb042b2ab4b6093bf22b81dee2e8a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:13:59.993 | 946d0c41c8b549039ff1da6e7194dc50 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:14:16.603 | 3db52da314fd49f78fa16c15a0226775 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:14:16.799 | 75325d72c98d42c49852d000a3c5f86d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:18:03.442 | f615cd888dca41b1a69a45fb21a7d442 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:18:03.548 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 14:18:03.548 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 14:18:06.383 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-13 14:18:06.384 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 14:18:07.438 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 14:18:07.439 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 14:18:07.441 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 14:18:07.925 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 14:18:08.445 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 14:18:08.446 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-13 14:18:08.599 | 9aed7fa6f1d84a3b96277c6d8606fdfd | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:19:15.141 | 68360fbd8bea4111894397599c7bc7f2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:19:15.332 | b6e650279f774f44b10a009b369ae207 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:21:16.036 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-13 14:21:16.037 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 14:21:17.038 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 14:21:17.039 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 14:21:17.040 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 14:21:17.487 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 14:21:18.048 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 14:21:18.048 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-13 14:21:46.525 | c9d3a5f0552e440d82564b444b9f41a6 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:21:46.711 | 46cf4f0faa1642ee941c0e39928b8058 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:22:13.467 | 9b3e2b7351cd49b884c58067fe3686fd | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:22:13.666 | 8ba854877c2647529170f282c6ac43b1 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:27:55.240 | 06921aa5187b44a3a3d01a4527854016 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:27:55.693 | e9f86c3deb544b0e8109bc7d8bc03d1f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:32:17.419 | ae6459cb609c4892afc321a47d9de139 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:32:17.613 | 3f70e3ec310e4fe2ad7ffdee77556781 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:33:34.517 | 2c342bd7464e40ce900c89825213bd5e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:33:34.739 | dcab46f72bd84ab6b56af58662484e91 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:33:41.241 | 50cfdf1fb7ec400a89997b477e8a7013 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:33:41.436 | bbcecb034f334ad4ae8574ef84e6ca0d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:52:15.214 | 4ae85ffc7abe4df6a961c24155533f2c | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 14:52:15.388 | 9e6b1d982537447baf9a569175df9b58 | ERROR    | module_admin.service.scheme_service:get_scheme_menu_categories_services:385 - 获取方案菜单分类失败: 1 validation error for SchemeMenuModel
typeName
  Field required [type=missing, input_value={'id': 25, 'name': '方...True, 'is_active': True}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-13 14:52:15.388 | 9e6b1d982537447baf9a569175df9b58 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:119 - 获取报告中心菜单分类成功
2025-06-13 14:52:17.002 | 51fcd06c1a584ae69e9bb0a2e08c4f72 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:52:17.174 | 3fab5a1d36c141c59344b93ee8716b4a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:52:17.780 | 9a91ec039c64407c98a18228078fd048 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 14:52:17.881 | 8dc7800504364c73966f112c5d385033 | ERROR    | module_admin.service.scheme_service:get_scheme_menu_categories_services:385 - 获取方案菜单分类失败: 1 validation error for SchemeMenuModel
typeName
  Field required [type=missing, input_value={'id': 25, 'name': '方...True, 'is_active': True}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-13 14:52:17.881 | 8dc7800504364c73966f112c5d385033 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:119 - 获取报告中心菜单分类成功
2025-06-13 14:58:55.958 | 12529b28efbb438ba40861c55fd25e5e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:58:56.043 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 14:58:56.044 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 14:58:59.151 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-13 14:58:59.152 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 14:59:00.103 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 14:59:00.103 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 14:59:00.106 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 14:59:00.537 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 14:59:01.042 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 14:59:01.043 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-13 14:59:01.181 | 62636788311a4e489e81ba6ccfc84ea2 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:59:01.575 | 42ed1efdc00d4437b4530c23987c751a | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 14:59:01.954 | f738f7b9f93e415eb20a32de526b2437 | ERROR    | module_admin.service.scheme_service:get_scheme_menu_categories_services:390 - 获取方案菜单分类失败: 1 validation error for SchemeMenuModel
typeName
  Field required [type=missing, input_value={'id': 25, 'name': '方...True, 'is_active': True}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-13 14:59:01.954 | f738f7b9f93e415eb20a32de526b2437 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:119 - 获取报告中心菜单分类成功
2025-06-13 14:59:30.332 | 81eafd58125e4856847020dfef021fc1 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 14:59:30.505 | 3acfaa309f244e2ea06c489904f055d3 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 14:59:30.869 | 6d928a5d3756478ea46a0659f2f44137 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 14:59:31.246 | 99d134836e2743cfbe75a6ff6e5c6ffc | ERROR    | module_admin.service.scheme_service:get_scheme_menu_categories_services:390 - 获取方案菜单分类失败: 1 validation error for SchemeMenuModel
typeName
  Field required [type=missing, input_value={'id': 25, 'name': '方...True, 'is_active': True}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-13 14:59:31.246 | 99d134836e2743cfbe75a6ff6e5c6ffc | INFO     | module_admin.controller.report_controller:get_report_menu_categories:119 - 获取报告中心菜单分类成功
2025-06-13 15:03:57.280 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 15:03:57.281 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 15:04:01.270 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-13 15:04:01.271 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 15:04:02.559 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 15:04:02.559 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 15:04:02.561 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 15:04:03.127 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 15:04:03.665 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 15:04:03.666 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-13 15:05:10.441 | f7c4c99a71444c5e9fc8d14e7e8ead1e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 15:05:10.493 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 15:05:10.494 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 15:05:13.838 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-13 15:05:13.838 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 15:05:15.012 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 15:05:15.012 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 15:05:15.018 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 15:05:15.754 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 15:05:16.336 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 15:05:16.336 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-13 15:05:16.575 | 04df3080bd9745b088d4391322449536 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 15:05:17.543 | 362c318800904f078de331b017ea4f10 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 15:05:17.655 | f0943d6885eb4e3d8943a0bb6f0075d9 | ERROR    | module_admin.service.scheme_service:get_scheme_menu_categories_services:384 - 获取方案菜单分类失败: 1 validation error for SchemeMenuModel
typeName
  Field required [type=missing, input_value={'id': 25, 'name': '方...True, 'is_active': True}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-13 15:05:17.658 | f0943d6885eb4e3d8943a0bb6f0075d9 | ERROR    | module_admin.service.scheme_service:get_scheme_menu_categories_services:387 - 详细错误信息: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_admin\service\scheme_service.py", line 346, in get_scheme_menu_categories_services
    scheme_menu = SchemeMenuModel(
        id=scheme.id,
    ...<4 lines>...
        is_active=scheme.status == 1
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for SchemeMenuModel
typeName
  Field required [type=missing, input_value={'id': 25, 'name': '方...True, 'is_active': True}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing

2025-06-13 15:05:17.659 | f0943d6885eb4e3d8943a0bb6f0075d9 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:119 - 获取报告中心菜单分类成功，共 0 项
2025-06-13 15:05:24.160 | fb879a5ed13c49a3a9fe31933d20268f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 15:05:24.575 | 7b2599c4c563436aaf69448a52ad93b3 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 15:05:25.149 | 845d10e476ed40e5a4675ccd8f9e2566 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 15:05:25.841 | 190c6cfd640d4114b3104c923c12e8b6 | ERROR    | module_admin.service.scheme_service:get_scheme_menu_categories_services:384 - 获取方案菜单分类失败: 1 validation error for SchemeMenuModel
typeName
  Field required [type=missing, input_value={'id': 25, 'name': '方...True, 'is_active': True}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-13 15:05:25.843 | 190c6cfd640d4114b3104c923c12e8b6 | ERROR    | module_admin.service.scheme_service:get_scheme_menu_categories_services:387 - 详细错误信息: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_admin\service\scheme_service.py", line 346, in get_scheme_menu_categories_services
    scheme_menu = SchemeMenuModel(
        id=scheme.id,
    ...<4 lines>...
        is_active=scheme.status == 1
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for SchemeMenuModel
typeName
  Field required [type=missing, input_value={'id': 25, 'name': '方...True, 'is_active': True}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing

2025-06-13 15:05:25.843 | 190c6cfd640d4114b3104c923c12e8b6 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:119 - 获取报告中心菜单分类成功，共 0 项
2025-06-13 15:10:12.159 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 15:10:12.159 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 15:10:14.758 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-13 15:10:14.758 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 15:10:15.971 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 15:10:15.971 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 15:10:15.974 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 15:10:16.427 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 15:10:17.016 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 15:10:17.016 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-13 16:22:51.093 | b6fa39df5e724b949bdb91571199d7ad | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-13 16:22:51.238 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 16:22:51.239 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 16:22:54.849 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-13 16:22:54.849 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 16:22:57.965 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 16:22:57.965 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 16:22:57.967 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 16:22:59.761 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 16:23:00.670 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 16:23:00.671 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-13 16:23:00.676 | 53b4b55fca2341049fe952bbe1e41285 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-13 16:23:00.746 | f3ec1424177b4f6f81bf9fd4e604651d | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为b9d938f6-cb2c-48c0-a221-9b5f5b272e4b的会话获取图片验证码成功
2025-06-13 16:23:05.212 | 290c897464e248d189c16627fe3f6a28 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-13 16:23:05.791 | 681784ec74584891bf8214c17e8627d2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 16:23:06.662 | 412070d526684381b979a1497069b877 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 16:23:09.706 | 35870198abe84457a10780926a65758a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 16:23:09.738 | 35870198abe84457a10780926a65758a | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 16:23:09.738 | 35870198abe84457a10780926a65758a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 16:23:09.739 | 35870198abe84457a10780926a65758a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-13 16:23:09.739 | 35870198abe84457a10780926a65758a | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 16:23:09.770 | 35870198abe84457a10780926a65758a | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 16:23:09.770 | 35870198abe84457a10780926a65758a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-13 16:23:10.187 | 35870198abe84457a10780926a65758a | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 1, 已处理: 2, 紧急: 2, 负面: 4, 正面: 1
2025-06-13 16:23:10.188 | 35870198abe84457a10780926a65758a | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-13 16:23:10.189 | 35870198abe84457a10780926a65758a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=1 processed_count=2 urgent_count=2 negative_count=4 positive_count=1
2025-06-13 16:23:10.189 | 35870198abe84457a10780926a65758a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-13 16:23:11.037 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-13 16:23:11.068 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-13 16:23:11.068 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-13 16:23:11.069 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-13 16:23:11.070 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-13 16:23:11.070 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-13 16:23:11.101 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-13 16:23:11.101 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-13 16:23:11.277 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-13 16:23:11.278 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-13 16:23:11.278 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-13 16:23:11.341 | f256add0e20f4bceb8cb2e30886d2fed | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-13 16:23:11.342 | f256add0e20f4bceb8cb2e30886d2fed | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-13 16:23:11.342 | f256add0e20f4bceb8cb2e30886d2fed | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-13 16:23:11.343 | f256add0e20f4bceb8cb2e30886d2fed | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-13 16:23:11.343 | f256add0e20f4bceb8cb2e30886d2fed | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-13 16:23:11.344 | f256add0e20f4bceb8cb2e30886d2fed | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-13 16:23:11.344 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-13 16:23:11.345 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-13 16:23:11.345 | f256add0e20f4bceb8cb2e30886d2fed | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-13 16:23:13.942 | 51ef02e395b24112b5d0ab63ea6c6fd3 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 16:23:14.128 | 92809673689f46368086879c5ebaa5f7 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:380 - 成功创建 4 个菜单分类
2025-06-13 16:23:14.129 | 92809673689f46368086879c5ebaa5f7 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:119 - 获取报告中心菜单分类成功，共 4 项
2025-06-13 16:23:15.460 | febd26ba93f54b9081de807f0600ef49 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 16:23:15.657 | 974aa4dcd841472ab75fb93f8d68e79c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 16:23:16.716 | 7a7d6269826e4576b15374923eebb41d | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 16:23:16.747 | 6d99874a470e4ce898d143eef7917331 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:380 - 成功创建 4 个菜单分类
2025-06-13 16:23:16.747 | 6d99874a470e4ce898d143eef7917331 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:119 - 获取报告中心菜单分类成功，共 4 项
2025-06-13 16:23:22.674 | 678ed3f5458947c784ea37fd96262169 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 16:23:23.179 | a0c5b3a5411b4f41b716fde66e38f5bd | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 16:23:23.536 | 6083e51778b04c0196b700fa2bad9ebe | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 16:23:24.611 | 3eb2d4dc32744f61bedd5503e837fc5e | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 16:24:43.356 | d4a0473ff5a049f791e2ff56adb38842 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 16:24:43.630 | f2938450355c4f92944efc11f0c59715 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 16:24:48.309 | 57c4a61fab294bdc8cebb83a8423a3a7 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:58 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 16:32:03.066 | 70b4fb79bcfd4bc1829bb0a45754b02e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 16:32:03.211 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 16:32:03.212 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 16:32:06.571 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-13 16:32:06.572 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 16:32:07.484 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 16:32:07.485 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 16:32:07.486 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 16:32:07.896 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 16:32:08.381 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 16:32:08.382 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-13 16:32:08.506 | 7f7e260d4d7e440cafc02fc8629e2817 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 16:32:08.878 | fbac5a61660247bdb028dd0dd3e1bac0 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 16:32:09.251 | 875f0160da3e4c3bafcfe56de3aaf756 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:380 - 成功创建 4 个菜单分类
2025-06-13 16:32:09.251 | 875f0160da3e4c3bafcfe56de3aaf756 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-13 16:32:11.835 | 66689b6a2f4947c6b27243f560c1c4ee | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 16:32:17.123 | 2ee87e761063447b9efed91665bb32ce | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 16:32:17.297 | 1c0b69aab65044f7800c92a6b2a27661 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 16:32:17.912 | 056a75912cdb40908e063931fc4f65d7 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: PageUtil() takes no arguments
2025-06-13 16:32:18.184 | ea524ef76f3f438ab114bd599d55d72f | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:380 - 成功创建 4 个菜单分类
2025-06-13 16:32:18.185 | ea524ef76f3f438ab114bd599d55d72f | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-13 16:35:06.377 | 5c6e31be9917426a8ac3d964234595fb | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-13 16:35:06.438 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 16:35:06.438 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-13 16:35:09.246 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-13 16:35:09.246 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-13 16:35:10.685 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-13 16:35:10.687 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-13 16:35:10.692 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-13 16:35:11.384 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-13 16:35:11.918 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-13 16:35:11.919 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-13 16:35:12.209 | d8a02572a73f4a94ab277f67f4fde7dd | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-13 16:35:12.965 | 315262ae426b4a7789a2e3f7857380e0 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-13 16:35:13.188 | b6bd953d49c84d97a241736892632619 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:380 - 成功创建 4 个菜单分类
2025-06-13 16:35:13.188 | b6bd953d49c84d97a241736892632619 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-13 16:35:19.037 | 5bdcb00220f342e6be98299b9c189a8d | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-13 16:35:19.872 | 5f6765e756bb40ca8d0b49bbb6b659d3 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-13 16:35:21.006 | c671131d88064c91be1395cf5c901e30 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-13 16:35:22.265 | 15c1b5bcbf33441db8c01e5e5617579e | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-13 16:35:24.452 | 6b6efa7d43654278b049d7450f429a78 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-13 19:19:58.513 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-13 19:19:58.516 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
