2025-06-14 13:23:08.727 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-14 13:23:08.728 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-14 13:23:10.078 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-14 13:23:10.078 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-14 13:23:10.085 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-14 13:23:10.844 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-14 13:23:11.549 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-14 13:23:11.549 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-14 13:23:34.684 | 130b069af6e744bda45a32c5f5f66ccd | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为b8b191c3-b1d0-47fc-a0e1-6fa1a6a7cdbd的会话获取图片验证码成功
2025-06-14 13:23:37.636 | b05171b99b3b4514aeae43880a97bdbe | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为11444b05-31cf-4cb2-84f9-c7f7fd18c588的会话获取图片验证码成功
2025-06-14 13:23:39.839 | 3427d39a9a9f401d88cf434e3d78a651 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为4e0e15b1-512f-4f56-8655-6a90c46c8e8d的会话获取图片验证码成功
2025-06-14 13:23:42.702 | b1d569a7250b48618c0f4a1541e61817 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为aba6899f-08aa-4e0b-9651-9e8bcdf9ca4a的会话获取图片验证码成功
2025-06-14 13:23:42.938 | e1df3dc3688a42a6a655a9f748be3fe6 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为5b1034ae-d243-454e-9f7a-b228109cea53的会话获取图片验证码成功
2025-06-14 13:23:43.858 | 7fe19cfd155243b8ae12f9b489d8feb6 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为a51c5ad1-1845-4d63-992f-2404510dc107的会话获取图片验证码成功
2025-06-14 13:23:49.643 | 57201091a84c40ac905e4fe4f61946a6 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-14 13:23:49.972 | 215ea63e55154f55a72a29971e5bf523 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 13:23:50.236 | f9127309b4834c4e98d70d82dd8ff843 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 13:23:53.376 | c696723cc5f34919ba01a07571bff10d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-14 13:23:53.410 | c696723cc5f34919ba01a07571bff10d | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 13:23:53.410 | c696723cc5f34919ba01a07571bff10d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 13:23:53.411 | c696723cc5f34919ba01a07571bff10d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-14 13:23:53.411 | c696723cc5f34919ba01a07571bff10d | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-14 13:23:53.445 | c696723cc5f34919ba01a07571bff10d | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 13:23:53.445 | c696723cc5f34919ba01a07571bff10d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-14 13:23:53.631 | c696723cc5f34919ba01a07571bff10d | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 1, 已处理: 2, 紧急: 2, 负面: 4, 正面: 1
2025-06-14 13:23:53.631 | c696723cc5f34919ba01a07571bff10d | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-14 13:23:53.632 | c696723cc5f34919ba01a07571bff10d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=1 processed_count=2 urgent_count=2 negative_count=4 positive_count=1
2025-06-14 13:23:53.632 | c696723cc5f34919ba01a07571bff10d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-14 13:23:53.879 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-14 13:23:53.910 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 13:23:53.911 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 13:23:53.911 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-14 13:23:53.912 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-14 13:23:53.912 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-14 13:23:53.944 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 13:23:53.945 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-14 13:23:54.136 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-14 13:23:54.137 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-14 13:23:54.137 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-14 13:23:54.204 | 224531f3c5a642bd8bc092c7a1728c62 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-14 13:23:54.205 | 224531f3c5a642bd8bc092c7a1728c62 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-14 13:23:54.205 | 224531f3c5a642bd8bc092c7a1728c62 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-14 13:23:54.206 | 224531f3c5a642bd8bc092c7a1728c62 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-14 13:23:54.206 | 224531f3c5a642bd8bc092c7a1728c62 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-14 13:23:54.206 | 224531f3c5a642bd8bc092c7a1728c62 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-14 13:23:54.207 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-14 13:23:54.207 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-14 13:23:54.207 | 224531f3c5a642bd8bc092c7a1728c62 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-14 13:24:02.423 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-14 13:24:02.454 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 13:24:02.454 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 13:24:02.454 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-14 13:24:02.455 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-14 13:24:02.455 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-14 13:24:02.486 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 13:24:02.486 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-14 13:24:02.669 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-14 13:24:02.670 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-14 13:24:02.670 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-14 13:24:02.733 | 7bebb47f924242809f1207e451600759 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-14 13:24:02.734 | 7bebb47f924242809f1207e451600759 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-14 13:24:02.734 | 7bebb47f924242809f1207e451600759 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-14 13:24:02.734 | 7bebb47f924242809f1207e451600759 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-14 13:24:02.735 | 7bebb47f924242809f1207e451600759 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-14 13:24:02.735 | 7bebb47f924242809f1207e451600759 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-14 13:24:02.736 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-14 13:24:02.736 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-14 13:24:02.736 | 7bebb47f924242809f1207e451600759 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-14 13:24:10.447 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 0
2025-06-14 13:24:10.480 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 13:24:10.480 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 13:24:10.481 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-14 13:24:10.481 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 0
2025-06-14 13:24:10.481 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-14 13:24:10.481 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 0
2025-06-14 13:24:10.513 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 13:24:10.513 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-14 13:24:10.698 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-14 13:24:10.699 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-14 13:24:10.699 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-14 13:24:10.762 | 1088b2dbab9542c38751e0c2375beef7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-14 13:24:10.763 | 1088b2dbab9542c38751e0c2375beef7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-14 13:24:10.763 | 1088b2dbab9542c38751e0c2375beef7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-14 13:24:10.763 | 1088b2dbab9542c38751e0c2375beef7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-14 13:24:10.764 | 1088b2dbab9542c38751e0c2375beef7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-14 13:24:10.764 | 1088b2dbab9542c38751e0c2375beef7 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-14 13:24:10.764 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-14 13:24:10.765 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-14 13:24:10.765 | 1088b2dbab9542c38751e0c2375beef7 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-14 13:24:13.698 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 1
2025-06-14 13:24:13.730 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 13:24:13.730 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 13:24:13.731 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-14 13:24:13.731 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 1
2025-06-14 13:24:13.732 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-14 13:24:13.732 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 1
2025-06-14 13:24:13.764 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 13:24:13.764 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-14 13:24:13.955 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-14 13:24:13.956 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-14 13:24:13.957 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-14 13:24:14.022 | c3ebf7da7dce443a91a494ff6be4d021 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-14 13:24:14.023 | c3ebf7da7dce443a91a494ff6be4d021 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-14 13:24:14.023 | c3ebf7da7dce443a91a494ff6be4d021 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-14 13:24:14.024 | c3ebf7da7dce443a91a494ff6be4d021 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-14 13:24:14.024 | c3ebf7da7dce443a91a494ff6be4d021 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-14 13:24:14.024 | c3ebf7da7dce443a91a494ff6be4d021 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-14 13:24:14.024 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-14 13:24:14.024 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-14 13:24:14.025 | c3ebf7da7dce443a91a494ff6be4d021 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-14 13:24:16.709 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 2
2025-06-14 13:24:16.741 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 13:24:16.741 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 13:24:16.742 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-14 13:24:16.742 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 2
2025-06-14 13:24:16.743 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-14 13:24:16.743 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 2
2025-06-14 13:24:16.777 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 13:24:16.777 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 2 条
2025-06-14 13:24:16.962 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 1, 已处理: 1, 紧急: 2, 负面: 3, 正面: 1
2025-06-14 13:24:16.963 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-14 13:24:16.964 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=1 processed_count=1 urgent_count=2 negative_count=3 positive_count=1
2025-06-14 13:24:17.028 | 718945c576194ab4b6ad978ad1a2c4af | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-14 13:24:17.029 | 718945c576194ab4b6ad978ad1a2c4af | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-14 13:24:17.029 | 718945c576194ab4b6ad978ad1a2c4af | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-14 13:24:17.029 | 718945c576194ab4b6ad978ad1a2c4af | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-14 13:24:17.029 | 718945c576194ab4b6ad978ad1a2c4af | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-14 13:24:17.030 | 718945c576194ab4b6ad978ad1a2c4af | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-14 13:24:17.030 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-14 13:24:17.031 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-14 13:24:17.031 | 718945c576194ab4b6ad978ad1a2c4af | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-14 13:24:25.416 | f2bab35a445044928de726f6c9f2efe2 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:100 - 开始编辑预警记录，输入数据: {'id': 4, 'scheme_id': None, 'warning_type': None, 'content': '行业媒体报道该品牌参加某展会，展示了最新技术成果', 'keywords': None, 'status': 0, 'create_time': None, 'update_time': datetime.datetime(2025, 6, 14, 13, 24, 25, 416373), 'create_by': '', 'update_by': 'admin', 'remark': ''}
2025-06-14 13:24:25.448 | f2bab35a445044928de726f6c9f2efe2 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:111 - 找到现有记录: ID=4, scheme_id=14
2025-06-14 13:24:25.448 | f2bab35a445044928de726f6c9f2efe2 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:118 - 准备更新的数据: {'id': 4, 'content': '行业媒体报道该品牌参加某展会，展示了最新技术成果', 'status': 0, 'update_time': datetime.datetime(2025, 6, 14, 13, 24, 25, 448910), 'update_by': 'admin', 'remark': ''}
2025-06-14 13:24:25.449 | f2bab35a445044928de726f6c9f2efe2 | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:129 - DAO层更新数据: record_id=4, update_data={'content': '行业媒体报道该品牌参加某展会，展示了最新技术成果', 'status': 0, 'update_time': datetime.datetime(2025, 6, 14, 13, 24, 25, 448910), 'update_by': 'admin', 'remark': ''}
2025-06-14 13:24:25.481 | f2bab35a445044928de726f6c9f2efe2 | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:137 - 数据库更新结果: 影响行数=1
2025-06-14 13:24:25.539 | f2bab35a445044928de726f6c9f2efe2 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:122 - 编辑预警记录成功，记录ID: 4
2025-06-14 13:24:25.539 | f2bab35a445044928de726f6c9f2efe2 | INFO     | module_warning.controller.warning_record_controller:edit_warning_record:134 - 更新成功
2025-06-14 13:24:27.279 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: 2
2025-06-14 13:24:27.312 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 13:24:27.313 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 13:24:27.313 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-14 13:24:27.314 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: 2
2025-06-14 13:24:27.314 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-14 13:24:27.315 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:60 - 添加状态筛选条件: 2
2025-06-14 13:24:27.347 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 13:24:27.348 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-14 13:24:27.535 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-14 13:24:27.536 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-14 13:24:27.536 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-14 13:24:27.600 | 806d195615fa4ae3adc74bd382d21ac9 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-14 13:24:27.600 | 806d195615fa4ae3adc74bd382d21ac9 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-14 13:24:27.601 | 806d195615fa4ae3adc74bd382d21ac9 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-14 13:24:27.601 | 806d195615fa4ae3adc74bd382d21ac9 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-14 13:24:27.601 | 806d195615fa4ae3adc74bd382d21ac9 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-14 13:24:27.602 | 806d195615fa4ae3adc74bd382d21ac9 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-14 13:24:27.602 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-14 13:24:27.603 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-14 13:24:27.603 | 806d195615fa4ae3adc74bd382d21ac9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-14 13:24:29.500 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-14 13:24:29.532 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 13:24:29.532 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 13:24:29.532 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-14 13:24:29.533 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-14 13:24:29.533 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-14 13:24:29.564 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 13:24:29.564 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-14 13:24:29.749 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-14 13:24:29.749 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-14 13:24:29.749 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-14 13:24:29.809 | 7792a73379024fd5a0613bef81a18185 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-14 13:24:29.810 | 7792a73379024fd5a0613bef81a18185 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-14 13:24:29.810 | 7792a73379024fd5a0613bef81a18185 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-14 13:24:29.810 | 7792a73379024fd5a0613bef81a18185 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-14 13:24:29.810 | 7792a73379024fd5a0613bef81a18185 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-14 13:24:29.810 | 7792a73379024fd5a0613bef81a18185 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-14 13:24:29.810 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-14 13:24:29.810 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-14 13:24:29.811 | 7792a73379024fd5a0613bef81a18185 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-14 13:24:37.880 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 18, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-14 13:24:37.917 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 13:24:37.917 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 13:24:37.918 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=18, pageNum=1, pageSize=10
2025-06-14 13:24:37.918 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 18, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-14 13:24:37.918 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 18
2025-06-14 13:24:37.949 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 13:24:37.949 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-14 13:24:38.131 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 18, 总计: 0, 待处理: 0, 已处理: 0, 紧急: 0, 负面: 0, 正面: 0
2025-06-14 13:24:38.131 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 18
2025-06-14 13:24:38.132 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=0 pending_count=0 processed_count=0 urgent_count=0 negative_count=0 positive_count=0
2025-06-14 13:24:38.193 | 65a19be5a58e4a13bb93e8ac990ade3b | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-14 13:24:38.194 | 65a19be5a58e4a13bb93e8ac990ade3b | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-14 13:24:38.194 | 65a19be5a58e4a13bb93e8ac990ade3b | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-14 13:24:38.195 | 65a19be5a58e4a13bb93e8ac990ade3b | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-14 13:24:38.195 | 65a19be5a58e4a13bb93e8ac990ade3b | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-14 13:24:38.195 | 65a19be5a58e4a13bb93e8ac990ade3b | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-14 13:24:38.196 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 18
2025-06-14 13:24:38.197 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 18 的预警设置成功
2025-06-14 13:24:38.197 | 65a19be5a58e4a13bb93e8ac990ade3b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 18, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-14 13:24:39.091 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-14 13:24:39.122 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 13:24:39.122 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 13:24:39.123 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-14 13:24:39.124 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-14 13:24:39.124 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-14 13:24:39.157 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 13:24:39.158 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-14 13:24:39.339 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-14 13:24:39.340 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-14 13:24:39.340 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-14 13:24:39.401 | 2317923803dc4f9a86c95a14b2bff753 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-14 13:24:39.402 | 2317923803dc4f9a86c95a14b2bff753 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-14 13:24:39.402 | 2317923803dc4f9a86c95a14b2bff753 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-14 13:24:39.403 | 2317923803dc4f9a86c95a14b2bff753 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-14 13:24:39.403 | 2317923803dc4f9a86c95a14b2bff753 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-14 13:24:39.403 | 2317923803dc4f9a86c95a14b2bff753 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-14 13:24:39.404 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-14 13:24:39.404 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-14 13:24:39.404 | 2317923803dc4f9a86c95a14b2bff753 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-14 13:24:40.608 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-14 13:24:40.639 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 13:24:40.639 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 13:24:40.639 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-14 13:24:40.640 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-14 13:24:40.640 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-14 13:24:40.672 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 13:24:40.672 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-14 13:24:40.861 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-14 13:24:40.862 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-14 13:24:40.862 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-14 13:24:40.929 | b05d309abb6f461d890290d0b2522b43 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-14 13:24:40.929 | b05d309abb6f461d890290d0b2522b43 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-14 13:24:40.929 | b05d309abb6f461d890290d0b2522b43 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-14 13:24:40.930 | b05d309abb6f461d890290d0b2522b43 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-14 13:24:40.930 | b05d309abb6f461d890290d0b2522b43 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-14 13:24:40.931 | b05d309abb6f461d890290d0b2522b43 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-14 13:24:40.931 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-14 13:24:40.932 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-14 13:24:40.932 | b05d309abb6f461d890290d0b2522b43 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-14 13:25:05.164 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-14 13:25:05.195 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 13:25:05.195 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 13:25:05.196 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-14 13:25:05.197 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-14 13:25:05.197 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-14 13:25:05.228 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 13:25:05.229 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-14 13:25:05.413 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 0, 已处理: 1, 紧急: 0, 负面: 1, 正面: 0
2025-06-14 13:25:05.414 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-14 13:25:05.414 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=0 processed_count=1 urgent_count=0 negative_count=1 positive_count=0
2025-06-14 13:25:05.476 | b819558fc56b49939e227d4841f6cfbe | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-14 13:25:05.476 | b819558fc56b49939e227d4841f6cfbe | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-14 13:25:05.477 | b819558fc56b49939e227d4841f6cfbe | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-14 13:25:05.477 | b819558fc56b49939e227d4841f6cfbe | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-14 13:25:05.477 | b819558fc56b49939e227d4841f6cfbe | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-14 13:25:05.478 | b819558fc56b49939e227d4841f6cfbe | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-14 13:25:05.478 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-14 13:25:05.478 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-14 13:25:05.479 | b819558fc56b49939e227d4841f6cfbe | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-14 13:25:14.487 | 4e913387d06a432cbe6054e5759e4d4d | INFO     | module_warning.service.warning_record_service:delete_warning_record_services:154 - 删除预警记录成功，记录数量: 1
2025-06-14 13:25:14.488 | 4e913387d06a432cbe6054e5759e4d4d | INFO     | module_warning.controller.warning_record_controller:delete_warning_record:155 - 删除成功
2025-06-14 13:25:14.929 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-14 13:25:14.962 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 13:25:14.963 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 13:25:14.963 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-14 13:25:14.964 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-14 13:25:14.964 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-14 13:25:14.996 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 13:25:14.996 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-14 13:25:15.183 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 0, 待处理: 0, 已处理: 0, 紧急: 0, 负面: 0, 正面: 0
2025-06-14 13:25:15.184 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-14 13:25:15.184 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=0 pending_count=0 processed_count=0 urgent_count=0 negative_count=0 positive_count=0
2025-06-14 13:25:15.245 | ef98a52a97fd419b9b3a618edac7487d | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-14 13:25:15.246 | ef98a52a97fd419b9b3a618edac7487d | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-14 13:25:15.246 | ef98a52a97fd419b9b3a618edac7487d | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-14 13:25:15.246 | ef98a52a97fd419b9b3a618edac7487d | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-14 13:25:15.247 | ef98a52a97fd419b9b3a618edac7487d | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-14 13:25:15.247 | ef98a52a97fd419b9b3a618edac7487d | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-14 13:25:15.248 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-14 13:25:15.248 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-14 13:25:15.248 | ef98a52a97fd419b9b3a618edac7487d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-14 13:27:53.618 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-14 13:27:53.618 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-14 14:41:06.652 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-14 14:41:06.653 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-14 14:41:07.613 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-14 14:41:07.614 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-14 14:41:07.624 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-14 14:41:08.204 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-14 14:41:08.723 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-14 14:41:08.723 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-14 14:41:33.945 | 5142162e25674a6c83dadd2ccb2fb29b | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为b460fa14-1c68-402a-a033-b66512574929的会话获取图片验证码成功
2025-06-14 14:41:41.618 | 7a4142523a434d06b3eafc216946e647 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-14 14:41:41.833 | 8545b5b74243426ba207e27b1a429ff2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 14:41:42.333 | cf86404dae104fdd9d90b4764f415552 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 14:42:01.021 | 3bc32b6468634665b8dda364149da539 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-14 14:42:01.047 | 3bc32b6468634665b8dda364149da539 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 14:42:01.048 | 3bc32b6468634665b8dda364149da539 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 14:42:01.048 | 3bc32b6468634665b8dda364149da539 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-14 14:42:01.049 | 3bc32b6468634665b8dda364149da539 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-14 14:42:01.074 | 3bc32b6468634665b8dda364149da539 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 14:42:01.074 | 3bc32b6468634665b8dda364149da539 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-14 14:42:01.220 | 3bc32b6468634665b8dda364149da539 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 3, 已处理: 1, 紧急: 1, 负面: 4, 正面: 1
2025-06-14 14:42:01.220 | 3bc32b6468634665b8dda364149da539 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-14 14:42:01.221 | 3bc32b6468634665b8dda364149da539 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=3 processed_count=1 urgent_count=1 negative_count=4 positive_count=1
2025-06-14 14:42:01.221 | 3bc32b6468634665b8dda364149da539 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-14 14:42:01.415 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-14 14:42:01.439 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-14 14:42:01.439 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-14 14:42:01.439 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-14 14:42:01.440 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-14 14:42:01.440 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-14 14:42:01.464 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-14 14:42:01.465 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-14 14:42:01.608 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-14 14:42:01.608 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-14 14:42:01.609 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-14 14:42:01.659 | 7ab11d16a8c54202b2af59208d9f00be | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-14 14:42:01.659 | 7ab11d16a8c54202b2af59208d9f00be | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-14 14:42:01.659 | 7ab11d16a8c54202b2af59208d9f00be | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-14 14:42:01.660 | 7ab11d16a8c54202b2af59208d9f00be | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-14 14:42:01.660 | 7ab11d16a8c54202b2af59208d9f00be | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-14 14:42:01.660 | 7ab11d16a8c54202b2af59208d9f00be | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-14 14:42:01.660 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-14 14:42:01.660 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-14 14:42:01.660 | 7ab11d16a8c54202b2af59208d9f00be | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-14 14:42:04.233 | a3fe6b0962b3471b8fc199857d688d3b | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-14 14:42:04.493 | 83dc0e6b52034b168aafaa31570cdc05 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:380 - 成功创建 4 个菜单分类
2025-06-14 14:42:04.493 | 83dc0e6b52034b168aafaa31570cdc05 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 14:42:11.263 | 8d3b7469a9f94a2f879ae9e14e824b31 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-14 14:42:11.589 | 8a913a44dd994f8db801dd3de4c4ef9f | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-14 14:42:12.439 | f1485e7648234a88933ed7628e1c037d | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-14 14:42:12.729 | f489a33fc1154987a857fd7475dca9c5 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-14 14:42:13.566 | d3003ce1727a43088dd03ef0d21f67df | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-14 14:42:13.839 | 77dd2c854a3d4227b4f9db937868871f | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-14 14:42:21.405 | a11f1b2b83fe4222beb48942079c2ba9 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 14:42:21.583 | 1dc3a0aa4088451890f05e2f25da5f67 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 14:42:22.080 | 1a57e782d00b494598637fb4ec8b9383 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-14 14:42:22.362 | 08bc6ddac4144c3b8455944cc57a4c80 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:380 - 成功创建 4 个菜单分类
2025-06-14 14:42:22.362 | 08bc6ddac4144c3b8455944cc57a4c80 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 14:48:42.449 | 988dd28a0b4e404582b70ffb6404c0f3 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-14 14:48:42.536 | f45366a0a5da4a18a8cfe9c3e6e0f99d | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:380 - 成功创建 4 个菜单分类
2025-06-14 14:48:42.536 | f45366a0a5da4a18a8cfe9c3e6e0f99d | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 14:51:30.782 | 6af0dd12c5c6481aa721f0093be3eea8 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 14:51:30.954 | 7d40b6e4764e4641a3eb2081c7ad6f30 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 14:51:31.441 | 9212518836fc4fc0aaef741b8041ba78 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-14 14:51:31.724 | 65b84847557f41459aa726830579370b | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:380 - 成功创建 4 个菜单分类
2025-06-14 14:51:31.725 | 65b84847557f41459aa726830579370b | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 14:52:22.574 | 1c49a4c086a94a6e928e6b56282f2989 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 14:52:22.746 | b3220e2679ed4ee9b38ac26b11d61074 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 14:52:23.494 | 52422fcd9b8047528bce53e010f0f0b3 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:380 - 成功创建 4 个菜单分类
2025-06-14 14:52:23.495 | 52422fcd9b8047528bce53e010f0f0b3 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 14:52:23.506 | 8e6c1b8a80da4491a1cac50e11d4159c | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value={'id': 31, 'user_id': 1, ...'admin', 'remark': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-14 14:57:40.238 | 37c1256aa63d412886a587cb888559ab | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 14:57:40.319 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-14 14:57:40.320 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-14 14:57:44.941 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-14 14:57:44.942 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-14 14:57:45.820 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-14 14:57:45.821 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-14 14:57:45.822 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-14 14:57:46.412 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-14 14:57:46.908 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-14 14:57:46.909 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-14 14:57:47.029 | e4ef8b813f5c43168449fc0bfdcf3ba5 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 14:57:48.028 | 6a2d5807bd2c4eec976f13b8e009a40e | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value=<module_admin.entity.do.s...t at 0x00000193E646BB60>, input_type=SchemeDO]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-14 14:57:48.246 | 263dcdcf8c534d279452df9fca3d162f | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:334 - 成功创建 4 个菜单分类
2025-06-14 14:57:48.247 | 263dcdcf8c534d279452df9fca3d162f | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 14:59:43.159 | 029b696d172245f19065637500a922cc | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 14:59:43.328 | 86b981390800473ca46681b78fbb0311 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 14:59:43.804 | a79ac827f3414fc6be0ed1b5b835a87a | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 1 validation error for SchemeModel
typeId
  Field required [type=missing, input_value=<module_admin.entity.do.s...t at 0x00000193E699F200>, input_type=SchemeDO]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-14 14:59:44.085 | acf5eeb3c03e4900aa9b9b7691773e66 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:334 - 成功创建 4 个菜单分类
2025-06-14 14:59:44.085 | acf5eeb3c03e4900aa9b9b7691773e66 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:02:41.984 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-14 15:02:41.985 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-14 15:02:45.219 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-14 15:02:45.220 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-14 15:02:46.136 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-14 15:02:46.137 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-14 15:02:46.141 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-14 15:02:46.552 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-14 15:02:47.069 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-14 15:02:47.069 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-14 15:04:03.055 | 2fe6e9f9ae2343efbe2fca0cf573c2b5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 15:04:03.228 | 6872e9f4542b4d3f8d75c42f1a786b1c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 15:04:03.744 | a0bf4f838bd74d9caea1a9afcde5f42e | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:04:03.744 | a0bf4f838bd74d9caea1a9afcde5f42e | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 'PageResponseModel' object has no attribute 'rows'
2025-06-14 15:04:04.001 | c7f29f2b56814eab97ae7beaba6c519e | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:384 - 成功创建 4 个菜单分类
2025-06-14 15:04:04.001 | c7f29f2b56814eab97ae7beaba6c519e | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:05:15.215 | 6d9b18d0a3064ea5b58f80010fe2adae | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 15:05:15.270 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-14 15:05:15.270 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-14 15:05:18.270 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-14 15:05:18.271 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-14 15:05:19.334 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-14 15:05:19.334 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-14 15:05:19.337 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-14 15:05:19.840 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-14 15:05:20.453 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-14 15:05:20.454 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-14 15:05:20.614 | a7a755404be4434f994be371232767a4 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 15:05:21.208 | 4a9ef39d4fa14b91a87850905b2460e6 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:05:21.208 | 4a9ef39d4fa14b91a87850905b2460e6 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 'PageResponseModel' object has no attribute 'rows'
2025-06-14 15:05:21.546 | 438860bad7d2426ab5161f992724c599 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:384 - 成功创建 4 个菜单分类
2025-06-14 15:05:21.546 | 438860bad7d2426ab5161f992724c599 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:33:14.714 | 6fd0331a4a824e4da6748188440204d8 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 15:33:14.919 | 072458162cf84ebc9b5f4af19c1b2f74 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 15:33:15.453 | 638d2d29aab347d0bebec0353ff36dd0 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:33:15.453 | 638d2d29aab347d0bebec0353ff36dd0 | ERROR    | module_admin.controller.report_controller:get_report_scheme_list:59 - 获取报告中心方案列表失败: 'PageResponseModel' object has no attribute 'rows'
2025-06-14 15:33:15.679 | 2512cf0a06e348cc8d03616c04454185 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:384 - 成功创建 4 个菜单分类
2025-06-14 15:33:15.679 | 2512cf0a06e348cc8d03616c04454185 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:35:53.841 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-14 15:35:53.841 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-14 15:35:56.492 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-14 15:35:56.492 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-14 15:35:57.441 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-14 15:35:57.441 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-14 15:35:57.443 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-14 15:35:57.875 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-14 15:35:58.465 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-14 15:35:58.466 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-14 15:40:00.076 | 28dca4d0f0c24b63be319edb208cd875 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 15:40:00.178 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-14 15:40:00.178 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-14 15:40:03.772 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-14 15:40:03.773 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-14 15:40:04.676 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-14 15:40:04.676 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-14 15:40:04.680 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-14 15:40:05.091 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-14 15:40:05.613 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-14 15:40:05.613 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-14 15:40:05.737 | 29c3da4793ce4fcaad398412be93b7ef | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 15:40:06.259 | 50007752ce4f4702b0e9568ba4fbc465 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:40:06.259 | 50007752ce4f4702b0e9568ba4fbc465 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:40:06.514 | 1917f0285bd44da4a431c14b9a2d17dd | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:384 - 成功创建 4 个菜单分类
2025-06-14 15:40:06.514 | 1917f0285bd44da4a431c14b9a2d17dd | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:42:05.692 | 3a4ea9a5e66a43caa5fa84a315984b4f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 15:42:05.877 | abb88efc7cbc43a5be83fda95b52d82f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 15:42:06.434 | 91a52b8c77de43efa6ef93e8ad46761c | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:42:06.435 | 91a52b8c77de43efa6ef93e8ad46761c | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:42:06.707 | f4801554ab7949b5aef67b3da788df45 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:384 - 成功创建 4 个菜单分类
2025-06-14 15:42:06.708 | f4801554ab7949b5aef67b3da788df45 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:47:19.745 | f0dc82bdbb8b45878e83edea66f2f2b5 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:47:19.745 | f0dc82bdbb8b45878e83edea66f2f2b5 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:47:19.829 | c21d5d03ad934e2c9ab9add9d13d6c40 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:384 - 成功创建 4 个菜单分类
2025-06-14 15:47:19.829 | c21d5d03ad934e2c9ab9add9d13d6c40 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:47:19.976 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-14 15:47:19.977 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-14 15:47:22.921 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-14 15:47:22.921 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-14 15:47:24.071 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-14 15:47:24.071 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-14 15:47:24.073 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-14 15:47:24.592 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-14 15:47:25.134 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-14 15:47:25.134 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-14 15:47:25.419 | bf371652eb9b4f658f0fdcabeb1761be | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:47:25.449 | bf371652eb9b4f658f0fdcabeb1761be | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:47:25.450 | bf371652eb9b4f658f0fdcabeb1761be | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:47:26.090 | 5e38898786024c6ba3e6c45d8aa0d546 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:47:26.119 | 5e38898786024c6ba3e6c45d8aa0d546 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:47:26.119 | 5e38898786024c6ba3e6c45d8aa0d546 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:47:26.750 | 8e235f23b8d14b1e99f8604691eb0659 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:47:26.780 | 8e235f23b8d14b1e99f8604691eb0659 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:47:26.780 | 8e235f23b8d14b1e99f8604691eb0659 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:47:27.418 | a988de4c100e44b6b3155e4c85a85318 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:47:27.449 | a988de4c100e44b6b3155e4c85a85318 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:47:27.449 | a988de4c100e44b6b3155e4c85a85318 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:47:28.101 | 40070c1adef24ad6bd5eb8df998d7d33 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:47:28.131 | 40070c1adef24ad6bd5eb8df998d7d33 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:47:28.132 | 40070c1adef24ad6bd5eb8df998d7d33 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:48:08.576 | d24bc07eaa9f4865ba16f27f3de7d7b1 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 15:48:08.720 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-14 15:48:08.720 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-14 15:48:11.263 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-14 15:48:11.263 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-14 15:48:12.270 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-14 15:48:12.270 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-14 15:48:12.272 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-14 15:48:12.734 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-14 15:48:13.240 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-14 15:48:13.241 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-14 15:48:13.385 | 9d7028cf1e614c90a92ec4d7a68657ab | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 15:48:13.931 | 4b337a25d76f4563a1c4a49d84601b83 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:48:13.958 | 4b337a25d76f4563a1c4a49d84601b83 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:48:13.958 | 4b337a25d76f4563a1c4a49d84601b83 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:48:13.958 | 4b337a25d76f4563a1c4a49d84601b83 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:48:13.958 | 4b337a25d76f4563a1c4a49d84601b83 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:48:14.290 | 36ed0b2db5d142c5ae70b7ab1bb95539 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:388 - 成功创建 4 个菜单分类
2025-06-14 15:48:14.291 | 36ed0b2db5d142c5ae70b7ab1bb95539 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:49:56.519 | cdebc7919d974fb08a4e07082d8ec11d | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-14 15:49:57.607 | 7d4af2365c414e6199e5d4dbc3d08069 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为43b5a6e7-69fd-48fb-8781-8ee086214d42的会话获取图片验证码成功
2025-06-14 15:50:00.673 | 91a485baec104e8493b034c0248b5e90 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-14 15:50:00.888 | 916ab8b7555041abb70b89e71288def3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 15:50:01.382 | 006e078bf71d4833a44520f677495023 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 15:50:05.232 | 1dda7e39b450408c9912d815ce6d4bc9 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:388 - 成功创建 4 个菜单分类
2025-06-14 15:50:05.232 | 1dda7e39b450408c9912d815ce6d4bc9 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:50:05.263 | 1b69dec530524c79ae74fcc39bb950ac | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:50:05.290 | 1b69dec530524c79ae74fcc39bb950ac | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:50:05.290 | 1b69dec530524c79ae74fcc39bb950ac | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:50:05.291 | 1b69dec530524c79ae74fcc39bb950ac | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:50:05.291 | 1b69dec530524c79ae74fcc39bb950ac | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:51:38.844 | 463a0120c09a4c80964f050e2a458b2c | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:388 - 成功创建 4 个菜单分类
2025-06-14 15:51:38.844 | 463a0120c09a4c80964f050e2a458b2c | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:51:38.847 | c4898540ce174c1eaec7ef099ab13893 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:51:38.870 | c4898540ce174c1eaec7ef099ab13893 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:51:38.870 | c4898540ce174c1eaec7ef099ab13893 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:51:38.871 | c4898540ce174c1eaec7ef099ab13893 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:51:38.871 | c4898540ce174c1eaec7ef099ab13893 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:51:39.570 | c72443f73cd1419b8cd233ce3191eb06 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 15:51:39.747 | 5f02cc7bca6a4adf9cc32f6523be049d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 15:51:40.205 | 5bab6131306f4a5696fd4f6442b45cc6 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:388 - 成功创建 4 个菜单分类
2025-06-14 15:51:40.205 | 5bab6131306f4a5696fd4f6442b45cc6 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:51:40.243 | 2d6a4278f21748989411aa7540165cab | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:51:40.268 | 2d6a4278f21748989411aa7540165cab | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:51:40.268 | 2d6a4278f21748989411aa7540165cab | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:51:40.269 | 2d6a4278f21748989411aa7540165cab | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:51:40.269 | 2d6a4278f21748989411aa7540165cab | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:51:58.456 | 18750d2619f94db083031d418d010f25 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 15:51:58.642 | efd0f423dfa4465b98498df6e9bb4f7c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 15:51:59.144 | 41497c0c2bce4bd99a741eb1b71a54f0 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:51:59.168 | 41497c0c2bce4bd99a741eb1b71a54f0 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:51:59.168 | 41497c0c2bce4bd99a741eb1b71a54f0 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:51:59.168 | 41497c0c2bce4bd99a741eb1b71a54f0 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:51:59.168 | 41497c0c2bce4bd99a741eb1b71a54f0 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:51:59.438 | 8a55fe3f5bdb42b28595aab9d069c4fe | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:388 - 成功创建 4 个菜单分类
2025-06-14 15:51:59.439 | 8a55fe3f5bdb42b28595aab9d069c4fe | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:55:08.390 | 35c76f88022043b9a990b2f1e5a11a4c | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:08.413 | 35c76f88022043b9a990b2f1e5a11a4c | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:08.413 | 35c76f88022043b9a990b2f1e5a11a4c | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:08.414 | 35c76f88022043b9a990b2f1e5a11a4c | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:08.414 | 35c76f88022043b9a990b2f1e5a11a4c | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:08.475 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-14 15:55:08.476 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-14 15:55:12.039 |  | INFO     | server:lifespan:39 - RuoYi-FastAPI开始启动
2025-06-14 15:55:12.040 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-14 15:55:12.919 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-14 15:55:12.919 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-14 15:55:12.922 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-14 15:55:13.333 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-14 15:55:13.839 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-14 15:55:13.839 |  | INFO     | server:lifespan:46 - RuoYi-FastAPI启动成功
2025-06-14 15:55:14.034 | b34efb087514443f838e98adc45e416a | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:388 - 成功创建 4 个菜单分类
2025-06-14 15:55:14.034 | b34efb087514443f838e98adc45e416a | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:55:14.079 | 92b56c2e9add47febc7ec6928690be80 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 15:55:14.250 | 723dabd2d39b497fb56c9792fc595a43 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 15:55:14.740 | 5fe60766f10a4316ad128b02d9ff717b | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:14.765 | 5fe60766f10a4316ad128b02d9ff717b | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:14.766 | 5fe60766f10a4316ad128b02d9ff717b | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:14.766 | 5fe60766f10a4316ad128b02d9ff717b | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:14.766 | 5fe60766f10a4316ad128b02d9ff717b | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:15.000 | c1cbc9e0cca04496a950efa87c2d8591 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:388 - 成功创建 4 个菜单分类
2025-06-14 15:55:15.000 | c1cbc9e0cca04496a950efa87c2d8591 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:55:16.048 | 5d2617ea75e44c12ab9b916b78d6b05e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-14 15:55:16.222 | 84679ec1329d4aaaa4fcf395834a17fa | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-14 15:55:16.923 | c5c490e6545b420ba8c0b7090bc2a864 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:388 - 成功创建 4 个菜单分类
2025-06-14 15:55:16.924 | c5c490e6545b420ba8c0b7090bc2a864 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:120 - 获取报告中心菜单分类成功，共 4 项
2025-06-14 15:55:16.963 | 4db3e942e5ce4f708134aad77336dbbb | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:16.985 | 4db3e942e5ce4f708134aad77336dbbb | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:16.986 | 4db3e942e5ce4f708134aad77336dbbb | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:16.986 | 4db3e942e5ce4f708134aad77336dbbb | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:16.986 | 4db3e942e5ce4f708134aad77336dbbb | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:20.026 | c45c8e95ed894fb7bec8f1a0a29205ca | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:20.050 | c45c8e95ed894fb7bec8f1a0a29205ca | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:20.050 | c45c8e95ed894fb7bec8f1a0a29205ca | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:20.051 | c45c8e95ed894fb7bec8f1a0a29205ca | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:20.051 | c45c8e95ed894fb7bec8f1a0a29205ca | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:20.555 | 124b89f4c14241ec8f6944f2bd35aa18 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:20.577 | 124b89f4c14241ec8f6944f2bd35aa18 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:20.577 | 124b89f4c14241ec8f6944f2bd35aa18 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:20.577 | 124b89f4c14241ec8f6944f2bd35aa18 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:20.578 | 124b89f4c14241ec8f6944f2bd35aa18 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:21.600 | 88c96319e0584e938807648b798ef1ca | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:21.623 | 88c96319e0584e938807648b798ef1ca | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:21.624 | 88c96319e0584e938807648b798ef1ca | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:21.624 | 88c96319e0584e938807648b798ef1ca | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:21.624 | 88c96319e0584e938807648b798ef1ca | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:23.660 | d8f3e09bc1694b61a07f83055e8dfdb1 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:23.683 | d8f3e09bc1694b61a07f83055e8dfdb1 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:23.683 | d8f3e09bc1694b61a07f83055e8dfdb1 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:23.684 | d8f3e09bc1694b61a07f83055e8dfdb1 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:23.684 | d8f3e09bc1694b61a07f83055e8dfdb1 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:25.790 | 23a49efae7e1464ca5c959eeb499303b | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:25.814 | 23a49efae7e1464ca5c959eeb499303b | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:25.815 | 23a49efae7e1464ca5c959eeb499303b | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:25.815 | 23a49efae7e1464ca5c959eeb499303b | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:25.816 | 23a49efae7e1464ca5c959eeb499303b | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:27.223 | 7d1f65f0488f403ea4020c5448b42f7e | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:27.246 | 7d1f65f0488f403ea4020c5448b42f7e | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:27.247 | 7d1f65f0488f403ea4020c5448b42f7e | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:27.247 | 7d1f65f0488f403ea4020c5448b42f7e | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:27.248 | 7d1f65f0488f403ea4020c5448b42f7e | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:28.216 | 91c76cc65d384556bd056239d4523f8a | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:28.240 | 91c76cc65d384556bd056239d4523f8a | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:28.241 | 91c76cc65d384556bd056239d4523f8a | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:28.241 | 91c76cc65d384556bd056239d4523f8a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:28.242 | 91c76cc65d384556bd056239d4523f8a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:28.503 | c2f3133e69b7488eb7e3328c1536a7fb | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:28.525 | c2f3133e69b7488eb7e3328c1536a7fb | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:28.526 | c2f3133e69b7488eb7e3328c1536a7fb | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:28.526 | c2f3133e69b7488eb7e3328c1536a7fb | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:28.526 | c2f3133e69b7488eb7e3328c1536a7fb | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:29.341 | 808a375bb56c4b249c87271939a00b92 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:29.365 | 808a375bb56c4b249c87271939a00b92 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:29.366 | 808a375bb56c4b249c87271939a00b92 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:29.366 | 808a375bb56c4b249c87271939a00b92 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:29.367 | 808a375bb56c4b249c87271939a00b92 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:29.629 | 90244ed574fe40b995ac48ed3b6fe2ea | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:29.653 | 90244ed574fe40b995ac48ed3b6fe2ea | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:29.653 | 90244ed574fe40b995ac48ed3b6fe2ea | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:29.654 | 90244ed574fe40b995ac48ed3b6fe2ea | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:29.654 | 90244ed574fe40b995ac48ed3b6fe2ea | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:31.176 | 69a8248b7f2841d2989e1c64b3d24e4c | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:31.200 | 69a8248b7f2841d2989e1c64b3d24e4c | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:31.200 | 69a8248b7f2841d2989e1c64b3d24e4c | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:31.201 | 69a8248b7f2841d2989e1c64b3d24e4c | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:31.201 | 69a8248b7f2841d2989e1c64b3d24e4c | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:31.464 | 394903afea8a49519591f0f34b7501b2 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:31.487 | 394903afea8a49519591f0f34b7501b2 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:31.487 | 394903afea8a49519591f0f34b7501b2 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:31.488 | 394903afea8a49519591f0f34b7501b2 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:31.488 | 394903afea8a49519591f0f34b7501b2 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:32.073 | 711eeab2b4314627b7fa4605c1f1375b | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-14 15:55:32.097 | 711eeab2b4314627b7fa4605c1f1375b | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 26
2025-06-14 15:55:32.098 | 711eeab2b4314627b7fa4605c1f1375b | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=26
2025-06-14 15:55:32.098 | 711eeab2b4314627b7fa4605c1f1375b | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 获取报告中心方案列表成功，共 26 条记录
2025-06-14 15:55:32.099 | 711eeab2b4314627b7fa4605c1f1375b | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-14 15:55:37.877 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-14 15:55:37.877 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
