from datetime import datetime
from fastapi import API<PERSON><PERSON>er, Depends, Request
from pydantic_validation_decorator import <PERSON><PERSON><PERSON><PERSON><PERSON>s
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.service.login_service import LoginService
from module_warning.service.warning_scheme_service import WarningSchemeService
from module_warning.entity.vo.warning_vo import (
    DeleteWarningSchemeModel,
    WarningSchemeModel,
    WarningSchemePageQueryModel
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


warningSchemeController = APIRouter(prefix='/warning/scheme', dependencies=[Depends(LoginService.get_current_user)])


@warningSchemeController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:list'))]
)
async def get_warning_scheme_list(
    request: Request,
    scheme_page_query: WarningSchemePageQueryModel = Depends(WarningSchemePageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取预警方案列表
    """
    try:
        # 获取分页数据
        scheme_page_query_result = await WarningSchemeService.get_warning_scheme_list_services(
            query_db, scheme_page_query, is_page=True
        )
        logger.info('获取预警方案列表成功')
        return ResponseUtil.success(model_content=scheme_page_query_result)
    except Exception as e:
        logger.error(f'获取预警方案列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警方案列表失败: {str(e)}')


@warningSchemeController.get(
    '/active', dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:list'))]
)
async def get_active_warning_schemes(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取所有启用的预警方案
    """
    try:
        active_schemes_result = await WarningSchemeService.get_active_warning_schemes_services(query_db)
        logger.info('获取启用预警方案列表成功')
        return ResponseUtil.success(data=active_schemes_result)
    except Exception as e:
        logger.error(f'获取启用预警方案列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取启用预警方案列表失败: {str(e)}')


@warningSchemeController.get(
    '/{scheme_id}', dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:query'))]
)
async def get_warning_scheme_detail(
    request: Request,
    scheme_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取预警方案详情
    """
    try:
        scheme_detail_result = await WarningSchemeService.get_warning_scheme_detail_services(query_db, scheme_id)
        logger.info(f'获取预警方案详情成功，方案ID: {scheme_id}')
        return ResponseUtil.success(data=scheme_detail_result)
    except Exception as e:
        logger.error(f'获取预警方案详情失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警方案详情失败: {str(e)}')


@warningSchemeController.post('', dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:add'))])
@ValidateFields(validate_model='add_warning_scheme')
@Log(title='预警方案', business_type=BusinessType.INSERT)
async def add_warning_scheme(
    request: Request,
    add_scheme: WarningSchemeModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增预警方案
    """
    try:
        add_scheme.create_by = current_user.user.user_name
        add_scheme.create_time = datetime.now()
        add_scheme.update_by = current_user.user.user_name
        add_scheme.update_time = datetime.now()
        add_scheme_result = await WarningSchemeService.add_warning_scheme_services(query_db, add_scheme)
        logger.info(add_scheme_result.message)
        return ResponseUtil.success(msg=add_scheme_result.message)
    except Exception as e:
        logger.error(f'新增预警方案失败: {str(e)}')
        return ResponseUtil.error(msg=f'新增预警方案失败: {str(e)}')


@warningSchemeController.put('', dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:edit'))])
@ValidateFields(validate_model='edit_warning_scheme')
@Log(title='预警方案', business_type=BusinessType.UPDATE)
async def edit_warning_scheme(
    request: Request,
    edit_scheme: WarningSchemeModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑预警方案
    """
    try:
        edit_scheme.update_by = current_user.user.user_name
        edit_scheme.update_time = datetime.now()
        edit_scheme_result = await WarningSchemeService.edit_warning_scheme_services(query_db, edit_scheme)
        logger.info(edit_scheme_result.message)
        return ResponseUtil.success(msg=edit_scheme_result.message)
    except Exception as e:
        logger.error(f'编辑预警方案失败: {str(e)}')
        return ResponseUtil.error(msg=f'编辑预警方案失败: {str(e)}')


@warningSchemeController.delete('', dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:remove'))])
@ValidateFields(validate_model='delete_warning_scheme')
@Log(title='预警方案', business_type=BusinessType.DELETE)
async def delete_warning_scheme(
    request: Request,
    delete_scheme: DeleteWarningSchemeModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除预警方案
    """
    try:
        delete_scheme_result = await WarningSchemeService.delete_warning_scheme_services(query_db, delete_scheme)
        logger.info(delete_scheme_result.message)
        return ResponseUtil.success(msg=delete_scheme_result.message)
    except Exception as e:
        logger.error(f'删除预警方案失败: {str(e)}')
        return ResponseUtil.error(msg=f'删除预警方案失败: {str(e)}')


@warningSchemeController.put(
    '/{scheme_id}/status', dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:edit'))]
)
@Log(title='预警方案状态', business_type=BusinessType.UPDATE)
async def toggle_warning_scheme_status(
    request: Request,
    scheme_id: int,
    is_active: bool,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    切换预警方案状态
    """
    try:
        toggle_result = await WarningSchemeService.toggle_scheme_status_services(
            query_db, scheme_id, is_active, current_user.user.user_name
        )
        logger.info(toggle_result.message)
        return ResponseUtil.success(msg=toggle_result.message)
    except Exception as e:
        logger.error(f'切换预警方案状态失败: {str(e)}')
        return ResponseUtil.error(msg=f'切换预警方案状态失败: {str(e)}')


@warningSchemeController.get(
    '/check/name', dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:list'))]
)
async def check_scheme_name_unique(
    request: Request,
    scheme_name: str,
    scheme_id: int = None,
    query_db: AsyncSession = Depends(get_db),
):
    """
    检查预警方案名称唯一性
    """
    try:
        is_unique = await WarningSchemeService.check_scheme_name_unique_services(query_db, scheme_name, scheme_id)
        if is_unique:
            logger.info(f'预警方案名称检查通过: {scheme_name}')
            return ResponseUtil.success(data={'unique': True}, msg='方案名称可用')
        else:
            logger.warning(f'预警方案名称已存在: {scheme_name}')
            return ResponseUtil.failure(data={'unique': False}, msg='方案名称已存在')
    except Exception as e:
        logger.error(f'检查预警方案名称唯一性失败: {str(e)}')
        return ResponseUtil.error(msg=f'检查预警方案名称唯一性失败: {str(e)}')


@warningSchemeController.get(
    '/export', dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:export'))]
)
async def export_warning_scheme(
    request: Request,
    scheme_page_query: WarningSchemePageQueryModel = Depends(WarningSchemePageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    导出预警方案
    """
    try:
        # 获取所有数据（不分页）
        scheme_list_result = await WarningSchemeService.get_warning_scheme_list_services(
            query_db, scheme_page_query, is_page=False
        )
        logger.info('导出预警方案成功')
        return ResponseUtil.success(data=scheme_list_result, msg='导出成功')
    except Exception as e:
        logger.error(f'导出预警方案失败: {str(e)}')
        return ResponseUtil.error(msg=f'导出预警方案失败: {str(e)}')
