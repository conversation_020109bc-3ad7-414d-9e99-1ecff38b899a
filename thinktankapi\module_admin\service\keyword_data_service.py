from datetime import datetime
from sqlalchemy import select, and_, func, case
from sqlalchemy.ext.asyncio import AsyncSession
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.dao.keyword_data_dao import KeywordDataDao
from module_admin.entity.do.keyword_data_do import KeywordData
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.keyword_data_vo import (
    DeleteKeywordDataModel,
    KeywordDataModel,
    KeywordDataPageQueryModel,
    KeywordDataResponseModel,
    KeywordDataStatisticsModel,
    FilterStatisticsModel,
    FilterItemModel
)
from utils.common_util import CamelCaseUtil
from utils.log_util import logger


class KeywordDataService:
    """
    关键词数据模块服务层
    """

    @classmethod
    async def get_keyword_data_list_services(
        cls, query_db: AsyncSession, query_object: KeywordDataPageQueryModel, is_page: bool = False
    ):
        """
        获取关键词数据列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 关键词数据列表信息对象
        """
        # 添加调试日志
        logger.info(f'Service层接收到的查询参数: begin_time={query_object.begin_time}, end_time={query_object.end_time}, time_filter={query_object.time_filter}')
        logger.info(f'Service层查询对象类型: {type(query_object)}')
        logger.info(f'Service层查询对象属性: {vars(query_object)}')
        
        keyword_data_list_result = await KeywordDataDao.get_keyword_data_list(query_db, query_object, is_page)

        if is_page:
            # 分页结果处理
            result = []
            for row in keyword_data_list_result.records:
                # 检查row是否是字典（经过CamelCaseUtil转换）还是对象
                if isinstance(row, dict):
                    keyword_data_dict = {
                        'id': row.get('id'),
                        'title': row.get('title'),
                        'content': row.get('content'),
                        'source_url': row.get('url'),
                        'keywords': row.get('keyword'),
                        'source': row.get('type'),
                        'time': row.get('createtime').strftime('%Y-%m-%d %H:%M:%S') if row.get('createtime') else '',
                        'platform_name': row.get('web'),
                        'selected': False,
                        'sentiment': row.get('sentiment') or 'neutral',  # 使用数据库中的真实sentiment值
                        'views': 0,  # 默认值
                        'comments': 0,  # 默认值
                        'images': []  # 默认空列表
                    }
                else:
                    keyword_data_dict = {
                        'id': row.id,
                        'title': row.title,
                        'content': row.content,
                        'source_url': row.url,
                        'keywords': row.keyword,
                        'source': row.type,
                        'time': row.createtime.strftime('%Y-%m-%d %H:%M:%S') if row.createtime else '',
                        'platform_name': row.web,
                        'selected': False,
                        'sentiment': row.sentiment or 'neutral',  # 使用数据库中的真实sentiment值
                        'views': 0,  # 默认值
                        'comments': 0,  # 默认值
                        'images': []  # 默认空列表
                    }
                result.append(KeywordDataResponseModel(**keyword_data_dict))

            keyword_data_list_result.records = result
            return keyword_data_list_result
        else:
            # 非分页结果处理
            result = []
            for row in keyword_data_list_result:
                keyword_data_dict = {
                    'id': row.id,
                    'title': row.title,
                    'content': row.content,
                    'source_url': row.url,
                    'keywords': row.keyword,
                    'source': row.type,
                    'time': row.createtime.strftime('%Y-%m-%d %H:%M:%S') if row.createtime else '',
                    'platform_name': row.web,
                    'selected': False,
                    'sentiment': row.sentiment or 'neutral',  # 使用数据库中的真实sentiment值
                    'views': 0,
                    'comments': 0,
                    'images': []
                }
                result.append(KeywordDataResponseModel(**keyword_data_dict))
            return result

    @classmethod
    async def add_keyword_data_services(cls, query_db: AsyncSession, add_keyword_data: KeywordDataModel):
        """
        新增关键词数据信息service

        :param query_db: orm对象
        :param add_keyword_data: 新增关键词数据对象
        :return: 新增关键词数据校验结果
        """
        add_keyword_data_result = await KeywordDataDao.add_keyword_data_dao(query_db, add_keyword_data)
        if add_keyword_data_result:
            return CrudResponseModel(is_success=True, message='新增成功')
        else:
            return CrudResponseModel(is_success=False, message='新增失败')

    @classmethod
    async def edit_keyword_data_services(cls, query_db: AsyncSession, edit_keyword_data: KeywordDataModel):
        """
        编辑关键词数据信息service

        :param query_db: orm对象
        :param edit_keyword_data: 编辑关键词数据对象
        :return: 编辑关键词数据校验结果
        """
        edit_keyword_data.update_time = datetime.now()
        await KeywordDataDao.edit_keyword_data_dao(query_db, edit_keyword_data.model_dump())

        return CrudResponseModel(is_success=True, message='更新成功')

    @classmethod
    async def delete_keyword_data_services(cls, query_db: AsyncSession, delete_keyword_data: DeleteKeywordDataModel):
        """
        删除关键词数据信息service

        :param query_db: orm对象
        :param delete_keyword_data: 删除关键词数据对象
        :return: 删除关键词数据校验结果
        """
        if delete_keyword_data.keyword_data_ids:
            keyword_data_id_list = delete_keyword_data.keyword_data_ids.split(',')
            for keyword_data_id in keyword_data_id_list:
                keyword_data_id_dict = dict(id=keyword_data_id)
                await KeywordDataDao.delete_keyword_data_dao(query_db, KeywordDataModel(**keyword_data_id_dict))
            return CrudResponseModel(is_success=True, message='删除成功')
        else:
            return CrudResponseModel(is_success=False, message='传入关键词数据id为空')

    @classmethod
    async def keyword_data_detail_services(cls, query_db: AsyncSession, keyword_data_id: int):
        """
        获取关键词数据详细信息service

        :param query_db: orm对象
        :param keyword_data_id: 关键词数据id
        :return: 关键词数据id对应的信息
        """
        keyword_data = await KeywordDataDao.get_keyword_data_detail_by_id(query_db, keyword_data_id)
        if keyword_data:
            result = KeywordDataModel(**CamelCaseUtil.transform_result(keyword_data))
        else:
            result = KeywordDataModel(**dict())

        return result

    @classmethod
    async def get_type_statistics_services(cls, query_db: AsyncSession):
        """
        获取类型统计数据service

        :param query_db: orm对象
        :return: 类型统计数据
        """
        statistics_data = await KeywordDataDao.get_filter_statistics(query_db)

        # 从filter_statistics结果中提取type_stats数据
        type_stats = statistics_data.get('type_stats', [])
        
        # 转换为统计模型列表
        result = []
        for row in type_stats:
            # 将Row对象转换为字典
            row_dict = {
                'type': row.type or '未分类',
                'total_count': row.total_count or 0,
                'today_count': row.today_count or 0
            }
            stat = KeywordDataStatisticsModel(**row_dict)
            result.append(stat)

        return result

    @classmethod
    async def get_filter_statistics_services(cls, query_db: AsyncSession, keyword: str = None, start_date: str = None, end_date: str = None, platform_types: list = None, sentiment_types: list = None, info_attributes: list = None, web_site: str = None, type_field: str = None):
        """
        获取筛选统计数据service

        :param query_db: orm对象
        :param keyword: 关键词筛选
        :param start_date: 开始日期
        :param end_date: 结束日期
        :param platform_types: 平台类型列表
        :param sentiment_types: 情感类型列表
        :param info_attributes: 信息属性列表
        :param web_site: 来源网站
        :return: 筛选统计数据
        """
        # 获取数据库统计数据
        statistics_data = await KeywordDataDao.get_filter_statistics(
            query_db, 
            keyword=keyword,
            start_date=start_date,
            end_date=end_date,
            platform_types=platform_types,
            sentiment_types=sentiment_types,
            info_attributes=info_attributes,
            web_site=web_site,
            type_field=type_field
        )

        # 检查是否有数据
        total_stats = statistics_data['total_stats']
        total_count = int(total_stats.total_count) if total_stats and total_stats.total_count else 0
        today_count = int(total_stats.today_count) if total_stats and total_stats.today_count else 0

        # 如果没有数据，则各项统计数据会为空列表或0，isMockData会是false

        # 处理平台分类统计
        platform_types = []
        for row in statistics_data['type_stats']:
            platform_types.append(FilterItemModel(
                label=cls._get_platform_label(row.type),
                value=row.type,
                totalCount=int(row.total_count) if row.total_count else 0,
                todayCount=int(row.today_count) if row.today_count else 0
            ))

        # 处理来源网站统计
        source_websites = []
        for row in statistics_data['web_stats']:
            source_websites.append(FilterItemModel(
                label=row.web,
                value=row.web,
                totalCount=int(row.total_count) if row.total_count else 0,
                todayCount=int(row.today_count) if row.today_count else 0
            ))

        # 处理关键词统计
        keywords = []
        for row in statistics_data['keyword_stats']:
            keywords.append(FilterItemModel(
                label=row.keyword,
                value=row.keyword,
                totalCount=int(row.total_count) if row.total_count else 0,
                todayCount=int(row.today_count) if row.today_count else 0
            ))

        # 处理情感倾向统计
        sentiment_types = []
        for row in statistics_data['sentiment_stats']:
            sentiment_types.append(FilterItemModel(
                label=cls._get_sentiment_label(row.sentiment),
                value=row.sentiment,
                totalCount=int(row.total_count) if row.total_count else 0,
                todayCount=int(row.today_count) if row.today_count else 0
            ))

        # 获取信息属性统计
        info_attributes = []
        try:
            # 获取今天开始时间
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            
            info_attribute_stats = await query_db.execute(
                 select(
                     KeywordData.type,
                     func.count(KeywordData.id).label('total_count'),
                     func.sum(case((KeywordData.createtime >= today_start, 1), else_=0)).label('today_count')
                 ).where(
                     and_(
                         KeywordData.type.isnot(None),
                         KeywordData.type != ''
                     )
                 ).group_by(KeywordData.type)
             )
            info_attribute_stats = info_attribute_stats.fetchall()

            for stat in info_attribute_stats:
                info_attributes.append(FilterItemModel(
                    label=cls._get_info_attribute_label(stat.type),
                    value=stat.type,
                    totalCount=stat.total_count or 0,
                    todayCount=stat.today_count or 0
                ))
        except Exception as e:
            logger.error(f"获取信息属性统计失败: {e}")
            # 获取信息属性统计失败，返回空列表
            info_attributes = []

        return FilterStatisticsModel(
            platformTypes=platform_types,
            sourceWebsites=source_websites,
            keywords=keywords,
            sentimentTypes=sentiment_types,
            infoAttributes=info_attributes,
            totalCount=total_count,
            todayCount=today_count,
            isMockData=False
        )

    @classmethod
    def _get_platform_label(cls, platform_type: str) -> str:
        """
        获取平台类型的显示标签

        :param platform_type: 平台类型
        :return: 显示标签
        """
        platform_mapping = {
            '新闻': '新闻',
            '科技': '科技',
            '调研': '调研',
            '企业动态': '企业动态',
            '产品': '产品',
            '微博': '微博',
            '微信': '微信',
            '视频': '视频',
            '论坛': '论坛',
            '电商': '电商',
            '问答': '问答',
            # 添加数据库中实际存在的平台类型
            '负面舆情': '负面舆情',
            '品牌': '品牌',
            '555': '其他'
        }
        return platform_mapping.get(platform_type, platform_type)

    @classmethod
    def _get_sentiment_label(cls, sentiment: str) -> str:
        """
        获取情感倾向的显示标签

        :param sentiment: 情感倾向
        :return: 显示标签
        """
        sentiment_mapping = {
            'positive': '正面',
            'neutral': '中性',
            'negative': '负面'
        }
        return sentiment_mapping.get(sentiment, sentiment)

    @classmethod
    def _get_info_attribute_label(cls, info_attribute: str) -> str:
        """
        获取信息属性的显示标签

        :param info_attribute: 信息属性
        :return: 显示标签
        """
        attribute_mapping = {
            'official': '官方发布',
            'media': '媒体报道',
            'user': '用户生成',
            'expert': '专家观点',
            'other': '其他'
        }
        return attribute_mapping.get(info_attribute, info_attribute)

    @classmethod
    async def get_web_statistics_services(cls, query_db: AsyncSession):
        """
        获取网站统计数据服务

        :param query_db: 数据库会话
        :return: 网站统计数据
        """
        try:
            # 查询所有不同的网站及其数据量
            web_stats_query = select(
                KeywordData.web,
                func.count(KeywordData.id).label('total_count'),
                func.sum(
                    case(
                        (func.date(KeywordData.createtime) == datetime.now().date(), 1),
                        else_=0
                    )
                ).label('today_count')
            ).group_by(KeywordData.web)

            result = await query_db.execute(web_stats_query)
            web_stats = result.fetchall()

            # 构建网站统计数据
            website_data = []
            total_all_count = 0
            today_all_count = 0

            for stat in web_stats:
                if stat.web:  # 确保网站名称不为空
                    website_data.append({
                        'name': stat.web,
                        'count': stat.total_count,
                        'todayCount': stat.today_count or 0
                    })
                    total_all_count += stat.total_count
                    today_all_count += (stat.today_count or 0)

            # 添加总览数据
            result_data = {
                'totalCount': total_all_count,
                'todayCount': today_all_count,
                'websites': website_data
            }

            return result_data

        except Exception as e:
            logger.error(f"获取网站统计数据失败: {e}")
            # 如果查询失败，返回空数据
            return {
                'totalCount': 0,
                'todayCount': 0,
                'websites': []
            }
