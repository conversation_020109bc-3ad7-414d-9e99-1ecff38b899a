2025-07-23 15:39:09.266 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-23 15:39:09.266 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-23 15:39:10.726 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-23 15:39:10.727 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-23 15:39:10.739 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-23 15:39:11.386 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-23 15:39:11.958 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-23 15:39:11.959 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-23 15:39:37.403 | 0a090e474493426b8f88121dff305695 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为1aac0eba-321e-4acd-bc8a-b0a66f696ad9的会话获取图片验证码成功
2025-07-23 15:45:02.110 | 0acca7c6e3444c5b8e0ab121cc1c88a3 | WARNING  | module_admin.service.login_service:__check_login_captcha:158 - 验证码已失效
2025-07-23 15:45:02.111 | 0acca7c6e3444c5b8e0ab121cc1c88a3 | WARNING  | module_admin.annotation.log_annotation:wrapper:123 - 验证码已失效
2025-07-23 15:45:02.206 | e60637b652624b69b02eff77cfc814f5 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为0ee43bf9-2eb5-4139-b17c-a232228395b2的会话获取图片验证码成功
2025-07-23 15:45:05.273 | 7aa58a6486a24328ad880ef54181bd66 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-23 15:45:05.806 | e57526bdc8ea4d1ca7b60679340f4ccb | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-23 15:45:06.069 | 36923e32cf4d4a788c03b9baf1e33668 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-23 15:45:06.726 | 1a1e7a5747c3460c80789f1f33732cc9 | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-23 15:45:06.783 | 1a1e7a5747c3460c80789f1f33732cc9 | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-23 15:45:06.783 | 1a1e7a5747c3460c80789f1f33732cc9 | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-23 15:45:07.027 | 9ec6ede3ee744d9c80d2d4def39185a3 | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-23 15:45:07.058 | 9ec6ede3ee744d9c80d2d4def39185a3 | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 24
2025-07-23 15:45:07.059 | 9ec6ede3ee744d9c80d2d4def39185a3 | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 24
2025-07-23 15:45:07.059 | 9ec6ede3ee744d9c80d2d4def39185a3 | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-23 15:45:07.154 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-23 15:45:07.154 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-23 15:45:07.155 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-23 15:45:07.155 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-23 00:00:00 到 2025-07-23 23:59:59.999999 ===
2025-07-23 15:45:07.156 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-22 00:00:00 到 2025-07-22 23:59:59.999999 ===
2025-07-23 15:45:07.158 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-23 15:45:07.182 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-23 15:45:07.302 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-23 15:45:07.581 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-23 15:45:07.582 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 23, 15, 45, 7, 557737), 'package_name': '企业版', 'is_expired': True} ===
2025-07-23 15:45:07.607 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-23 15:45:07.608 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-23 15:45:07.631 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-23 15:45:07.631 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-23 15:45:07.632 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-23 15:45:07.633 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': -100.0, 'negative_count': 0, 'negative_change': Decimal('-100.0'), 'completed_count': 11, 'completed_change': 37.5, 'pending_count': 13, 'pending_change': 550.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-23 15:45:07.633 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-23 15:45:07.634 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': -100.0, 'negative_count': 0, 'negative_change': Decimal('-100.0'), 'completed_count': 11, 'completed_change': 37.5, 'pending_count': 13, 'pending_change': 550.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-23 15:45:07.634 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=-100.0 negative_count=0 negative_change=-100.0 completed_count=11 completed_change=37.5 pending_count=13 pending_change=550.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-23 15:45:07.635 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-23 15:45:07.635 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=-100.0 negative_count=0 negative_change=-100.0 completed_count=11 completed_change=37.5 pending_count=13 pending_change=550.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-23 15:45:07.636 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-23 15:45:07.636 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': -100.0, 'negative_count': 0, 'negative_change': -100.0, 'completed_count': 11, 'completed_change': 37.5, 'pending_count': 13, 'pending_change': 550.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-23 15:45:07.637 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-23 15:45:07.637 | c4a57de0ae9e414c94e8520aab64f159 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x0000018AE67B6F50> ===
2025-07-23 15:45:53.760 | a65fb7ef1e65432c8722d704477dd01f | INFO     | module_admin.controller.user_controller:query_detail_system_user_profile:210 - 获取user_id为1的信息成功
2025-07-23 15:45:53.820 | 7000b399bc6a4b09aeb13f1dfbaed1ab | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-23 15:45:53.866 | 7000b399bc6a4b09aeb13f1dfbaed1ab | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-23 15:45:53.889 | 7000b399bc6a4b09aeb13f1dfbaed1ab | INFO     | module_bill.controller.bill_controller:get_bill_dashboard_data:173 - 获取用户1的账单仪表板数据成功
2025-07-23 15:45:55.007 | 4693dd8629764b8dab55983ba9c18cd9 | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-23 15:45:55.058 | 4693dd8629764b8dab55983ba9c18cd9 | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-23 15:45:55.059 | 4693dd8629764b8dab55983ba9c18cd9 | INFO     | module_bill.controller.bill_controller:get_current_user_subscription_info:78 - 获取用户1的订阅信息成功
2025-07-23 15:45:55.692 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 23, 15, 45, 5), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-23 15:45:55.692 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-23 15:45:55.692 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-23 15:45:55.692 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-23 15:45:55.692 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-23 15:45:55.693 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-23 15:45:55.693 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-23 15:45:55.693 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-23 15:45:55.693 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-23 15:45:55.693 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-23 15:45:55.693 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-23 15:45:55.694 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-23 15:45:55.694 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-23 15:45:55.696 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-23 15:45:55.697 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-23 15:45:55.744 | ec674ec575384605949ed1c02532e66c | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-23 15:45:55.744 | ec674ec575384605949ed1c02532e66c | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-23 15:45:55.744 | ec674ec575384605949ed1c02532e66c | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-23 15:45:55.745 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-23 15:45:55.745 | ec674ec575384605949ed1c02532e66c | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-23 15:48:00.039 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-23 15:48:00.040 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-23 15:52:21.891 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-23 15:52:21.892 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-23 15:52:23.397 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-23 15:52:23.397 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-23 15:52:23.399 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-23 15:52:23.921 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-23 15:52:24.882 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-23 15:52:24.883 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-23 15:52:57.116 | e4d631d4e11145f3a1b9dab22c400df1 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为13c1e57c-ebd8-4d0f-8bac-e749a2fa9507的会话获取图片验证码成功
2025-07-23 15:56:03.587 | 79578c317ffd47148abdaa2bb73316d2 | WARNING  | module_admin.service.login_service:__check_login_captcha:158 - 验证码已失效
2025-07-23 15:56:03.587 | 79578c317ffd47148abdaa2bb73316d2 | WARNING  | module_admin.annotation.log_annotation:wrapper:123 - 验证码已失效
2025-07-23 15:56:03.675 | 60b3706d0dc646ea8abda3daa10bb632 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为a1ec9bb8-03fd-465d-9571-7f8a660321c9的会话获取图片验证码成功
2025-07-23 15:56:07.040 | a7b9ebe10f4c43dda1e248549d76dfe6 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-23 15:56:07.572 | 4b9a793cc03d416f93637df324563432 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-23 15:56:07.822 | 6215c3f797104c2288f7daf1a391065f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-23 15:56:08.299 | a687b98bad004705a60dcdadac725144 | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-23 15:56:08.356 | a687b98bad004705a60dcdadac725144 | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-23 15:56:08.357 | a687b98bad004705a60dcdadac725144 | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-23 15:56:08.617 | 877fd8efd02d4e89a9c3482d0bb6d4de | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-23 15:56:08.644 | 877fd8efd02d4e89a9c3482d0bb6d4de | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 24
2025-07-23 15:56:08.644 | 877fd8efd02d4e89a9c3482d0bb6d4de | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 24
2025-07-23 15:56:08.645 | 877fd8efd02d4e89a9c3482d0bb6d4de | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-23 15:56:08.824 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-23 15:56:08.824 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-23 15:56:08.825 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-23 15:56:08.825 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-23 00:00:00 到 2025-07-23 23:59:59.999999 ===
2025-07-23 15:56:08.825 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-22 00:00:00 到 2025-07-22 23:59:59.999999 ===
2025-07-23 15:56:08.826 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-23 15:56:08.858 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-23 15:56:09.017 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-23 15:56:09.292 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-23 15:56:09.292 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 23, 15, 56, 9, 260329), 'package_name': '企业版', 'is_expired': True} ===
2025-07-23 15:56:09.325 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-23 15:56:09.325 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-23 15:56:09.356 | 04e173ce094c46c9abb869be974baa0d | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-23 15:56:09.356 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-23 15:56:09.357 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-23 15:56:09.357 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': -100.0, 'negative_count': 0, 'negative_change': Decimal('-100.0'), 'completed_count': 11, 'completed_change': 37.5, 'pending_count': 13, 'pending_change': 550.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-23 15:56:09.357 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-23 15:56:09.357 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': -100.0, 'negative_count': 0, 'negative_change': Decimal('-100.0'), 'completed_count': 11, 'completed_change': 37.5, 'pending_count': 13, 'pending_change': 550.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-23 15:56:09.358 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=-100.0 negative_count=0 negative_change=-100.0 completed_count=11 completed_change=37.5 pending_count=13 pending_change=550.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-23 15:56:09.359 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-23 15:56:09.359 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=-100.0 negative_count=0 negative_change=-100.0 completed_count=11 completed_change=37.5 pending_count=13 pending_change=550.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-23 15:56:09.360 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-23 15:56:09.360 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': -100.0, 'negative_count': 0, 'negative_change': -100.0, 'completed_count': 11, 'completed_change': 37.5, 'pending_count': 13, 'pending_change': 550.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-23 15:56:09.361 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-23 15:56:09.361 | 04e173ce094c46c9abb869be974baa0d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x000002A58D1C6F50> ===
2025-07-23 15:56:12.018 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 23, 15, 56, 7), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-23 15:56:12.018 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-23 15:56:12.019 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-23 15:56:12.019 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-23 15:56:12.020 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-23 15:56:12.020 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-23 15:56:12.020 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-23 15:56:12.021 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-23 15:56:12.021 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-23 15:56:12.022 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-23 15:56:12.022 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-23 15:56:12.022 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-23 15:56:12.023 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-23 15:56:12.026 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-23 15:56:12.027 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-23 15:56:12.086 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-23 15:56:12.087 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-23 15:56:12.087 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-23 15:56:12.088 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-23 15:56:12.088 | 73d3a4a0eb4d44acb51e8e54126cedbb | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-23 15:56:16.719 | 988408f9c54c43979648f42761183b3e | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-23 15:56:16.782 | 988408f9c54c43979648f42761183b3e | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-23 15:56:16.783 | 988408f9c54c43979648f42761183b3e | INFO     | module_bill.controller.bill_controller:get_current_user_subscription_info:78 - 获取用户1的订阅信息成功
2025-07-23 15:56:17.179 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 23, 15, 56, 7), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-23 15:56:17.180 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-23 15:56:17.181 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-23 15:56:17.181 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-23 15:56:17.182 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-23 15:56:17.182 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-23 15:56:17.183 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-23 15:56:17.184 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-23 15:56:17.184 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-23 15:56:17.184 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-23 15:56:17.185 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-23 15:56:17.185 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-23 15:56:17.185 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-23 15:56:17.187 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-23 15:56:17.188 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-23 15:56:17.244 | 3d48fefa93004a039887933c9e6079f1 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-23 15:56:17.244 | 3d48fefa93004a039887933c9e6079f1 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-23 15:56:17.245 | 3d48fefa93004a039887933c9e6079f1 | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-23 15:56:17.245 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-23 15:56:17.245 | 3d48fefa93004a039887933c9e6079f1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-23 15:56:18.594 | 2cabe4fae45e4835ac2c60066c4445dc | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:123 - 获取关键词分类列表成功
2025-07-23 15:56:19.138 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 23, 15, 56, 7), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-23 15:56:19.138 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-23 15:56:19.139 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-23 15:56:19.139 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-23 15:56:19.140 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-23 15:56:19.140 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-23 15:56:19.140 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-23 15:56:19.141 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-23 15:56:19.141 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-23 15:56:19.141 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-23 15:56:19.141 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-23 15:56:19.142 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-23 15:56:19.143 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-23 15:56:19.145 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-23 15:56:19.146 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-23 15:56:19.204 | 57f8aeb132194370b71f7dc06223f887 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-23 15:56:19.204 | 57f8aeb132194370b71f7dc06223f887 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-23 15:56:19.205 | 57f8aeb132194370b71f7dc06223f887 | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-23 15:56:19.205 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-23 15:56:19.206 | 57f8aeb132194370b71f7dc06223f887 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-23 16:10:23.536 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-23 16:10:23.537 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-23 16:24:26.898 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-23 16:24:26.899 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-23 16:24:29.595 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-23 16:24:29.595 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-23 16:24:29.598 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-23 16:24:30.473 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-23 16:24:31.094 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-23 16:24:31.094 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-23 16:24:56.192 | 3aa8bae256e447329c9dfc662ef87bd5 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为cd47f9ca-e50b-4e43-84b9-8f2771b36a61的会话获取图片验证码成功
2025-07-23 16:25:03.992 | bcba8dbe06f14eaf8dcec8044566279e | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-23 16:25:04.376 | 5b49b8461c444c2797af7121ab4c4eac | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-23 16:25:05.033 | 0c32afb05aa3457099678779f250e8e4 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-23 16:25:05.973 | 80e9bc91ab5e4f90b0a60ca07391d964 | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-23 16:25:06.079 | 80e9bc91ab5e4f90b0a60ca07391d964 | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-23 16:25:06.080 | 80e9bc91ab5e4f90b0a60ca07391d964 | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-23 16:25:06.279 | b0059c0a2ce64059b6bb2a3775b7974f | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-23 16:25:06.407 | b0059c0a2ce64059b6bb2a3775b7974f | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 24
2025-07-23 16:25:06.407 | b0059c0a2ce64059b6bb2a3775b7974f | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 24
2025-07-23 16:25:06.407 | b0059c0a2ce64059b6bb2a3775b7974f | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-23 16:25:06.409 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-23 16:25:06.410 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-23 16:25:06.410 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-23 16:25:06.410 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-23 00:00:00 到 2025-07-23 23:59:59.999999 ===
2025-07-23 16:25:06.410 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-22 00:00:00 到 2025-07-22 23:59:59.999999 ===
2025-07-23 16:25:06.411 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-23 16:25:06.441 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-23 16:25:06.582 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-23 16:25:06.645 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-23 16:25:06.645 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 23, 16, 25, 6, 616610), 'package_name': '企业版', 'is_expired': True} ===
2025-07-23 16:25:06.674 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-23 16:25:06.674 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-23 16:25:06.702 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-23 16:25:06.702 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-23 16:25:06.703 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-23 16:25:06.703 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': -100.0, 'negative_count': 0, 'negative_change': Decimal('-100.0'), 'completed_count': 11, 'completed_change': 37.5, 'pending_count': 13, 'pending_change': 550.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-23 16:25:06.703 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-23 16:25:06.703 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': -100.0, 'negative_count': 0, 'negative_change': Decimal('-100.0'), 'completed_count': 11, 'completed_change': 37.5, 'pending_count': 13, 'pending_change': 550.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-23 16:25:06.703 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=-100.0 negative_count=0 negative_change=-100.0 completed_count=11 completed_change=37.5 pending_count=13 pending_change=550.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-23 16:25:06.704 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-23 16:25:06.704 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=-100.0 negative_count=0 negative_change=-100.0 completed_count=11 completed_change=37.5 pending_count=13 pending_change=550.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-23 16:25:06.704 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-23 16:25:06.705 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': -100.0, 'negative_count': 0, 'negative_change': -100.0, 'completed_count': 11, 'completed_change': 37.5, 'pending_count': 13, 'pending_change': 550.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-23 16:25:06.705 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-23 16:25:06.705 | 7078cf5d5c7b43d284252e71cce77981 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x0000020092686E00> ===
