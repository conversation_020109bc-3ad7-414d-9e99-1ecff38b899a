from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, BigInteger, Float, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from config.database import Base


class EventNews(Base):
    """
    事件新闻关联表
    """

    __tablename__ = 'event_news'
    __table_args__ = {'extend_existing': True}

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    event_id = Column(BigInteger, ForeignKey('event.id'), nullable=False, comment='事件ID')
    news_id = Column(BigInteger, ForeignKey('news.id'), nullable=False, comment='新闻ID')
    association_type = Column(String(50), default='direct_mention', comment='关联类型：direct_mention, indirect_reference, related_topic')
    relevance_score = Column(Float, default=0.0, comment='相关性分数：0-1')
    confidence_score = Column(Float, default=0.0, comment='置信度分数：0-1')
    match_keywords = Column(String(500), nullable=True, comment='匹配的关键词')
    is_verified = Column(Boolean, default=False, comment='是否已验证')
    verification_status = Column(String(20), default='pending', comment='验证状态：pending, verified, rejected')
    verification_reason = Column(String(200), nullable=True, comment='验证原因')
    auto_generated = Column(Boolean, default=True, comment='是否自动生成')
    weight = Column(Float, default=1.0, comment='权重')
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')

    # 关联关系
    # event = relationship("Event", back_populates="event_news")  # 暂时注释掉
    # news = relationship("News", back_populates="event_news")  # 暂时注释掉
