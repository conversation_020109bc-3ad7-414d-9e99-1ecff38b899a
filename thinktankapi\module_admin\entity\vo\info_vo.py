from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from module_admin.annotation.pydantic_annotation import as_query


class InfoSummaryModel(BaseModel):
    """
    信息汇总模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='信息ID')
    title: str = Field(description='标题')
    content: Optional[str] = Field(default=None, description='内容')
    summary: Optional[str] = Field(default=None, description='摘要')
    platform_type: str = Field(description='平台类型')
    source_name: Optional[str] = Field(default=None, description='来源名称')
    source_url: Optional[str] = Field(default=None, description='来源URL')
    publish_time: Optional[datetime] = Field(default=None, description='发布时间')
    sentiment: Optional[str] = Field(default=None, description='情感倾向')
    info_attribute: Optional[str] = Field(default=None, description='信息属性')
    views_count: Optional[int] = Field(default=None, description='浏览数')
    comments_count: Optional[int] = Field(default=None, description='评论数')
    shares_count: Optional[int] = Field(default=None, description='分享数')
    entity_type: Optional[str] = Field(default=None, description='实体类型')
    entity_name: Optional[str] = Field(default=None, description='实体名称')
    keywords: Optional[str] = Field(default=None, description='关键词')
    images: Optional[str] = Field(default=None, description='图片')
    status: Optional[str] = Field(default=None, description='状态')
    create_by: Optional[str] = Field(default=None, description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[str] = Field(default=None, description='更新者')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    remark: Optional[str] = Field(default=None, description='备注')


class NewsModel(BaseModel):
    """
    新闻模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='新闻ID')
    title: str = Field(description='标题')
    content: Optional[str] = Field(default=None, description='内容')
    url: Optional[str] = Field(default=None, description='链接')
    source: Optional[str] = Field(default=None, description='来源')
    author: Optional[str] = Field(default=None, description='作者')
    publish_time: Optional[datetime] = Field(default=None, description='发布时间')
    platform: Optional[str] = Field(default=None, description='平台')
    media_level: Optional[str] = Field(default=None, description='媒体级别')
    emotion_type: Optional[str] = Field(default=None, description='情感类型')
    article_type: Optional[str] = Field(default=None, description='文章类型')
    ip_location: Optional[str] = Field(default=None, description='IP位置')
    publish_area: Optional[str] = Field(default=None, description='发布地区')
    is_sensitive: Optional[int] = Field(default=0, description='是否敏感')
    keywords: Optional[str] = Field(default=None, description='关键词')
    similarity_group: Optional[int] = Field(default=None, description='相似度分组')
    hot_score: Optional[float] = Field(default=0, description='热度分数')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    create_by: Optional[str] = Field(default=None, description='创建者')
    update_by: Optional[str] = Field(default=None, description='更新者')
    remark: Optional[str] = Field(default=None, description='备注')


class InfoSummaryQueryModel(BaseModel):
    """
    信息汇总查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    title: Optional[str] = Field(default=None, description='标题')
    platform_type: Optional[str] = Field(default=None, description='平台类型')
    sentiment: Optional[str] = Field(default=None, description='情感倾向')
    entity_type: Optional[str] = Field(default=None, description='实体类型')
    entity_name: Optional[str] = Field(default=None, description='实体名称')
    start_time: Optional[datetime] = Field(default=None, description='开始时间')
    end_time: Optional[datetime] = Field(default=None, description='结束时间')
    search_keyword: Optional[str] = Field(default=None, description='搜索关键词')


@as_query
class InfoSummaryPageQueryModel(InfoSummaryQueryModel):
    """
    信息汇总分页查询模型
    """

    page_num: int = Field(default=1, description='页码')
    page_size: int = Field(default=10, description='每页数量')


class DeleteInfoSummaryModel(BaseModel):
    """
    删除信息汇总模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    info_ids: str = Field(description='信息ID列表，逗号分隔')


class InfoSummaryStatisticsModel(BaseModel):
    """
    信息汇总统计模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    platform_type: str = Field(description='平台类型')
    total_count: int = Field(default=0, description='总数')
    today_count: int = Field(default=0, description='今日数量')
    positive_count: int = Field(default=0, description='正面数量')
    neutral_count: int = Field(default=0, description='中性数量')
    negative_count: int = Field(default=0, description='负面数量')


class InfoPageQueryModel(BaseModel):
    """
    信息分页查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    page_num: int = Field(default=1, description='页码')
    page_size: int = Field(default=10, description='每页数量')
    title: Optional[str] = Field(default=None, description='标题')
    platform_type: Optional[str] = Field(default=None, description='平台类型')
    sentiment: Optional[str] = Field(default=None, description='情感倾向')
    entity_type: Optional[str] = Field(default=None, description='实体类型')
    entity_name: Optional[str] = Field(default=None, description='实体名称')
    start_time: Optional[datetime] = Field(default=None, description='开始时间')
    end_time: Optional[datetime] = Field(default=None, description='结束时间')
    search_keyword: Optional[str] = Field(default=None, description='搜索关键词')

    @classmethod
    def as_query(cls,
                 page_num: int = 1,
                 page_size: int = 10,
                 title: Optional[str] = None,
                 platform_type: Optional[str] = None,
                 sentiment: Optional[str] = None,
                 entity_type: Optional[str] = None,
                 entity_name: Optional[str] = None,
                 start_time: Optional[datetime] = None,
                 end_time: Optional[datetime] = None,
                 search_keyword: Optional[str] = None):
        return cls(
            page_num=page_num,
            page_size=page_size,
            title=title,
            platform_type=platform_type,
            sentiment=sentiment,
            entity_type=entity_type,
            entity_name=entity_name,
            start_time=start_time,
            end_time=end_time,
            search_keyword=search_keyword
        )


class NewsPageQueryModel(BaseModel):
    """
    新闻分页查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    page_num: int = Field(default=1, description='页码')
    page_size: int = Field(default=10, description='每页数量')
    title: Optional[str] = Field(default=None, description='标题')
    platform: Optional[str] = Field(default=None, description='平台')
    emotion_type: Optional[str] = Field(default=None, description='情感类型')
    media_level: Optional[str] = Field(default=None, description='媒体级别')
    article_type: Optional[str] = Field(default=None, description='文章类型')
    is_sensitive: Optional[int] = Field(default=None, description='是否敏感')
    start_time: Optional[datetime] = Field(default=None, description='开始时间')
    end_time: Optional[datetime] = Field(default=None, description='结束时间')
    search_keyword: Optional[str] = Field(default=None, description='搜索关键词')

    @classmethod
    def as_query(cls,
                 page_num: int = 1,
                 page_size: int = 10,
                 title: Optional[str] = None,
                 platform: Optional[str] = None,
                 emotion_type: Optional[str] = None,
                 media_level: Optional[str] = None,
                 article_type: Optional[str] = None,
                 is_sensitive: Optional[int] = None,
                 start_time: Optional[datetime] = None,
                 end_time: Optional[datetime] = None,
                 search_keyword: Optional[str] = None):
        return cls(
            page_num=page_num,
            page_size=page_size,
            title=title,
            platform=platform,
            emotion_type=emotion_type,
            media_level=media_level,
            article_type=article_type,
            is_sensitive=is_sensitive,
            start_time=start_time,
            end_time=end_time,
            search_keyword=search_keyword
        )
