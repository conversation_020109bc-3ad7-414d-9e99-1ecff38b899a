"""
联网搜索相关的数据模型
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any


class OnlineSearchRequestModel(BaseModel):
    """
    联网搜索请求模型
    """
    entity_keyword: str = Field(..., description='实体关键词')
    specific_requirement: str = Field(..., description='具体需求')
    selected_keywords: List[str] = Field(..., description='选中的关键词列表')
    requirement_id: Optional[int] = Field(default=None, description='需求ID')


class OnlineSearchResponseModel(BaseModel):
    """
    联网搜索响应模型
    """
    success: bool = Field(..., description='是否成功')
    data: Dict[str, Any] = Field(..., description='搜索结果数据')
    message: str = Field(..., description='响应消息')


class AnalysisStartRequestModel(BaseModel):
    """
    开始分析请求模型
    """
    requirement_id: int = Field(..., description='需求ID')
    entity_keyword: str = Field(..., description='实体关键词')
    specific_requirement: str = Field(..., description='具体需求')
    selected_keywords: List[str] = Field(..., description='选中的关键词列表')
    enable_online_search: bool = Field(default=False, description='是否启用联网搜索')
    enable_custom_data_source: bool = Field(default=False, description='是否启用自定义数据源搜索')
    custom_data_sources: List[Dict[str, Any]] = Field(default=[], description='自定义数据源列表')
