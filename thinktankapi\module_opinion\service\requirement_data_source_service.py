from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from exceptions.exception import ServiceException
from module_opinion.dao.requirement_data_source_dao import RequirementDataSourceDao
from module_opinion.dao.opinion_requirement_dao import OpinionRequirementDao
from module_opinion.entity.vo.requirement_data_source_vo import (
    RequirementDataSourceModel,
    RequirementDataSourcePageQueryModel,
    CreateRequirementDataSourceModel,
    UpdateRequirementDataSourceModel,
    DeleteRequirementDataSourceModel,
    BatchCreateDataSourceModel
)
from module_admin.entity.vo.common_vo import CrudResponseModel
from utils.common_util import CamelCaseUtil
import json


class RequirementDataSourceService:
    """
    需求数据源管理模块服务层
    """

    @classmethod
    async def get_requirement_data_source_list_services(
        cls, query_db: AsyncSession, query_object: RequirementDataSourcePageQueryModel, is_page: bool = False
    ):
        """
        获取需求数据源列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 需求数据源列表信息对象
        """
        data_source_list_result = await RequirementDataSourceDao.get_requirement_data_source_list(query_db, query_object, is_page)
        
        if is_page:
            return data_source_list_result
        else:
            return CamelCaseUtil.transform_result(data_source_list_result)

    @classmethod
    async def get_requirement_data_source_detail_services(cls, query_db: AsyncSession, data_source_id: int):
        """
        获取需求数据源详细信息service

        :param query_db: orm对象
        :param data_source_id: 数据源ID
        :return: 需求数据源详细信息对象
        """
        data_source_detail_result = await RequirementDataSourceDao.get_requirement_data_source_by_id(query_db, data_source_id)
        
        if not data_source_detail_result:
            raise ServiceException(message='需求数据源不存在')
            
        return CamelCaseUtil.transform_result(data_source_detail_result)

    @classmethod
    async def add_requirement_data_source_services(cls, query_db: AsyncSession, add_data_source: CreateRequirementDataSourceModel, current_user_id: int):
        """
        新增需求数据源信息service

        :param query_db: orm对象
        :param add_data_source: 新增需求数据源对象
        :param current_user_id: 当前用户ID
        :return: 新增需求数据源校验结果
        """
        # 检查需求是否存在
        requirement = await OpinionRequirementDao.get_opinion_requirement_by_id(query_db, add_data_source.requirement_id)
        if not requirement:
            raise ServiceException(message='关联的舆情需求不存在')

        # 检查数据源是否已存在
        if await RequirementDataSourceDao.check_data_source_exists(
            query_db, add_data_source.requirement_id, add_data_source.source_type, add_data_source.source_name
        ):
            raise ServiceException(message=f'数据源"{add_data_source.source_name}"已存在')

        # 构建数据源对象
        data_source_data = RequirementDataSourceModel(
            requirement_id=add_data_source.requirement_id,
            source_type=add_data_source.source_type,
            source_name=add_data_source.source_name,
            source_url=add_data_source.source_url,
            source_config=json.dumps(add_data_source.source_config) if add_data_source.source_config else None,
            status=1,
            create_by=str(current_user_id),
            remark=add_data_source.remark
        )

        try:
            new_data_source = await RequirementDataSourceDao.add_requirement_data_source_dao(query_db, data_source_data)
            # 在commit之前获取ID
            data_source_id = new_data_source.id
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功', data={'id': data_source_id})
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def batch_add_data_sources_services(cls, query_db: AsyncSession, batch_add: BatchCreateDataSourceModel, current_user_id: int):
        """
        批量新增数据源service

        :param query_db: orm对象
        :param batch_add: 批量新增数据源对象
        :param current_user_id: 当前用户ID
        :return: 批量新增结果
        """
        # 检查需求是否存在
        requirement = await OpinionRequirementDao.get_opinion_requirement_by_id(query_db, batch_add.requirement_id)
        if not requirement:
            raise ServiceException(message='关联的舆情需求不存在')

        # 构建数据源对象列表
        data_source_models = []
        for data_source_info in batch_add.data_sources:
            # 检查数据源是否已存在
            if await RequirementDataSourceDao.check_data_source_exists(
                query_db, batch_add.requirement_id, data_source_info.source_type, data_source_info.source_name
            ):
                continue  # 跳过已存在的数据源

            data_source_model = RequirementDataSourceModel(
                requirement_id=batch_add.requirement_id,
                source_type=data_source_info.source_type,
                source_name=data_source_info.source_name,
                source_url=data_source_info.source_url,
                source_config=json.dumps(data_source_info.source_config) if data_source_info.source_config else None,
                status=1,
                create_by=str(current_user_id),
                remark=data_source_info.remark
            )
            data_source_models.append(data_source_model)

        if not data_source_models:
            raise ServiceException(message='没有可添加的数据源，所有数据源都已存在')

        try:
            new_data_sources = await RequirementDataSourceDao.batch_add_requirement_data_sources_dao(query_db, data_source_models)
            await query_db.commit()
            return CrudResponseModel(
                is_success=True, 
                message=f'批量新增成功，共添加{len(new_data_sources)}个数据源',
                data={'count': len(new_data_sources)}
            )
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_requirement_data_source_services(cls, query_db: AsyncSession, edit_data_source: UpdateRequirementDataSourceModel, current_user_id: int):
        """
        编辑需求数据源信息service

        :param query_db: orm对象
        :param edit_data_source: 编辑需求数据源对象
        :param current_user_id: 当前用户ID
        :return: 编辑需求数据源校验结果
        """
        # 检查数据源是否存在
        existing_data_source = await RequirementDataSourceDao.get_requirement_data_source_by_id(query_db, edit_data_source.id)
        if not existing_data_source:
            raise ServiceException(message='需求数据源不存在')

        # 构建更新数据
        update_data = {
            'id': edit_data_source.id,
            'update_time': datetime.now(),
            'update_by': str(current_user_id)
        }
        
        # 只更新非空字段
        for field, value in edit_data_source.model_dump(exclude={'id'}, exclude_none=True).items():
            if field == 'source_config' and value:
                update_data[field] = json.dumps(value)
            else:
                update_data[field] = value

        try:
            await RequirementDataSourceDao.edit_requirement_data_source_dao(query_db, update_data)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def delete_requirement_data_source_services(cls, query_db: AsyncSession, delete_data_source: DeleteRequirementDataSourceModel):
        """
        删除需求数据源信息service

        :param query_db: orm对象
        :param delete_data_source: 删除需求数据源对象
        :return: 删除需求数据源校验结果
        """
        if not delete_data_source.ids:
            raise ServiceException(message='请选择要删除的数据源')

        try:
            await RequirementDataSourceDao.delete_requirement_data_source_dao(query_db, delete_data_source.ids)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='删除成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def get_data_sources_by_requirement_services(cls, query_db: AsyncSession, requirement_id: int):
        """
        根据需求ID获取数据源列表service

        :param query_db: orm对象
        :param requirement_id: 需求ID
        :return: 数据源列表
        """
        data_sources = await RequirementDataSourceDao.get_data_sources_by_requirement(query_db, requirement_id)
        return CamelCaseUtil.transform_result(data_sources)

    @classmethod
    async def get_data_sources_by_type_services(cls, query_db: AsyncSession, source_type: str, requirement_id: Optional[int] = None):
        """
        根据数据源类型获取数据源列表service

        :param query_db: orm对象
        :param source_type: 数据源类型
        :param requirement_id: 需求ID（可选）
        :return: 数据源列表
        """
        data_sources = await RequirementDataSourceDao.get_data_sources_by_type(query_db, source_type, requirement_id)
        return CamelCaseUtil.transform_result(data_sources)

    @classmethod
    async def get_data_source_statistics_services(cls, query_db: AsyncSession, requirement_id: Optional[int] = None):
        """
        获取数据源统计信息service

        :param query_db: orm对象
        :param requirement_id: 需求ID（可选）
        :return: 统计信息
        """
        return await RequirementDataSourceDao.get_data_source_statistics(query_db, requirement_id)

    @classmethod
    async def get_default_data_sources_services(cls):
        """
        获取默认数据源配置service

        :return: 默认数据源列表
        """
        # 返回常用的数据源配置
        default_sources = [
            {
                'source_type': 'weibo',
                'source_name': '新浪微博',
                'source_url': 'https://weibo.com',
                'description': '新浪微博平台数据'
            },
            {
                'source_type': 'wechat',
                'source_name': '微信公众号',
                'source_url': 'https://mp.weixin.qq.com',
                'description': '微信公众号文章数据'
            },
            {
                'source_type': 'news',
                'source_name': '新闻网站',
                'source_url': '',
                'description': '各大新闻网站数据'
            },
            {
                'source_type': 'forum',
                'source_name': '论坛社区',
                'source_url': '',
                'description': '各类论坛社区数据'
            },
            {
                'source_type': 'video',
                'source_name': '视频平台',
                'source_url': '',
                'description': '视频平台评论数据'
            }
        ]
        
        return default_sources
