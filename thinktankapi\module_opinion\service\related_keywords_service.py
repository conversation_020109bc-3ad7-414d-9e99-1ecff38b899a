"""
关联词生成服务层
用于基于用户需求内容生成相关关联词
"""
import asyncio
import json
import re
from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from utils.log_util import logger
from exceptions.exception import ServiceException
from module_opinion.service.external_api_service import ExternalApiService


class RelatedKeywordsService:
    """
    关联词生成服务类
    """

    # 关联词生成的prompt模板
    RELATED_KEYWORDS_PROMPT_TEMPLATE = """
你是一个专业的舆情分析关键词生成助手。请根据用户提供的具体需求内容，生成相关的关联词。

**任务要求**：
1. 分析用户需求内容的核心主题和关注点
2. 生成与该需求高度相关的关联词
3. 关联词应该涵盖不同维度：正面、负面、中性表达
4. 关联词应该包含同义词、相关词、衍生词等
5. 确保生成的关联词具有实际的舆情监测价值

**输出格式要求**：
- 只返回JSON数组格式，不要包含任何其他文字
- 每个关联词作为数组中的一个字符串元素
- 关联词长度控制在2-10个字符之间
- 去除重复的关联词
- 按相关性排序，最相关的排在前面

**用户需求内容**：
{requirement_content}

**最大生成数量**：{max_count}

**输出示例**：
["关键词1", "关键词2", "关键词3", ...]

请严格按照JSON数组格式输出，开头必须是"["，结尾必须是"]"。
"""

    @classmethod
    async def generate_related_keywords_services(
        cls,
        query_db: AsyncSession,
        requirement_content: str,
        max_count: int = 20,
        user_id: int = None
    ) -> Dict[str, Any]:
        """
        生成关联词服务方法

        :param query_db: orm对象
        :param requirement_content: 具体需求内容
        :param max_count: 最大生成数量
        :param user_id: 用户ID，用于套餐次数扣减
        :return: 生成的关联词结果
        """
        try:
            logger.info(f"开始生成关联词，需求内容长度: {len(requirement_content)}, 最大数量: {max_count}, 用户ID: {user_id}")

            # 调用AI服务生成关联词
            keywords = await cls._generate_keywords_by_ai(requirement_content, max_count)

            if not keywords:
                logger.warning("AI生成关联词失败，使用降级策略")
                # 降级策略：使用规则生成
                keywords = cls._generate_keywords_by_rules(requirement_content, max_count)

            # 关联词后处理
            processed_keywords = cls._process_keywords(keywords, max_count)

            # 关键词生成成功后，创建opinion_requirement记录来扣减套餐次数
            if user_id and processed_keywords:
                await cls._deduct_package_usage_for_keywords(query_db, user_id, requirement_content)

            result = {
                "keywords": processed_keywords,
                "total": len(processed_keywords)
            }

            logger.info(f"关联词生成成功，共生成 {len(processed_keywords)} 个关联词")
            return result

        except Exception as e:
            logger.error(f"生成关联词失败: {str(e)}")
            raise ServiceException(message=f"生成关联词失败: {str(e)}")

    @classmethod
    async def _generate_keywords_by_ai(cls, requirement_content: str, max_count: int) -> List[str]:
        """
        使用AI生成关联词

        :param requirement_content: 需求内容
        :param max_count: 最大生成数量
        :return: 生成的关联词列表
        """
        try:
            # 构建AI prompt
            prompt = cls.RELATED_KEYWORDS_PROMPT_TEMPLATE.format(
                requirement_content=requirement_content[:1000],  # 限制输入长度
                max_count=max_count
            )
            
            # 调用豆包AI API
            api_response = await ExternalApiService._call_ark_api(prompt)
            
            # 解析AI返回结果
            if "choices" in api_response and len(api_response["choices"]) > 0:
                ai_content = api_response["choices"][0].get("message", {}).get("content", "").strip()
                
                logger.info(f"AI返回的原始内容: {ai_content[:200]}...")
                
                # 解析JSON格式的关联词
                keywords = cls._parse_ai_keywords_response(ai_content)
                return keywords
            else:
                logger.warning("AI API返回格式异常")
                return []
                
        except asyncio.TimeoutError:
            logger.error("AI服务调用超时")
            raise ServiceException(message="AI服务调用超时，请稍后重试")
        except Exception as e:
            logger.error(f"AI生成关联词异常: {str(e)}")
            return []

    @classmethod
    def _parse_ai_keywords_response(cls, ai_content: str) -> List[str]:
        """
        解析AI返回的关联词内容

        :param ai_content: AI返回的原始内容
        :return: 解析后的关联词列表
        """
        try:
            # 清理AI返回的内容，提取JSON部分
            cleaned_content = cls._clean_ai_json_response(ai_content)
            
            if not cleaned_content:
                logger.warning("AI返回内容无法解析为JSON")
                return []
            
            # 解析JSON
            keywords_data = json.loads(cleaned_content)
            
            if isinstance(keywords_data, list):
                # 过滤和验证关联词
                valid_keywords = []
                for keyword in keywords_data:
                    if isinstance(keyword, str) and 2 <= len(keyword.strip()) <= 10:
                        valid_keywords.append(keyword.strip())
                
                logger.info(f"成功解析AI生成的关联词: {len(valid_keywords)} 个")
                return valid_keywords
            else:
                logger.warning("AI返回的JSON格式不是数组")
                return []
                
        except json.JSONDecodeError as e:
            logger.error(f"AI返回内容JSON解析失败: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"解析AI关联词响应异常: {str(e)}")
            return []

    @classmethod
    def _clean_ai_json_response(cls, content: str) -> str:
        """
        清理AI返回的内容，提取有效的JSON部分

        :param content: AI返回的原始内容
        :return: 清理后的JSON字符串
        """
        if not content:
            return ""
        
        # 移除可能的markdown代码块标记
        content = re.sub(r'```json\s*', '', content)
        content = re.sub(r'```\s*', '', content)
        
        # 查找JSON数组的开始和结束位置
        start_idx = content.find('[')
        end_idx = content.rfind(']')
        
        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            json_content = content[start_idx:end_idx + 1]
            return json_content.strip()
        
        return ""

    @classmethod
    def _generate_keywords_by_rules(cls, requirement_content: str, max_count: int) -> List[str]:
        """
        使用规则生成关联词（降级策略）

        :param requirement_content: 需求内容
        :param max_count: 最大生成数量
        :return: 生成的关联词列表
        """
        logger.info("使用规则生成关联词作为降级策略")
        
        # 提取需求内容中的关键词
        keywords = []
        
        # 基础关联词模板
        base_templates = [
            "投诉", "举报", "维权", "不满", "问题", "故障", "服务",
            "质量", "态度", "处理", "解决", "回应", "声明", "澄清",
            "改进", "整改", "道歉", "赔偿", "退款", "维修"
        ]
        
        # 情感相关词汇
        sentiment_words = [
            "满意", "不满意", "失望", "愤怒", "担心", "质疑",
            "支持", "反对", "赞扬", "批评", "建议", "意见"
        ]
        
        # 合并关联词
        keywords.extend(base_templates)
        keywords.extend(sentiment_words)
        
        # 限制数量并去重
        unique_keywords = list(set(keywords))
        return unique_keywords[:max_count]

    @classmethod
    async def _deduct_package_usage_for_keywords(
        cls,
        query_db: AsyncSession,
        user_id: int,
        requirement_content: str
    ):
        """
        为关键词生成功能扣减套餐使用次数
        通过创建opinion_requirement记录来实现次数扣减

        :param query_db: 数据库会话
        :param user_id: 用户ID
        :param requirement_content: 需求内容
        """
        try:
            from module_opinion.entity.do.opinion_requirement_do import OpinionRequirement
            from datetime import datetime
            import uuid

            logger.info(f"开始为用户{user_id}的关键词生成功能扣减套餐次数")

            # 创建一个opinion_requirement记录来扣减次数
            # 这个记录标记为关键词生成类型，不会影响正常的舆情分析流程
            new_requirement = OpinionRequirement(
                requirement_name=f"关键词生成_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                entity_keyword="关键词生成",
                specific_requirement=requirement_content[:500],  # 限制长度
                user_id=user_id,
                # 移除了不存在的requirement_type字段
                status=2,  # 直接标记为完成状态（2-已完成）
                create_time=datetime.now(),
                update_time=datetime.now(),
                is_deleted=0
            )

            query_db.add(new_requirement)
            await query_db.commit()
            await query_db.refresh(new_requirement)

            logger.info(f"成功为用户{user_id}创建关键词生成记录，ID: {new_requirement.id}")

        except Exception as e:
            logger.error(f"扣减套餐次数失败: {str(e)}")
            # 扣减失败不影响关键词生成功能，只记录错误
            await query_db.rollback()

    @classmethod
    def _process_keywords(cls, keywords: List[str], max_count: int) -> List[str]:
        """
        处理关联词：去重、过滤、排序

        :param keywords: 原始关联词列表
        :param max_count: 最大数量
        :return: 处理后的关联词列表
        """
        if not keywords:
            return []
        
        # 去重并过滤
        processed = []
        seen = set()
        
        for keyword in keywords:
            if isinstance(keyword, str):
                cleaned = keyword.strip()
                if (cleaned and 
                    2 <= len(cleaned) <= 10 and 
                    cleaned not in seen and
                    not cls._is_invalid_keyword(cleaned)):
                    processed.append(cleaned)
                    seen.add(cleaned)
        
        # 限制数量
        return processed[:max_count]

    @classmethod
    def _is_invalid_keyword(cls, keyword: str) -> bool:
        """
        检查关联词是否无效

        :param keyword: 关联词
        :return: 是否无效
        """
        # 过滤无效的关联词
        invalid_patterns = [
            r'^\d+$',  # 纯数字
            r'^[a-zA-Z]+$',  # 纯英文
            r'[^\u4e00-\u9fa5\w]',  # 包含特殊字符
        ]
        
        for pattern in invalid_patterns:
            if re.match(pattern, keyword):
                return True
        
        return False
