from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, BigInteger, Float
from config.database import Base


class InfoSummaryDO(Base):
    """
    信息汇总数据对象
    """
    __tablename__ = 'info_summary'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='信息ID')
    title = Column(String(500), nullable=False, comment='标题')
    content = Column(Text, comment='内容')
    summary = Column(Text, comment='摘要')
    platform_type = Column(String(50), nullable=False, comment='平台类型')
    source_name = Column(String(200), comment='来源名称')
    source_url = Column(String(1000), comment='来源URL')
    publish_time = Column(DateTime, comment='发布时间')
    sentiment = Column(String(20), comment='情感倾向')
    info_attribute = Column(String(50), comment='信息属性')
    views_count = Column(Integer, comment='浏览数')
    comments_count = Column(Integer, comment='评论数')
    shares_count = Column(Integer, comment='分享数')
    entity_type = Column(String(50), comment='实体类型')
    entity_name = Column(String(200), comment='实体名称')
    keywords = Column(Text, comment='关键词')
    images = Column(Text, comment='图片')
    status = Column(String(1), comment='状态')
    create_by = Column(String(64), comment='创建者')
    create_time = Column(DateTime, comment='创建时间')
    update_by = Column(String(64), comment='更新者')
    update_time = Column(DateTime, comment='更新时间')
    remark = Column(String(500), comment='备注')


class NewsDO(Base):
    """
    新闻数据对象
    """
    __tablename__ = 'news'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='新闻ID')
    title = Column(String(255), nullable=False, comment='标题')
    content = Column(Text, comment='内容')
    url = Column(String(255), comment='链接')
    source = Column(String(100), comment='来源')
    author = Column(String(100), comment='作者')
    publish_time = Column(DateTime, comment='发布时间')
    platform = Column(String(50), comment='平台')
    media_level = Column(String(20), comment='媒体级别')
    emotion_type = Column(String(20), comment='情感类型')
    article_type = Column(String(30), comment='文章类型')
    ip_location = Column(String(100), comment='IP位置')
    publish_area = Column(String(100), comment='发布地区')
    is_sensitive = Column(Integer, default=0, comment='是否敏感')
    keywords = Column(Text, comment='关键词')
    similarity_group = Column(Integer, comment='相似度分组')
    hot_score = Column(Float, default=0, comment='热度分数')
    create_time = Column(DateTime, comment='创建时间')
    create_by = Column(String(64), comment='创建者')
    update_by = Column(String(64), comment='更新者')
    remark = Column(String(500), comment='备注')
