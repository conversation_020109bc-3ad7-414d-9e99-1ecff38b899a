import request from '@/utils/request'

// 获取传播分析汇总数据
export function getSpreadAnalysisSummary(schemeId, params) {
  return request({
    url: `/api/spread-analysis/summary/${schemeId}`,
    method: 'get',
    params: params
  })
}

// 根据方案类型获取方案列表
export function getSchemesByType(schemeType) {
  return request({
    url: `/api/spread-analysis/schemes/${schemeType}`,
    method: 'get'
  })
}

// 获取模拟传播分析数据
export function getMockSpreadAnalysisData(schemeId) {
  return request({
    url: `/api/spread-analysis/mock-data/${schemeId}`,
    method: 'get'
  })
}

// 方案管理相关API
export function getSchemeList(query) {
  return request({
    url: '/system/scheme/list',
    method: 'get',
    params: query
  })
}

export function getScheme(schemeId) {
  return request({
    url: `/system/scheme/${schemeId}`,
    method: 'get'
  })
}

export function addScheme(data) {
  return request({
    url: '/system/scheme/add',
    method: 'post',
    data: data
  })
}

export function updateScheme(data) {
  return request({
    url: '/system/scheme/edit',
    method: 'put',
    data: data
  })
}

export function delScheme(ids) {
  return request({
    url: '/system/scheme/delete',
    method: 'delete',
    data: { ids: ids }
  })
}

// 情感分析汇总相关API
export function getEmotionAnalysisSummaryList(query) {
  return request({
    url: '/system/emotionAnalysisSummary/list',
    method: 'get',
    params: query
  })
}

export function getEmotionAnalysisSummary(id) {
  return request({
    url: `/system/emotionAnalysisSummary/${id}`,
    method: 'get'
  })
}

export function addEmotionAnalysisSummary(data) {
  return request({
    url: '/system/emotionAnalysisSummary/add',
    method: 'post',
    data: data
  })
}

export function updateEmotionAnalysisSummary(data) {
  return request({
    url: '/system/emotionAnalysisSummary/edit',
    method: 'put',
    data: data
  })
}

export function delEmotionAnalysisSummary(ids) {
  return request({
    url: '/system/emotionAnalysisSummary/delete',
    method: 'delete',
    data: { ids: ids }
  })
}

// 热点新闻传播分析相关API
export function getHotNewsSpreadAnalysisSummary(params) {
  return request({
    url: '/api/hot-news-spread-analysis/summary',
    method: 'get',
    params: params
  })
}

export function getHotNewsTrendData(params) {
  return request({
    url: '/api/hot-news-spread-analysis/trend',
    method: 'get',
    params: params
  })
}

export function getMockHotNewsSpreadAnalysisData(params) {
  return request({
    url: '/api/hot-news-spread-analysis/mock-data',
    method: 'get',
    params: params
  })
}

export function getProvincesList() {
  return request({
    url: '/api/hot-news-spread-analysis/provinces',
    method: 'get'
  })
}

// 综合传播分析相关API
export function getComprehensiveSpreadAnalysis(schemeId, params) {
  return request({
    url: `/api/spread-analysis/comprehensive/${schemeId}`,
    method: 'get',
    params: params
  })
}

export function getEmotionStatistics() {
  return request({
    url: '/api/spread-analysis/emotion-statistics',
    method: 'get'
  })
}

// 热点新闻扩展数据相关API
export function getHotNewsExtensionList(params) {
  return request({
    url: '/system/hotNewsExtension/list',
    method: 'get',
    params: params
  })
}

export function getHotNewsExtensionStatistics(params) {
  return request({
    url: '/api/spread-analysis/hot-news-extension/statistics',
    method: 'get',
    params: params
  })
}

// 事件相关API
export function getEventList(params) {
  return request({
    url: '/system/event/list',
    method: 'get',
    params: params
  })
}

export function getEventStatistics(params) {
  return request({
    url: '/api/spread-analysis/event/statistics',
    method: 'get',
    params: params
  })
}

// 事件新闻关联相关API
export function getEventNewsList(params) {
  return request({
    url: '/system/eventNews/list',
    method: 'get',
    params: params
  })
}

export function getEventNewsStatistics(params) {
  return request({
    url: '/api/spread-analysis/event-news/statistics',
    method: 'get',
    params: params
  })
}

// 情感定义相关API
export function getEmotionDefinitionList(params) {
  return request({
    url: '/system/emotionDefinition/list',
    method: 'get',
    params: params
  })
}
