from datetime import datetime
from sqlalchemy import and_, or_, desc, asc, func, select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from module_opinion.entity.do.opinion_requirement_do import OpinionRequirement
from module_opinion.entity.vo.opinion_requirement_vo import OpinionRequirementPageQueryModel, OpinionRequirementModel
from utils.page_util import PageUtil


class OpinionRequirementDao:
    """
    舆情需求数据访问层
    """

    @classmethod
    async def get_opinion_requirement_list(
        cls, db: AsyncSession, query_object: OpinionRequirementPageQueryModel, is_page: bool = False
    ):
        """
        根据查询参数获取舆情需求列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 舆情需求列表信息对象
        """
        # 添加日志输出，检查DAO层接收到的查询参数
        from utils.log_util import logger
        logger.info(f'🔍 DAO层接收到的查询对象: {query_object.model_dump()}')
        logger.info(f'🔍 DAO层 - 需求名称: {query_object.requirement_name}')
        logger.info(f'🔍 DAO层 - 实体关键词: {query_object.entity_keyword}')
        logger.info(f'🔍 DAO层 - 分析状态: {query_object.analysis_status}')

        query = select(OpinionRequirement).where(OpinionRequirement.is_deleted == 0)

        # 构建查询条件
        filter_count = 0
        applied_filters = []

        if query_object.requirement_name and query_object.requirement_name.strip():
            requirement_name = query_object.requirement_name.strip()
            logger.info(f'🔍 添加需求名称筛选条件: {requirement_name}')
            query = query.where(OpinionRequirement.requirement_name.like(f'%{requirement_name}%'))
            filter_count += 1
            applied_filters.append(f'需求名称包含"{requirement_name}"')

        if query_object.entity_keyword and query_object.entity_keyword.strip():
            entity_keyword = query_object.entity_keyword.strip()
            logger.info(f'🔍 添加实体关键词筛选条件: {entity_keyword}')
            query = query.where(OpinionRequirement.entity_keyword.like(f'%{entity_keyword}%'))
            filter_count += 1
            applied_filters.append(f'关键词包含"{entity_keyword}"')

        if query_object.status is not None:
            logger.info(f'🔍 添加状态筛选条件: {query_object.status}')
            query = query.where(OpinionRequirement.status == query_object.status)
            filter_count += 1
            applied_filters.append(f'状态={query_object.status}')

        if query_object.analysis_status is not None:
            logger.info(f'🔍 添加分析状态筛选条件: {query_object.analysis_status}')
            query = query.where(OpinionRequirement.analysis_status == query_object.analysis_status)
            filter_count += 1
            applied_filters.append(f'分析状态={query_object.analysis_status}')

        if query_object.priority:
            query = query.where(OpinionRequirement.priority == query_object.priority)
            filter_count += 1
            applied_filters.append(f'优先级={query_object.priority}')

        if query_object.create_time_start:
            query = query.where(OpinionRequirement.create_time >= query_object.create_time_start)
            filter_count += 1
            applied_filters.append(f'创建时间>={query_object.create_time_start}')

        if query_object.create_time_end:
            query = query.where(OpinionRequirement.create_time <= query_object.create_time_end)
            filter_count += 1
            applied_filters.append(f'创建时间<={query_object.create_time_end}')

        # 用户权限过滤
        if query_object.user_id:
            query = query.where(OpinionRequirement.user_id == query_object.user_id)
            filter_count += 1
            applied_filters.append(f'用户ID={query_object.user_id}')

        # 记录筛选条件汇总
        if filter_count > 0:
            logger.info(f'🔍 共应用了 {filter_count} 个筛选条件: {", ".join(applied_filters)}')
        else:
            logger.info('🔍 未应用任何筛选条件，将返回全部数据')

        # 排序
        query = query.order_by(desc(OpinionRequirement.create_time))

        if is_page:
            # 分页查询
            result = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size)

            # 验证筛选结果
            if hasattr(result, 'records') and hasattr(result, 'total'):
                logger.info(f'🔍 筛选结果验证: 返回 {len(result.records)} 条记录，总计 {result.total} 条')

                if filter_count > 0:
                    if result.total == 0:
                        logger.warning(f'⚠️ 筛选条件过严，未找到匹配记录')
                    elif len(result.records) > 0:
                        # 验证第一条记录是否符合筛选条件
                        first_record = result.records[0]
                        logger.info(f'🔍 第一条记录验证: ID={first_record.id}, 需求名称="{first_record.requirement_name}", 关键词="{first_record.entity_keyword}", 分析状态={first_record.analysis_status}')

                        # 检查是否符合筛选条件
                        matches = []
                        if query_object.requirement_name and query_object.requirement_name in (first_record.requirement_name or ''):
                            matches.append('需求名称匹配')
                        if query_object.entity_keyword and query_object.entity_keyword in (first_record.entity_keyword or ''):
                            matches.append('关键词匹配')
                        if query_object.analysis_status is not None and first_record.analysis_status == query_object.analysis_status:
                            matches.append('分析状态匹配')

                        if matches:
                            logger.info(f'✅ 筛选生效: {", ".join(matches)}')
                        else:
                            logger.warning(f'⚠️ 筛选可能未生效: 第一条记录不符合筛选条件')
                else:
                    logger.info('🔍 无筛选条件，返回全部数据')

            return result
        else:
            # 不分页查询
            result = await db.execute(query)
            records = result.scalars().all()

            # 验证筛选结果
            logger.info(f'🔍 不分页查询结果: 返回 {len(records)} 条记录')
            if filter_count > 0 and len(records) > 0:
                first_record = records[0]
                logger.info(f'🔍 第一条记录: ID={first_record.id}, 需求名称="{first_record.requirement_name}"')

            return records

    @classmethod
    async def get_opinion_requirement_by_id(cls, db: AsyncSession, requirement_id: int):
        """
        根据ID获取舆情需求详细信息

        :param db: orm对象
        :param requirement_id: 需求ID
        :return: 舆情需求信息对象
        """
        query = select(OpinionRequirement).where(
            and_(OpinionRequirement.id == requirement_id, OpinionRequirement.is_deleted == 0)
        )
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def add_opinion_requirement_dao(cls, db: AsyncSession, requirement: OpinionRequirementModel):
        """
        新增舆情需求数据库操作

        :param db: orm对象
        :param requirement: 舆情需求对象
        :return: 新增的舆情需求对象
        """
        db_requirement = OpinionRequirement(**requirement.model_dump(exclude={'id'}))
        db.add(db_requirement)
        await db.flush()
        return db_requirement

    @classmethod
    async def edit_opinion_requirement_dao(cls, db: AsyncSession, requirement: dict):
        """
        编辑舆情需求数据库操作

        :param db: orm对象
        :param requirement: 需要更新的舆情需求字典
        :return:
        """
        await db.execute(
            update(OpinionRequirement).where(OpinionRequirement.id == requirement['id']).values(**requirement)
        )

    @classmethod
    async def delete_opinion_requirement_dao(cls, db: AsyncSession, requirement_ids: List[int]):
        """
        删除舆情需求数据库操作（软删除）

        :param db: orm对象
        :param requirement_ids: 舆情需求ID列表
        :return:
        """
        await db.execute(
            update(OpinionRequirement)
            .where(OpinionRequirement.id.in_(requirement_ids))
            .values(is_deleted=1, update_time=datetime.now())
        )

    @classmethod
    async def get_requirement_list_for_select(cls, db: AsyncSession, user_id: Optional[int] = None):
        """
        获取需求列表用于下拉选择

        :param db: orm对象
        :param user_id: 用户ID，可选
        :return: 需求列表
        """
        query = select(
            OpinionRequirement.id,
            OpinionRequirement.requirement_name,
            OpinionRequirement.entity_keyword,
            OpinionRequirement.status,
            OpinionRequirement.analysis_status,
            OpinionRequirement.create_time
        ).where(
            and_(OpinionRequirement.is_deleted == 0, OpinionRequirement.status == 1)
        )
        
        if user_id:
            query = query.where(OpinionRequirement.user_id == user_id)
            
        query = query.order_by(desc(OpinionRequirement.create_time))
        
        result = await db.execute(query)
        return result.fetchall()

    @classmethod
    async def check_requirement_name_unique(cls, db: AsyncSession, requirement_name: str, requirement_id: Optional[int] = None):
        """
        检查需求名称是否唯一

        :param db: orm对象
        :param requirement_name: 需求名称
        :param requirement_id: 需求ID（编辑时排除自己）
        :return: 是否唯一
        """
        query = select(func.count(OpinionRequirement.id)).where(
            and_(
                OpinionRequirement.requirement_name == requirement_name,
                OpinionRequirement.is_deleted == 0
            )
        )
        
        if requirement_id:
            query = query.where(OpinionRequirement.id != requirement_id)
            
        result = await db.execute(query)
        count = result.scalar()
        return count == 0

    @classmethod
    async def update_requirement_step(cls, db: AsyncSession, requirement_id: int, step: int):
        """
        更新需求当前步骤

        :param db: orm对象
        :param requirement_id: 需求ID
        :param step: 步骤号
        :return:
        """
        await db.execute(
            update(OpinionRequirement)
            .where(OpinionRequirement.id == requirement_id)
            .values(current_step=step, update_time=datetime.now())
        )

    @classmethod
    async def update_keywords_count(cls, db: AsyncSession, requirement_id: int, count: int):
        """
        更新已选择关键词数量

        :param db: orm对象
        :param requirement_id: 需求ID
        :param count: 关键词数量
        :return:
        """
        await db.execute(
            update(OpinionRequirement)
            .where(OpinionRequirement.id == requirement_id)
            .values(selected_keywords_count=count, update_time=datetime.now())
        )
