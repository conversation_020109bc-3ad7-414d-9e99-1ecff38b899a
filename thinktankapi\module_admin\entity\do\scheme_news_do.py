from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, BigInteger, Float, ForeignKey, <PERSON>olean
from sqlalchemy.orm import relationship
from config.database import Base


class SchemeNews(Base):
    """
    方案新闻关联表
    """

    __tablename__ = 'scheme_news'
    __table_args__ = {'extend_existing': True}

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    scheme_id = Column(Integer, ForeignKey('scheme.id'), nullable=False, comment='方案ID')
    news_id = Column(BigInteger, ForeignKey('news.id'), nullable=False, comment='新闻ID')
    match_type = Column(String(50), default='keyword', comment='匹配类型：keyword, semantic, manual')
    match_keywords = Column(String(500), nullable=True, comment='匹配的关键词')
    relevance_score = Column(Float, default=0.0, comment='相关性分数：0-1')
    confidence_score = Column(Float, default=0.0, comment='置信度分数：0-1')
    is_included = Column(Boolean, default=True, comment='是否包含在方案中')
    is_excluded = Column(Boolean, default=False, comment='是否被排除')
    exclude_reason = Column(String(200), nullable=True, comment='排除原因')
    weight = Column(Float, default=1.0, comment='权重')
    auto_generated = Column(Boolean, default=True, comment='是否自动生成')
    verified_by = Column(String(64), nullable=True, comment='验证人')
    verified_time = Column(DateTime, nullable=True, comment='验证时间')
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')

    # 关联关系
    # scheme = relationship("SchemeDO", back_populates="scheme_news")  # 暂时注释掉
    # news = relationship("News", back_populates="scheme_news")  # 暂时注释掉
