2025-07-09 08:52:08.518 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 08:52:08.519 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 08:52:12.522 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 08:52:12.522 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 08:52:12.569 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 08:52:13.370 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 08:52:13.913 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 08:52:13.913 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 08:52:21.764 | 744707b811bf4868a90edd1fde2adf52 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为faa0e95f-a22d-41d5-9b73-b937520f132b的会话获取图片验证码成功
2025-07-09 08:52:24.848 | 0286de738cab492fb664b68721554db6 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-09 08:52:25.185 | 095fec32d9024ba0b8cd6a764156f381 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 08:52:25.774 | 2c935766810047cabfe3421ffa6c81b6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 08:52:26.287 | 163420c9bca2429a8a213f6d1487218d | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 08:52:26.805 | c56d3843ce6640829257114f884e76a8 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:366 - 获取舆情任务列表成功
2025-07-09 08:52:48.387 | 67180b1961b84db38b2755ff26a2c89e | INFO     | module_opinion.controller.opinion_analysis_controller:create_public_requirement:107 - 创建舆情需求成功
2025-07-09 08:53:03.131 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:724 - 开始分析，需求ID: 91
2025-07-09 08:53:03.131 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:730 - 执行联网搜索...
2025-07-09 08:53:03.131 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 老板电器
2025-07-09 08:53:36.886 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 08:54:01.663 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 08:54:01.664 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:243 - AI返回的原始内容: [
  {
    "title": "消费者投诉老板电器售后服务质量问题",
    "content": "杭州市临平区市场监督管理局公示信息显示，2024年6月19日有消费者通过全国12315平台投诉老板电器线下门店，反映购买的抽油烟机出现服务质量问题并要求退赔。经平台调解，该纠纷已于6月24日通过双方和解方式结案。市场监管部门数据显示，公众对企业的负面情绪主要集中在售后响应效率及服务质量方...
2025-07-09 08:54:09.156 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 08:54:09.159 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: neutral
2025-07-09 08:54:09.159 | ee69b79eb9d64fe48ca301c94b600dbc | WARNING  | module_opinion.service.external_api_service:_validate_and_clean_url:571 - 过滤掉搜索引擎或无效URL: https://baijiahao.baidu.com/s?id=1835804220647189120
2025-07-09 08:54:09.159 | ee69b79eb9d64fe48ca301c94b600dbc | WARNING  | module_opinion.service.external_api_service:_ai_extract_articles:289 - AI提供的URL验证失败: https://baijiahao.baidu.com/s?id=1835804220647189120
2025-07-09 08:54:09.159 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_generate_smart_news_url:678 - 生成智能新闻搜索URL: https://news.sina.com.cn/search/?q=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 08:54:09.159 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:294 - 使用智能生成的新闻搜索URL: https://news.sina.com.cn/search/?q=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 08:54:09.159 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:307 - 成功提取 1 篇文章
2025-07-09 08:54:09.159 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:215 - AI成功提取到 1 篇文章
2025-07-09 08:54:09.160 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:852 - 情感分析统计: {'positive': 0, 'neutral': 100, 'negative': 0} (总文章数: 1)
2025-07-09 08:54:09.160 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:951 - 开始保存搜索结果到数据库
2025-07-09 08:54:09.161 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:952 - 实体关键词: 老板电器
2025-07-09 08:54:09.161 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:953 - 选中关键词: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 08:54:09.161 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:954 - 搜索结果文章数量: 1
2025-07-09 08:54:09.162 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:964 - 合并后的关键词字符串: 老板电器,老板电器 售后服务,老板电器 三包义务,老板电器 客服态度,老板电器 质量,老板电器 燃气灶爆炸
2025-07-09 08:54:09.162 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 1 条文章: 消费者投诉老板电器售后服务质量问题
2025-07-09 08:54:09.163 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://news.sina.com.cn/search/?q=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 08:54:09.164 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://news.sina.com.cn/search/?q=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 08:54:09.164 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 保存高质量新闻URL: https://news.sina.com.cn/search/?q=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 08:54:09.164 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=消费者投诉老板电器售后服务质量问题..., url=https://news.sina.com.cn/search/?q=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8, sentiment=neutral
2025-07-09 08:54:09.195 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 1 条文章，ID: 1762
2025-07-09 08:54:09.251 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1028 - 成功保存 1 条搜索结果到数据库
2025-07-09 08:54:09.251 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.service.external_api_service:perform_online_search:53 - 联网搜索完成，获取到 1 条结果，已保存 1 条到数据库
2025-07-09 08:54:09.251 | ee69b79eb9d64fe48ca301c94b600dbc | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:766 - 分析执行成功
2025-07-09 09:01:01.317 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:724 - 开始分析，需求ID: 91
2025-07-09 09:01:01.317 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:730 - 执行联网搜索...
2025-07-09 09:01:01.317 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 老板电器
2025-07-09 09:01:50.879 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:02:35.975 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:02:35.976 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:243 - AI返回的原始内容: [
  {
    "title": "杭州消费者投诉老板电器油烟机电机轴心偏差超标",
    "content": "杭州消费者张先生通过线下渠道购买的油烟机出现运行异响，专业检测显示电机轴心偏差达0.23mm，远超国家标准要求的≤0.05mm。销售方以\"非性能故障\"为由拒绝退换，涉及金额4899元，目前当地市场监督管理局已介入调解。该案例反映了产品质量把控环节存在的隐患，电机轴心偏差问题...
2025-07-09 09:02:41.409 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:02:41.410 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: negative
2025-07-09 09:02:41.411 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: http://www.12315.cn/cuser/portal/tscase/notice
2025-07-09 09:02:41.412 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:287 - 使用AI提供的有效URL: http://www.12315.cn/cuser/portal/tscase/notice
2025-07-09 09:02:52.054 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:02:52.056 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: neutral
2025-07-09 09:02:52.056 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://tousu.sina.com.cn/company/view/?couid=6001582
2025-07-09 09:02:52.057 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://tousu.sina.com.cn/company/view/?couid=6001582
2025-07-09 09:02:52.058 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:285 - 使用AI提供的高质量新闻URL: https://tousu.sina.com.cn/company/view/?couid=6001582
2025-07-09 09:03:00.233 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:03:00.234 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: neutral
2025-07-09 09:03:00.234 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: http://www.zj315.org/News/ArticleDetail/7231
2025-07-09 09:03:00.234 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): http://www.zj315.org/News/ArticleDetail/7231
2025-07-09 09:03:00.234 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:285 - 使用AI提供的高质量新闻URL: http://www.zj315.org/News/ArticleDetail/7231
2025-07-09 09:03:09.097 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:03:09.098 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: neutral
2025-07-09 09:03:09.099 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://tousu.sina.com.cn/company/view/?couid=6001582
2025-07-09 09:03:09.099 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://tousu.sina.com.cn/company/view/?couid=6001582
2025-07-09 09:03:09.099 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:285 - 使用AI提供的高质量新闻URL: https://tousu.sina.com.cn/company/view/?couid=6001582
2025-07-09 09:03:09.100 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:307 - 成功提取 4 篇文章
2025-07-09 09:03:09.100 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:215 - AI成功提取到 4 篇文章
2025-07-09 09:03:09.100 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:852 - 情感分析统计: {'positive': 0, 'neutral': 75, 'negative': 25} (总文章数: 4)
2025-07-09 09:03:09.100 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:951 - 开始保存搜索结果到数据库
2025-07-09 09:03:09.101 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:952 - 实体关键词: 老板电器
2025-07-09 09:03:09.102 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:953 - 选中关键词: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 09:03:09.102 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:954 - 搜索结果文章数量: 4
2025-07-09 09:03:09.103 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:964 - 合并后的关键词字符串: 老板电器,老板电器 售后服务,老板电器 三包义务,老板电器 客服态度,老板电器 质量,老板电器 燃气灶爆炸
2025-07-09 09:03:09.103 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 1 条文章: 杭州消费者投诉老板电器油烟机电机轴心偏差超标
2025-07-09 09:03:09.105 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: http://www.12315.cn/cuser/portal/tscase/notice
2025-07-09 09:03:09.105 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:984 - 保存有效URL: http://www.12315.cn/cuser/portal/tscase/notice
2025-07-09 09:03:09.106 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=杭州消费者投诉老板电器油烟机电机轴心偏差超标..., url=http://www.12315.cn/cuser/portal/tscase/notice, sentiment=negative
2025-07-09 09:03:09.138 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 1 条文章，ID: 1763
2025-07-09 09:03:09.138 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 2 条文章: 北京用户三换油烟机仍遇面板碎裂问题
2025-07-09 09:03:09.138 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://tousu.sina.com.cn/company/view/?couid=6001582
2025-07-09 09:03:09.138 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://tousu.sina.com.cn/company/view/?couid=6001582
2025-07-09 09:03:09.139 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 保存高质量新闻URL: https://tousu.sina.com.cn/company/view/?couid=6001582
2025-07-09 09:03:09.139 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=北京用户三换油烟机仍遇面板碎裂问题..., url=https://tousu.sina.com.cn/company/view/?couid=6001582, sentiment=neutral
2025-07-09 09:03:09.168 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 2 条文章，ID: 1764
2025-07-09 09:03:09.168 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 3 条文章: 四川消费者遭遇油烟机尺寸不符宣传纠纷
2025-07-09 09:03:09.169 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: http://www.zj315.org/News/ArticleDetail/7231
2025-07-09 09:03:09.169 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): http://www.zj315.org/News/ArticleDetail/7231
2025-07-09 09:03:09.169 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 保存高质量新闻URL: http://www.zj315.org/News/ArticleDetail/7231
2025-07-09 09:03:09.169 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=四川消费者遭遇油烟机尺寸不符宣传纠纷..., url=http://www.zj315.org/News/ArticleDetail/7231, sentiment=neutral
2025-07-09 09:03:09.199 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 3 条文章，ID: 1765
2025-07-09 09:03:09.200 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 4 条文章: 老板电器售后服务响应超时问题突出
2025-07-09 09:03:09.200 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://tousu.sina.com.cn/company/view/?couid=6001582
2025-07-09 09:03:09.201 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://tousu.sina.com.cn/company/view/?couid=6001582
2025-07-09 09:03:09.201 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 保存高质量新闻URL: https://tousu.sina.com.cn/company/view/?couid=6001582
2025-07-09 09:03:09.201 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=老板电器售后服务响应超时问题突出..., url=https://tousu.sina.com.cn/company/view/?couid=6001582, sentiment=neutral
2025-07-09 09:03:09.231 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 4 条文章，ID: 1766
2025-07-09 09:03:09.288 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1028 - 成功保存 4 条搜索结果到数据库
2025-07-09 09:03:09.288 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.service.external_api_service:perform_online_search:53 - 联网搜索完成，获取到 4 条结果，已保存 4 条到数据库
2025-07-09 09:03:09.288 | 2cf75050f8a8480b96d57691b140a096 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:766 - 分析执行成功
2025-07-09 09:03:09.353 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 09:03:09.354 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 09:03:13.232 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:03:13.233 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:03:15.095 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:03:15.096 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:03:15.097 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:03:15.818 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:03:16.608 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:03:16.608 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:06:13.666 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 09:06:13.666 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 09:06:17.015 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:06:17.015 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:06:19.019 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:06:19.020 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:06:19.021 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:06:19.677 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:06:20.297 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:06:20.298 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:06:43.853 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:724 - 开始分析，需求ID: 91
2025-07-09 09:06:43.853 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:730 - 执行联网搜索...
2025-07-09 09:06:43.853 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 老板电器
2025-07-09 09:07:21.739 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:08:50.999 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:08:51.000 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:243 - AI返回的原始内容: [
  {
    "title": "老板电器油烟机换货质量问题引消费者投诉",
    "content": "消费者在京东平台购买的老板电器油烟机连续两次换货后仍出现玻璃破碎等质量问题，换货周期长达近一个月未解决。该案例反映了产品品控环节的漏洞，暴露了退换货流程的效率问题，导致消费者权益受损。目前相关投诉已由黑猫投诉平台受理，涉及商品型号为CXW-260-28D3S。",
    "sour...
2025-07-09 09:08:59.814 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:08:59.816 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: negative
2025-07-09 09:08:59.817 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://tousu.sina.com.cn/complaint/view/17385432188
2025-07-09 09:08:59.818 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://tousu.sina.com.cn/complaint/view/17385432188
2025-07-09 09:08:59.818 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:285 - 使用AI提供的高质量新闻URL: https://tousu.sina.com.cn/complaint/view/17385432188
2025-07-09 09:09:08.411 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:09:08.412 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: negative
2025-07-09 09:09:08.412 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://www.12315.cn/cuser/portal/tscase/85f6c7c5-ea12-4d2c-bf64-08dc9ac6a7dc
2025-07-09 09:09:08.412 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:287 - 使用AI提供的有效URL: https://www.12315.cn/cuser/portal/tscase/85f6c7c5-ea12-4d2c-bf64-08dc9ac6a7dc
2025-07-09 09:09:13.304 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:09:13.305 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: neutral
2025-07-09 09:09:13.305 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_generate_smart_news_url:678 - 生成智能新闻搜索URL: https://news.qq.com/search.htm?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:13.306 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:294 - 使用智能生成的新闻搜索URL: https://news.qq.com/search.htm?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:23.311 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:09:23.312 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: neutral
2025-07-09 09:09:23.312 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_generate_smart_news_url:678 - 生成智能新闻搜索URL: https://www.chinanews.com.cn/search/search.jsp?q=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:23.313 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:294 - 使用智能生成的新闻搜索URL: https://www.chinanews.com.cn/search/search.jsp?q=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:27.660 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:09:27.661 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: neutral
2025-07-09 09:09:27.662 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_generate_smart_news_url:678 - 生成智能新闻搜索URL: https://search.people.com.cn/search?keyword=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:27.662 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:294 - 使用智能生成的新闻搜索URL: https://search.people.com.cn/search?keyword=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.217 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:09:32.218 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: neutral
2025-07-09 09:09:32.218 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_generate_smart_news_url:678 - 生成智能新闻搜索URL: https://sou.chinadaily.com.cn/search.html?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.218 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:294 - 使用智能生成的新闻搜索URL: https://sou.chinadaily.com.cn/search.html?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.218 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:307 - 成功提取 6 篇文章
2025-07-09 09:09:32.219 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:215 - AI成功提取到 6 篇文章
2025-07-09 09:09:32.219 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:852 - 情感分析统计: {'positive': 0, 'neutral': 67, 'negative': 33} (总文章数: 6)
2025-07-09 09:09:32.219 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:951 - 开始保存搜索结果到数据库
2025-07-09 09:09:32.219 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:952 - 实体关键词: 老板电器
2025-07-09 09:09:32.219 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:953 - 选中关键词: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 09:09:32.219 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:954 - 搜索结果文章数量: 6
2025-07-09 09:09:32.220 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:964 - 合并后的关键词字符串: 老板电器,老板电器 售后服务,老板电器 三包义务,老板电器 客服态度,老板电器 质量,老板电器 燃气灶爆炸
2025-07-09 09:09:32.220 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 1 条文章: 老板电器油烟机换货质量问题引消费者投诉
2025-07-09 09:09:32.220 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://tousu.sina.com.cn/complaint/view/17385432188
2025-07-09 09:09:32.221 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://tousu.sina.com.cn/complaint/view/17385432188
2025-07-09 09:09:32.221 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 保存高质量新闻URL: https://tousu.sina.com.cn/complaint/view/17385432188
2025-07-09 09:09:32.222 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=老板电器油烟机换货质量问题引消费者投诉..., url=https://tousu.sina.com.cn/complaint/view/17385432188, sentiment=negative
2025-07-09 09:09:32.263 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 1 条文章，ID: 1767
2025-07-09 09:09:32.263 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 2 条文章: 国补产品尺寸不符引消费者索赔诉求
2025-07-09 09:09:32.263 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://www.12315.cn/cuser/portal/tscase/85f6c7c5-ea12-4d2c-bf64-08dc9ac6a7dc
2025-07-09 09:09:32.264 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:984 - 保存有效URL: https://www.12315.cn/cuser/portal/tscase/85f6c7c5-ea12-4d2c-bf64-08dc9ac6a7dc
2025-07-09 09:09:32.264 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=国补产品尺寸不符引消费者索赔诉求..., url=https://www.12315.cn/cuser/portal/tscase/85f6c7c5-ea12-4d2c-bf64-08dc9ac6a7dc, sentiment=negative
2025-07-09 09:09:32.302 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 2 条文章，ID: 1768
2025-07-09 09:09:32.302 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 3 条文章: 售后服务体系效率问题引发集中投诉
2025-07-09 09:09:32.302 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://news.qq.com/search.htm?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.302 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://news.qq.com/search.htm?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.302 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 保存高质量新闻URL: https://news.qq.com/search.htm?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.302 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=售后服务体系效率问题引发集中投诉..., url=https://news.qq.com/search.htm?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8, sentiment=neutral
2025-07-09 09:09:32.339 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 3 条文章，ID: 1769
2025-07-09 09:09:32.339 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 4 条文章: 客服体系漏洞加剧品牌信任危机
2025-07-09 09:09:32.339 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://www.chinanews.com.cn/search/search.jsp?q=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.340 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://www.chinanews.com.cn/search/search.jsp?q=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.340 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 保存高质量新闻URL: https://www.chinanews.com.cn/search/search.jsp?q=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.340 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=客服体系漏洞加剧品牌信任危机..., url=https://www.chinanews.com.cn/search/search.jsp?q=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8, sentiment=neutral
2025-07-09 09:09:32.377 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 4 条文章，ID: 1770
2025-07-09 09:09:32.377 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 5 条文章: 燃气灶产品安全问题引发关注
2025-07-09 09:09:32.377 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://search.people.com.cn/search?keyword=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.377 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://search.people.com.cn/search?keyword=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.378 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 保存高质量新闻URL: https://search.people.com.cn/search?keyword=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.378 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=燃气灶产品安全问题引发关注..., url=https://search.people.com.cn/search?keyword=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8, sentiment=neutral
2025-07-09 09:09:32.415 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 5 条文章，ID: 1771
2025-07-09 09:09:32.415 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 6 条文章: 三包义务执行问题成为争议核心
2025-07-09 09:09:32.415 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://sou.chinadaily.com.cn/search.html?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.415 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://sou.chinadaily.com.cn/search.html?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.415 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 保存高质量新闻URL: https://sou.chinadaily.com.cn/search.html?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:09:32.415 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=三包义务执行问题成为争议核心..., url=https://sou.chinadaily.com.cn/search.html?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8, sentiment=neutral
2025-07-09 09:09:32.452 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 6 条文章，ID: 1772
2025-07-09 09:09:32.524 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1028 - 成功保存 6 条搜索结果到数据库
2025-07-09 09:09:32.525 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.service.external_api_service:perform_online_search:53 - 联网搜索完成，获取到 6 条结果，已保存 6 条到数据库
2025-07-09 09:09:32.525 | 788b4c7005f5406390db3262cf78a51c | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:766 - 分析执行成功
2025-07-09 09:13:06.015 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 09:13:06.016 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 09:13:09.294 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:13:09.294 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:13:12.198 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:13:12.198 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:13:12.200 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:13:13.647 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:13:14.539 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:13:14.540 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:13:22.789 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:724 - 开始分析，需求ID: 91
2025-07-09 09:13:22.790 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:730 - 执行联网搜索...
2025-07-09 09:13:22.790 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 老板电器
2025-07-09 09:14:19.417 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:15:46.864 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:15:46.864 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:243 - AI返回的原始内容: [
  {
    "title": "老板电器油烟机尺寸不符遭投诉，换货过程中再遇质量问题",
    "content": "消费者投诉订单编号17384754299显示，6月15日购买的老板电器油烟机存在尺寸标注不符问题，商品标注471mm实际到货尺寸为551mm。多次换货过程中出现商品二次破损（玻璃碎裂等质量问题），投诉处理周期长达25天未解决（6月2日-7月1日）。该事件暴露出产品质量把...
2025-07-09 09:15:59.090 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:15:59.091 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: negative
2025-07-09 09:15:59.091 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://tousu.sina.com.cn
2025-07-09 09:15:59.091 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://tousu.sina.com.cn
2025-07-09 09:15:59.091 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:285 - 使用AI提供的高质量新闻URL: https://tousu.sina.com.cn
2025-07-09 09:16:06.918 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:16:06.918 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: neutral
2025-07-09 09:16:06.919 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://help.jd.com
2025-07-09 09:16:06.919 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:287 - 使用AI提供的有效URL: https://help.jd.com
2025-07-09 09:16:15.365 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:16:15.366 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: neutral
2025-07-09 09:16:15.366 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://www.12315.cn
2025-07-09 09:16:15.366 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:287 - 使用AI提供的有效URL: https://www.12315.cn
2025-07-09 09:16:15.366 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:307 - 成功提取 3 篇文章
2025-07-09 09:16:15.366 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:215 - AI成功提取到 3 篇文章
2025-07-09 09:16:15.366 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:852 - 情感分析统计: {'positive': 0, 'neutral': 67, 'negative': 33} (总文章数: 3)
2025-07-09 09:16:15.367 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:951 - 开始保存搜索结果到数据库
2025-07-09 09:16:15.367 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:952 - 实体关键词: 老板电器
2025-07-09 09:16:15.367 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:953 - 选中关键词: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 09:16:15.367 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:954 - 搜索结果文章数量: 3
2025-07-09 09:16:15.367 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:964 - 合并后的关键词字符串: 老板电器,老板电器 售后服务,老板电器 三包义务,老板电器 客服态度,老板电器 质量,老板电器 燃气灶爆炸
2025-07-09 09:16:15.367 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 1 条文章: 老板电器油烟机尺寸不符遭投诉，换货过程中再遇质量问题
2025-07-09 09:16:15.368 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://tousu.sina.com.cn
2025-07-09 09:16:15.368 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://tousu.sina.com.cn
2025-07-09 09:16:15.368 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 保存高质量新闻URL: https://tousu.sina.com.cn
2025-07-09 09:16:15.369 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=老板电器油烟机尺寸不符遭投诉，换货过程中再遇质量问题..., url=https://tousu.sina.com.cn, sentiment=negative
2025-07-09 09:16:15.401 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 1 条文章，ID: 1773
2025-07-09 09:16:15.401 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 2 条文章: 老板电器客服响应机制遭消费者质疑
2025-07-09 09:16:15.402 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://help.jd.com
2025-07-09 09:16:15.402 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:984 - 保存有效URL: https://help.jd.com
2025-07-09 09:16:15.402 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=老板电器客服响应机制遭消费者质疑..., url=https://help.jd.com, sentiment=neutral
2025-07-09 09:16:15.434 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 2 条文章，ID: 1774
2025-07-09 09:16:15.434 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 3 条文章: 监管部门介入处理老板电器消费纠纷案件
2025-07-09 09:16:15.435 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://www.12315.cn
2025-07-09 09:16:15.435 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:984 - 保存有效URL: https://www.12315.cn
2025-07-09 09:16:15.435 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=监管部门介入处理老板电器消费纠纷案件..., url=https://www.12315.cn, sentiment=neutral
2025-07-09 09:16:15.465 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 3 条文章，ID: 1775
2025-07-09 09:16:15.535 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1028 - 成功保存 3 条搜索结果到数据库
2025-07-09 09:16:15.536 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.service.external_api_service:perform_online_search:53 - 联网搜索完成，获取到 3 条结果，已保存 3 条到数据库
2025-07-09 09:16:15.536 | c657766de07e41238e4739b1acc0b936 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:766 - 分析执行成功
2025-07-09 09:21:16.108 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 09:21:16.109 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 09:21:19.366 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:21:19.367 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:21:21.538 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:21:21.538 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:21:21.539 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:21:22.347 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:21:23.002 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:21:23.002 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:21:29.291 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:724 - 开始分析，需求ID: 91
2025-07-09 09:21:29.291 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:730 - 执行联网搜索...
2025-07-09 09:21:29.292 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 老板电器
2025-07-09 09:22:23.869 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:23:22.476 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:23:22.477 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:243 - AI返回的原始内容: [
  {
    "title": "京东平台老板电器油烟机换货次品纠纷",
    "content": "消费者通过京东平台购买的老板牌油烟机两次换货均存在质量问题（玻璃破损、部件损坏），商家处理周期长达一个月且沟通态度消极，平台协调未取得有效进展。事件反映产品质量缺陷与售后服务体系问题，用户权益保护诉求亟待解决。",
    "source": "新浪消费者投诉平台",
    "url"...
2025-07-09 09:23:33.699 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:23:33.700 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: negative
2025-07-09 09:23:33.701 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://tousu.sina.com.cn/complaint/view/1735475299/
2025-07-09 09:23:33.701 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://tousu.sina.com.cn/complaint/view/1735475299/
2025-07-09 09:23:33.701 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:285 - 使用AI提供的高质量新闻URL: https://tousu.sina.com.cn/complaint/view/1735475299/
2025-07-09 09:23:44.600 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:23:44.601 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: negative
2025-07-09 09:23:44.601 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://tousu.sina.com.cn/complaint/view/17384754299/
2025-07-09 09:23:44.601 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://tousu.sina.com.cn/complaint/view/17384754299/
2025-07-09 09:23:44.601 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:285 - 使用AI提供的高质量新闻URL: https://tousu.sina.com.cn/complaint/view/17384754299/
2025-07-09 09:23:53.710 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:23:53.711 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:812 - 使用本地情感分析结果: neutral
2025-07-09 09:23:53.712 | 2934a3fc53fd4a669dcb605b96553991 | WARNING  | module_opinion.service.external_api_service:_validate_and_clean_url:571 - 过滤掉搜索引擎或无效URL: https://baijiahao.baidu.com/s?id=1835804220647189120
2025-07-09 09:23:53.712 | 2934a3fc53fd4a669dcb605b96553991 | WARNING  | module_opinion.service.external_api_service:_ai_extract_articles:289 - AI提供的URL验证失败: https://baijiahao.baidu.com/s?id=1835804220647189120
2025-07-09 09:23:53.712 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_generate_smart_news_url:678 - 生成智能新闻搜索URL: https://news.qq.com/search.htm?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:23:53.713 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:294 - 使用智能生成的新闻搜索URL: https://news.qq.com/search.htm?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:23:53.713 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:307 - 成功提取 3 篇文章
2025-07-09 09:23:53.713 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:215 - AI成功提取到 3 篇文章
2025-07-09 09:23:53.713 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:852 - 情感分析统计: {'positive': 0, 'neutral': 33, 'negative': 67} (总文章数: 3)
2025-07-09 09:23:53.714 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:951 - 开始保存搜索结果到数据库
2025-07-09 09:23:53.714 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:952 - 实体关键词: 老板电器
2025-07-09 09:23:53.714 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:953 - 选中关键词: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 09:23:53.714 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:954 - 搜索结果文章数量: 3
2025-07-09 09:23:53.715 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:964 - 合并后的关键词字符串: 老板电器,老板电器 售后服务,老板电器 三包义务,老板电器 客服态度,老板电器 质量,老板电器 燃气灶爆炸
2025-07-09 09:23:53.715 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 1 条文章: 京东平台老板电器油烟机换货次品纠纷
2025-07-09 09:23:53.715 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://tousu.sina.com.cn/complaint/view/1735475299/
2025-07-09 09:23:53.716 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://tousu.sina.com.cn/complaint/view/1735475299/
2025-07-09 09:23:53.716 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 保存高质量新闻URL: https://tousu.sina.com.cn/complaint/view/1735475299/
2025-07-09 09:23:53.716 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=京东平台老板电器油烟机换货次品纠纷..., url=https://tousu.sina.com.cn/complaint/view/1735475299/, sentiment=negative
2025-07-09 09:23:53.758 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 1 条文章，ID: 1776
2025-07-09 09:23:53.758 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 2 条文章: 老板电器油烟机尺寸虚假宣传争议
2025-07-09 09:23:53.758 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://tousu.sina.com.cn/complaint/view/17384754299/
2025-07-09 09:23:53.759 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://tousu.sina.com.cn/complaint/view/17384754299/
2025-07-09 09:23:53.759 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 保存高质量新闻URL: https://tousu.sina.com.cn/complaint/view/17384754299/
2025-07-09 09:23:53.759 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=老板电器油烟机尺寸虚假宣传争议..., url=https://tousu.sina.com.cn/complaint/view/17384754299/, sentiment=negative
2025-07-09 09:23:53.798 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 2 条文章，ID: 1777
2025-07-09 09:23:53.799 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:968 - 正在保存第 3 条文章: 老板电器抽油烟机消费争议调解撤案
2025-07-09 09:23:53.799 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_validate_and_clean_url:587 - URL验证通过: https://news.qq.com/search.htm?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:23:53.799 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_is_news_url:624 - 识别为新闻URL (域名匹配): https://news.qq.com/search.htm?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:23:53.799 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 保存高质量新闻URL: https://news.qq.com/search.htm?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8
2025-07-09 09:23:53.800 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1011 - 准备保存数据: title=老板电器抽油烟机消费争议调解撤案..., url=https://news.qq.com/search.htm?query=%E8%80%81%E6%9D%BF%E7%94%B5%E5%99%A8, sentiment=neutral
2025-07-09 09:23:53.838 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1017 - 成功保存第 3 条文章，ID: 1778
2025-07-09 09:23:53.914 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1028 - 成功保存 3 条搜索结果到数据库
2025-07-09 09:23:53.914 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.service.external_api_service:perform_online_search:53 - 联网搜索完成，获取到 3 条结果，已保存 3 条到数据库
2025-07-09 09:23:53.915 | 2934a3fc53fd4a669dcb605b96553991 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:766 - 分析执行成功
2025-07-09 09:25:08.304 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 09:25:08.304 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 09:25:11.587 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:25:11.588 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:25:13.819 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:25:13.819 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:25:13.821 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:25:14.550 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:25:15.131 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:25:15.131 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:25:39.829 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 09:25:39.830 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 09:25:43.072 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:25:43.073 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:25:44.877 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:25:44.878 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:25:44.883 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:25:45.488 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:25:46.152 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:25:46.152 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:36:04.493 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 09:36:04.494 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 09:36:07.980 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:36:07.980 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:36:10.126 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:36:10.126 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:36:10.127 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:36:11.002 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:36:11.543 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:36:11.544 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:36:31.963 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 09:36:31.964 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 09:36:35.190 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:36:35.190 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:36:36.801 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:36:36.801 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:36:36.803 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:36:37.333 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:36:37.898 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:36:37.898 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:36:41.115 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:36:41.115 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:36:42.542 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:36:42.543 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:36:42.545 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:36:43.005 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:36:43.840 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:36:43.841 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:37:08.683 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 09:37:08.685 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 09:37:11.885 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:37:11.886 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:37:13.388 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:37:13.389 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:37:13.392 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:37:13.895 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:37:14.620 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:37:14.620 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:37:17.780 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:37:17.780 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:37:18.971 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:37:18.971 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:37:18.973 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:37:19.379 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:37:19.965 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:37:19.966 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:38:23.267 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:724 - 开始分析，需求ID: 91
2025-07-09 09:38:23.268 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:730 - 执行联网搜索...
2025-07-09 09:38:23.268 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 老板电器
2025-07-09 09:39:20.281 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:41:12.218 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:41:12.219 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:243 - AI返回的原始内容: [
  {
    "title": "消费者京东平台换货纠纷",
    "content": "某消费者于2024年6月2日在京东平台购买老板电器油烟机，经历两次换货均失败，换货商品存在玻璃破碎、内部损坏等问题。商家延迟换货流程超过20天，客服推诿未提供有效解决方案，且未主动联系消费者。消费者要求实物交付或退款处理，情绪呈现负面（愤怒/失望）。",
    "source": "京东平台",
...
2025-07-09 09:41:19.386 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:41:19.387 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:797 - 使用本地情感分析结果: negative
2025-07-09 09:41:19.387 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:279 - 使用AI返回的原始URL: 
2025-07-09 09:41:30.648 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:41:30.649 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:797 - 使用本地情感分析结果: negative
2025-07-09 09:41:30.649 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:279 - 使用AI返回的原始URL: https://tousu.sina.com.cn/
2025-07-09 09:41:43.428 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:41:43.429 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:797 - 使用本地情感分析结果: negative
2025-07-09 09:41:43.429 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:279 - 使用AI返回的原始URL: https://www.12315.cn/
2025-07-09 09:41:52.056 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:41:52.059 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:797 - 使用本地情感分析结果: negative
2025-07-09 09:41:52.060 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:279 - 使用AI返回的原始URL: 
2025-07-09 09:41:52.060 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:292 - 成功提取 4 篇文章
2025-07-09 09:41:52.061 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:215 - AI成功提取到 4 篇文章
2025-07-09 09:41:52.061 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:837 - 情感分析统计: {'positive': 0, 'neutral': 0, 'negative': 100} (总文章数: 4)
2025-07-09 09:41:52.062 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:936 - 开始保存搜索结果到数据库
2025-07-09 09:41:52.062 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:937 - 实体关键词: 老板电器
2025-07-09 09:41:52.062 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:938 - 选中关键词: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 09:41:52.062 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:939 - 搜索结果文章数量: 4
2025-07-09 09:41:52.063 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:949 - 合并后的关键词字符串: 老板电器,老板电器 售后服务,老板电器 三包义务,老板电器 客服态度,老板电器 质量,老板电器 燃气灶爆炸
2025-07-09 09:41:52.063 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:953 - 正在保存第 1 条文章: 消费者京东平台换货纠纷
2025-07-09 09:41:52.064 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:961 - 保存AI返回的原始URL: 
2025-07-09 09:41:52.064 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 准备保存数据: title=消费者京东平台换货纠纷..., url=, sentiment=negative
2025-07-09 09:41:52.089 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:987 - 成功保存第 1 条文章，ID: 1779
2025-07-09 09:41:52.089 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:953 - 正在保存第 2 条文章: 油烟机尺寸不符引发连锁损失
2025-07-09 09:41:52.089 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:961 - 保存AI返回的原始URL: https://tousu.sina.com.cn/
2025-07-09 09:41:52.090 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 准备保存数据: title=油烟机尺寸不符引发连锁损失..., url=https://tousu.sina.com.cn/, sentiment=negative
2025-07-09 09:41:52.113 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:987 - 成功保存第 2 条文章，ID: 1780
2025-07-09 09:41:52.114 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:953 - 正在保存第 3 条文章: 杭州12315投诉达成和解案例
2025-07-09 09:41:52.114 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:961 - 保存AI返回的原始URL: https://www.12315.cn/
2025-07-09 09:41:52.114 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 准备保存数据: title=杭州12315投诉达成和解案例..., url=https://www.12315.cn/, sentiment=negative
2025-07-09 09:41:52.136 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:987 - 成功保存第 3 条文章，ID: 1781
2025-07-09 09:41:52.136 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:953 - 正在保存第 4 条文章: 多平台投诉揭示系统性服务缺陷
2025-07-09 09:41:52.136 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:961 - 保存AI返回的原始URL: 
2025-07-09 09:41:52.136 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 准备保存数据: title=多平台投诉揭示系统性服务缺陷..., url=, sentiment=negative
2025-07-09 09:41:52.157 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:987 - 成功保存第 4 条文章，ID: 1782
2025-07-09 09:41:52.198 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:998 - 成功保存 4 条搜索结果到数据库
2025-07-09 09:41:52.199 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.service.external_api_service:perform_online_search:53 - 联网搜索完成，获取到 4 条结果，已保存 4 条到数据库
2025-07-09 09:41:52.199 | a37901034ac94c64a69953bd90dd71f0 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:766 - 分析执行成功
2025-07-09 09:46:24.656 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 09:46:24.657 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 09:46:27.839 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:46:27.840 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:46:29.613 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:46:29.614 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:46:29.617 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:46:30.352 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:46:30.883 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:46:30.884 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:46:35.975 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:724 - 开始分析，需求ID: 91
2025-07-09 09:46:35.975 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:730 - 执行联网搜索...
2025-07-09 09:46:35.975 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 老板电器
2025-07-09 09:47:20.644 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:49:55.608 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:49:55.610 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:243 - AI返回的原始内容: [
  {
    "title": "京东平台老板电器油烟机破损换货纠纷",
    "content": "消费者在京东平台购买老板电器油烟机后，两次收到破损商品。换货流程拖延超过25天，期间平台与商家均未有效解决问题。投诉涉及产品质量缺陷、客服响应迟缓、物流管理失职等多重问题，消费者情绪表现为显著愤怒与失望，相关讨论包含#产品缺陷#和#服务推诿#标签。",
    "source": "京东...
2025-07-09 09:50:04.057 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:50:04.059 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:797 - 使用本地情感分析结果: negative
2025-07-09 09:50:04.060 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:279 - 使用AI返回的原始URL: https://club.jd.com/315658/post/12658382.html
2025-07-09 09:50:15.814 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:50:15.816 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:797 - 使用本地情感分析结果: negative
2025-07-09 09:50:15.816 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:279 - 使用AI返回的原始URL: https://tousu.sina.com.cn/complaint/view/17384754299
2025-07-09 09:50:27.080 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_call_ark_api:144 - 豆包AI API调用成功
2025-07-09 09:50:27.081 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:797 - 使用本地情感分析结果: negative
2025-07-09 09:50:27.082 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:279 - 使用AI返回的原始URL: http://www.12315.cn/cuser/portal/tsjbDetail?uuid=8b42d15d9b7645e98c0c9c8aee3e345b
2025-07-09 09:50:27.082 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:292 - 成功提取 3 篇文章
2025-07-09 09:50:27.082 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:215 - AI成功提取到 3 篇文章
2025-07-09 09:50:27.082 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:837 - 情感分析统计: {'positive': 0, 'neutral': 0, 'negative': 100} (总文章数: 3)
2025-07-09 09:50:27.083 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:936 - 开始保存搜索结果到数据库
2025-07-09 09:50:27.083 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:937 - 实体关键词: 老板电器
2025-07-09 09:50:27.083 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:938 - 选中关键词: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 09:50:27.083 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:939 - 搜索结果文章数量: 3
2025-07-09 09:50:27.083 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:949 - 合并后的关键词字符串: 老板电器,老板电器 售后服务,老板电器 三包义务,老板电器 客服态度,老板电器 质量,老板电器 燃气灶爆炸
2025-07-09 09:50:27.084 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:953 - 正在保存第 1 条文章: 京东平台老板电器油烟机破损换货纠纷
2025-07-09 09:50:27.084 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:961 - 保存AI返回的原始URL: https://club.jd.com/315658/post/12658382.html
2025-07-09 09:50:27.084 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 准备保存数据: title=京东平台老板电器油烟机破损换货纠纷..., url=https://club.jd.com/315658/post/12658382.html, sentiment=negative
2025-07-09 09:50:27.119 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:987 - 成功保存第 1 条文章，ID: 1783
2025-07-09 09:50:27.119 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:953 - 正在保存第 2 条文章: 老板电器油烟机规格不符消费争议
2025-07-09 09:50:27.120 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:961 - 保存AI返回的原始URL: https://tousu.sina.com.cn/complaint/view/17384754299
2025-07-09 09:50:27.120 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 准备保存数据: title=老板电器油烟机规格不符消费争议..., url=https://tousu.sina.com.cn/complaint/view/17384754299, sentiment=negative
2025-07-09 09:50:27.151 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:987 - 成功保存第 2 条文章，ID: 1784
2025-07-09 09:50:27.152 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:953 - 正在保存第 3 条文章: 杭州工商部门公示老板电器消费纠纷案
2025-07-09 09:50:27.152 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:961 - 保存AI返回的原始URL: http://www.12315.cn/cuser/portal/tsjbDetail?uuid=8b42d15d9b7645e98c0c9c8aee3e345b
2025-07-09 09:50:27.153 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:981 - 准备保存数据: title=杭州工商部门公示老板电器消费纠纷案..., url=http://www.12315.cn/cuser/portal/tsjbDetail?uuid=8b42d15d9b7645e98c0c9c8aee3e345b, sentiment=negative
2025-07-09 09:50:27.184 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:987 - 成功保存第 3 条文章，ID: 1785
2025-07-09 09:50:27.244 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:998 - 成功保存 3 条搜索结果到数据库
2025-07-09 09:50:27.244 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.service.external_api_service:perform_online_search:53 - 联网搜索完成，获取到 3 条结果，已保存 3 条到数据库
2025-07-09 09:50:27.245 | 20a6131d596247f289d6176cac5d1538 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:766 - 分析执行成功
2025-07-09 09:51:25.648 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 09:51:25.649 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 09:51:28.899 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:51:28.899 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:51:30.239 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:51:30.239 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:51:30.241 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:51:30.698 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:51:31.280 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:51:31.280 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:55:29.831 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 09:55:29.832 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 09:55:33.778 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 09:55:33.779 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 09:55:35.076 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 09:55:35.077 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 09:55:35.082 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 09:55:35.526 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 09:55:36.245 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 09:55:36.246 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 09:56:15.531 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:724 - 开始分析，需求ID: 91
2025-07-09 09:56:15.531 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:730 - 执行联网搜索...
2025-07-09 09:56:15.531 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 老板电器
2025-07-09 09:56:15.532 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:perform_online_search:40 - 第一阶段：开始模型能力验证
2025-07-09 09:56:15.532 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:88 - 开始验证AI模型的URL提供能力
2025-07-09 09:56:22.687 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_call_ark_api:550 - 豆包AI API调用成功
2025-07-09 09:56:22.688 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:135 - AI能力测试回复: 

https://www.news.cn/2024-05/01/c_1129043202.htm...
2025-07-09 09:56:22.688 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:154 - 发现有效URL: ['https://www.news.cn/2024-05/01/c_1129043202.htm']
2025-07-09 09:56:22.688 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:108 - 模型能力验证完成: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://www.news.cn/2024-05/01/c_1129043202.htm'], 'url_count': 1}
2025-07-09 09:56:22.688 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:perform_online_search:42 - 模型能力验证结果: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://www.news.cn/2024-05/01/c_1129043202.htm'], 'url_count': 1}
2025-07-09 09:56:22.689 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:perform_online_search:45 - 第二阶段：开始正式搜索
2025-07-09 09:56:22.689 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_build_adaptive_search_query:239 - 使用高能力搜索模式，强调URL真实性
2025-07-09 09:57:15.394 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_call_ark_api:550 - 豆包AI API调用成功
2025-07-09 09:58:15.608 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_call_ark_api:550 - 豆包AI API调用成功
2025-07-09 09:58:15.610 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:649 - AI返回的原始内容: [
  {
    "title": "消费者投诉老板电器抽油烟机质量问题达成和解",
    "content": "消费者张女士于2025年6月19日通过实体渠道购买老板电器抽油烟机后提出产品质量异议，主张可能存在其他投诉问题并要求退赔费用。经杭州市临平区市场监督管理局调解，双方于6月24日达成和解协议。事件涉及三包义务具体执行争议，投诉处理周期为5个工作日，最终以和解撤诉告终。",
    ...
2025-07-09 09:58:23.825 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_call_ark_api:550 - 豆包AI API调用成功
2025-07-09 09:58:23.827 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1203 - 使用本地情感分析结果: negative
2025-07-09 09:58:23.827 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:685 - 使用AI返回的原始URL: 
2025-07-09 09:58:23.828 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:698 - 成功提取 1 篇文章
2025-07-09 09:58:23.828 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:621 - AI成功提取到 1 篇文章
2025-07-09 09:58:23.829 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:1243 - 情感分析统计: {'positive': 0, 'neutral': 0, 'negative': 100} (总文章数: 1)
2025-07-09 09:58:23.829 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_evaluate_search_quality:421 - 搜索质量评估完成: 总分 37.98, 等级 poor
2025-07-09 09:58:23.830 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:perform_online_search:58 - 搜索结果质量评估: {'overall_quality': 'poor', 'overall_score': 37.98, 'url_quality': {'score': 0.0, 'stats': {'total': 1, 'with_url': 0, 'valid_format': 0, 'likely_real': 0}}, 'content_quality': {'score': 63.3, 'stats': {'avg_title_length': 21.0, 'avg_content_length': 133.0, 'with_source': 1, 'with_time': 1}}, 'recommendations': ['模型具备URL能力但质量不佳，建议优化提示词']}
2025-07-09 09:58:23.830 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1342 - 开始保存搜索结果到数据库
2025-07-09 09:58:23.831 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 实体关键词: 老板电器
2025-07-09 09:58:23.831 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1344 - 选中关键词: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 09:58:23.832 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1345 - 搜索结果文章数量: 1
2025-07-09 09:58:23.832 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1355 - 合并后的关键词字符串: 老板电器,老板电器 售后服务,老板电器 三包义务,老板电器 客服态度,老板电器 质量,老板电器 燃气灶爆炸
2025-07-09 09:58:23.833 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1359 - 正在保存第 1 条文章: 消费者投诉老板电器抽油烟机质量问题达成和解
2025-07-09 09:58:23.834 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1367 - 保存AI返回的原始URL: 
2025-07-09 09:58:23.834 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1387 - 准备保存数据: title=消费者投诉老板电器抽油烟机质量问题达成和解..., url=, sentiment=negative
2025-07-09 09:58:23.861 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1393 - 成功保存第 1 条文章，ID: 1786
2025-07-09 09:58:23.905 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1404 - 成功保存 1 条搜索结果到数据库
2025-07-09 09:58:23.905 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.service.external_api_service:perform_online_search:65 - 联网搜索完成，获取到 1 条结果，已保存 1 条到数据库
2025-07-09 09:58:23.905 | 99bad6fa3b1c416b8e8c045db4c88a9e | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:766 - 分析执行成功
2025-07-09 10:01:41.269 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 10:01:41.269 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 10:01:44.671 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 10:01:44.671 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 10:01:46.441 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 10:01:46.442 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 10:01:46.446 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 10:01:47.143 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 10:01:47.793 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 10:01:47.795 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 10:01:50.486 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:724 - 开始分析，需求ID: 91
2025-07-09 10:01:50.486 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:730 - 执行联网搜索...
2025-07-09 10:01:50.487 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 老板电器
2025-07-09 10:01:50.487 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:perform_online_search:40 - 第一阶段：开始模型能力验证
2025-07-09 10:01:50.487 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:88 - 开始验证AI模型的URL提供能力
2025-07-09 10:02:00.485 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_call_ark_api:550 - 豆包AI API调用成功
2025-07-09 10:02:00.488 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:135 - AI能力测试回复: 

https://global.chinadaily.com.cn/a/202406/12/WS66696081a3109510f59e4c4c.html...
2025-07-09 10:02:00.489 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:154 - 发现有效URL: ['https://global.chinadaily.com.cn/a/202406/12/WS66696081a3109510f59e4c4c.html']
2025-07-09 10:02:00.489 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:108 - 模型能力验证完成: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://global.chinadaily.com.cn/a/202406/12/WS66696081a3109510f59e4c4c.html'], 'url_count': 1}
2025-07-09 10:02:00.489 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:perform_online_search:42 - 模型能力验证结果: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://global.chinadaily.com.cn/a/202406/12/WS66696081a3109510f59e4c4c.html'], 'url_count': 1}
2025-07-09 10:02:00.490 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:perform_online_search:45 - 第二阶段：开始正式搜索
2025-07-09 10:02:00.490 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_build_adaptive_search_query:239 - 使用高能力搜索模式，强调URL真实性
2025-07-09 10:03:02.902 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_call_ark_api:550 - 豆包AI API调用成功
2025-07-09 10:04:04.148 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_call_ark_api:550 - 豆包AI API调用成功
2025-07-09 10:04:04.149 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:649 - AI返回的原始内容: [
  {
    "title": "消费者投诉老板电器售后及费用争议",
    "content": "2025年6月19日，消费者张某通过全国12315平台投诉杭州老板电器股份有限公司，反映其线下购买的抽油烟机存在未明示问题，要求退赔费用。6月24日临平区市监局公示该投诉以“双方和解”结案。事件反映出售后流程中潜在的服务透明度争议，社交平台讨论中部分用户担忧“和解”掩盖产品质量或服务疏漏，...
2025-07-09 10:04:09.967 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_call_ark_api:550 - 豆包AI API调用成功
2025-07-09 10:04:09.968 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1203 - 使用本地情感分析结果: negative
2025-07-09 10:04:09.968 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:685 - 使用AI返回的原始URL: https://baijiahao.baidu.com/s?id=1835804220647189120
2025-07-09 10:04:20.512 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_call_ark_api:550 - 豆包AI API调用成功
2025-07-09 10:04:20.512 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1203 - 使用本地情感分析结果: negative
2025-07-09 10:04:20.513 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:685 - 使用AI返回的原始URL: https://tousu.sina.com.cn/
2025-07-09 10:04:22.971 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_call_ark_api:550 - 豆包AI API调用成功
2025-07-09 10:04:22.972 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1203 - 使用本地情感分析结果: neutral
2025-07-09 10:04:22.973 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:685 - 使用AI返回的原始URL: 
2025-07-09 10:04:22.974 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:698 - 成功提取 3 篇文章
2025-07-09 10:04:22.975 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:621 - AI成功提取到 3 篇文章
2025-07-09 10:04:22.975 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:1243 - 情感分析统计: {'positive': 0, 'neutral': 33, 'negative': 67} (总文章数: 3)
2025-07-09 10:04:22.975 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_evaluate_search_quality:421 - 搜索质量评估完成: 总分 50.91, 等级 fair
2025-07-09 10:04:22.976 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:perform_online_search:58 - 搜索结果质量评估: {'overall_quality': 'fair', 'overall_score': 50.91, 'url_quality': {'score': 33.33, 'stats': {'total': 3, 'with_url': 2, 'valid_format': 2, 'likely_real': 1}}, 'content_quality': {'score': 62.63, 'stats': {'avg_title_length': 15.333333333333334, 'avg_content_length': 126.33333333333333, 'with_source': 3, 'with_time': 3}}, 'recommendations': ['模型具备URL能力但质量不佳，建议优化提示词']}
2025-07-09 10:04:22.976 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1342 - 开始保存搜索结果到数据库
2025-07-09 10:04:22.976 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 实体关键词: 老板电器
2025-07-09 10:04:22.976 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1344 - 选中关键词: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 10:04:22.976 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1345 - 搜索结果文章数量: 3
2025-07-09 10:04:22.977 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1355 - 合并后的关键词字符串: 老板电器,老板电器 售后服务,老板电器 三包义务,老板电器 客服态度,老板电器 质量,老板电器 燃气灶爆炸
2025-07-09 10:04:22.977 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1359 - 正在保存第 1 条文章: 消费者投诉老板电器售后及费用争议
2025-07-09 10:04:22.977 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1367 - 保存AI返回的原始URL: https://baijiahao.baidu.com/s?id=1835804220647189120
2025-07-09 10:04:22.978 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1387 - 准备保存数据: title=消费者投诉老板电器售后及费用争议..., url=https://baijiahao.baidu.com/s?id=1835804220647189120, sentiment=negative
2025-07-09 10:04:23.013 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1393 - 成功保存第 1 条文章，ID: 1787
2025-07-09 10:04:23.014 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1359 - 正在保存第 2 条文章: 电商平台家电翻新产业链争议引关注
2025-07-09 10:04:23.014 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1367 - 保存AI返回的原始URL: https://tousu.sina.com.cn/
2025-07-09 10:04:23.014 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1387 - 准备保存数据: title=电商平台家电翻新产业链争议引关注..., url=https://tousu.sina.com.cn/, sentiment=negative
2025-07-09 10:04:23.046 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1393 - 成功保存第 2 条文章，ID: 1788
2025-07-09 10:04:23.046 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1359 - 正在保存第 3 条文章: 老板电器核心产品市场份额下滑
2025-07-09 10:04:23.047 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1367 - 保存AI返回的原始URL: 
2025-07-09 10:04:23.047 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1387 - 准备保存数据: title=老板电器核心产品市场份额下滑..., url=, sentiment=neutral
2025-07-09 10:04:23.078 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1393 - 成功保存第 3 条文章，ID: 1789
2025-07-09 10:04:23.138 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1404 - 成功保存 3 条搜索结果到数据库
2025-07-09 10:04:23.138 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.service.external_api_service:perform_online_search:65 - 联网搜索完成，获取到 3 条结果，已保存 3 条到数据库
2025-07-09 10:04:23.139 | 44ca8a29aa66484b8e4e2d32a75478cd | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:766 - 分析执行成功
2025-07-09 10:08:17.104 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 10:08:17.105 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 10:08:20.319 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 10:08:20.320 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 10:08:23.487 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 10:08:23.488 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 10:08:23.491 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 10:08:24.170 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 10:08:24.696 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 10:08:24.696 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 10:08:28.102 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 10:08:28.102 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 10:08:29.695 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 10:08:29.695 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 10:08:29.698 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 10:08:30.259 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 10:08:31.118 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 10:08:31.119 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 10:08:48.385 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 10:08:48.386 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 10:08:51.817 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 10:08:51.818 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 10:08:53.239 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 10:08:53.239 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 10:08:53.241 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 10:08:53.703 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 10:08:54.373 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 10:08:54.374 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 10:08:57.563 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 10:08:57.563 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 10:08:59.755 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 10:08:59.755 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 10:08:59.757 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 10:09:00.484 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 10:09:01.240 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 10:09:01.240 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 10:11:15.029 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 10:11:15.030 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 10:11:18.908 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 10:11:18.908 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 10:11:21.049 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 10:11:21.049 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 10:11:21.051 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 10:11:21.742 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 10:11:22.335 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 10:11:22.336 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 10:12:16.534 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:724 - 开始分析，需求ID: 91
2025-07-09 10:12:16.534 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:730 - 执行联网搜索...
2025-07-09 10:12:16.534 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 老板电器
2025-07-09 10:12:16.534 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:perform_online_search:40 - 第一阶段：开始模型能力验证
2025-07-09 10:12:16.535 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:88 - 开始验证AI模型的URL提供能力
2025-07-09 10:12:23.821 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 10:12:23.823 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:135 - AI能力测试回复: 

https://news.cctv.com/2024/06/04/ARTIu4tHzJxGD9c2pGt0HZME240604.shtml...
2025-07-09 10:12:23.824 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:146 - AI返回了URL: ['https://news.cctv.com/2024/06/04/ARTIu4tHzJxGD9c2pGt0HZME240604.shtml']
2025-07-09 10:12:23.824 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:108 - 模型能力验证完成: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://news.cctv.com/2024/06/04/ARTIu4tHzJxGD9c2pGt0HZME240604.shtml'], 'url_count': 1}
2025-07-09 10:12:23.825 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:perform_online_search:42 - 模型能力验证结果: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://news.cctv.com/2024/06/04/ARTIu4tHzJxGD9c2pGt0HZME240604.shtml'], 'url_count': 1}
2025-07-09 10:12:23.826 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:perform_online_search:45 - 第二阶段：开始正式搜索
2025-07-09 10:12:23.827 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_build_adaptive_search_query:232 - 使用高能力搜索模式，强调URL真实性
2025-07-09 10:13:04.470 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 10:14:03.465 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 10:14:03.466 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:631 - AI返回的原始内容: [
  {
    "title": "消费者投诉老板电器京东商品破损换货拖延",
    "content": "2025年7月消费者在京东平台购买老板牌油烟机，连续两次收到破损商品，换货流程拖延超一个月未解决。消费者多次联系客服未获有效跟进，最终导致产品闲置未安装。该案例反映平台与商家责任推诿问题。",
    "source": "百度百家号",
    "url": "https://ba...
2025-07-09 10:14:14.690 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 10:14:14.692 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: neutral
2025-07-09 10:14:14.692 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: https://baijiahao.baidu.com/s?id=1835804220647189120&wfr=spider&for=pc
2025-07-09 10:14:17.909 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 10:14:17.910 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: neutral
2025-07-09 10:14:17.910 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: https://tousu.sina.com.cn/complaint/view/17362847345/
2025-07-09 10:14:32.073 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 10:14:32.074 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: negative
2025-07-09 10:14:32.074 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: http://www.hangzhou.gov.cn/art/2025/6/15/art_1229498763_58901234.html
2025-07-09 10:14:38.856 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 10:14:38.857 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: neutral
2025-07-09 10:14:38.857 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: https://weibo.com/2315236452/NzT9WvnBx
2025-07-09 10:14:38.857 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:680 - 成功提取 4 篇文章
2025-07-09 10:14:38.857 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:603 - AI成功提取到 4 篇文章
2025-07-09 10:14:38.857 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:1227 - 情感分析统计: {'positive': 0, 'neutral': 75, 'negative': 25} (总文章数: 4)
2025-07-09 10:14:38.857 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_evaluate_search_quality:402 - 搜索质量评估完成: 总分 58.67, 等级 fair
2025-07-09 10:14:38.858 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:perform_online_search:58 - 搜索结果质量评估: {'overall_quality': 'fair', 'overall_score': 58.67, 'url_coverage': {'score': 100.0, 'stats': {'total': 4, 'with_url': 4}}, 'content_quality': {'score': 58.67, 'stats': {'avg_title_length': 18.5, 'avg_content_length': 86.75, 'with_source': 4, 'with_time': 4}}, 'recommendations': ['内容质量需要改进，建议调整搜索策略']}
2025-07-09 10:14:38.858 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1326 - 开始保存搜索结果到数据库
2025-07-09 10:14:38.858 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1327 - 实体关键词: 老板电器
2025-07-09 10:14:38.858 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1328 - 选中关键词: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 10:14:38.859 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1329 - 搜索结果文章数量: 4
2025-07-09 10:14:38.859 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1339 - 合并后的关键词字符串: 老板电器,老板电器 售后服务,老板电器 三包义务,老板电器 客服态度,老板电器 质量,老板电器 燃气灶爆炸
2025-07-09 10:14:38.859 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 1 条文章: 消费者投诉老板电器京东商品破损换货拖延
2025-07-09 10:14:38.859 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: https://baijiahao.baidu.com/s?id=1835804220647189120&wfr=spider&for=pc
2025-07-09 10:14:38.860 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=消费者投诉老板电器京东商品破损换货拖延..., url=https://baijiahao.baidu.com/s?id=1835804220647189120&wfr=spider&for=pc, sentiment=neutral
2025-07-09 10:14:38.901 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 1 条文章，ID: 1790
2025-07-09 10:14:38.902 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 2 条文章: 老板电器专卖店商品尺寸不符引赔偿纠纷
2025-07-09 10:14:38.902 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: https://tousu.sina.com.cn/complaint/view/17362847345/
2025-07-09 10:14:38.903 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=老板电器专卖店商品尺寸不符引赔偿纠纷..., url=https://tousu.sina.com.cn/complaint/view/17362847345/, sentiment=neutral
2025-07-09 10:14:38.942 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 2 条文章，ID: 1791
2025-07-09 10:14:38.943 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 3 条文章: 杭州监管部门调解老板电器油烟机质量投诉
2025-07-09 10:14:38.943 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: http://www.hangzhou.gov.cn/art/2025/6/15/art_1229498763_58901234.html
2025-07-09 10:14:38.944 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=杭州监管部门调解老板电器油烟机质量投诉..., url=http://www.hangzhou.gov.cn/art/2025/6/15/art_1229498763_58901234.html, sentiment=negative
2025-07-09 10:14:38.982 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 3 条文章，ID: 1792
2025-07-09 10:14:38.982 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 4 条文章: 老板电器售后服务问题引发社交媒体热议
2025-07-09 10:14:38.983 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: https://weibo.com/2315236452/NzT9WvnBx
2025-07-09 10:14:38.983 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=老板电器售后服务问题引发社交媒体热议..., url=https://weibo.com/2315236452/NzT9WvnBx, sentiment=neutral
2025-07-09 10:14:39.022 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 4 条文章，ID: 1793
2025-07-09 10:14:39.101 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1388 - 成功保存 4 条搜索结果到数据库
2025-07-09 10:14:39.101 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.service.external_api_service:perform_online_search:65 - 联网搜索完成，获取到 4 条结果，已保存 4 条到数据库
2025-07-09 10:14:39.101 | 95fb03c65cdf44c089938251fe2d142b | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:766 - 分析执行成功
2025-07-09 10:26:36.365 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 10:26:36.367 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 10:26:40.456 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 10:26:40.457 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 10:26:42.143 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 10:26:42.143 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 10:26:42.144 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 10:26:42.870 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 10:26:43.565 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 10:26:43.565 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 10:31:47.807 | 9371bbac38ce49b19e38a49139962cf8 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-07-09 10:31:50.837 | d4a95c00b7904cd6a5f1b60316dbe30d | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-07-09 10:31:51.587 | d8ffe3fd329e4da88bdea0f8fa28a157 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为615dc558-add7-4723-8213-c5976b8e5009的会话获取图片验证码成功
2025-07-09 10:31:55.404 | 35495ac4ef924f59927075170f613d88 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-09 10:31:55.672 | 7db5f30b6fc646f7a1520b3d1675a47a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 10:31:56.229 | 6abd6728907646888579fd3e54d90a1c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 10:31:57.438 | 8672b6e0f69e43cc8939727ccab1c60e | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 10:31:57.951 | e0d0eda3b9d24d0d94d82038bac2904a | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:366 - 获取舆情任务列表成功
2025-07-09 10:32:19.578 | 7e9aba0765ac413b91000b4707274ab3 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:110 - 创建舆情需求失败: 
2025-07-09 10:32:24.602 | 9803749301024de1aee2ef2ec3101c57 | INFO     | module_opinion.controller.opinion_analysis_controller:create_public_requirement:107 - 创建舆情需求成功
2025-07-09 10:32:29.985 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:747 - 开始分析，需求ID: 92
2025-07-09 10:32:29.985 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:753 - 执行联网搜索...
2025-07-09 10:32:29.986 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 老板电器
2025-07-09 10:32:29.986 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:perform_online_search:40 - 第一阶段：开始模型能力验证
2025-07-09 10:32:29.986 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:88 - 开始验证AI模型的URL提供能力
2025-07-09 10:32:43.601 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 10:32:43.603 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:135 - AI能力测试回复: 

https://www.chinanews.com.cn/gn/2024/06-10/10205223.shtml...
2025-07-09 10:32:43.604 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:146 - AI返回了URL: ['https://www.chinanews.com.cn/gn/2024/06-10/10205223.shtml']
2025-07-09 10:32:43.605 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:108 - 模型能力验证完成: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://www.chinanews.com.cn/gn/2024/06-10/10205223.shtml'], 'url_count': 1}
2025-07-09 10:32:43.605 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:perform_online_search:42 - 模型能力验证结果: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://www.chinanews.com.cn/gn/2024/06-10/10205223.shtml'], 'url_count': 1}
2025-07-09 10:32:43.605 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:perform_online_search:45 - 第二阶段：开始正式搜索
2025-07-09 10:32:43.607 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_build_adaptive_search_query:232 - 使用高能力搜索模式，强调URL真实性
2025-07-09 10:33:31.560 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 10:34:25.768 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 10:34:25.770 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:631 - AI返回的原始内容: [
  {
    "title": "消费者投诉老板电器油烟机尺寸不符及售后推诿",
    "content": "消费者在黑猫投诉平台反映，购买的老板电器油烟机实际尺寸（551mm）与商家承诺（471mm）严重不符。多次沟通后仅获100京豆补偿，京东专员态度强硬且未主动解决问题，导致消费者额外损失140元安装费。",
    "source": "黑猫投诉平台",
    "url": "h...
2025-07-09 10:34:29.410 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 10:34:29.412 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: negative
2025-07-09 10:34:29.412 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: https://tousu.sina.com.cn/complaint/view/17384754299/
2025-07-09 10:34:32.880 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 10:34:32.881 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: negative
2025-07-09 10:34:32.881 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: https://jubao.jd.com/claim/detail?id=CB202507060001
2025-07-09 10:34:36.943 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 10:34:36.945 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: negative
2025-07-09 10:34:36.946 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: https://baijiahao.baidu.com/s?id=1835804220647189120
2025-07-09 10:34:36.946 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:680 - 成功提取 3 篇文章
2025-07-09 10:34:36.946 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:603 - AI成功提取到 3 篇文章
2025-07-09 10:34:36.947 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:1227 - 情感分析统计: {'positive': 0, 'neutral': 0, 'negative': 100} (总文章数: 3)
2025-07-09 10:34:36.947 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_evaluate_search_quality:402 - 搜索质量评估完成: 总分 58.63, 等级 fair
2025-07-09 10:34:36.947 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:perform_online_search:58 - 搜索结果质量评估: {'overall_quality': 'fair', 'overall_score': 58.63, 'url_coverage': {'score': 100.0, 'stats': {'total': 3, 'with_url': 3}}, 'content_quality': {'score': 58.63, 'stats': {'avg_title_length': 22.333333333333332, 'avg_content_length': 86.33333333333333, 'with_source': 3, 'with_time': 3}}, 'recommendations': ['内容质量需要改进，建议调整搜索策略']}
2025-07-09 10:34:36.948 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1326 - 开始保存搜索结果到数据库
2025-07-09 10:34:36.948 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1327 - 实体关键词: 老板电器
2025-07-09 10:34:36.948 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1328 - 选中关键词: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 10:34:36.949 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1329 - 搜索结果文章数量: 3
2025-07-09 10:34:36.949 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1339 - 合并后的关键词字符串: 老板电器,老板电器 售后服务,老板电器 三包义务,老板电器 客服态度,老板电器 质量,老板电器 燃气灶爆炸
2025-07-09 10:34:36.950 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 1 条文章: 消费者投诉老板电器油烟机尺寸不符及售后推诿
2025-07-09 10:34:36.950 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: https://tousu.sina.com.cn/complaint/view/17384754299/
2025-07-09 10:34:36.951 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=消费者投诉老板电器油烟机尺寸不符及售后推诿..., url=https://tousu.sina.com.cn/complaint/view/17384754299/, sentiment=negative
2025-07-09 10:34:36.984 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 1 条文章，ID: 1794
2025-07-09 10:34:36.984 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 2 条文章: 老板电器被曝连续两次发货残次品 售后拖延超一个月
2025-07-09 10:34:36.985 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: https://jubao.jd.com/claim/detail?id=CB202507060001
2025-07-09 10:34:36.985 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=老板电器被曝连续两次发货残次品 售后拖延超一个月..., url=https://jubao.jd.com/claim/detail?id=CB202507060001, sentiment=negative
2025-07-09 10:34:37.017 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 2 条文章，ID: 1795
2025-07-09 10:34:37.018 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 3 条文章: 杭州临平市监局公示老板电器未明示问题退赔案件
2025-07-09 10:34:37.018 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: https://baijiahao.baidu.com/s?id=1835804220647189120
2025-07-09 10:34:37.018 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=杭州临平市监局公示老板电器未明示问题退赔案件..., url=https://baijiahao.baidu.com/s?id=1835804220647189120, sentiment=negative
2025-07-09 10:34:37.049 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 3 条文章，ID: 1796
2025-07-09 10:34:37.108 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1388 - 成功保存 3 条搜索结果到数据库
2025-07-09 10:34:37.108 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.service.external_api_service:perform_online_search:65 - 联网搜索完成，获取到 3 条结果，已保存 3 条到数据库
2025-07-09 10:34:37.109 | eb5ce4643a334f8cb8b4a6091feb0b63 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:789 - 分析执行成功
2025-07-09 10:41:04.340 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 10:41:04.342 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 10:41:08.362 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 10:41:08.363 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 10:41:09.938 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 10:41:09.939 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 10:41:09.942 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 10:41:10.559 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 10:41:11.366 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 10:41:11.367 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 10:42:50.460 | 6cc87335bbee4bc49499ca64dd31c00a | INFO     | module_opinion.controller.opinion_analysis_controller:check_task_exists:397 - 检查任务是否存在: {'exists': False}
2025-07-09 10:42:50.885 | af2858cba11c416db078af6fb11e08be | INFO     | module_opinion.controller.opinion_analysis_controller:create_task:437 - 创建舆情任务成功
2025-07-09 10:42:51.213 | 92491fe95a7f4247b80f08bb386d4c13 | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:813 - 开始推送报告到: http://baidu.com
2025-07-09 10:42:51.213 | 92491fe95a7f4247b80f08bb386d4c13 | INFO     | module_opinion.service.push_report_service:push_report:43 - 开始推送报告到: http://baidu.com
2025-07-09 10:42:51.213 | 92491fe95a7f4247b80f08bb386d4c13 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:69 - 生成HTML - report_data keys: ['totalArticles', 'totalKeywords', 'dataSources', 'sentiment', 'onlineSearchCount', 'customSourceCounts', 'requirementName', 'entityKeyword', 'specificRequirement', 'selectedKeywords', 'reportPageUrl', 'pushTime']
2025-07-09 10:42:51.214 | 92491fe95a7f4247b80f08bb386d4c13 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:70 - 生成HTML - analysis_results keys: ['requirement_id', 'analysis_results', 'status', 'message']
2025-07-09 10:42:51.214 | 92491fe95a7f4247b80f08bb386d4c13 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:71 - 生成HTML - selectedKeywords: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 10:42:51.214 | 92491fe95a7f4247b80f08bb386d4c13 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:96 - 生成HTML - 提取到 3 篇文章
2025-07-09 10:42:51.214 | 92491fe95a7f4247b80f08bb386d4c13 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:100 - 生成HTML - 提取到 5 个关键词: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 10:42:51.216 | 92491fe95a7f4247b80f08bb386d4c13 | INFO     | module_opinion.service.report_html_generator_service:generate_report_html:50 - 报告HTML页面生成成功: report_53c773765374_1752028971
2025-07-09 10:42:51.216 | 92491fe95a7f4247b80f08bb386d4c13 | INFO     | module_opinion.service.push_report_service:push_report:61 - 报告页面生成成功: http://localhost:9099/dev-api/public/report/view/report_53c773765374_1752028971
2025-07-09 10:42:52.158 | 92491fe95a7f4247b80f08bb386d4c13 | INFO     | module_opinion.service.push_report_service:_log_push_result:442 - 推送成功: {"push_id": "push_1752028971213", "target_url": "http://baidu.com", "success": true, "response_status": 200, "response_data": {"text": "<html>\n<meta http-equiv=\"refresh\" content=\"0;url=http://www.baidu.com/\">\n</html>\n"}, "error_message": null, "push_time": "2025-07-09T10:42:51.213624", "user_id": null}
2025-07-09 10:42:52.158 | 92491fe95a7f4247b80f08bb386d4c13 | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:823 - 报告推送成功，推送ID: push_1752028971213
2025-07-09 10:43:06.038 | cd5520bf787b41f7b1e8801b4ae58da1 | INFO     | module_opinion.controller.opinion_analysis_controller:check_task_exists:397 - 检查任务是否存在: {'exists': True, 'task_id': 31, 'task_name': '12332 - 立即推送', 'create_time': '2025-07-09T10:42:51', 'status': 'pending'}
2025-07-09 10:43:06.414 | b51454736cd44b2799a1e7d5fdd8b4de | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:813 - 开始推送报告到: http://baidu.com
2025-07-09 10:43:06.415 | b51454736cd44b2799a1e7d5fdd8b4de | INFO     | module_opinion.service.push_report_service:push_report:43 - 开始推送报告到: http://baidu.com
2025-07-09 10:43:06.415 | b51454736cd44b2799a1e7d5fdd8b4de | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:69 - 生成HTML - report_data keys: ['totalArticles', 'totalKeywords', 'dataSources', 'sentiment', 'onlineSearchCount', 'customSourceCounts', 'requirementName', 'entityKeyword', 'specificRequirement', 'selectedKeywords', 'reportPageUrl', 'pushTime']
2025-07-09 10:43:06.416 | b51454736cd44b2799a1e7d5fdd8b4de | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:70 - 生成HTML - analysis_results keys: ['requirement_id', 'analysis_results', 'status', 'message']
2025-07-09 10:43:06.416 | b51454736cd44b2799a1e7d5fdd8b4de | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:71 - 生成HTML - selectedKeywords: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 10:43:06.416 | b51454736cd44b2799a1e7d5fdd8b4de | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:96 - 生成HTML - 提取到 3 篇文章
2025-07-09 10:43:06.416 | b51454736cd44b2799a1e7d5fdd8b4de | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:100 - 生成HTML - 提取到 5 个关键词: ['老板电器 售后服务', '老板电器 三包义务', '老板电器 客服态度', '老板电器 质量', '老板电器 燃气灶爆炸']
2025-07-09 10:43:06.418 | b51454736cd44b2799a1e7d5fdd8b4de | INFO     | module_opinion.service.report_html_generator_service:generate_report_html:50 - 报告HTML页面生成成功: report_ba2dd94b4b23_1752028986
2025-07-09 10:43:06.418 | b51454736cd44b2799a1e7d5fdd8b4de | INFO     | module_opinion.service.push_report_service:push_report:61 - 报告页面生成成功: http://localhost:9099/dev-api/public/report/view/report_ba2dd94b4b23_1752028986
2025-07-09 10:43:07.245 | b51454736cd44b2799a1e7d5fdd8b4de | INFO     | module_opinion.service.push_report_service:_log_push_result:442 - 推送成功: {"push_id": "push_1752028986415", "target_url": "http://baidu.com", "success": true, "response_status": 200, "response_data": {"text": "<html>\n<meta http-equiv=\"refresh\" content=\"0;url=http://www.baidu.com/\">\n</html>\n"}, "error_message": null, "push_time": "2025-07-09T10:43:06.415451", "user_id": null}
2025-07-09 10:43:07.245 | b51454736cd44b2799a1e7d5fdd8b4de | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:823 - 报告推送成功，推送ID: push_1752028986415
2025-07-09 11:11:50.160 | ba0aae0832ac4b459dfafa5d5eeea66b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 11:11:50.372 | 21421528862e499ca27f6715a5d52cf2 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 11:11:50.878 | 0e06fc315b224dc5a730eca63d3da78f | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:366 - 获取舆情任务列表成功
2025-07-09 11:11:51.006 | 783d16d8172b4680b25b287ad9deebbb | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 11:12:18.212 | 9350cbe7d54a42eb84ec6655df857ca6 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:110 - 创建舆情需求失败: 
2025-07-09 11:13:06.147 | d058269f0f234be9876c1e94f08795c3 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:110 - 创建舆情需求失败: 
2025-07-09 11:17:10.829 | fc0261d21c26499ca58bace5d76a9281 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:110 - 创建舆情需求失败: 
2025-07-09 11:21:28.814 | 1a3493211085454cbbc9ef66fb6d0705 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:110 - 创建舆情需求失败: 
2025-07-09 11:22:14.086 | 878e9e6fc4d14cdbbf85c4ab016eecd7 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:110 - 创建舆情需求失败: 
2025-07-09 11:22:18.509 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 11:22:18.510 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 11:22:26.173 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 11:22:26.174 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 11:22:27.641 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 11:22:27.641 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 11:22:27.643 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 11:22:28.248 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 11:22:28.782 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 11:22:28.782 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 11:22:32.708 | 936a169d80614e7e8c6811102a2bdf34 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 11:22:32.907 | ebf3702ef08e41a9a733b6a4c6e4df3a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 11:22:33.480 | 602d834fd0b844c9b0f78891af633ed4 | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 11:22:33.943 | 561603bb58134bfd8ea92a909a29847b | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:366 - 获取舆情任务列表成功
2025-07-09 11:22:37.061 | e21232c365ff4435b457bd9bfffb9619 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:110 - 创建舆情需求失败: 
2025-07-09 11:23:00.725 | 1c3a8ee159fc490abd289af3b0135498 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:110 - 创建舆情需求失败: 
2025-07-09 11:23:22.157 | defb6b15bade4525b1f4e9e0066d74ee | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:110 - 创建舆情需求失败: 
2025-07-09 11:30:12.177 | 747559e57ae8476e8526e86030b8a0b0 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:110 - 创建舆情需求失败: 
2025-07-09 11:30:22.209 | 3b9cf89340c6445b9fa1abc9a1b1caf0 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:110 - 创建舆情需求失败: 
2025-07-09 11:30:36.045 | fdf6dfbc551040a0adeffee9907f42bc | INFO     | module_opinion.controller.opinion_analysis_controller:create_public_requirement:107 - 创建舆情需求成功
2025-07-09 11:30:39.648 | c365c1c1ba064dde80bfece12160c6f2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 11:30:39.895 | f3645cb5dd5f441dadc6881bdc4de1f2 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 11:30:40.530 | 6074b62fea53440b9c59f41a1b6c2f6f | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 11:30:40.833 | a2bd5242d4664897b41eddc0ce4d10a9 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:366 - 获取舆情任务列表成功
2025-07-09 11:30:54.821 | 50254459dbb24bf7b1b521fed79252b9 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:110 - 创建舆情需求失败: 
2025-07-09 11:41:08.586 | b384077cb7bf42a19dd71d0485a28350 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:110 - 创建舆情需求失败: 
2025-07-09 12:59:12.700 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 12:59:12.702 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 12:59:17.413 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 12:59:17.413 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 13:17:22.023 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 13:17:22.024 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 13:17:23.524 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 13:17:23.524 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 13:17:23.526 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 13:17:24.179 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 13:17:24.830 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 13:17:24.830 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 13:18:58.741 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 13:18:58.742 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 13:19:01.882 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 13:19:01.882 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 13:19:03.482 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 13:19:03.484 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 13:19:03.488 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 13:19:04.018 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 13:19:04.816 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 13:19:04.816 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 13:22:36.023 | 8cdb4fd6e7414e2799ebc68dff905a3c | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-07-09 13:22:36.087 | 570b4f2eb4b746e6b4fc83143651b16a | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-07-09 13:22:36.148 | 3455a83073cc4e61875dfb57a0d89e9f | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为457a26d1-e064-4e12-ba66-ac651d4a7820的会话获取图片验证码成功
2025-07-09 13:24:03.102 | 73f6d296f7f94180bff07c0a166fb6b4 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-09 13:24:03.346 | 27ba7e1d266e41b5a09001110a74fc9e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 13:24:03.865 | 1aaefb11bbdc4f13983a0e288b0299e8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 13:24:04.266 | 75ac7269786142f1a0eb843ae17c4cbe | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 13:24:04.751 | 14594ce0e09841a9ae9989df22631cbb | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:374 - 获取舆情任务列表成功
2025-07-09 13:24:26.404 | cb9e198798464ea38ffc9c73e87bfbb0 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:111 - 创建舆情需求失败: 需求名称"1555"已存在
2025-07-09 13:24:26.405 | cb9e198798464ea38ffc9c73e87bfbb0 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:112 - 异常类型: ServiceException
2025-07-09 13:24:26.405 | cb9e198798464ea38ffc9c73e87bfbb0 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:113 - 异常详情: ServiceException('需求名称"1555"已存在')
2025-07-09 13:24:26.406 | cb9e198798464ea38ffc9c73e87bfbb0 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:115 - 异常消息: 需求名称"1555"已存在
2025-07-09 13:24:26.811 | b75905a69fd3415bbbd9283b905a6bcf | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_requirement_list:68 - 获取舆情需求列表成功
2025-07-09 13:24:41.424 | 1b6142d27b604b0e99cd2c90d5f2d8a4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 13:24:41.637 | 7475a48cbab34513b9b05c0e0c7fc373 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 13:24:42.218 | 104d2839783d47e8ab3b2c125eff776e | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 13:24:42.580 | 93d793dead16402f856a91c47b720d78 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:374 - 获取舆情任务列表成功
2025-07-09 13:25:15.179 | 7d5c9da9f61143098329ff0af7ceabd8 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:111 - 创建舆情需求失败: 需求名称"1555"已存在
2025-07-09 13:25:15.180 | 7d5c9da9f61143098329ff0af7ceabd8 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:112 - 异常类型: ServiceException
2025-07-09 13:25:15.180 | 7d5c9da9f61143098329ff0af7ceabd8 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:113 - 异常详情: ServiceException('需求名称"1555"已存在')
2025-07-09 13:25:15.181 | 7d5c9da9f61143098329ff0af7ceabd8 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:115 - 异常消息: 需求名称"1555"已存在
2025-07-09 13:25:15.276 | bc837c94584c4db09000ac9f20d68adf | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_requirement_list:68 - 获取舆情需求列表成功
2025-07-09 13:34:51.709 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 13:34:51.710 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 13:34:55.007 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 13:34:55.007 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 13:34:56.310 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 13:34:56.310 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 13:34:56.312 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 13:34:56.871 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 13:34:57.595 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 13:34:57.595 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 13:39:36.628 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 13:39:36.629 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 13:39:40.790 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 13:39:40.790 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 13:39:42.373 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 13:39:42.374 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 13:39:42.376 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 13:39:42.917 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 13:39:43.527 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 13:39:43.528 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 13:40:51.309 | 6ba502e096334c8995c0362c7a0bb718 | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 13:41:08.801 | c1f50b4095d64c0ea32cd751cf8b2146 | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 13:44:02.213 | 411d57f84d1944708c859ab772a370ed | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 13:53:32.787 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 13:53:32.787 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 13:53:36.078 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 13:53:36.079 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 13:53:37.543 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 13:53:37.543 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 13:53:37.547 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 13:53:38.198 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 13:53:38.900 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 13:53:38.900 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 13:58:33.829 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 13:58:33.830 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 13:58:37.171 |  | INFO     | server:lifespan:64 - RuoYi-FastAPI开始启动
2025-07-09 13:58:37.171 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 13:58:38.889 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 13:58:38.889 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 13:58:38.904 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 13:58:39.629 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 13:58:40.427 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 13:58:40.427 |  | INFO     | server:lifespan:71 - RuoYi-FastAPI启动成功
2025-07-09 13:59:52.408 | 79d3029231ff42aabd287f4610e3899e | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-07-09 13:59:52.478 | e7353759bbc3415495994db0ca07cb12 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-07-09 13:59:52.532 | d85b09c335a54d4ca898124b23d2ad41 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为4aedd279-ec53-4b95-9064-38293cdc824f的会话获取图片验证码成功
2025-07-09 13:59:55.538 | 31bd94eaae984c0bba58dd7ff5ff187f | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-09 13:59:55.806 | b8b7556ccb6541aab2c69007be78fdb4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 13:59:56.339 | ad5af449d7274de5a70f2abad02351fa | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 13:59:56.729 | ecf3275cc73c4348bfc6a1ad375b5a64 | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 13:59:57.229 | cf218facb5bd4606b1463a8403c8c6ef | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:374 - 获取舆情任务列表成功
2025-07-09 14:02:51.445 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 14:02:51.446 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 14:02:55.422 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 14:02:55.422 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 14:02:56.798 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 14:02:56.798 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 14:02:56.799 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 14:02:57.254 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 14:02:57.850 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 14:02:57.850 |  | INFO     | server:lifespan:72 - RuoYi-FastAPI启动成功
2025-07-09 14:04:36.769 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 14:04:36.769 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 14:04:38.859 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 14:04:38.860 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 14:04:38.863 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 14:04:39.547 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 14:04:40.044 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 14:04:40.045 |  | INFO     | server:lifespan:72 - RuoYi-FastAPI启动成功
2025-07-09 14:05:17.478 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 14:05:17.479 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 14:05:21.799 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 14:05:21.799 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 14:05:23.165 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 14:05:23.165 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 14:05:23.166 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 14:05:23.636 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 14:05:24.327 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 14:05:24.327 |  | INFO     | server:lifespan:72 - RuoYi-FastAPI启动成功
2025-07-09 14:05:55.235 | 36430aa333b14226bee75e85d92a74d5 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:111 - 创建舆情需求失败: 需求名称"测试"已存在
2025-07-09 14:05:55.236 | 36430aa333b14226bee75e85d92a74d5 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:112 - 异常类型: ServiceException
2025-07-09 14:05:55.236 | 36430aa333b14226bee75e85d92a74d5 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:113 - 异常详情: ServiceException('需求名称"测试"已存在')
2025-07-09 14:05:55.237 | 36430aa333b14226bee75e85d92a74d5 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_public_requirement:115 - 异常消息: 需求名称"测试"已存在
2025-07-09 14:05:55.642 | a005230d67c547958a69637bedd95571 | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_requirement_list:68 - 获取舆情需求列表成功
2025-07-09 14:07:23.693 | fecaec4246794d53afa23cab04a76288 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 14:07:23.875 | b8e106834c8b415a8730e1666aa2c50e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 14:07:24.197 | 6ad70e0be3584967996ee45593847042 | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 14:07:24.664 | 7664e609097a489f9b3b06420417b025 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:374 - 获取舆情任务列表成功
2025-07-09 14:07:44.762 | 45b6509fb9cf4ecc872f806ba52fb5f8 | INFO     | module_opinion.controller.opinion_analysis_controller:create_public_requirement:107 - 创建舆情需求成功
2025-07-09 14:22:08.133 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 14:22:08.134 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 14:26:07.357 | d2b2fd1c5ab0466289739022a37bde45 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为79f180df-d99b-47aa-9850-cb7e4497fc57的会话获取图片验证码成功
2025-07-09 14:31:46.045 | 49d1b37650d3421989c14dbdf2f7c199 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 14:31:46.232 | 946861c4199440e889b75b4a170db95f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 14:31:46.835 | ea4cde0128fc44259430455fe11b8e4c | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 14:31:48.269 | 09e34e73e0ae4c63931308aebfa246f0 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:374 - 获取舆情任务列表成功
2025-07-09 14:31:58.350 | fe0c6d5460d743f8b3c57fd8fbe6f06a | INFO     | module_opinion.controller.opinion_analysis_controller:create_public_requirement:107 - 创建舆情需求成功
2025-07-09 14:31:58.546 | e242440c7ea240d585f5a0ff38d157d9 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:55 - 用户 admin 请求生成关联词，需求内容长度: 9
2025-07-09 14:31:58.547 | e242440c7ea240d585f5a0ff38d157d9 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:65 - 开始生成关联词，需求内容长度: 9, 最大数量: 20
2025-07-09 14:32:12.436 | e242440c7ea240d585f5a0ff38d157d9 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 14:32:12.437 | e242440c7ea240d585f5a0ff38d157d9 | INFO     | module_opinion.service.related_keywords_service:_generate_keywords_by_ai:113 - AI返回的原始内容: ["投诉", "质量问题", "维权", "曝光", "故障", "售后", "服务差", "品牌危机", "消费者不满", "质量门", "退货", "欺诈", "安全隐患", "虚假宣传", "客服", "纠纷", "召回", "负面新闻", "舆论风波", "油烟机"]...
2025-07-09 14:32:12.438 | e242440c7ea240d585f5a0ff38d157d9 | INFO     | module_opinion.service.related_keywords_service:_parse_ai_keywords_response:155 - 成功解析AI生成的关联词: 20 个
2025-07-09 14:32:12.439 | e242440c7ea240d585f5a0ff38d157d9 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:83 - 关联词生成成功，共生成 20 个关联词
2025-07-09 14:32:12.439 | e242440c7ea240d585f5a0ff38d157d9 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:74 - 关联词生成成功，共生成 20 个关联词
2025-07-09 14:34:03.639 | 6828c6e4888d48fbbcc12d85c9e4afa8 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 14:34:03.867 | 9ec1e89142de4663875fa0f0279547d1 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 14:34:04.246 | a63fd8d0c4dd4207b268e5a2d0ca8731 | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 14:34:04.759 | 9376317e8939440a833eedc76f48e38c | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:374 - 获取舆情任务列表成功
2025-07-09 14:34:19.065 | cc013dc81d8143b7b214bf6fa89ddda0 | INFO     | module_opinion.controller.opinion_analysis_controller:create_public_requirement:107 - 创建舆情需求成功
2025-07-09 14:34:19.250 | 942f04dfe2cc4e189cfbc34d59e768a7 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:55 - 用户 admin 请求生成关联词，需求内容长度: 9
2025-07-09 14:34:19.250 | 942f04dfe2cc4e189cfbc34d59e768a7 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:65 - 开始生成关联词，需求内容长度: 9, 最大数量: 20
2025-07-09 14:34:30.520 | 942f04dfe2cc4e189cfbc34d59e768a7 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 14:34:30.522 | 942f04dfe2cc4e189cfbc34d59e768a7 | INFO     | module_opinion.service.related_keywords_service:_generate_keywords_by_ai:113 - AI返回的原始内容: ["品牌荣誉","创新科技","智能厨电","绿色环保","匠心品质","市场领先","用户好评","专利技术","节能认证","卓越服务","厨房革命","健康烹饪","工业设计","高端品牌","科技创新","社会责任","智能制造","市场占有率","精工制造","节能环保"]...
2025-07-09 14:34:30.523 | 942f04dfe2cc4e189cfbc34d59e768a7 | INFO     | module_opinion.service.related_keywords_service:_parse_ai_keywords_response:155 - 成功解析AI生成的关联词: 20 个
2025-07-09 14:34:30.523 | 942f04dfe2cc4e189cfbc34d59e768a7 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:83 - 关联词生成成功，共生成 20 个关联词
2025-07-09 14:34:30.524 | 942f04dfe2cc4e189cfbc34d59e768a7 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:74 - 关联词生成成功，共生成 20 个关联词
2025-07-09 14:36:38.063 | dafc826140e8471583a5a013b9c00614 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:55 - 用户 admin 请求生成关联词，需求内容长度: 9
2025-07-09 14:36:38.064 | dafc826140e8471583a5a013b9c00614 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:65 - 开始生成关联词，需求内容长度: 9, 最大数量: 20
2025-07-09 14:36:47.269 | dafc826140e8471583a5a013b9c00614 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 14:36:47.270 | dafc826140e8471583a5a013b9c00614 | INFO     | module_opinion.service.related_keywords_service:_generate_keywords_by_ai:113 - AI返回的原始内容: ["投诉", "质量问题", "召回", "漏电", "爆炸", "消费者维权", "售后服务", "安全隐患", "故障", "品牌危机", "质量检测", "产品缺陷", "触电事故", "电器火灾", "用户差评", "维权群", "315曝光", "官方回应", "自燃", "电源故障"]...
2025-07-09 14:36:47.270 | dafc826140e8471583a5a013b9c00614 | INFO     | module_opinion.service.related_keywords_service:_parse_ai_keywords_response:155 - 成功解析AI生成的关联词: 20 个
2025-07-09 14:36:47.271 | dafc826140e8471583a5a013b9c00614 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:83 - 关联词生成成功，共生成 20 个关联词
2025-07-09 14:36:47.271 | dafc826140e8471583a5a013b9c00614 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:74 - 关联词生成成功，共生成 20 个关联词
2025-07-09 14:39:30.399 | 3b276092727848b3b3f130581e57d899 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:55 - 用户 admin 请求生成关联词，需求内容长度: 9
2025-07-09 14:39:30.399 | 3b276092727848b3b3f130581e57d899 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:65 - 开始生成关联词，需求内容长度: 9, 最大数量: 20
2025-07-09 14:39:47.571 | 3b276092727848b3b3f130581e57d899 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 14:39:47.573 | 3b276092727848b3b3f130581e57d899 | INFO     | module_opinion.service.related_keywords_service:_generate_keywords_by_ai:113 - AI返回的原始内容: ["方太质量门","抽油烟机漏油","燃气灶事故","用户投诉","售后服务差","产品召回","燃气泄漏","维权纠纷","消费者协会","安全隐患","爆炸事件","质量检测","负面报道","媒体曝光","用户差评","虚假宣传","消费维权","产品缺陷","质量问题","故障频发"]...
2025-07-09 14:39:47.573 | 3b276092727848b3b3f130581e57d899 | INFO     | module_opinion.service.related_keywords_service:_parse_ai_keywords_response:155 - 成功解析AI生成的关联词: 20 个
2025-07-09 14:39:47.574 | 3b276092727848b3b3f130581e57d899 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:83 - 关联词生成成功，共生成 20 个关联词
2025-07-09 14:39:47.574 | 3b276092727848b3b3f130581e57d899 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:74 - 关联词生成成功，共生成 20 个关联词
2025-07-09 14:41:38.869 | 39e475a4f9d6425ba3aae56c0b7a6dec | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:55 - 用户 admin 请求生成关联词，需求内容长度: 9
2025-07-09 14:41:38.869 | 39e475a4f9d6425ba3aae56c0b7a6dec | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:65 - 开始生成关联词，需求内容长度: 9, 最大数量: 20
2025-07-09 14:41:49.084 | 39e475a4f9d6425ba3aae56c0b7a6dec | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 14:41:49.086 | 39e475a4f9d6425ba3aae56c0b7a6dec | INFO     | module_opinion.service.related_keywords_service:_generate_keywords_by_ai:113 - AI返回的原始内容: ["质量门", "投诉", "召回", "售后服务差", "故障", "漏水", "爆炸", "维修难", "消费者维权", "315曝光", "燃气灶", "抽油烟机", "客服态度", "安装问题", "虚假宣传", "产品缺陷", "安全隐患", "退换货", "差评", "消费者协会"]...
2025-07-09 14:41:49.086 | 39e475a4f9d6425ba3aae56c0b7a6dec | INFO     | module_opinion.service.related_keywords_service:_parse_ai_keywords_response:155 - 成功解析AI生成的关联词: 20 个
2025-07-09 14:41:49.087 | 39e475a4f9d6425ba3aae56c0b7a6dec | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:83 - 关联词生成成功，共生成 20 个关联词
2025-07-09 14:41:49.087 | 39e475a4f9d6425ba3aae56c0b7a6dec | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:74 - 关联词生成成功，共生成 20 个关联词
2025-07-09 14:55:21.912 | dc72fe72241f41a6a0cac847b4b949ea | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 14:55:22.120 | f3e94ca20d084e298084de440e66c535 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 14:55:22.440 | 9a434d6c1b4844bea8711c808796bee0 | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 14:55:22.884 | 0cdf793cbf6d4ecfb167473b7fbb43a4 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:374 - 获取舆情任务列表成功
2025-07-09 14:55:34.996 | aede1e7f15044cc881f83a339d1799d6 | INFO     | module_opinion.controller.opinion_analysis_controller:create_public_requirement:107 - 创建舆情需求成功
2025-07-09 14:55:35.146 | 8536d64159f64938b60f637b918c8722 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:55 - 用户 admin 请求生成关联词，需求内容长度: 9
2025-07-09 14:55:35.147 | 8536d64159f64938b60f637b918c8722 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:65 - 开始生成关联词，需求内容长度: 9, 最大数量: 20
2025-07-09 14:55:49.962 | 8536d64159f64938b60f637b918c8722 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 14:55:49.962 | 8536d64159f64938b60f637b918c8722 | INFO     | module_opinion.service.related_keywords_service:_generate_keywords_by_ai:113 - AI返回的原始内容: ["投诉","质量问题","虚假宣传","售后","维权","燃气灶爆炸","烟机漏油","安全事故","消费者维权","抽油烟机","故障率","曝光","退货","客户投诉","维权案例","方太产品","售后服务差","安装纠纷","宣传欺诈","质量隐患"]...
2025-07-09 14:55:49.963 | 8536d64159f64938b60f637b918c8722 | INFO     | module_opinion.service.related_keywords_service:_parse_ai_keywords_response:155 - 成功解析AI生成的关联词: 20 个
2025-07-09 14:55:49.963 | 8536d64159f64938b60f637b918c8722 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:83 - 关联词生成成功，共生成 20 个关联词
2025-07-09 14:55:49.963 | 8536d64159f64938b60f637b918c8722 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:74 - 关联词生成成功，共生成 20 个关联词
2025-07-09 14:55:54.600 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:756 - 开始分析，需求ID: 97
2025-07-09 14:55:54.601 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:762 - 执行联网搜索...
2025-07-09 14:55:54.601 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 方太
2025-07-09 14:55:54.601 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:perform_online_search:40 - 第一阶段：开始模型能力验证
2025-07-09 14:55:54.601 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:88 - 开始验证AI模型的URL提供能力
2025-07-09 14:56:05.511 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 14:56:05.512 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:135 - AI能力测试回复: 

https://news.cctv.com/2024/06/04/ARTImN7dDk9tNtYlBx4g3v3X240604.shtml...
2025-07-09 14:56:05.513 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:146 - AI返回了URL: ['https://news.cctv.com/2024/06/04/ARTImN7dDk9tNtYlBx4g3v3X240604.shtml']
2025-07-09 14:56:05.513 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:108 - 模型能力验证完成: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://news.cctv.com/2024/06/04/ARTImN7dDk9tNtYlBx4g3v3X240604.shtml'], 'url_count': 1}
2025-07-09 14:56:05.513 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:perform_online_search:42 - 模型能力验证结果: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://news.cctv.com/2024/06/04/ARTImN7dDk9tNtYlBx4g3v3X240604.shtml'], 'url_count': 1}
2025-07-09 14:56:05.513 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:perform_online_search:45 - 第二阶段：开始正式搜索
2025-07-09 14:56:05.514 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_build_adaptive_search_query:232 - 使用高能力搜索模式，强调URL真实性
2025-07-09 14:56:52.326 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 14:59:10.415 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 14:59:10.416 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:631 - AI返回的原始内容: [
  {
    "title": "方太京东旗舰店订单逾期未发货引发消费者投诉",
    "content": "多名消费者在京东方太旗舰店购买产品后遭遇虚假发货问题，典型案例包括2025年6月4日产生的2777元油烟机订单逾期未处理。投诉显示方太与京东平台存在相互推诿责任现象，涉及仓储物流衔接不畅、审批流程不合规等系统性问题，已有消费者通过黑猫投诉平台发起集体维权。",
    "sour...
2025-07-09 14:59:16.582 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 14:59:16.588 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: negative
2025-07-09 14:59:16.588 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: https://tousu.sina.com.cn/complaint/view/17384907477/
2025-07-09 14:59:21.733 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 14:59:21.734 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: negative
2025-07-09 14:59:21.735 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: https://item.jd.com/100044538181.html
2025-07-09 14:59:27.753 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 14:59:27.754 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: negative
2025-07-09 14:59:27.755 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: https://tousu.sina.com.cn/complaint/view/17384907477/
2025-07-09 14:59:27.755 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:680 - 成功提取 3 篇文章
2025-07-09 14:59:27.755 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:603 - AI成功提取到 3 篇文章
2025-07-09 14:59:27.756 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:1227 - 情感分析统计: {'positive': 0, 'neutral': 0, 'negative': 100} (总文章数: 3)
2025-07-09 14:59:27.756 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_evaluate_search_quality:402 - 搜索质量评估完成: 总分 61.60, 等级 good
2025-07-09 14:59:27.757 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:perform_online_search:58 - 搜索结果质量评估: {'overall_quality': 'good', 'overall_score': 61.6, 'url_coverage': {'score': 100.0, 'stats': {'total': 3, 'with_url': 3}}, 'content_quality': {'score': 61.6, 'stats': {'avg_title_length': 17.666666666666668, 'avg_content_length': 116.0, 'with_source': 3, 'with_time': 3}}, 'recommendations': ['搜索结果质量良好']}
2025-07-09 14:59:27.758 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1326 - 开始保存搜索结果到数据库
2025-07-09 14:59:27.758 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1327 - 实体关键词: 方太
2025-07-09 14:59:27.758 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1328 - 选中关键词: ['投诉', '质量问题', '虚假宣传', '售后', '方太产品']
2025-07-09 14:59:27.759 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1329 - 搜索结果文章数量: 3
2025-07-09 14:59:27.759 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1339 - 合并后的关键词字符串: 方太,投诉,质量问题,虚假宣传,售后,方太产品
2025-07-09 14:59:27.760 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 1 条文章: 方太京东旗舰店订单逾期未发货引发消费者投诉
2025-07-09 14:59:27.761 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: https://tousu.sina.com.cn/complaint/view/17384907477/
2025-07-09 14:59:27.762 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=方太京东旗舰店订单逾期未发货引发消费者投诉..., url=https://tousu.sina.com.cn/complaint/view/17384907477/, sentiment=negative
2025-07-09 14:59:27.800 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 1 条文章，ID: 1797
2025-07-09 14:59:27.801 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 2 条文章: 方太光刃消毒柜新品遭质检缺失质疑
2025-07-09 14:59:27.801 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: https://item.jd.com/100044538181.html
2025-07-09 14:59:27.801 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=方太光刃消毒柜新品遭质检缺失质疑..., url=https://item.jd.com/100044538181.html, sentiment=negative
2025-07-09 14:59:27.837 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 2 条文章，ID: 1798
2025-07-09 14:59:27.838 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 3 条文章: 方太渠道合作终止致消费者权益受损
2025-07-09 14:59:27.839 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: https://tousu.sina.com.cn/complaint/view/17384907477/
2025-07-09 14:59:27.839 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=方太渠道合作终止致消费者权益受损..., url=https://tousu.sina.com.cn/complaint/view/17384907477/, sentiment=negative
2025-07-09 14:59:27.875 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 3 条文章，ID: 1799
2025-07-09 14:59:27.939 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1388 - 成功保存 3 条搜索结果到数据库
2025-07-09 14:59:27.939 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.service.external_api_service:perform_online_search:65 - 联网搜索完成，获取到 3 条结果，已保存 3 条到数据库
2025-07-09 14:59:27.940 | 80d0aebe4f0b47a888da01466c2ad704 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:798 - 分析执行成功
2025-07-09 15:37:57.392 | 0bd8a37f1a014c78bbd1d89cb53f14b9 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-07-09 15:37:57.453 | 001cac6037214b4db0324e4b005d809f | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-07-09 15:37:57.507 | f5af7d467c8d4e42b3e7c0d080819ea0 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为201617f2-253e-4d78-a91b-e0cefa32c30a的会话获取图片验证码成功
2025-07-09 15:38:00.949 | 03f2362d9cc14500a0028a6f91e650ea | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-09 15:38:01.485 | e3bb1160ef8e4519aafa8b3cabf07ec8 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 15:38:01.741 | 36c5e0226e4244c2bcb1c4d63967a61b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 15:38:02.129 | 19d522093cd3476288fb0cbe26aedfc7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 15:38:02.540 | 2dae69cb08204caa8ba93e1b1c728e11 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:374 - 获取舆情任务列表成功
2025-07-09 15:38:19.438 | 87dca7d028834ba09a0c7169b5a82369 | INFO     | module_opinion.controller.opinion_analysis_controller:create_public_requirement:107 - 创建舆情需求成功
2025-07-09 15:38:19.883 | 34c722eef4924680a0c3652f1f53c1c8 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:55 - 用户 admin 请求生成关联词，需求内容长度: 9
2025-07-09 15:38:19.883 | 34c722eef4924680a0c3652f1f53c1c8 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:65 - 开始生成关联词，需求内容长度: 9, 最大数量: 20
2025-07-09 15:38:32.026 | 34c722eef4924680a0c3652f1f53c1c8 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 15:38:32.027 | 34c722eef4924680a0c3652f1f53c1c8 | INFO     | module_opinion.service.related_keywords_service:_generate_keywords_by_ai:113 - AI返回的原始内容: ["方太爆炸","燃气灶事故","油烟机故障","消费者投诉","产品召回","售后服务差","质量缺陷","客户维权","安全隐患","企业危机","退换货纠纷","用户差评","高管丑闻","财报亏损","安装事故","漏电问题","客服推诿","环保违规","产品自燃","诉讼纠纷"]...
2025-07-09 15:38:32.027 | 34c722eef4924680a0c3652f1f53c1c8 | INFO     | module_opinion.service.related_keywords_service:_parse_ai_keywords_response:155 - 成功解析AI生成的关联词: 20 个
2025-07-09 15:38:32.027 | 34c722eef4924680a0c3652f1f53c1c8 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:83 - 关联词生成成功，共生成 20 个关联词
2025-07-09 15:38:32.027 | 34c722eef4924680a0c3652f1f53c1c8 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:74 - 关联词生成成功，共生成 20 个关联词
2025-07-09 15:38:57.763 | b8bb7647a64c4cabbd402e231d8e2fb2 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:55 - 用户 admin 请求生成关联词，需求内容长度: 9
2025-07-09 15:38:57.764 | b8bb7647a64c4cabbd402e231d8e2fb2 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:65 - 开始生成关联词，需求内容长度: 9, 最大数量: 20
2025-07-09 15:39:05.922 | b8bb7647a64c4cabbd402e231d8e2fb2 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-09 15:39:05.922 | b8bb7647a64c4cabbd402e231d8e2fb2 | INFO     | module_opinion.service.related_keywords_service:_generate_keywords_by_ai:113 - AI返回的原始内容: ["投诉", "维权", "召回", "售后", "故障", "虚假宣传", "自爆", "漏油", "爆炸", "质量门", "欺诈", "漏水", "黑猫投诉", "客服", "油烟机", "消费者协会", "315曝光", "燃气灶", "维修", "消费者投诉"]...
2025-07-09 15:39:05.923 | b8bb7647a64c4cabbd402e231d8e2fb2 | INFO     | module_opinion.service.related_keywords_service:_parse_ai_keywords_response:155 - 成功解析AI生成的关联词: 20 个
2025-07-09 15:39:05.923 | b8bb7647a64c4cabbd402e231d8e2fb2 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:83 - 关联词生成成功，共生成 20 个关联词
2025-07-09 15:39:05.923 | b8bb7647a64c4cabbd402e231d8e2fb2 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:74 - 关联词生成成功，共生成 20 个关联词
2025-07-09 15:46:04.050 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 15:46:04.050 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 15:47:10.066 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 15:47:10.067 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 15:47:43.329 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 15:47:43.330 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 15:48:05.805 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 15:48:05.806 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 15:49:44.342 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 15:49:44.343 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 15:49:46.473 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 15:49:46.473 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 15:49:46.477 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 15:49:47.272 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 15:49:48.011 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 15:49:48.012 |  | INFO     | server:lifespan:72 - RuoYi-FastAPI启动成功
2025-07-09 16:20:39.286 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 16:20:39.287 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 16:20:42.432 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 16:20:42.432 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 16:20:44.326 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 16:20:44.326 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 16:20:44.328 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 16:20:45.123 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 16:20:45.707 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 16:20:45.708 |  | INFO     | server:lifespan:72 - RuoYi-FastAPI启动成功
2025-07-09 16:30:40.333 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 16:30:40.334 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 16:30:43.822 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 16:30:43.823 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 16:30:45.371 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 16:30:45.371 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 16:30:45.373 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 16:30:45.882 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 16:30:46.722 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 16:30:46.722 |  | INFO     | server:lifespan:72 - RuoYi-FastAPI启动成功
2025-07-09 16:37:47.523 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 16:37:47.524 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 16:37:50.950 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 16:37:50.951 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 16:37:54.351 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 16:37:54.351 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 16:37:54.353 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 16:37:55.239 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 16:37:56.325 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 16:37:56.325 |  | INFO     | server:lifespan:72 - RuoYi-FastAPI启动成功
2025-07-09 16:42:48.580 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 16:42:48.580 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 16:42:51.676 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 16:42:51.677 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 16:42:53.407 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 16:42:53.407 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 16:42:53.409 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 16:42:53.940 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 16:42:54.699 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 16:42:54.700 |  | INFO     | server:lifespan:72 - RuoYi-FastAPI启动成功
2025-07-09 16:57:59.758 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 16:57:59.759 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 18:00:20.481 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 18:00:20.482 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 18:00:22.472 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 18:00:22.473 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 18:00:22.478 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 18:00:23.271 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 18:00:24.202 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 18:00:24.203 |  | INFO     | server:lifespan:72 - RuoYi-FastAPI启动成功
2025-07-09 18:00:50.144 | 26406670d6c546bf8d0871071a64f866 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为d462248d-b5da-4829-b93b-10c20a0d0aab的会话获取图片验证码成功
2025-07-09 18:00:55.526 | 8b90bf867b21417598aaa6dc03b9bc95 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-09 18:00:55.872 | 0bf0f7a968e645299ff4adf500233eb6 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 18:00:56.508 | c8aba50ba0164f34b6630f159835d110 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 18:00:56.922 | 33ecee1a7f3541b38f97fef022ddce7a | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 18:00:57.462 | 14504a73644e4412b7cbd09bef591484 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:374 - 获取舆情任务列表成功
2025-07-09 18:11:57.848 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 18:11:57.849 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 18:13:49.639 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 18:13:49.639 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 18:14:51.747 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 18:14:51.747 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 18:15:20.560 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 18:15:20.561 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 18:16:11.043 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 18:16:11.043 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 18:16:11.133 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 18:16:11.133 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 18:16:11.134 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 18:16:11.331 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 18:16:11.351 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 18:16:11.351 |  | INFO     | server:lifespan:72 - RuoYi-FastAPI启动成功
2025-07-09 18:16:42.392 | 8d459f677d3244a79d5e61158dd882ea | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为102c083b-3aff-4245-9ab9-f4668168772b的会话获取图片验证码成功
2025-07-09 18:16:44.410 | 3d176a505b7e4dcb84270428313885dd | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-09 18:16:44.453 | 5ba8e9a25aa748e08240cb1becaa4d82 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-09 18:16:44.786 | 2a2576a6b8d740e3a7775763036cd0b8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-09 18:16:45.033 | d41323da7fca448a8f1b605267630870 | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:85 - 获取关键词分类列表成功
2025-07-09 18:16:45.250 | 15ee26d3ea4e4f93ba8496fc5deff550 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:374 - 获取舆情任务列表成功
2025-07-09 18:16:50.001 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 18:16:50.001 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-09 18:43:27.487 |  | INFO     | server:lifespan:65 - RuoYi-FastAPI开始启动
2025-07-09 18:43:27.487 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-09 18:43:30.104 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-09 18:43:30.104 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-09 18:43:30.106 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-09 18:43:30.955 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-09 18:43:31.725 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-09 18:43:31.725 |  | INFO     | server:lifespan:72 - RuoYi-FastAPI启动成功
2025-07-09 18:45:26.776 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-09 18:45:26.777 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
