from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_warning.dao.warning_record_dao import WarningRecordDao
from module_warning.entity.vo.warning_vo import (
    WarningRecordModel,
    WarningRecordPageQueryModel,
    DeleteWarningRecordModel,
    WarningStatisticsModel
)
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil
from utils.log_util import logger


class WarningRecordService:
    """
    预警记录管理模块服务层
    """

    @classmethod
    async def get_warning_record_list_services(
        cls, query_db: AsyncSession, query_object: WarningRecordPageQueryModel, is_page: bool = False
    ):
        """
        获取预警记录列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 预警记录列表信息对象
        """
        try:
            record_list_result = await WarningRecordDao.get_warning_record_list(query_db, query_object, is_page)
            logger.info('获取预警记录列表成功')
            return record_list_result
        except Exception as e:
            logger.error(f'获取预警记录列表失败: {str(e)}')
            raise ServiceException(message=f'获取预警记录列表失败: {str(e)}')

    @classmethod
    async def get_warning_record_detail_services(cls, query_db: AsyncSession, record_id: int):
        """
        获取预警记录详细信息service

        :param query_db: orm对象
        :param record_id: 预警记录id
        :return: 预警记录详细信息对象
        """
        try:
            record_detail_result = await WarningRecordDao.get_warning_record_by_id(query_db, record_id)
            if record_detail_result:
                result = CamelCaseUtil.transform_result(record_detail_result)
                logger.info(f'获取预警记录详情成功，记录ID: {record_id}')
                return result
            else:
                raise ServiceException(message='预警记录不存在')
        except ServiceException:
            raise
        except Exception as e:
            logger.error(f'获取预警记录详情失败: {str(e)}')
            raise ServiceException(message=f'获取预警记录详情失败: {str(e)}')

    @classmethod
    async def add_warning_record_services(cls, query_db: AsyncSession, page_object: WarningRecordModel):
        """
        新增预警记录信息service

        :param query_db: orm对象
        :param page_object: 新增预警记录对象
        :return: 新增预警记录校验结果
        """
        try:
            # 设置创建时间
            page_object.create_time = datetime.now()
            page_object.update_time = datetime.now()
            
            await WarningRecordDao.add_warning_record_dao(query_db, page_object)
            await query_db.commit()
            logger.info(f'新增预警记录成功，方案ID: {page_object.scheme_id}')
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            logger.error(f'新增预警记录失败: {str(e)}')
            raise ServiceException(message=f'新增预警记录失败: {str(e)}')

    @classmethod
    async def edit_warning_record_services(cls, query_db: AsyncSession, page_object: WarningRecordModel):
        """
        编辑预警记录信息service

        :param query_db: orm对象
        :param page_object: 编辑预警记录对象
        :return: 编辑预警记录校验结果
        """
        try:
            # 记录输入数据用于调试
            logger.info(f'开始编辑预警记录，输入数据: {page_object.model_dump()}')

            # 验证必要字段
            if not page_object.id:
                raise ServiceException(message='预警记录ID不能为空')

            # 检查记录是否存在
            existing_record = await WarningRecordDao.get_warning_record_by_id(query_db, page_object.id)
            if not existing_record:
                raise ServiceException(message='预警记录不存在')

            logger.info(f'找到现有记录: ID={existing_record.id}, scheme_id={existing_record.scheme_id}')

            # 设置更新时间
            page_object.update_time = datetime.now()

            # 生成更新数据
            edit_record = page_object.model_dump(exclude_unset=True, by_alias=False)
            logger.info(f'准备更新的数据: {edit_record}')

            await WarningRecordDao.edit_warning_record_dao(query_db, edit_record)
            await query_db.commit()
            logger.info(f'编辑预警记录成功，记录ID: {page_object.id}')
            return CrudResponseModel(is_success=True, message='更新成功')
        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f'编辑预警记录失败，详细错误: {type(e).__name__}: {str(e)}')
            logger.error(f'错误发生时的数据: {page_object.model_dump() if page_object else "None"}')
            raise ServiceException(message=f'编辑预警记录失败: {str(e)}')

    @classmethod
    async def delete_warning_record_services(cls, query_db: AsyncSession, page_object: DeleteWarningRecordModel):
        """
        删除预警记录信息service

        :param query_db: orm对象
        :param page_object: 删除预警记录对象
        :return: 删除预警记录校验结果
        """
        try:
            if not page_object.record_ids:
                raise ServiceException(message='请选择要删除的预警记录')

            # 检查记录是否存在
            for record_id in page_object.record_ids:
                existing_record = await WarningRecordDao.get_warning_record_by_id(query_db, record_id)
                if not existing_record:
                    raise ServiceException(message=f'预警记录ID {record_id} 不存在')

            await WarningRecordDao.delete_warning_record_dao(query_db, page_object.record_ids)
            await query_db.commit()
            logger.info(f'删除预警记录成功，记录数量: {len(page_object.record_ids)}')
            return CrudResponseModel(is_success=True, message='删除成功')
        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f'删除预警记录失败: {str(e)}')
            raise ServiceException(message=f'删除预警记录失败: {str(e)}')

    @classmethod
    async def get_warning_statistics_services(cls, query_db: AsyncSession, scheme_id: int = None):
        """
        获取预警统计数据service

        :param query_db: orm对象
        :param scheme_id: 方案ID，可选
        :return: 统计数据对象
        """
        try:
            statistics_data = await WarningRecordDao.get_warning_statistics(query_db, scheme_id)
            result = WarningStatisticsModel(**statistics_data)
            logger.info(f'获取预警统计数据成功，方案ID: {scheme_id}')
            return result
        except Exception as e:
            logger.error(f'获取预警统计数据失败: {str(e)}')
            raise ServiceException(message=f'获取预警统计数据失败: {str(e)}')

    @classmethod
    async def update_warning_record_status_services(
        cls, query_db: AsyncSession, record_id: int, status: int, update_by: str
    ):
        """
        更新预警记录状态service

        :param query_db: orm对象
        :param record_id: 预警记录ID
        :param status: 新状态
        :param update_by: 更新者
        :return: 更新结果
        """
        try:
            # 检查记录是否存在
            existing_record = await WarningRecordDao.get_warning_record_by_id(query_db, record_id)
            if not existing_record:
                raise ServiceException(message='预警记录不存在')

            await WarningRecordDao.update_warning_record_status(query_db, record_id, status, update_by)
            await query_db.commit()
            logger.info(f'更新预警记录状态成功，记录ID: {record_id}，新状态: {status}')
            return CrudResponseModel(is_success=True, message='状态更新成功')
        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f'更新预警记录状态失败: {str(e)}')
            raise ServiceException(message=f'更新预警记录状态失败: {str(e)}')

    @staticmethod
    async def export_warning_record_list_services(warning_record_list: List):
        """
        导出预警记录信息service

        :param warning_record_list: 预警记录信息列表
        :return: 预警记录信息对应excel的二进制数据
        """
        # 创建一个映射字典，将英文键映射到中文键
        mapping_dict = {
            'id': '记录ID',
            'schemeId': '方案ID',
            'warningType': '预警类型',
            'content': '预警内容',
            'keywords': '关键词',
            'status': '状态',
            'createBy': '创建者',
            'createTime': '创建时间',
            'updateBy': '更新者',
            'updateTime': '更新时间',
            'remark': '备注',
        }

        # 格式化状态字段
        for item in warning_record_list:
            if item.get('status') == 0:
                item['status'] = '待处理'
            elif item.get('status') == 1:
                item['status'] = '已处理'
            elif item.get('status') == 2:
                item['status'] = '紧急'
            else:
                item['status'] = '未知'

        binary_data = ExcelUtil.export_list2excel(warning_record_list, mapping_dict)
        return binary_data
