from datetime import datetime
from sqlalchemy import Column, Integer, String, DECIMAL, TIMESTAMP, Text, Date, Index, JSON, BigInteger
from sqlalchemy.ext.declarative import declarative_base
from config.database import Base


class UserOrder(Base):
    """
    用户订单表 - 基于实际数据库表结构
    """
    __tablename__ = 'user_orders'

    order_id = Column(BigInteger, primary_key=True, autoincrement=True, comment='订单ID')
    order_no = Column(String(64), nullable=False, unique=True, comment='订单号')
    user_id = Column(BigInteger, nullable=False, comment='用户ID')
    package_id = Column(BigInteger, nullable=False, comment='套餐ID')
    package_name = Column(String(100), nullable=False, comment='套餐名称')
    original_price = Column(DECIMAL(10, 2), nullable=False, comment='原价')
    discount_amount = Column(DECIMAL(10, 2), default=0.00, comment='优惠金额')
    final_price = Column(DECIMAL(10, 2), nullable=False, comment='实付金额')
    payment_method = Column(String(20), nullable=True, comment='支付方式')
    order_status = Column(String(20), default='pending', comment='订单状态')
    payment_status = Column(String(20), default='unpaid', comment='支付状态')
    payment_time = Column(TIMESTAMP, nullable=True, comment='支付时间')
    expire_time = Column(TIMESTAMP, nullable=True, comment='过期时间')
    transaction_id = Column(String(100), nullable=True, comment='交易ID')
    payment_data = Column(JSON, nullable=True, comment='支付数据')
    remark = Column(String(500), nullable=True, comment='备注')
    create_time = Column(TIMESTAMP, default=datetime.now, comment='创建时间')
    update_time = Column(TIMESTAMP, default=datetime.now, onupdate=datetime.now, comment='更新时间')

    __table_args__ = (
        Index('idx_user_id', 'user_id'),
        Index('idx_order_no', 'order_no'),
        Index('idx_payment_status', 'payment_status'),
        Index('idx_create_time', 'create_time'),
    )


class UserPackage(Base):
    """
    用户套餐表 - 基于实际数据库表结构
    """
    __tablename__ = 'user_packages'

    package_id = Column(BigInteger, primary_key=True, autoincrement=True, comment='套餐ID')
    package_name = Column(String(100), nullable=False, comment='套餐名称')
    package_type = Column(String(20), nullable=False, comment='套餐类型')
    analysis_limit = Column(Integer, default=-1, comment='分析次数限制')
    duration_days = Column(Integer, nullable=False, comment='有效期天数')
    original_price = Column(DECIMAL(10, 2), nullable=False, comment='原价')
    current_price = Column(DECIMAL(10, 2), nullable=False, comment='当前价格')
    discount_rate = Column(DECIMAL(5, 2), default=0.00, comment='折扣率')
    features = Column(JSON, nullable=True, comment='功能特性')
    is_active = Column(Integer, default=1, comment='是否启用')
    sort_order = Column(Integer, default=0, comment='排序')
    description = Column(Text, nullable=True, comment='描述')
    create_time = Column(TIMESTAMP, default=datetime.now, comment='创建时间')
    update_time = Column(TIMESTAMP, default=datetime.now, onupdate=datetime.now, comment='更新时间')


# 注意：根据数据库查看结果，没有单独的user_membership表
# 会员信息通过user_orders表和sys_user表的vip_level等字段来管理
