2025-06-22 15:47:41.695 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-06-22 15:47:41.696 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-22 15:47:42.897 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-22 15:47:42.897 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-22 15:47:42.906 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-22 15:50:00.965 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-06-22 15:50:00.966 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-22 15:50:02.156 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-22 15:50:02.156 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-22 15:50:02.159 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-22 16:01:02.198 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-06-22 16:01:02.198 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-22 16:01:03.325 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-22 16:01:03.325 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-22 16:01:03.327 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-22 16:02:39.886 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-06-22 16:02:39.886 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-22 16:02:41.009 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-22 16:02:41.009 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-22 16:02:41.011 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-22 16:02:54.918 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-06-22 16:02:54.919 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-22 16:02:56.132 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-22 16:02:56.132 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-22 16:02:56.134 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-22 16:03:52.339 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-06-22 16:03:52.339 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-22 16:03:53.586 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-22 16:03:53.587 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-22 16:03:53.589 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-22 16:06:02.067 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-06-22 16:06:02.068 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-22 16:06:03.313 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-22 16:06:03.313 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-22 16:06:03.315 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-22 16:06:03.889 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-22 16:06:04.531 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-22 16:06:04.531 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI启动成功
2025-06-22 16:06:47.583 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-06-22 16:06:47.584 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-22 16:06:48.811 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-22 16:06:48.811 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-22 16:06:48.813 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-22 16:06:49.271 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-22 16:06:49.813 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-22 16:06:49.813 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI启动成功
2025-06-22 16:06:55.589 | 3fe241d3c8944c89a8888e1dc7cbe2fb | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为939f94e3-ec34-4440-90d2-e051a4e096a2的会话获取图片验证码成功
2025-06-22 16:06:55.741 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-22 16:06:55.741 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-22 16:06:58.630 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-06-22 16:06:58.631 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-22 16:06:59.774 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-22 16:06:59.774 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-22 16:06:59.776 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-22 16:07:00.212 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-22 16:07:00.759 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-22 16:07:00.759 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI启动成功
2025-06-22 16:07:04.448 | dc9bbc88a04c4937b93c6181cd8e153f | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-22 16:07:04.668 | 0f79a55b94e34e41bacf590f0c395dd5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 16:07:05.156 | 82de926ad4ab48aea265c543d50616a6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-22 16:07:05.542 | 1437be16a480408b94aeb10a3a4f322c | INFO     | module_admin.controller.keyword_data_controller:get_web_statistics_public:273 - 获取网站统计数据成功
2025-06-22 16:07:05.822 | 04ebca33af604e39af9d3cf6ddba1995 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:174 - 查询参数: pageNum=1, pageSize=10, timeFilter=None, actual_start_date=None, actual_end_date=None
2025-06-22 16:07:05.823 | 04ebca33af604e39af9d3cf6ddba1995 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:40 - Service层接收到的查询参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:07:05.823 | 04ebca33af604e39af9d3cf6ddba1995 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:41 - Service层查询对象类型: <class 'module_admin.entity.vo.keyword_data_vo.KeywordDataPageQueryModel'>
2025-06-22 16:07:05.824 | 04ebca33af604e39af9d3cf6ddba1995 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:42 - Service层查询对象属性: {'page_num': 1, 'page_size': 10, 'title': None, 'keyword': None, 'type': None, 'web': None, 'begin_time': None, 'end_time': None, 'platform_types': None, 'sentiment_types': None, 'info_attributes': None, 'web_site': None, 'time_filter': None}
2025-06-22 16:07:05.824 | 04ebca33af604e39af9d3cf6ddba1995 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:112 - DAO层时间筛选参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:07:05.825 | 04ebca33af604e39af9d3cf6ddba1995 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:182 - 总共构建了 0 个查询条件
2025-06-22 16:07:05.825 | 04ebca33af604e39af9d3cf6ddba1995 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:183 - 查询参数: time_filter=None, platform_types=None, sentiment_types=None
2025-06-22 16:07:05.825 | 04ebca33af604e39af9d3cf6ddba1995 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:189 - 没有查询条件，将返回所有数据
2025-06-22 16:07:05.973 | c190802886154e268b538e4298f7de52 | INFO     | module_admin.controller.keyword_data_controller:get_filter_statistics_public:256 - 获取筛选统计数据成功
2025-06-22 16:07:06.007 | 04ebca33af604e39af9d3cf6ddba1995 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:178 - 查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-06-22 16:07:06.008 | 04ebca33af604e39af9d3cf6ddba1995 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:179 - 查询结果: records=[KeywordDataResponseModel(id=288, title='上海电气与<em>西门子</em>中国签署战略协议', content='上海电气与西门子中国签署战略协议\n\n2025年05月05日 07:56\n\n据上海电气消息，近日，上海电气输配电集团与西门子（中国）有限公司签署战略合作协议。根据协议，双方将紧密围绕国家战略需求，聚焦中低压电气产品和电气自动化系统，强化协同创新，并在数字化及低碳经济等新领域探索新项目开发。此外，双方还将探索更多领域的协同。\n\n（文章来源：界面新闻）', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=295, title='四川工业链博馆亮相2025成都国际工业博览会', content='看起来采集该网页时出现了问题，无法成功获取正文内容。请尝试提供其他网页链接或检查网络连接后再次请求。', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=291, title='“健康中国2025护‘胃’行动”在京启动，多方携手共筑全民胃健康防线', content='看来在尝试获取该网页内容时出现了问题，导致无法正常提取正文。您可以检查该网页是否正常访问，或者稍后再尝试采集。如果问题持续存在，请告知我，我会进一步协助您！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=289, title='<em class="highlight">西门子</em>Xcelerator产业生态西部中心在成都金牛区投运', content='看起来采集该网页时出现了问题，无法成功获取正文内容。您可以稍后尝试重新采集，或者检查链接是否有效。如果有其他需要帮助的地方，请随时告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=293, title='<em>西门子</em>中国工厂首个光储一体化项目投运', content='看起来采集该网页时出现了问题，无法成功获取正文内容。您可以检查链接是否有效，或者尝试在其他时间段重新采集。如果有其他需求，请告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=287, title='公司产品能否用于海上核电，海上风电？中环海陆回应', content='以下是该网页的正文内容：\n\n---\n\n# 公司产品能否用于海上核电，海上风电？中环海陆回应\n\n每日经济新闻\n\n2025-05-09 17:19:43\n\n每经AI快讯，有投资者在投资者互动平台提问：贵公司产品能否用于海上核电，海上风电？\n\n中环海陆（301040.SZ）5月9日在投资者互动平台表示，公司生产的6.0MW以上海上风电基础桩法兰锻件、7.0MW以上转子房法兰锻件供应于西门子、上海电气等知名企业的海上风电项目，具备了给海上风电大兆瓦风机配套相关产品的能力。\n\n（记者 毕陆名）\n\n免责声明：本文内容与数据仅供参考，不构成投资建议，使用前核实。据此操作，风险自担。\n\n如需转载请与《每日经济新闻》报社联系。\n\n未经《每日经济新闻》报社授权，严禁转载或镜像，违者必究。\n\n特别提醒：如果我们使用了您的图片，请作者与 [本站联系](http://www.nbd.com.cn/contact) 索取稿酬。如您不希望作品出现在本站，可联系我们要求撤下您的作品。', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=292, title='投行对黄金最激进预期：年内冲击4000美元！金价居高不下，有年轻人租三金结婚', content='以下是该网页的正文内容：\n\n---\n\n# 投行对黄金最激进预期：年内冲击4000美元！金价居高不下，有年轻人租三金结婚\n\n每日经济新闻\n\n2025-05-09 01:19:32\n\n每经编辑｜陈柯名\n\n5月8日，国际金价出现下跌。现货黄金一度跌超1.5%，截止发稿，下跌0.28%，报3354美元/盎司。\n\nCOMEX黄金一度跌近2%，截至发稿，下跌1%，报3358.1美元/盎司。\n\n**黄金最激进预期：年内冲击4000美元**\n\n值得注意的是，据财联社消息，美国银行的大宗商品分析师对黄金的前景越来越乐观。在美银周三（5月7日）发布的一份报告中，Michael Widmer领导的贵金属团队预计，金价在今年下半年触及每盎司4,000美元的可能性越来越大。\n\n美银的这一最新预测是目前华尔街各大银行中最激进的预测之一。\n\n今年3月，Widmer和他的团队曾预测，到2027年，金价将达到3500美元。而在不到一个月的时间内，金价就达到了这一目标。\n\n**年轻人开始租三金结婚**\n\n此外，国内各大品牌金饰品价格仍然上涨中，周大福、周六福、潮宏基、周大生、六福珠宝等金饰品价格均涨至1040元/克。\n\n据央视网报道，近段时间，金价居高不下，准备结婚的准新人想要凑齐心仪的“三金”首饰是一笔不小的开销。于是，一些年轻人选择以租赁的方式完成婚俗仪式。龙凤盘旋的足金颈链、刻着双喜的手镯、甚至还有黄金制作而成的“结婚证书”……在广州市白云区的这家金饰店，老板专门为可供租赁的“三金”配饰开了个专柜。老板说，随着金价走高，如今，越来越多的人愿意接受这种租“三金”的方式。\n\n据北京商报报道，近期，部分有购置结婚“三金”需求的消费者选择了自己打金或租赁金饰。一家主营金饰租赁业务的商家向北京商报记者表示，租“三金”是近期才出现的形式，“五一”假期是结婚高峰期，不少热门款式被订购空了。上述商家介绍称，租赁黄金首饰的价格一般在30元/克，押金则按黄金大盘价格收取。\n\n**老铺黄金股价涨了16倍**\n\n值得注意的是，随着金价攀升，“黄金界爱马仕”老铺黄金股价迎来大涨。自去年6月份上市以来，老铺黄金凭借快速增长的业绩接连大涨，截至目前，股价涨幅达到1605%。\n\n5月8日，老铺黄金(06181.HK)高开后回落，截至收盘，公司股价报698港元/股，股价上涨1.90%。\n\n公开信息显示，老铺黄金于2024年6月28日在香港挂牌上市，当时以每股40.50港元的IPO发行价募资10.42亿港元。\n\n今年开年，老铺黄金凭借其火热的销售势头成为市场“顶流”：排队5小时才能买到、黄牛日赚1万等消息频繁刷屏。\n\n4月28日，老铺黄金披露2024年度报告。报告期内，公司实现营业总收入85.26亿元，同比增长167.94%；归母净利润14.73亿元，同比增长253.86%；经营活动产生的现金流量净额为-12.28亿元，上年同期为-2919.6万元。\n\n那近期是入手黄金的好时机吗？职业黄金投资分析师吕超认为，从长期投资角度看，黄金具有货币属性和避险属性，能在经济不稳定时起到保值作用。若投资者资产配置中黄金占比较低，可考虑逢低买入。从短期投资角度看，黄金价格波动受多种因素影响，如美元走势、国际贸易局势、美联储政策等。目前金价虽有回调，但这些不确定因素仍存在，若后续出现新的催化剂，如地缘政治紧张、经济数据下滑等，金价可能再次上涨。\n\n**免责声明：本文内容与数据仅供参考，不构成投资建议，使用前核实。据此操作，风险自担。**\n\n**编辑｜陈柯名 杜波 盖源源**\n\n**校对｜金冥羽**\n\n每日经济新闻综合自央视网、财联社、北京商报等\n\n****推荐阅读↓****\n\n****最新！9连发，于东来再回应质疑：可以邀请柴怼怼、宋清辉等来考察！刚刚，永辉超市发公开信支持****\n\n****盘前涨超10%！极氪传来大消息：将被吉利私有化，并于纽交所退市！李书福最新回应****\n\n****最新！巴方称已击落3架印度无人机！两国战机均未侵入对方领空，巴方公布对印回击细节！中方：对印度的军事行动表示遗憾****\n\n---\n\n以上就是文章的核心内容。', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=290, title='围绕国家战略需求 上海电气与<em>西门子</em>中国签署战略协议', content='看起来采集该网页时出现了问题，提示“Failed to wait for the message endpoint”。这可能是由于网络问题或目标网站的加载问题导致的。你可以稍后再尝试采集，或者检查是否有其他限制（如防火墙、网站反爬机制等）影响了采集过程。如果有其他问题，请告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=285, title='国信证券发布<em class="highlight">老板</em>电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放', content='以下是该网页的正文内容：\n\n---\n\n# 国信证券发布老板电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放\n\n每日经济新闻\n\n2025-05-09 14:47:56\n\n每经AI快讯，国信证券5月9日发布研报称，给予老板电器（002508.SZ，最新价：19.82元）优于大市评级。评级理由主要包括：1）烟灶主品类增长稳健，二三品类有所承压；2）零售渠道增长较好；3）名气品牌持续成长，盈利扭亏；4）投入加大，盈利有所下降。风险提示：行业竞争加剧；原材料价格大幅上涨；行业需求恢复不及预期。\n\n---\n\n如需转载，请与《每日经济新闻》报社联系。  \n未经报社授权，严禁转载或镜像，违者必究。', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=284, title='国信证券发布<em>老板</em>电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放', content='国信证券发布研报指出，在地产行业压力下，老板电器经营较为稳健，期待以旧换新需求进一步释放。报告给予老板电器优于大市的评级，主要理由包括：\n\n1. 烟灶主品类增长稳健，但二三品类有所承压。\n2. 零售渠道增长较好。\n3. 名气品牌持续成长，并实现盈利扭亏。\n4. 投入加大导致盈利有所下降。\n\n报告同时提示了风险因素，包括行业竞争加剧、原材料价格大幅上涨以及行业需求恢复不及预期等。\n\n（来源：每日经济新闻）', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[])] page_num=1 page_size=10 total=504 has_next=True
2025-06-22 16:07:08.667 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-22 16:07:08.667 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-22 16:07:09.414 | bff244c478b246a88ce57cc3e852d558 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:174 - 查询参数: pageNum=1, pageSize=10, timeFilter=None, actual_start_date=None, actual_end_date=None
2025-06-22 16:07:09.414 | bff244c478b246a88ce57cc3e852d558 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:40 - Service层接收到的查询参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:07:09.415 | bff244c478b246a88ce57cc3e852d558 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:41 - Service层查询对象类型: <class 'module_admin.entity.vo.keyword_data_vo.KeywordDataPageQueryModel'>
2025-06-22 16:07:09.415 | bff244c478b246a88ce57cc3e852d558 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:42 - Service层查询对象属性: {'page_num': 1, 'page_size': 10, 'title': None, 'keyword': None, 'type': None, 'web': None, 'begin_time': None, 'end_time': None, 'platform_types': None, 'sentiment_types': None, 'info_attributes': None, 'web_site': None, 'time_filter': None}
2025-06-22 16:07:09.415 | bff244c478b246a88ce57cc3e852d558 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:112 - DAO层时间筛选参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:07:09.416 | bff244c478b246a88ce57cc3e852d558 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:182 - 总共构建了 0 个查询条件
2025-06-22 16:07:09.416 | bff244c478b246a88ce57cc3e852d558 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:183 - 查询参数: time_filter=None, platform_types=None, sentiment_types=None
2025-06-22 16:07:09.417 | bff244c478b246a88ce57cc3e852d558 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:189 - 没有查询条件，将返回所有数据
2025-06-22 16:07:09.468 | bff244c478b246a88ce57cc3e852d558 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:178 - 查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-06-22 16:07:09.468 | bff244c478b246a88ce57cc3e852d558 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:179 - 查询结果: records=[KeywordDataResponseModel(id=288, title='上海电气与<em>西门子</em>中国签署战略协议', content='上海电气与西门子中国签署战略协议\n\n2025年05月05日 07:56\n\n据上海电气消息，近日，上海电气输配电集团与西门子（中国）有限公司签署战略合作协议。根据协议，双方将紧密围绕国家战略需求，聚焦中低压电气产品和电气自动化系统，强化协同创新，并在数字化及低碳经济等新领域探索新项目开发。此外，双方还将探索更多领域的协同。\n\n（文章来源：界面新闻）', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=295, title='四川工业链博馆亮相2025成都国际工业博览会', content='看起来采集该网页时出现了问题，无法成功获取正文内容。请尝试提供其他网页链接或检查网络连接后再次请求。', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=291, title='“健康中国2025护‘胃’行动”在京启动，多方携手共筑全民胃健康防线', content='看来在尝试获取该网页内容时出现了问题，导致无法正常提取正文。您可以检查该网页是否正常访问，或者稍后再尝试采集。如果问题持续存在，请告知我，我会进一步协助您！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=289, title='<em class="highlight">西门子</em>Xcelerator产业生态西部中心在成都金牛区投运', content='看起来采集该网页时出现了问题，无法成功获取正文内容。您可以稍后尝试重新采集，或者检查链接是否有效。如果有其他需要帮助的地方，请随时告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=293, title='<em>西门子</em>中国工厂首个光储一体化项目投运', content='看起来采集该网页时出现了问题，无法成功获取正文内容。您可以检查链接是否有效，或者尝试在其他时间段重新采集。如果有其他需求，请告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=287, title='公司产品能否用于海上核电，海上风电？中环海陆回应', content='以下是该网页的正文内容：\n\n---\n\n# 公司产品能否用于海上核电，海上风电？中环海陆回应\n\n每日经济新闻\n\n2025-05-09 17:19:43\n\n每经AI快讯，有投资者在投资者互动平台提问：贵公司产品能否用于海上核电，海上风电？\n\n中环海陆（301040.SZ）5月9日在投资者互动平台表示，公司生产的6.0MW以上海上风电基础桩法兰锻件、7.0MW以上转子房法兰锻件供应于西门子、上海电气等知名企业的海上风电项目，具备了给海上风电大兆瓦风机配套相关产品的能力。\n\n（记者 毕陆名）\n\n免责声明：本文内容与数据仅供参考，不构成投资建议，使用前核实。据此操作，风险自担。\n\n如需转载请与《每日经济新闻》报社联系。\n\n未经《每日经济新闻》报社授权，严禁转载或镜像，违者必究。\n\n特别提醒：如果我们使用了您的图片，请作者与 [本站联系](http://www.nbd.com.cn/contact) 索取稿酬。如您不希望作品出现在本站，可联系我们要求撤下您的作品。', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=292, title='投行对黄金最激进预期：年内冲击4000美元！金价居高不下，有年轻人租三金结婚', content='以下是该网页的正文内容：\n\n---\n\n# 投行对黄金最激进预期：年内冲击4000美元！金价居高不下，有年轻人租三金结婚\n\n每日经济新闻\n\n2025-05-09 01:19:32\n\n每经编辑｜陈柯名\n\n5月8日，国际金价出现下跌。现货黄金一度跌超1.5%，截止发稿，下跌0.28%，报3354美元/盎司。\n\nCOMEX黄金一度跌近2%，截至发稿，下跌1%，报3358.1美元/盎司。\n\n**黄金最激进预期：年内冲击4000美元**\n\n值得注意的是，据财联社消息，美国银行的大宗商品分析师对黄金的前景越来越乐观。在美银周三（5月7日）发布的一份报告中，Michael Widmer领导的贵金属团队预计，金价在今年下半年触及每盎司4,000美元的可能性越来越大。\n\n美银的这一最新预测是目前华尔街各大银行中最激进的预测之一。\n\n今年3月，Widmer和他的团队曾预测，到2027年，金价将达到3500美元。而在不到一个月的时间内，金价就达到了这一目标。\n\n**年轻人开始租三金结婚**\n\n此外，国内各大品牌金饰品价格仍然上涨中，周大福、周六福、潮宏基、周大生、六福珠宝等金饰品价格均涨至1040元/克。\n\n据央视网报道，近段时间，金价居高不下，准备结婚的准新人想要凑齐心仪的“三金”首饰是一笔不小的开销。于是，一些年轻人选择以租赁的方式完成婚俗仪式。龙凤盘旋的足金颈链、刻着双喜的手镯、甚至还有黄金制作而成的“结婚证书”……在广州市白云区的这家金饰店，老板专门为可供租赁的“三金”配饰开了个专柜。老板说，随着金价走高，如今，越来越多的人愿意接受这种租“三金”的方式。\n\n据北京商报报道，近期，部分有购置结婚“三金”需求的消费者选择了自己打金或租赁金饰。一家主营金饰租赁业务的商家向北京商报记者表示，租“三金”是近期才出现的形式，“五一”假期是结婚高峰期，不少热门款式被订购空了。上述商家介绍称，租赁黄金首饰的价格一般在30元/克，押金则按黄金大盘价格收取。\n\n**老铺黄金股价涨了16倍**\n\n值得注意的是，随着金价攀升，“黄金界爱马仕”老铺黄金股价迎来大涨。自去年6月份上市以来，老铺黄金凭借快速增长的业绩接连大涨，截至目前，股价涨幅达到1605%。\n\n5月8日，老铺黄金(06181.HK)高开后回落，截至收盘，公司股价报698港元/股，股价上涨1.90%。\n\n公开信息显示，老铺黄金于2024年6月28日在香港挂牌上市，当时以每股40.50港元的IPO发行价募资10.42亿港元。\n\n今年开年，老铺黄金凭借其火热的销售势头成为市场“顶流”：排队5小时才能买到、黄牛日赚1万等消息频繁刷屏。\n\n4月28日，老铺黄金披露2024年度报告。报告期内，公司实现营业总收入85.26亿元，同比增长167.94%；归母净利润14.73亿元，同比增长253.86%；经营活动产生的现金流量净额为-12.28亿元，上年同期为-2919.6万元。\n\n那近期是入手黄金的好时机吗？职业黄金投资分析师吕超认为，从长期投资角度看，黄金具有货币属性和避险属性，能在经济不稳定时起到保值作用。若投资者资产配置中黄金占比较低，可考虑逢低买入。从短期投资角度看，黄金价格波动受多种因素影响，如美元走势、国际贸易局势、美联储政策等。目前金价虽有回调，但这些不确定因素仍存在，若后续出现新的催化剂，如地缘政治紧张、经济数据下滑等，金价可能再次上涨。\n\n**免责声明：本文内容与数据仅供参考，不构成投资建议，使用前核实。据此操作，风险自担。**\n\n**编辑｜陈柯名 杜波 盖源源**\n\n**校对｜金冥羽**\n\n每日经济新闻综合自央视网、财联社、北京商报等\n\n****推荐阅读↓****\n\n****最新！9连发，于东来再回应质疑：可以邀请柴怼怼、宋清辉等来考察！刚刚，永辉超市发公开信支持****\n\n****盘前涨超10%！极氪传来大消息：将被吉利私有化，并于纽交所退市！李书福最新回应****\n\n****最新！巴方称已击落3架印度无人机！两国战机均未侵入对方领空，巴方公布对印回击细节！中方：对印度的军事行动表示遗憾****\n\n---\n\n以上就是文章的核心内容。', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=290, title='围绕国家战略需求 上海电气与<em>西门子</em>中国签署战略协议', content='看起来采集该网页时出现了问题，提示“Failed to wait for the message endpoint”。这可能是由于网络问题或目标网站的加载问题导致的。你可以稍后再尝试采集，或者检查是否有其他限制（如防火墙、网站反爬机制等）影响了采集过程。如果有其他问题，请告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=285, title='国信证券发布<em class="highlight">老板</em>电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放', content='以下是该网页的正文内容：\n\n---\n\n# 国信证券发布老板电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放\n\n每日经济新闻\n\n2025-05-09 14:47:56\n\n每经AI快讯，国信证券5月9日发布研报称，给予老板电器（002508.SZ，最新价：19.82元）优于大市评级。评级理由主要包括：1）烟灶主品类增长稳健，二三品类有所承压；2）零售渠道增长较好；3）名气品牌持续成长，盈利扭亏；4）投入加大，盈利有所下降。风险提示：行业竞争加剧；原材料价格大幅上涨；行业需求恢复不及预期。\n\n---\n\n如需转载，请与《每日经济新闻》报社联系。  \n未经报社授权，严禁转载或镜像，违者必究。', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=284, title='国信证券发布<em>老板</em>电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放', content='国信证券发布研报指出，在地产行业压力下，老板电器经营较为稳健，期待以旧换新需求进一步释放。报告给予老板电器优于大市的评级，主要理由包括：\n\n1. 烟灶主品类增长稳健，但二三品类有所承压。\n2. 零售渠道增长较好。\n3. 名气品牌持续成长，并实现盈利扭亏。\n4. 投入加大导致盈利有所下降。\n\n报告同时提示了风险因素，包括行业竞争加剧、原材料价格大幅上涨以及行业需求恢复不及预期等。\n\n（来源：每日经济新闻）', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[])] page_num=1 page_size=10 total=504 has_next=True
2025-06-22 16:07:09.697 | e8d3a7b12f654032b58b4adf8bf0598e | INFO     | module_admin.controller.keyword_data_controller:get_filter_statistics_public:256 - 获取筛选统计数据成功
2025-06-22 16:07:11.313 | 8881a0fe6bc14c7e9c84b4a104d22b46 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:174 - 查询参数: pageNum=1, pageSize=10, timeFilter=None, actual_start_date=None, actual_end_date=None
2025-06-22 16:07:11.314 | 8881a0fe6bc14c7e9c84b4a104d22b46 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:40 - Service层接收到的查询参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:07:11.314 | 8881a0fe6bc14c7e9c84b4a104d22b46 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:41 - Service层查询对象类型: <class 'module_admin.entity.vo.keyword_data_vo.KeywordDataPageQueryModel'>
2025-06-22 16:07:11.314 | 8881a0fe6bc14c7e9c84b4a104d22b46 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:42 - Service层查询对象属性: {'page_num': 1, 'page_size': 10, 'title': None, 'keyword': None, 'type': None, 'web': None, 'begin_time': None, 'end_time': None, 'platform_types': None, 'sentiment_types': None, 'info_attributes': None, 'web_site': '123', 'time_filter': None}
2025-06-22 16:07:11.315 | 8881a0fe6bc14c7e9c84b4a104d22b46 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:112 - DAO层时间筛选参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:07:11.316 | 8881a0fe6bc14c7e9c84b4a104d22b46 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:182 - 总共构建了 1 个查询条件
2025-06-22 16:07:11.316 | 8881a0fe6bc14c7e9c84b4a104d22b46 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:183 - 查询参数: time_filter=None, platform_types=None, sentiment_types=None
2025-06-22 16:07:11.316 | 8881a0fe6bc14c7e9c84b4a104d22b46 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:187 - 已应用查询条件到SQL查询
2025-06-22 16:07:11.368 | 8881a0fe6bc14c7e9c84b4a104d22b46 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:178 - 查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-06-22 16:07:11.369 | 8881a0fe6bc14c7e9c84b4a104d22b46 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:179 - 查询结果: records=[KeywordDataResponseModel(id=513, title='111', content='11', source_url=None, keywords='22', source='555', time='2025-06-20 11:43:34', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=514, title='258', content='00', source_url=None, keywords='22', source='555', time='2025-06-20 11:43:34', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[])] page_num=1 page_size=10 total=2 has_next=False
2025-06-22 16:07:11.719 | 5fa2b26bac344ed7b6bd4ac589be1bf4 | INFO     | module_admin.controller.keyword_data_controller:get_filter_statistics_public:256 - 获取筛选统计数据成功
2025-06-22 16:07:12.194 | b2b5ef001aba46b9b4d0020e744f1525 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:174 - 查询参数: pageNum=1, pageSize=10, timeFilter=None, actual_start_date=None, actual_end_date=None
2025-06-22 16:07:12.195 | b2b5ef001aba46b9b4d0020e744f1525 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:40 - Service层接收到的查询参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:07:12.196 | b2b5ef001aba46b9b4d0020e744f1525 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:41 - Service层查询对象类型: <class 'module_admin.entity.vo.keyword_data_vo.KeywordDataPageQueryModel'>
2025-06-22 16:07:12.196 | b2b5ef001aba46b9b4d0020e744f1525 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:42 - Service层查询对象属性: {'page_num': 1, 'page_size': 10, 'title': None, 'keyword': None, 'type': None, 'web': None, 'begin_time': None, 'end_time': None, 'platform_types': None, 'sentiment_types': None, 'info_attributes': None, 'web_site': None, 'time_filter': None}
2025-06-22 16:07:12.196 | b2b5ef001aba46b9b4d0020e744f1525 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:112 - DAO层时间筛选参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:07:12.196 | b2b5ef001aba46b9b4d0020e744f1525 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:182 - 总共构建了 0 个查询条件
2025-06-22 16:07:12.197 | b2b5ef001aba46b9b4d0020e744f1525 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:183 - 查询参数: time_filter=None, platform_types=None, sentiment_types=None
2025-06-22 16:07:12.197 | b2b5ef001aba46b9b4d0020e744f1525 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:189 - 没有查询条件，将返回所有数据
2025-06-22 16:07:12.247 | b2b5ef001aba46b9b4d0020e744f1525 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:178 - 查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-06-22 16:07:12.248 | b2b5ef001aba46b9b4d0020e744f1525 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:179 - 查询结果: records=[KeywordDataResponseModel(id=288, title='上海电气与<em>西门子</em>中国签署战略协议', content='上海电气与西门子中国签署战略协议\n\n2025年05月05日 07:56\n\n据上海电气消息，近日，上海电气输配电集团与西门子（中国）有限公司签署战略合作协议。根据协议，双方将紧密围绕国家战略需求，聚焦中低压电气产品和电气自动化系统，强化协同创新，并在数字化及低碳经济等新领域探索新项目开发。此外，双方还将探索更多领域的协同。\n\n（文章来源：界面新闻）', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=295, title='四川工业链博馆亮相2025成都国际工业博览会', content='看起来采集该网页时出现了问题，无法成功获取正文内容。请尝试提供其他网页链接或检查网络连接后再次请求。', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=291, title='“健康中国2025护‘胃’行动”在京启动，多方携手共筑全民胃健康防线', content='看来在尝试获取该网页内容时出现了问题，导致无法正常提取正文。您可以检查该网页是否正常访问，或者稍后再尝试采集。如果问题持续存在，请告知我，我会进一步协助您！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=289, title='<em class="highlight">西门子</em>Xcelerator产业生态西部中心在成都金牛区投运', content='看起来采集该网页时出现了问题，无法成功获取正文内容。您可以稍后尝试重新采集，或者检查链接是否有效。如果有其他需要帮助的地方，请随时告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=293, title='<em>西门子</em>中国工厂首个光储一体化项目投运', content='看起来采集该网页时出现了问题，无法成功获取正文内容。您可以检查链接是否有效，或者尝试在其他时间段重新采集。如果有其他需求，请告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=287, title='公司产品能否用于海上核电，海上风电？中环海陆回应', content='以下是该网页的正文内容：\n\n---\n\n# 公司产品能否用于海上核电，海上风电？中环海陆回应\n\n每日经济新闻\n\n2025-05-09 17:19:43\n\n每经AI快讯，有投资者在投资者互动平台提问：贵公司产品能否用于海上核电，海上风电？\n\n中环海陆（301040.SZ）5月9日在投资者互动平台表示，公司生产的6.0MW以上海上风电基础桩法兰锻件、7.0MW以上转子房法兰锻件供应于西门子、上海电气等知名企业的海上风电项目，具备了给海上风电大兆瓦风机配套相关产品的能力。\n\n（记者 毕陆名）\n\n免责声明：本文内容与数据仅供参考，不构成投资建议，使用前核实。据此操作，风险自担。\n\n如需转载请与《每日经济新闻》报社联系。\n\n未经《每日经济新闻》报社授权，严禁转载或镜像，违者必究。\n\n特别提醒：如果我们使用了您的图片，请作者与 [本站联系](http://www.nbd.com.cn/contact) 索取稿酬。如您不希望作品出现在本站，可联系我们要求撤下您的作品。', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=292, title='投行对黄金最激进预期：年内冲击4000美元！金价居高不下，有年轻人租三金结婚', content='以下是该网页的正文内容：\n\n---\n\n# 投行对黄金最激进预期：年内冲击4000美元！金价居高不下，有年轻人租三金结婚\n\n每日经济新闻\n\n2025-05-09 01:19:32\n\n每经编辑｜陈柯名\n\n5月8日，国际金价出现下跌。现货黄金一度跌超1.5%，截止发稿，下跌0.28%，报3354美元/盎司。\n\nCOMEX黄金一度跌近2%，截至发稿，下跌1%，报3358.1美元/盎司。\n\n**黄金最激进预期：年内冲击4000美元**\n\n值得注意的是，据财联社消息，美国银行的大宗商品分析师对黄金的前景越来越乐观。在美银周三（5月7日）发布的一份报告中，Michael Widmer领导的贵金属团队预计，金价在今年下半年触及每盎司4,000美元的可能性越来越大。\n\n美银的这一最新预测是目前华尔街各大银行中最激进的预测之一。\n\n今年3月，Widmer和他的团队曾预测，到2027年，金价将达到3500美元。而在不到一个月的时间内，金价就达到了这一目标。\n\n**年轻人开始租三金结婚**\n\n此外，国内各大品牌金饰品价格仍然上涨中，周大福、周六福、潮宏基、周大生、六福珠宝等金饰品价格均涨至1040元/克。\n\n据央视网报道，近段时间，金价居高不下，准备结婚的准新人想要凑齐心仪的“三金”首饰是一笔不小的开销。于是，一些年轻人选择以租赁的方式完成婚俗仪式。龙凤盘旋的足金颈链、刻着双喜的手镯、甚至还有黄金制作而成的“结婚证书”……在广州市白云区的这家金饰店，老板专门为可供租赁的“三金”配饰开了个专柜。老板说，随着金价走高，如今，越来越多的人愿意接受这种租“三金”的方式。\n\n据北京商报报道，近期，部分有购置结婚“三金”需求的消费者选择了自己打金或租赁金饰。一家主营金饰租赁业务的商家向北京商报记者表示，租“三金”是近期才出现的形式，“五一”假期是结婚高峰期，不少热门款式被订购空了。上述商家介绍称，租赁黄金首饰的价格一般在30元/克，押金则按黄金大盘价格收取。\n\n**老铺黄金股价涨了16倍**\n\n值得注意的是，随着金价攀升，“黄金界爱马仕”老铺黄金股价迎来大涨。自去年6月份上市以来，老铺黄金凭借快速增长的业绩接连大涨，截至目前，股价涨幅达到1605%。\n\n5月8日，老铺黄金(06181.HK)高开后回落，截至收盘，公司股价报698港元/股，股价上涨1.90%。\n\n公开信息显示，老铺黄金于2024年6月28日在香港挂牌上市，当时以每股40.50港元的IPO发行价募资10.42亿港元。\n\n今年开年，老铺黄金凭借其火热的销售势头成为市场“顶流”：排队5小时才能买到、黄牛日赚1万等消息频繁刷屏。\n\n4月28日，老铺黄金披露2024年度报告。报告期内，公司实现营业总收入85.26亿元，同比增长167.94%；归母净利润14.73亿元，同比增长253.86%；经营活动产生的现金流量净额为-12.28亿元，上年同期为-2919.6万元。\n\n那近期是入手黄金的好时机吗？职业黄金投资分析师吕超认为，从长期投资角度看，黄金具有货币属性和避险属性，能在经济不稳定时起到保值作用。若投资者资产配置中黄金占比较低，可考虑逢低买入。从短期投资角度看，黄金价格波动受多种因素影响，如美元走势、国际贸易局势、美联储政策等。目前金价虽有回调，但这些不确定因素仍存在，若后续出现新的催化剂，如地缘政治紧张、经济数据下滑等，金价可能再次上涨。\n\n**免责声明：本文内容与数据仅供参考，不构成投资建议，使用前核实。据此操作，风险自担。**\n\n**编辑｜陈柯名 杜波 盖源源**\n\n**校对｜金冥羽**\n\n每日经济新闻综合自央视网、财联社、北京商报等\n\n****推荐阅读↓****\n\n****最新！9连发，于东来再回应质疑：可以邀请柴怼怼、宋清辉等来考察！刚刚，永辉超市发公开信支持****\n\n****盘前涨超10%！极氪传来大消息：将被吉利私有化，并于纽交所退市！李书福最新回应****\n\n****最新！巴方称已击落3架印度无人机！两国战机均未侵入对方领空，巴方公布对印回击细节！中方：对印度的军事行动表示遗憾****\n\n---\n\n以上就是文章的核心内容。', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=290, title='围绕国家战略需求 上海电气与<em>西门子</em>中国签署战略协议', content='看起来采集该网页时出现了问题，提示“Failed to wait for the message endpoint”。这可能是由于网络问题或目标网站的加载问题导致的。你可以稍后再尝试采集，或者检查是否有其他限制（如防火墙、网站反爬机制等）影响了采集过程。如果有其他问题，请告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=285, title='国信证券发布<em class="highlight">老板</em>电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放', content='以下是该网页的正文内容：\n\n---\n\n# 国信证券发布老板电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放\n\n每日经济新闻\n\n2025-05-09 14:47:56\n\n每经AI快讯，国信证券5月9日发布研报称，给予老板电器（002508.SZ，最新价：19.82元）优于大市评级。评级理由主要包括：1）烟灶主品类增长稳健，二三品类有所承压；2）零售渠道增长较好；3）名气品牌持续成长，盈利扭亏；4）投入加大，盈利有所下降。风险提示：行业竞争加剧；原材料价格大幅上涨；行业需求恢复不及预期。\n\n---\n\n如需转载，请与《每日经济新闻》报社联系。  \n未经报社授权，严禁转载或镜像，违者必究。', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=284, title='国信证券发布<em>老板</em>电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放', content='国信证券发布研报指出，在地产行业压力下，老板电器经营较为稳健，期待以旧换新需求进一步释放。报告给予老板电器优于大市的评级，主要理由包括：\n\n1. 烟灶主品类增长稳健，但二三品类有所承压。\n2. 零售渠道增长较好。\n3. 名气品牌持续成长，并实现盈利扭亏。\n4. 投入加大导致盈利有所下降。\n\n报告同时提示了风险因素，包括行业竞争加剧、原材料价格大幅上涨以及行业需求恢复不及预期等。\n\n（来源：每日经济新闻）', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[])] page_num=1 page_size=10 total=504 has_next=True
2025-06-22 16:07:12.782 | 4a745ad466cc435597b32661ff508807 | INFO     | module_admin.controller.keyword_data_controller:get_filter_statistics_public:256 - 获取筛选统计数据成功
2025-06-22 16:07:12.868 | 329959a1956c467591306ffc8d01e6cd | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:174 - 查询参数: pageNum=1, pageSize=10, timeFilter=None, actual_start_date=None, actual_end_date=None
2025-06-22 16:07:12.868 | 329959a1956c467591306ffc8d01e6cd | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:40 - Service层接收到的查询参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:07:12.868 | 329959a1956c467591306ffc8d01e6cd | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:41 - Service层查询对象类型: <class 'module_admin.entity.vo.keyword_data_vo.KeywordDataPageQueryModel'>
2025-06-22 16:07:12.868 | 329959a1956c467591306ffc8d01e6cd | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:42 - Service层查询对象属性: {'page_num': 1, 'page_size': 10, 'title': None, 'keyword': None, 'type': None, 'web': None, 'begin_time': None, 'end_time': None, 'platform_types': None, 'sentiment_types': None, 'info_attributes': None, 'web_site': '东方财富', 'time_filter': None}
2025-06-22 16:07:12.869 | 329959a1956c467591306ffc8d01e6cd | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:112 - DAO层时间筛选参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:07:12.870 | 329959a1956c467591306ffc8d01e6cd | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:182 - 总共构建了 1 个查询条件
2025-06-22 16:07:12.870 | 329959a1956c467591306ffc8d01e6cd | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:183 - 查询参数: time_filter=None, platform_types=None, sentiment_types=None
2025-06-22 16:07:12.871 | 329959a1956c467591306ffc8d01e6cd | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:187 - 已应用查询条件到SQL查询
2025-06-22 16:07:12.945 | 329959a1956c467591306ffc8d01e6cd | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:178 - 查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-06-22 16:07:12.946 | 329959a1956c467591306ffc8d01e6cd | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:179 - 查询结果: records=[KeywordDataResponseModel(id=293, title='<em>西门子</em>中国工厂首个光储一体化项目投运', content='看起来采集该网页时出现了问题，无法成功获取正文内容。您可以检查链接是否有效，或者尝试在其他时间段重新采集。如果有其他需求，请告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=315, title='突然坠机！<em>西门子</em>高管一家5口遇难', content='看起来在尝试抓取该网页时遇到了一些问题，导致无法成功获取内容。您可以稍后再试或者提供更多的信息以便进一步处理。如果有其他链接或者内容需要帮助，请随时告知！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=308, title='以多维价值为引擎，<em>西门子</em>医疗助力非公医疗高质量升级', content='以多维价值为引擎，西门子医疗助力非公医疗高质量升级\n\n在过去十年里，我国不断推出一系列政策，大力推动非公医疗领域的成长。伴随着社会财富的积累和消费水平的提升，公众对于个性化、高效的医疗服务需求日益旺盛，使得非公医疗事业进入高速发展期。数据显示，自2003年至2023年，我国民营医院的数量已从2000余所激增至2.66万所，其在我国医院总数中的比例也升至近70%。\n\n在医疗行业快速变革的背景下，非公医疗机构面临转型升级的关键期。一方面，医保支付改革和监管政策趋严对机构的规范化运营提出更高要求；另一方面，公立医院通过医联体建设和特需服务延伸，使市场竞争更趋激烈，非公医疗机构面临服务升级与成本管控的双重考验。\n\n非公医疗行业正在经历一场深刻的变革，探索如何充分挖掘机构的发展潜力，全面提升竞争力，已成为行业发展的关键议题。为了协助非公医疗同仁探索发展路径，西门子医疗在上海举办了“非公医疗领袖峰会”。\n\n作为一家在中国市场耕耘超过三十年的跨国医疗器械企业，西门子医疗凭借其多样化的产品组合和模块化的增值服务，全方位助力医疗机构在运营效率、疾病诊疗以及数字化转型等方面的创新与变革。在全球范围内，西门子医疗已与众多医疗机构建立了长期的价值合作关系，通过构建专科差异化优势、增强医院运营能力、整合全球优质资源等策略，助力机构提升服务品质，增强竞争力。西门子医疗所积累的丰富经验和成功案例，也将为我国更多非公医疗机构的高质量创新发展提供借鉴与动力。\n\n构筑非公医疗竞争力“护城河”\n\n在公立医疗体系不断深化改革的大潮中，非公立医疗机构作为我国医疗服务领域的重要补充力量，根据弗若斯特沙利文的数据，我国私立医疗服务机构的收益从2019年的6016亿元增至2023年的11637亿元，年复合增长率高达17.9%，预计到2028年将增至19581亿元。\n\n随着我国富裕人群的扩大和可支配收入的提高，在商业医疗保险迅猛发展的推动下，公众对于高端和个性化医疗服务的需求也日益增长。弗若斯特沙利文预测，到2028年，我国中高端医疗服务市场的总收入将达到10206亿元，其中，私立中高端医疗服务机构的总收入预计达到7221亿元，占比超过七成。\n\n面对市场的这一发展趋势和需求变化，非公立医疗机构过去依赖的“规模扩张”式的粗放型增长模式已无法满足当前市场对高品质、差异化医疗服务的追求，“精准破局”的战略已成为行业发展的必由之路。在这一过程中，高端医疗设备的采购与应用成为关键性的突破口。\n\n对此，西门子医疗大中华区总裁王皓分析说：“在采购超高端设备与解决方案用于解决复杂的医疗难题时，相较于公立医疗机构，非公立医疗机构受限较小，在实现差异化方面拥有多种模式，尤其在临床应用层面拥有广阔的操作空间。西门子医疗以其独特的技术优势，为非公立机构提供了实现差异化发展的强大助力，这正是我们核心能力的体现。”\n\n在设备方面，西门子医疗为肝癌、肺癌、乳腺癌等常见癌症推出了覆盖早期筛查、精确诊断、个性化治疗以及预后管理的全生命周期解决方案。凭借西门子医疗和瓦里安医疗在肿瘤产品方面的全球领先优势，非公立医疗机构可以得到从影像诊断到放射治疗的全面技术支持。\n\n在心脑健康领域，西门子医疗推出的“心脑联合筛查”方案，通过整合低剂量CT和快速磁共振扫描，实现了心脑的一站式联合检查。例如，快速磁共振序列能够在短短15分钟内完成心脏和脑部的精确扫描，并生成详尽的风险评估报告。\n\n此外，西门子医疗利用AI赋能的精准影像技术，建立了从早期筛查到治疗效果评估的阿尔茨海默病全周期管理闭环。该技术能够提前10-15年发现阿尔茨海默病的可能性，为早期干预提供了宝贵的时间窗口；同时，智能磁共振平台能够实时量化评估治疗效果，为阿尔茨海默病治疗的疗效监测提供了客观的影像依据。\n\n“得益于我们产品在定位和性能等多方面的优势，西门子医疗产品在高端民营医疗机构具有较大的市场竞争优势并广受认可。对于更看重性价比的基层医疗机构，我们也提供了标准化的服务包。”王皓补充道。\n\n这种分层次的赋能模式不仅符合民营医疗补充公立医院的定位，也增强了高端民营医疗机构的差异化竞争力。\n\n重塑非公医疗高质量发展生态圈\n\n交银国际研究报告显示，我国政府将继续鼓励民营医院的发展，因其对于弥补医疗需求缺口、适度提升市场竞争力和提高医疗资源利用效率具有不可替代的作用，这一趋势预计将进一步增强。伴随着DRG/DIP付费改革的不断深入和规则优化，那些运营效率高、医保收入占比适中、药耗占比低下的医院将受到青睐，而那些较多使用创新药品和医疗器械的医院有望获得边际收益。\n\n不断探索高效的运营模式，不仅是非公立医疗实现可持续发展的本质需求，更是构建及巩固核心竞争力的关键路径。这要求非公医疗在经营管理、市场运营、人才培养等方面发挥综合优势，以提升患者体验为核心，推动医疗服务的持续繁荣。\n\n在此基础上，西门子医疗已从单纯的设备和产品供应商，逐步转型为提供全方位产品与服务的综合提供商，以适应非公医疗的发展需求。王皓指出，西门子医疗所倡导的“价值合作”模式，涵盖了“效率提升、人才培养、患者体验”三大维度的运营管理指导和相应解决方案，旨在助力非公医院提高整体医疗服务水平，同时为当地患者提供更加优质、高效、便捷的医疗服务。\n\n在机构运营方面，西门子医疗运用最新的绿色低碳技术（如DryCool磁共振平台）和人工智能技术，助力非公医疗机构提升服务质量、优化工作流程、优化结构布局，显著提高了医院的诊疗效率。\n\n在人才培养方面，西门子医疗充分利用行业领先的数字化技术和5G远程解决方案，以及丰富的专家资源，为不同机构和不同类型的人才提供远程指导和在线教学，有效提升专业人才的技能水平；同时，与合作伙伴共同探索“产学研用”四位一体的定向培养计划，打造医学影像人才培养的高地。\n\n在提升患者体验方面，西门子医疗在客户新建医院时提供包括医院布局、空间规划、工作流程、患者动线在内的全方位专业服务，以赋能医院提供高质量、高效率的诊疗服务；运用cVRT（实影渲染）技术，帮助医生与患者及其家属更有效地沟通病情和治疗方案。\n\n王皓认为，“价值合作模式”的成熟，得益于西门子医疗在全球医疗体系中的深厚积淀。在中国，西门子医疗始终秉持这一理念，从单一的产品和服务提供商转型为全方位的赋能者，通过先进技术助力非公医疗机构应对疾病挑战，利用数字化工具提升管理和绩效，并通过国际合作与交流为民营医院品牌建设和患者引流赋能。\n\n以与上海嘉会国际医院的合作为例，西门子医疗不仅通过运营咨询及管理设备服务助力嘉会医疗实现精细化管理、提高资产效率，还利用元宇宙教研平台和技术将传统医学影像转化为沉浸式真实场景，促进医患交流和跨学科互动。双方还通过科研合作的形式充分发挥双方在业界的技术和资源优势，助力科研人才培养、学科建设和临床研究发展。\n\n全球视野下，非公医疗的进阶之路\n\n在全球医疗价值链重塑的浪潮之下，我国非公医疗的国际化进程已由曾经的“可选项”转变为当前的“必选项”。这既是我国医疗机构突破发展瓶颈的关键战略，也是全球医疗资源优化配置的必然方向。\n\n一方面，发达国家市场需求不断升级。伴随着欧美等发达国家人口老龄化问题的加剧，对慢性病管理、康复护理、高端医疗设备及创新药物的需求日益增长。特别是，如美国等发达国家的人均医疗支出远超我国，反映出其较高的支付能力以及对创新药物的需求潜力。\n\n另一方面，新兴市场的需求潜力同样巨大。东南亚、中东、非洲等地区医疗基础设施的建设步伐加快，对基础医疗设备、药品的需求强劲。随着经济的增长和人民健康意识的提升，对医疗美容和健康管理服务的需求也呈现出稳定上升的态势。\n\n国际交流与合作不仅是推动医疗技术创新、资源优化配置和培养全球化视野人才的有效途径，更是非公医疗对接国际先进理念、探索发展新模式的关键窗口。\n\n在这场全球化的竞争与合作中，西门子医疗以其百年积累的全球网络，为非公医疗机构的国际化发展提供了独特的范例，积极推动非公客户在临床进步、科研协作、科室建设、医院管理等多个方面向国际先进水平看齐，全面提升业务实力及影响力。\n\n据悉，西安大兴医院作为国内首家全方位价值合作伙伴，与西门子医疗在八大领域展开了长达八年的深度合作。西门子医疗通过院士工作站创新转化赋能服务，与西安大兴医院共同构建创新生态，激发创新活力。\n\n同时，提供的国际交流及定制化高管课程，专注于国际医院管理研究前沿，通过系统化、专业化、国际化的培训，提升了医院高层的管理能力。\n\n在这场全球化征途中，西门子医疗的实践揭示了深层规律：国际化不仅是市场的拓展，更是通过技术融合、标准共建、生态共享，实现医疗价值的全球再分配。未来的医疗创新将是无国界的协同成果。\n\n在这条充满挑战的进阶之路上，西门子医疗正在助力我国非公医疗航行于全球医疗创新的深海之中。', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=296, title='<em>西门子</em>中国工厂首个光储一体化项目成功投运', content='看起来在尝试获取该网页内容时遇到了一些问题，导致无法直接提取正文。我们可以尝试其他方法或工具来采集这个网页的内容。您是否希望我使用另一种方式进行尝试？', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=312, title='“<em>西门子</em>高管一家遇难”，特朗普发声', content='看起来在尝试采集该网页时遇到了问题，无法成功获取正文内容。可能的原因包括网页加载问题或目标页面的访问限制。如果有其他可用的链接或具体的文本内容需求，请提供更多信息或尝试提供备用链接以便进一步协助。', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=303, title='找布、制衣、卖衫，广东<em>老板</em>用上AI做全球生意', content='找布、制衣、卖衫，广东老板用上AI做全球生意\n\n2025年05月07日 11:03\n\n来源：南方都市报\n\n在中大纺织商圈，面料销售专员几乎人手一个AI“小饭盒”，找布从几天缩短到2分钟，面料商户的成交刷刷提速；在广州海珠的“制衣村”，每几分钟就有新的“爆款”设计出炉；在销售环节，跨境直播也不再需要主播熬更守夜，数字人成为了“赛博劳模”，可以用120多种语言实现7x24小时无间断直播。\n\n如今，AI的触角伸到了各行各业，让生意人有了自己的“军师”，助力小生意蓬勃发展，燃起新的发展引擎。\n\n**AI“找布机器人”精准匹配面料**\n\n广州的三月，气温高得如同夏天，跟气温一样高的，是中大纺织商圈的火热交易。商圈内外，小货车、电动车首尾相接，运着各色布料进进出出，往来客商摩肩接踵，伴随着议价声与缝纫机的嗡鸣。商圈占地5平方公里，近2万家经营商户，这里是中国的“米兰”。与传统服装面料交易市场不一样的是，这里很多商户手里都有一个秘密武器，他们亲切地叫它AI“小饭盒”。\n\n拿着样衣找布曾经是布行商家们最苦恼的一环，为找一块布，奔忙数万步一点也不稀奇。但今天，AI技术让找布从几天缩短到了2分钟。在中大纺织商圈的新宏伟布业，南都记者见到了AI“小饭盒”的真容，这个很像POS机的小机子就是来自致景科技的“对布机器人”。\n\n新宏伟布业销售专员郑创彬现场随手拿起一件衣服，向南都记者展示对布机器人的实力。用对布机器人拍完衣服正反面的两张照片，“嘀——”的一声后，机器屏幕上就已经精准匹配到了这件衣服的面料，针织、梭织、颜色、纹路、成分比例、克重、密度、是否在本站仓库等信息一应俱全。\n\n“一开始来店里工作时，对档口、自家库房的面料还不是很熟悉，有几千个品种、几千个色卡，一时间记不了那么多，肯定要通过这个对布机器人来操作的，不能让客户觉得你效率太慢了。”郑创彬告诉南都记者。\n\n新宏伟布业的老板罗伟槟从业11年了，作为一个中大纺织商圈的“老江湖”，他见证了对布机器人对面料人的改变。他告诉南都记者，传统的找布甚至是一个专门的行当，需要销售出去外面一家家档口找，靠人工翻阅色卡、肉眼识别。“这么大的中大纺织商圈，这样人工找，可能会好几天都找不到布，导致订单丢失。”罗伟槟回忆道：“但现在，几分钟就能完成。”\n\n在罗伟槟看来，节省的时间就是金钱，除此外，对布机器人也给他带来了新的商机。据他介绍，新宏伟布业是生产型厂家，除了档口，还有自己的面料工厂，他们跟致景科技合作后，将产品都录入在“百布”成品布交易服务平台中。罗伟槟说：“以前客户来面料市场找布，先走到哪一家找到布，或者哪家营销做得好就去哪家购买了，但是现在客户用对布机器人找布，一扫出来就有我们能供应的面料，他就能直接联系找到我们，让物理空间制造的信息差不存在了。”对他来说，这就是一种“抢占先机”——与百布合作多年，他们的质量和价格都有优势，下游客户选购得多，销量也好，所以就会排在首页，自然能持续获得商机。\n\n不仅如此，有了AI帮忙，罗伟槟和他的团队能腾出更多的人手去开发新品种、新面料了。他们随时接收客户反馈的信息，研究市场上最新的面料流行趋势，比如最近抢手的速干、抗皱、抗菌、有冰凉感的黑科技面料，提前投入开发，抢得更多商机。\n\n作为面料行业的新手销售，郑创彬也因为AI帮忙，加速成长。“这就是我快速成为面料专家的秘密武器。”他说，“对布机器人里面有很多面料的参数和介绍，足不出店就能学习了解各种布料的情况，好像一座富矿。以前在这个行当成为专家可能要学个五六年，但现在，我觉得用不了太久，就能掌握了。”他信心满满。\n\n**AI“设计大脑”几秒钟出新款**\n\n在时尚之都广州，拥有全国80%的高端男装品牌，女装卖到全球百余个国家，近30年来，广州服装产业产量和出口额一直排在全国首位，如今，这样一个“传统”的服装制衣行业正在悄悄地长出AI大脑。走入海珠区大塘一片生活气息极浓的老社区，南都记者来到一座不太起眼的小创业园区，在这里，坐落着好几家服装公司和它们的“小车间”，那些源源不断销往海外的“爆款”女装就在这里诞生。\n\n从业20多年的李喜民，是广州金云服饰有限公司的总经理，江湖人称“喜哥”。“我们这属于老服装人用起了新技术，老树也发出了新芽。”提到AI，喜哥的脸上洋溢出一种兴奋：“用上了AI大脑，那真的是‘质的飞跃！’”他口中的“AI大脑”是致景科技一款智能设计开款系统“Fashion Mind”（以下简称FM系统），依托了致景服装AI大模型。据喜哥介绍，FM系统有“图生图、文生图”的功能，只要设计师上传一张设计参考图，或输入设计标签，几分钟能设计出一款新衣服，并生成一组带有背景、不同姿势的照片级营销图，直接就能用。\n\n在喜哥这样的传统服装人看来，引入AI工具是他们跨出了勇于吃螃蟹的一步，而这也让他实实在在地尝到甜头：用了FM系统，我们的开款效率提升了10倍。\n\n以前设计打版有平面设计、平面制版、立体剪裁、面辅料挑选、服装裁剪、模特试穿、拍图上架等一系列流程，需要主设计师、副设计师、设计师助理、采购等一整套团队配合完成，整个逻辑链条很长。如果加上线下沟通、反复修改等过程，从设计、打版到样衣制作要花15到20天。“现在，这些工作3-5天就能做完。设计师在用FM系统开款的时候就可以智能匹配面料，还能同时完成花型设计，在此之前，花型也是需要专业设计师来单独完成的。此外，FM系统还能自主选择模特人种和风格、适配服装场景，整个能效都在提质加速。”\n\n据他介绍，如今团队的设计师主要在做一些挖掘流行趋势，研究消费者喜好的一些工作，比如对时尚的预测，观察未来几个月会流行什么色彩、面料，会跑出怎样的爆款。喜哥感慨道：“真的不一样了。以前前端工作会比较耗时耗力，现在既轻巧又快。”\n\n“做服装电商拼的就是一个‘快’字，我们的时效很快的，反应快，上新快，转化率自然也快起来。”据他介绍，不少品牌店直接拿“AI样衣”去直播，图片上架线上店铺进行销售，根据预售情况再下单生产，卖多少产多少，大大减少了库存成本。谈到对AI工具未来的期待，喜哥说，“未来希望AI能普及实现个性化定制，这样可以更好的满足消费者个性化需求，提升品牌竞争力。”\n\n在时尚产业，数字化转型升级、AI工具辅助创意设计成为趋势。根据2025年广州市高质量发展大会发布，广州计划用五年时间集中资源力量重点攻坚，奋力打造4个5000亿级产业集群，时尚消费品产业便是其中之一。以AI技术赋能，致景科技在海珠区打造了时尚快反基地，喜哥的公司就是其中一家“先吃螃蟹者”，也是海珠区纺织服装转型升级一个先行先试的缩影，AI+都市工业的根在海珠越扎越深。\n\n**中国跨境商家靠AI技术卖全球**\n\n在销售环节， 技术“浓度”也在重塑商业生态。根据2025年广州市政府工作报告数据，2024年广州电商直播、即时零售等新消费模式不断发展，直播电商零售额5171亿元，位居全国第一。\n\n在广州，AI选品、虚拟主播、VR全景直播正在深度渗透，技术驱动的新质生产力正改写“广货卖全球”的路径。沙河档口的爆款服饰可以实现72小时发往全球，番禺的岭南电商园建设起“OMG网红直播街”“上河里国潮街”等主题街区，吸引超3000万粉丝量网红及MCN机构入驻，形成“场景化直播+跨境选品”生态。\n\n值得注意的是，如今的跨境直播卖货主播中，有了不少数字人的身影，他们可以用120多种语言实现7x24小时无间断直播，广东制造的出海故事，似乎挣脱了人类生物钟的枷锁。\n\n一位阿里国际站商家告诉南都记者，用数字人直播抢占夜间时段流量，比普通真人主播转化更高。“以前用业务真人直播，3-4小时，单场观看量仅100多人，评论个位数。”该商家表示，“而刚一用上数字人直播，就有2400多人的单场观看量、百余条评论，TM转化率超5%，马上收获36个客户。”\n\n据万兴科技旗下数字人营销视频创作工具“万兴播爆”业务负责人Dour介绍，曾有几个阿里国际站客户向其反馈称，使用数字人，一个季度可以完成高达1700-2000小时的直播时长，相当于平均每天直播17个小时。\n\n一位TikTok Shop商家小齐也是跨境电商直播数字人的尝鲜者。据他介绍，拍摄一条外国人真人出镜的短视频，工期在7-10天左右，其中还有很多浪费人力时间成本的来回沟通，费用也很高。而数字人解决方案就显得省心多了。从成本上说，主流厂商的直播类产品年包费用基本在2万-6万元左右，而视频类产品每分钟制作成本则在5元-30元之间。Dour也表示，他曾和一位印尼铺货型商家打过交道，该商家只花了几千元就做出上千条数字人视频，用的都是本土味十足的印尼语言，大范围地进行矩阵投放。\n\n此外，面对当前风云变幻的外贸形势，中国跨境商家也有自己的应对方式，他们越来越善于使用AI等数字化手段提升自身竞争力，开创第二增长曲线。广州速鲨网络科技创始人张海涵是一名80后创业者，是一个典型的从淘宝店家转型过来的外贸创业者，在数字化技术的助力下，从2023年至今，他的公司都是希音的年度最佳供应商。\n\n2024年起，他跑上了AI的驱动新赛道，搭建Pod（按需印刷）跨境电商平台PrintFash，主打让海外网红们可一键下单、在线付款，通过AI技术赋能更个性化的小单定制，把广东T恤卖得更有“含金量”。同时，他通过万里汇贸易履约及交易保障产品World Trade的API接口，实现了订单的全自动创建、管理与发送，智能履约，不需要人工干预。\n\n“原本最短也要15天的交货流程现在缩短到了最快2天。”张海涵告诉南都记者，“接入万里汇之后，每年大概能为我们节省40%的支付成本。更重要的是，资金结汇后可以直接付给我们的珠三角上游供应链工厂，可以形成完整的一体化贸易闭环，进一步提升资金运转效率。”在他看来，AI工具的引入，不仅增强了与客户之间的黏性，也给外贸业务带来了更稳定、持续的增长动力。接下来，他还将考虑通过万里汇的中非解决方案，尝试开拓非洲新市场。\n\n[想炒股，先开户！选东方财富证券，行情交易一个APP搞定>>]\n\n（文章来源：南方都市报）\n\n郑重声明：东方财富发布此内容旨在传播更多信息，与本站立场无关，不构成投资建议。据此操作，风险自担。', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=284, title='国信证券发布<em>老板</em>电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放', content='国信证券发布研报指出，在地产行业压力下，老板电器经营较为稳健，期待以旧换新需求进一步释放。报告给予老板电器优于大市的评级，主要理由包括：\n\n1. 烟灶主品类增长稳健，但二三品类有所承压。\n2. 零售渠道增长较好。\n3. 名气品牌持续成长，并实现盈利扭亏。\n4. 投入加大导致盈利有所下降。\n\n报告同时提示了风险因素，包括行业竞争加剧、原材料价格大幅上涨以及行业需求恢复不及预期等。\n\n（来源：每日经济新闻）', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=288, title='上海电气与<em>西门子</em>中国签署战略协议', content='上海电气与西门子中国签署战略协议\n\n2025年05月05日 07:56\n\n据上海电气消息，近日，上海电气输配电集团与西门子（中国）有限公司签署战略合作协议。根据协议，双方将紧密围绕国家战略需求，聚焦中低压电气产品和电气自动化系统，强化协同创新，并在数字化及低碳经济等新领域探索新项目开发。此外，双方还将探索更多领域的协同。\n\n（文章来源：界面新闻）', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=309, title='15亿身家的绍兴<em>老板</em>，豪赌陪伴机器人', content='以下是该网页的正文内容：\n\n---\n\n**15亿身家的绍兴老板，豪赌陪伴机器人**\n\n2025年05月06日 15:09  \n来源：21世纪经济报道  \n\n绍兴老板朱志刚，转行发力机器人。  \n\n4月末，他控制的汤姆猫公告，去年净亏8.6亿元。这家市值170多亿的公司，计提了约9亿的商誉减值。  \n\n管理层特意提到，2024年底，汤姆猫AI语音情感陪伴机器人上线售卖，“有望为业绩增长开辟新路径”。  \n\n“未来计划推出其他形态、场景的机器人。”公司证券事务部向《21CBR》记者表示，汤姆猫家族包含6个IP角色，将尽快推出不同类型的机器人。  \n\n猛攻AI业务外，朱志刚团队多线并进，致力于将手头IP变现，还在全国各地建设主题乐园。  \n\n### 主打陪伴  \n\n最新年报封面，是朱志刚寄予厚望的一款高18厘米的“AI玩具”。  \n\n这款AI情感陪伴机器人，售价1499元起，使用汤姆猫经典IP，采用肤感硅胶材质。其头部可以120°转动，耳朵与嘴巴可以随对话、动作活动，眼睛内置20多种表情。“让每一个拥抱都安心，让你的童年多个伙伴。”公司介绍。  \n\n功能方面，其搭载公司自研的“技术中间层”与定制的“情感陪伴垂直模型”，同时调用豆包、DeepSeek等模型的部分能力。接收用户输入后，“技术中间层”按需调用不同大模型，比如在意图识别方面采用豆包，在数学能力方面使用DeepSeek。  \n\n“当用户通过语音提问时，AI情感陪伴机器人不仅能用语音回答，还会通过表情、动作强化交互的真实感。”  \n\n朱志刚认为，这不是简单地套了大模型的壳，公司为产品植入了汤姆猫的有趣灵魂。他介绍，一千个用户身边，会有一千种性格的汤姆猫。  \n\n对儿童而言，其处于语言敏感期，AI情感陪伴机器人可以通过自然语言交互与知识科普，成为其“家庭教师”与玩伴；对老人而言，它可以提供日常陪伴与防诈骗提醒，缓解其孤独感。  \n\n朱志刚雄心满满，联合伙伴，完善其功能。3月，他与上海光羽芯辰达成合作意向。光羽芯辰擅长将大模型部署在终端设备中，核心产品是基于大模型的端侧AI芯片。双方计划携手研发低延时、数据安全性高、存算一体的端侧AI软硬件。  \n\n上线4个月，其首款AI情感陪伴机器人销量一般，截至5月3日，天猫平台共售出700余台，京东销售400台。“产品目前只在线上渠道售卖，销量确实较少，后面会考虑拓展线下渠道。”汤姆猫相关负责人表示。  \n\n评论褒贬不一，有消费者认为该机器人上手方便，很适合跟小朋友互动；也有人吐槽对普通话的要求高。  \n\n### 开发乐园  \n\n眼下，AI业务规模尚小，汤姆猫的收入大头仍来自游戏内置广告。数年来，其开发的游戏积累4.7亿月活跃用户。基于此，汤姆猫与Google、Meta、字节跳动旗下穿山甲、华为等多家广告营销服务商合作。“通过互联网广告服务商，公司获得充足、优质的广告订单。”  \n\n同时，其自建广告控制平台Mediation，获取广告主的营销需求，再通过瀑布流竞价和实时竞价的模式，优先选择价格高的广告推送给用户，获取收入。“该控制平台智能、实时、高效地帮助广告分发，保证了广告变现效率。”信达证券分析师评价道。  \n\n“广告市场竞争加剧，需求和客单价下滑，收入下降。”汤姆猫称，去年这块业务收入为8.5亿元，同比减少18%。  \n\n朱志刚盯上“谷子经济”和主题乐园。“利用‘会说话的汤姆猫家族’IP的优质内容与品牌影响力，持续布局IP衍生品与授权业务。”公司介绍。  \n\n截至2024年11月底，其推出3000多款产品，涵盖汤姆猫家族IP的潮玩、毛绒公仔、潮流鞋服、家清洗护等多类周边。自去年以来，其还在江苏连云港、山东威海、浙江金华、广东珠海、浙江湖州等城市落地室内主题乐园。“其中，珠海南湾华发店自开业以来，连续多月接待超过5000组家庭；浙江湖州店，开业不到一个月，便吸引了7000余组亲子家庭打卡游玩。”官方介绍。反映到业绩上，汤姆猫新商业服务业务入账1.16亿元，贡献一成营收。  \n\n### 频繁运作  \n\n汤姆猫的前身是浙江金科，由朱志刚在2007年创立，主要生产氧系漂白助剂SPC（过碳酸钠）。在他的带领下，浙江金科与多家日化巨头建立合作关系，并于2015年上市。上市没多久，他就花29亿元收购杭州哲信，并将公司更名为金科娱乐。2017年，朱志刚再次出手，以10亿美元的价格收购Outfit7，把“汤姆猫”IP收入麾下。  \n\n初期发展态势良好，朱志刚还以26亿元财富荣登《2019年胡润百富榜》。硬币的另一面是，多起大手笔并购让汤姆猫积累了数额较大的商誉资产。近两年，Outfit7等子公司业绩下滑，朱志刚计提资产减值、商誉减值。截至2024年底，商誉价值为17.85亿元，约占总资产额四成。去年，公司一次性计提8.9亿元商誉减值。“受部分游戏活跃用户数下滑、新游戏与AI硬件产品创收有限、客户广告竞价机制改革等因素影响，基于谨慎性原则，公司对商誉等部分资产计提减值准备。”汤姆猫解释。  \n\n朱志刚缔造的公司集合了多个资本热点。根据同花顺，汤姆猫身上有超40个概念，包括小红书、NFT、元宇宙、ChatGPT、华为、抖音、AIGC、鸿蒙等。资本运作不断，高管却“花式减持”。汤姆猫前任董事长王健曾因减持违规受到交易所通报批评。朱志刚直接持有的汤姆猫股份也处于高质押状态，质押比例超97.6%。按照其8.79%的股比，身家依然有15个亿左右。  \n\n备受掣肘下，朱志刚坚持开发AI情感机器人，坚信终将迎来价值回报。“2024年，（取得）技术突破时，一位自闭症儿童家长告诉我们，汤姆猫成了孩子第一位朋友。”  \n\nAI机器人能否助其扭亏为盈，还是个未知数。  \n\n---  \n\n以上就是您请求的内容，如需进一步分析或提取信息，请告知！', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=290, title='围绕国家战略需求 上海电气与<em>西门子</em>中国签署战略协议', content='看起来采集该网页时出现了问题，提示“Failed to wait for the message endpoint”。这可能是由于网络问题或目标网站的加载问题导致的。你可以稍后再尝试采集，或者检查是否有其他限制（如防火墙、网站反爬机制等）影响了采集过程。如果有其他问题，请告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[])] page_num=1 page_size=10 total=289 has_next=True
2025-06-22 16:07:13.175 | 6cc8da4b079949e38e24f81d982ca27a | INFO     | module_admin.controller.keyword_data_controller:get_filter_statistics_public:256 - 获取筛选统计数据成功
2025-06-22 16:07:13.638 | b9075732587c499192399aa40abfceea | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:174 - 查询参数: pageNum=1, pageSize=10, timeFilter=None, actual_start_date=None, actual_end_date=None
2025-06-22 16:07:13.639 | b9075732587c499192399aa40abfceea | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:40 - Service层接收到的查询参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:07:13.639 | b9075732587c499192399aa40abfceea | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:41 - Service层查询对象类型: <class 'module_admin.entity.vo.keyword_data_vo.KeywordDataPageQueryModel'>
2025-06-22 16:07:13.639 | b9075732587c499192399aa40abfceea | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:42 - Service层查询对象属性: {'page_num': 1, 'page_size': 10, 'title': None, 'keyword': None, 'type': None, 'web': None, 'begin_time': None, 'end_time': None, 'platform_types': None, 'sentiment_types': None, 'info_attributes': None, 'web_site': None, 'time_filter': None}
2025-06-22 16:07:13.640 | b9075732587c499192399aa40abfceea | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:112 - DAO层时间筛选参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:07:13.640 | b9075732587c499192399aa40abfceea | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:182 - 总共构建了 0 个查询条件
2025-06-22 16:07:13.640 | b9075732587c499192399aa40abfceea | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:183 - 查询参数: time_filter=None, platform_types=None, sentiment_types=None
2025-06-22 16:07:13.641 | b9075732587c499192399aa40abfceea | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:189 - 没有查询条件，将返回所有数据
2025-06-22 16:07:13.693 | b9075732587c499192399aa40abfceea | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:178 - 查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-06-22 16:07:13.694 | b9075732587c499192399aa40abfceea | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:179 - 查询结果: records=[KeywordDataResponseModel(id=288, title='上海电气与<em>西门子</em>中国签署战略协议', content='上海电气与西门子中国签署战略协议\n\n2025年05月05日 07:56\n\n据上海电气消息，近日，上海电气输配电集团与西门子（中国）有限公司签署战略合作协议。根据协议，双方将紧密围绕国家战略需求，聚焦中低压电气产品和电气自动化系统，强化协同创新，并在数字化及低碳经济等新领域探索新项目开发。此外，双方还将探索更多领域的协同。\n\n（文章来源：界面新闻）', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=295, title='四川工业链博馆亮相2025成都国际工业博览会', content='看起来采集该网页时出现了问题，无法成功获取正文内容。请尝试提供其他网页链接或检查网络连接后再次请求。', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=291, title='“健康中国2025护‘胃’行动”在京启动，多方携手共筑全民胃健康防线', content='看来在尝试获取该网页内容时出现了问题，导致无法正常提取正文。您可以检查该网页是否正常访问，或者稍后再尝试采集。如果问题持续存在，请告知我，我会进一步协助您！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=289, title='<em class="highlight">西门子</em>Xcelerator产业生态西部中心在成都金牛区投运', content='看起来采集该网页时出现了问题，无法成功获取正文内容。您可以稍后尝试重新采集，或者检查链接是否有效。如果有其他需要帮助的地方，请随时告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=293, title='<em>西门子</em>中国工厂首个光储一体化项目投运', content='看起来采集该网页时出现了问题，无法成功获取正文内容。您可以检查链接是否有效，或者尝试在其他时间段重新采集。如果有其他需求，请告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=287, title='公司产品能否用于海上核电，海上风电？中环海陆回应', content='以下是该网页的正文内容：\n\n---\n\n# 公司产品能否用于海上核电，海上风电？中环海陆回应\n\n每日经济新闻\n\n2025-05-09 17:19:43\n\n每经AI快讯，有投资者在投资者互动平台提问：贵公司产品能否用于海上核电，海上风电？\n\n中环海陆（301040.SZ）5月9日在投资者互动平台表示，公司生产的6.0MW以上海上风电基础桩法兰锻件、7.0MW以上转子房法兰锻件供应于西门子、上海电气等知名企业的海上风电项目，具备了给海上风电大兆瓦风机配套相关产品的能力。\n\n（记者 毕陆名）\n\n免责声明：本文内容与数据仅供参考，不构成投资建议，使用前核实。据此操作，风险自担。\n\n如需转载请与《每日经济新闻》报社联系。\n\n未经《每日经济新闻》报社授权，严禁转载或镜像，违者必究。\n\n特别提醒：如果我们使用了您的图片，请作者与 [本站联系](http://www.nbd.com.cn/contact) 索取稿酬。如您不希望作品出现在本站，可联系我们要求撤下您的作品。', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=292, title='投行对黄金最激进预期：年内冲击4000美元！金价居高不下，有年轻人租三金结婚', content='以下是该网页的正文内容：\n\n---\n\n# 投行对黄金最激进预期：年内冲击4000美元！金价居高不下，有年轻人租三金结婚\n\n每日经济新闻\n\n2025-05-09 01:19:32\n\n每经编辑｜陈柯名\n\n5月8日，国际金价出现下跌。现货黄金一度跌超1.5%，截止发稿，下跌0.28%，报3354美元/盎司。\n\nCOMEX黄金一度跌近2%，截至发稿，下跌1%，报3358.1美元/盎司。\n\n**黄金最激进预期：年内冲击4000美元**\n\n值得注意的是，据财联社消息，美国银行的大宗商品分析师对黄金的前景越来越乐观。在美银周三（5月7日）发布的一份报告中，Michael Widmer领导的贵金属团队预计，金价在今年下半年触及每盎司4,000美元的可能性越来越大。\n\n美银的这一最新预测是目前华尔街各大银行中最激进的预测之一。\n\n今年3月，Widmer和他的团队曾预测，到2027年，金价将达到3500美元。而在不到一个月的时间内，金价就达到了这一目标。\n\n**年轻人开始租三金结婚**\n\n此外，国内各大品牌金饰品价格仍然上涨中，周大福、周六福、潮宏基、周大生、六福珠宝等金饰品价格均涨至1040元/克。\n\n据央视网报道，近段时间，金价居高不下，准备结婚的准新人想要凑齐心仪的“三金”首饰是一笔不小的开销。于是，一些年轻人选择以租赁的方式完成婚俗仪式。龙凤盘旋的足金颈链、刻着双喜的手镯、甚至还有黄金制作而成的“结婚证书”……在广州市白云区的这家金饰店，老板专门为可供租赁的“三金”配饰开了个专柜。老板说，随着金价走高，如今，越来越多的人愿意接受这种租“三金”的方式。\n\n据北京商报报道，近期，部分有购置结婚“三金”需求的消费者选择了自己打金或租赁金饰。一家主营金饰租赁业务的商家向北京商报记者表示，租“三金”是近期才出现的形式，“五一”假期是结婚高峰期，不少热门款式被订购空了。上述商家介绍称，租赁黄金首饰的价格一般在30元/克，押金则按黄金大盘价格收取。\n\n**老铺黄金股价涨了16倍**\n\n值得注意的是，随着金价攀升，“黄金界爱马仕”老铺黄金股价迎来大涨。自去年6月份上市以来，老铺黄金凭借快速增长的业绩接连大涨，截至目前，股价涨幅达到1605%。\n\n5月8日，老铺黄金(06181.HK)高开后回落，截至收盘，公司股价报698港元/股，股价上涨1.90%。\n\n公开信息显示，老铺黄金于2024年6月28日在香港挂牌上市，当时以每股40.50港元的IPO发行价募资10.42亿港元。\n\n今年开年，老铺黄金凭借其火热的销售势头成为市场“顶流”：排队5小时才能买到、黄牛日赚1万等消息频繁刷屏。\n\n4月28日，老铺黄金披露2024年度报告。报告期内，公司实现营业总收入85.26亿元，同比增长167.94%；归母净利润14.73亿元，同比增长253.86%；经营活动产生的现金流量净额为-12.28亿元，上年同期为-2919.6万元。\n\n那近期是入手黄金的好时机吗？职业黄金投资分析师吕超认为，从长期投资角度看，黄金具有货币属性和避险属性，能在经济不稳定时起到保值作用。若投资者资产配置中黄金占比较低，可考虑逢低买入。从短期投资角度看，黄金价格波动受多种因素影响，如美元走势、国际贸易局势、美联储政策等。目前金价虽有回调，但这些不确定因素仍存在，若后续出现新的催化剂，如地缘政治紧张、经济数据下滑等，金价可能再次上涨。\n\n**免责声明：本文内容与数据仅供参考，不构成投资建议，使用前核实。据此操作，风险自担。**\n\n**编辑｜陈柯名 杜波 盖源源**\n\n**校对｜金冥羽**\n\n每日经济新闻综合自央视网、财联社、北京商报等\n\n****推荐阅读↓****\n\n****最新！9连发，于东来再回应质疑：可以邀请柴怼怼、宋清辉等来考察！刚刚，永辉超市发公开信支持****\n\n****盘前涨超10%！极氪传来大消息：将被吉利私有化，并于纽交所退市！李书福最新回应****\n\n****最新！巴方称已击落3架印度无人机！两国战机均未侵入对方领空，巴方公布对印回击细节！中方：对印度的军事行动表示遗憾****\n\n---\n\n以上就是文章的核心内容。', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=290, title='围绕国家战略需求 上海电气与<em>西门子</em>中国签署战略协议', content='看起来采集该网页时出现了问题，提示“Failed to wait for the message endpoint”。这可能是由于网络问题或目标网站的加载问题导致的。你可以稍后再尝试采集，或者检查是否有其他限制（如防火墙、网站反爬机制等）影响了采集过程。如果有其他问题，请告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=285, title='国信证券发布<em class="highlight">老板</em>电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放', content='以下是该网页的正文内容：\n\n---\n\n# 国信证券发布老板电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放\n\n每日经济新闻\n\n2025-05-09 14:47:56\n\n每经AI快讯，国信证券5月9日发布研报称，给予老板电器（002508.SZ，最新价：19.82元）优于大市评级。评级理由主要包括：1）烟灶主品类增长稳健，二三品类有所承压；2）零售渠道增长较好；3）名气品牌持续成长，盈利扭亏；4）投入加大，盈利有所下降。风险提示：行业竞争加剧；原材料价格大幅上涨；行业需求恢复不及预期。\n\n---\n\n如需转载，请与《每日经济新闻》报社联系。  \n未经报社授权，严禁转载或镜像，违者必究。', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=284, title='国信证券发布<em>老板</em>电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放', content='国信证券发布研报指出，在地产行业压力下，老板电器经营较为稳健，期待以旧换新需求进一步释放。报告给予老板电器优于大市的评级，主要理由包括：\n\n1. 烟灶主品类增长稳健，但二三品类有所承压。\n2. 零售渠道增长较好。\n3. 名气品牌持续成长，并实现盈利扭亏。\n4. 投入加大导致盈利有所下降。\n\n报告同时提示了风险因素，包括行业竞争加剧、原材料价格大幅上涨以及行业需求恢复不及预期等。\n\n（来源：每日经济新闻）', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[])] page_num=1 page_size=10 total=504 has_next=True
2025-06-22 16:07:14.043 | 7aa5818948a845ed85462e171ccc24b9 | INFO     | module_admin.controller.keyword_data_controller:get_filter_statistics_public:256 - 获取筛选统计数据成功
2025-06-22 16:07:14.628 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-06-22 16:07:14.628 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-22 16:07:15.815 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-22 16:07:15.816 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-22 16:07:15.817 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-22 16:07:16.028 | f02c65df5dd8467eb0922d924522f2b7 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:07:16.209 | fd7b82cd49604984978baca43ac95163 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:07:16.237 | 8e35c1ccc6034d88948996a44420c90f | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:07:16.269 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-22 16:07:16.356 | 47e6ef008d954236b275f25e55f11284 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:07:16.387 | 4d09f32656184c9db6ba26f86e5da38a | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:07:16.807 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-22 16:07:16.808 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI启动成功
2025-06-22 16:07:21.929 | f98576d9fb6d4a02b4b640bcaa3d1d6c | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:07:22.051 | 0ad975ffd8504a1da305ff943e600f24 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:07:22.763 | 2172b03fb8dc41aabf926f7025beec31 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:07:22.817 | c4416de065504f448e03afecec7f6537 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:07:22.851 | a19331a3758346ec97162e77d368c85a | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:07:22.992 | ddd08b17f2e244278ba7d7b0655e3bde | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:07:23.145 | f273edc3a37f4ce0a1b507e4595a32bc | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:07:23.623 | ad311a0dae1a4002b1780519b9db6a31 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:07:23.685 | d55ffa2ee5dd44048ce08cd4c37be934 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:07:23.992 | 7ea40402248f4555b9151577b935f32f | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:07:24.336 | ad15c24029994f26ad67abc167000664 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:07:24.738 | 5b117006a8784203ba962060f32b5ef9 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:07:24.765 | b7ae7d79c6e2412390fa952e70c6497b | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:07:24.768 | 1d54460a97cd454dbffd3fe3b6ac387a | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:07:24.785 | 10d1489035fd495e8dbd8bec087f1126 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:07:25.115 | 8b58206c7f7148129448fcd41cb03997 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:07:25.206 | 68234c990df14750999ce08a20b22e5e | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:07:25.542 | 0b893e0d067042ac9394a8af1d1097d9 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:07:25.546 | fd7c0ef3c8604127a976782971396346 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:07:25.565 | 20b1c2e5bfa74deeb8a67087609c3031 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:07:55.926 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-22 16:07:55.927 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-22 16:09:28.158 | 86b38572e1d040edba5b7e7c42096915 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 16:09:28.334 | 4f40f1cd081e47838d62141184476c6d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-22 16:09:28.948 | 1951b302653c4f4a87f6f7d35c1e275a | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:09:29.126 | 0548fe7235d74ee2833c70c1a6fb1d7e | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:09:29.153 | 4adefb84f30c41f8989c5561e3eaced8 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:09:29.157 | 3cc551e8b26840bc89eaa1b1a8a2f546 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:09:29.176 | d129a9aaa95b4f12b9e6fec91b0ed86e | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:21.209 | 22ff1a7d8d3e40588656bbed173ca11a | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:21.536 | 1295a97199a846719561380dff81a00c | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:21.614 | 9db7c98454244b4ca2535ff711b61e16 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:22.020 | 4984c9c50fbd475cb14f3424cbbacd1f | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:22.045 | 389039a0da2d43d48d50811e8760807e | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:22.061 | 737549f093094b59ac597d97ab153b45 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:22.415 | 24186f801d4b441084e4ad7561487223 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:22.432 | fd2556728e194b4090b7808f0c79ae2d | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:22.465 | 8567c3b9c0d44e9ab4f5037d31dc0bf7 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:22.733 | b1323a428baa4d9f915f86444ae3c21e | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:22.900 | 8c9704215b60401485cd844412fc0013 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:23.300 | 016e9aad40894914820b39df57c52b93 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:23.803 | e4974a1a1c9747549c5890aca0a40581 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:23.805 | 4f056fd92e2d4c87898bf4a29bad3bcd | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:23.930 | be9774943188408aa192513d8429287d | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:24.394 | 35ff31d6a64d4483b76685a9d403894d | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:24.802 | 2cc04a86d5e546549e5cecdb73cdc74c | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:25.133 | c3ec4cb19d5e4d9dbc6dd7e821b45820 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:25.173 | 3559b929de184d11b4bd958c2891889b | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:25.303 | 92110072e6c74392a6c30537052786df | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:25.719 | 83aa8526dd1c46ad8438d1b148da6dee | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:26.121 | 84b1ee2466b24cec8aeed895331d71b9 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:26.149 | 90bcb9e35f4c44c397222f244a23f39a | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:26.158 | 55c816ffe11c4d1581683e9b4343fa8c | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:26.171 | 55d3d44fc23b431ab27c94db2639ad5c | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:26.967 | 58628a1cf3b4401bafbe09d6c70576c3 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:27.371 | ac65772ff89f4081895eeee2a719491b | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:27.400 | 4bb18150d3e34324968a614f3d10eb82 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:27.407 | b17353059068404b8860ff0b4418a98e | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:27.421 | 974973488e4647b09a6653eed8ec7f3a | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:27.864 | 93bab2ce35914e0fb924204ed9910143 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:28.257 | c0da33a948034286a0def4778938edc5 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:28.285 | ab593123ff1741829f38a3a1a9d48f99 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:28.288 | f995c30e586645da92509c36b1487b35 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:28.322 | 884e02a92f304d748ac81f3727ed6484 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:29.065 | 78da2b8eff2542438a50292bee418cb4 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:29.155 | 0f4f4908b89d4343a861a8b0807a0cf7 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:29.859 | 46e1d7c77f7e4415ad749bfa880affb9 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:29.876 | 496a9642a86043c3b17660d43bb3d305 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:29.919 | 54d697145dc645b2a28546263dda3482 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:35.442 | 194bf581b72443c68fe3217b4ae65145 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:35.537 | f7f602682b804a58a23d665556fd8645 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:36.288 | 3db9612d4acf4fd3b91026c5b5f63d15 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:36.362 | bfa7b714d20345989a3cba47f4c4d993 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:36.421 | 2cda7853103644528d618ee86fb3d7c5 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:37.188 | b351c44910a64ab1a5febec5a7ec7469 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:37.836 | 574cdd8a40d44324a97b14dceceeddc5 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:38.003 | ebee022b372141a9a21c3c080db0aca0 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:38.131 | 4a7843a8ca9b41e3a484f039db2295f1 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:38.201 | 70e9eec855e8428aa63d4a704495c5c8 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:41.859 | 00327035c8e24da591858d5aba85a38f | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:42.253 | 8d02bbe0421842cabc801cd37b9fa2f4 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:42.616 | f6e4977dafc047f4b1c36eb6dc94c0b4 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:42.815 | 1568d42438a2429c888e945cd62f15b2 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:42.920 | 0d63957d3f1c4974bae95be5d80dfb94 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:44.690 | edba4a402ee341eeb236d61cd00e55d9 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:45.084 | 1f1ebf0d219946bcb989e9d564036f58 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:45.510 | 943b043846d642069be23af0f8503007 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:45.569 | fff3aaa1a90f415499939d567c6db483 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:45.750 | ed62bfe692a94a2ab2ba666c429d6abc | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:45.906 | 3fd3a3dec5274355835e4d9547165e3f | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:45.999 | c788b4feaa32495482f493360bd3f117 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:46.677 | dd1eb096d98f4fd9a88d2f7b08f8872e | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:46.808 | d27b6a9096f74041b7180581e06b3602 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:46.858 | f20ffefccf8b433c802ba127484524ff | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:47.600 | e225c2657881417b8a84c8fb2a4e207d | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:47.999 | 70a11cab8a374d208619a0a261d502bc | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:48.229 | c191e7448da846cab12851dda1292ffb | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:48.317 | aedf68eebda44a85b83438d8ffadaee7 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:48.608 | 2c7e8334187e48c6be06a2afa861abd5 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:48.618 | 7ae8d47e070f4f4796c2b0647fdc8e09 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:48.676 | d734e295c32e41cc8af5424120f9f30f | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:49.074 | 30888211312040f3bc243fba3c20aea5 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:49.368 | 8b16bd6c9ef44ea5a2a7e8711ab38e0a | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:49.455 | 382baa2f1e4e4afba220f8baeb745043 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:50.218 | 9b64088cb67b4dee9236b4d30604b85f | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:50.608 | 7a474a5774494db084a00d3ffd91d69c | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:51.061 | b7a2fd3e52284a148387e304ff869856 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:51.064 | 03ad70dd10c34bccb2f1634051ea4d04 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:51.072 | 843f77d32c0846d8969520f1a26579a3 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:53.085 | 5102ac4738db4d73ba4d19f905b6b10e | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:53.484 | 9ba52439f03d47dfbc8b692a37749beb | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:53.854 | 5c6dee77ac464c079b6aa2b76b890c8a | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:54.030 | 0cda89b77d2a40da93aa5f8483429d82 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:54.205 | 6a87eeb279734586b37fc5f48e40585b | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:56.717 | 9f59648b116f47669e1b0a735425ffb8 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:57.343 | e3916e995d9b407dacd2ff21c566c782 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:57.549 | 97c74ad3acf740579f89a30eee70008f | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:57.740 | 74801f3dd08d44eab2ab9b093ff60a5b | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:57.770 | bc6425d8f294446dbb0c9b0788001fff | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:10:57.952 | a565854550c443958cd2cb3592e7804a | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:10:58.047 | 5295d34c4f06472db016803f45d98f99 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:10:58.733 | 9d9dd9a7c0774e088e286e842872d5fa | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:10:59.002 | 4736cdc060d145caa69a9b5b701c38fe | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:10:59.039 | 06af0de021424535be900ed44ad2606e | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:11:00.450 | a250199b52d440b0965eb5beeaa7a2b2 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:11:00.539 | 034f3a9bcc6f446c9482b046ca2cb850 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:11:01.161 | 6c7b2d9c403442c4b16aeefccb73268f | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:11:01.318 | b97db23114984bbb8df8c63db5f92ebb | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:11:01.480 | 17ed10269acb45ee941e4a883b624894 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:12:04.246 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-22 16:12:04.246 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-22 16:17:30.087 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-06-22 16:17:30.088 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-22 16:17:31.146 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-22 16:17:31.146 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-22 16:17:31.148 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-22 16:17:31.831 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-22 16:17:32.439 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-22 16:17:32.439 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI启动成功
2025-06-22 16:17:55.196 | 22ce6495669f48c8b3d0f1feab30f797 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为f69f77d7-9b35-45d1-9703-9e954eee7adc的会话获取图片验证码成功
2025-06-22 16:18:03.613 | df8361a396244d65ae52506bb5acbc17 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-22 16:18:03.871 | 04a598942f8d4544b1a065fca924a77e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 16:18:04.345 | 7604fc0ea9794b5fa19c5a7f071e6f89 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-22 16:18:09.023 | 0956d801da3942fcb2c262141cbe41b7 | INFO     | module_admin.controller.keyword_data_controller:get_web_statistics_public:273 - 获取网站统计数据成功
2025-06-22 16:18:09.288 | 2b207a7acf8b4a6888376dfd1baf3353 | INFO     | module_admin.controller.keyword_data_controller:get_filter_statistics_public:256 - 获取筛选统计数据成功
2025-06-22 16:18:09.298 | 788be864b464431da07dc88f49c92aaa | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:174 - 查询参数: pageNum=1, pageSize=10, timeFilter=None, actual_start_date=None, actual_end_date=None
2025-06-22 16:18:09.298 | 788be864b464431da07dc88f49c92aaa | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:40 - Service层接收到的查询参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:18:09.299 | 788be864b464431da07dc88f49c92aaa | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:41 - Service层查询对象类型: <class 'module_admin.entity.vo.keyword_data_vo.KeywordDataPageQueryModel'>
2025-06-22 16:18:09.299 | 788be864b464431da07dc88f49c92aaa | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:42 - Service层查询对象属性: {'page_num': 1, 'page_size': 10, 'title': None, 'keyword': None, 'type': None, 'web': None, 'begin_time': None, 'end_time': None, 'platform_types': None, 'sentiment_types': None, 'info_attributes': None, 'web_site': None, 'time_filter': None}
2025-06-22 16:18:09.300 | 788be864b464431da07dc88f49c92aaa | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:112 - DAO层时间筛选参数: begin_time=None, end_time=None, time_filter=None
2025-06-22 16:18:09.300 | 788be864b464431da07dc88f49c92aaa | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:182 - 总共构建了 0 个查询条件
2025-06-22 16:18:09.301 | 788be864b464431da07dc88f49c92aaa | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:183 - 查询参数: time_filter=None, platform_types=None, sentiment_types=None
2025-06-22 16:18:09.301 | 788be864b464431da07dc88f49c92aaa | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:189 - 没有查询条件，将返回所有数据
2025-06-22 16:18:09.351 | 788be864b464431da07dc88f49c92aaa | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:178 - 查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-06-22 16:18:09.351 | 788be864b464431da07dc88f49c92aaa | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:179 - 查询结果: records=[KeywordDataResponseModel(id=288, title='上海电气与<em>西门子</em>中国签署战略协议', content='上海电气与西门子中国签署战略协议\n\n2025年05月05日 07:56\n\n据上海电气消息，近日，上海电气输配电集团与西门子（中国）有限公司签署战略合作协议。根据协议，双方将紧密围绕国家战略需求，聚焦中低压电气产品和电气自动化系统，强化协同创新，并在数字化及低碳经济等新领域探索新项目开发。此外，双方还将探索更多领域的协同。\n\n（文章来源：界面新闻）', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=295, title='四川工业链博馆亮相2025成都国际工业博览会', content='看起来采集该网页时出现了问题，无法成功获取正文内容。请尝试提供其他网页链接或检查网络连接后再次请求。', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=291, title='“健康中国2025护‘胃’行动”在京启动，多方携手共筑全民胃健康防线', content='看来在尝试获取该网页内容时出现了问题，导致无法正常提取正文。您可以检查该网页是否正常访问，或者稍后再尝试采集。如果问题持续存在，请告知我，我会进一步协助您！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=289, title='<em class="highlight">西门子</em>Xcelerator产业生态西部中心在成都金牛区投运', content='看起来采集该网页时出现了问题，无法成功获取正文内容。您可以稍后尝试重新采集，或者检查链接是否有效。如果有其他需要帮助的地方，请随时告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=293, title='<em>西门子</em>中国工厂首个光储一体化项目投运', content='看起来采集该网页时出现了问题，无法成功获取正文内容。您可以检查链接是否有效，或者尝试在其他时间段重新采集。如果有其他需求，请告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=287, title='公司产品能否用于海上核电，海上风电？中环海陆回应', content='以下是该网页的正文内容：\n\n---\n\n# 公司产品能否用于海上核电，海上风电？中环海陆回应\n\n每日经济新闻\n\n2025-05-09 17:19:43\n\n每经AI快讯，有投资者在投资者互动平台提问：贵公司产品能否用于海上核电，海上风电？\n\n中环海陆（301040.SZ）5月9日在投资者互动平台表示，公司生产的6.0MW以上海上风电基础桩法兰锻件、7.0MW以上转子房法兰锻件供应于西门子、上海电气等知名企业的海上风电项目，具备了给海上风电大兆瓦风机配套相关产品的能力。\n\n（记者 毕陆名）\n\n免责声明：本文内容与数据仅供参考，不构成投资建议，使用前核实。据此操作，风险自担。\n\n如需转载请与《每日经济新闻》报社联系。\n\n未经《每日经济新闻》报社授权，严禁转载或镜像，违者必究。\n\n特别提醒：如果我们使用了您的图片，请作者与 [本站联系](http://www.nbd.com.cn/contact) 索取稿酬。如您不希望作品出现在本站，可联系我们要求撤下您的作品。', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=292, title='投行对黄金最激进预期：年内冲击4000美元！金价居高不下，有年轻人租三金结婚', content='以下是该网页的正文内容：\n\n---\n\n# 投行对黄金最激进预期：年内冲击4000美元！金价居高不下，有年轻人租三金结婚\n\n每日经济新闻\n\n2025-05-09 01:19:32\n\n每经编辑｜陈柯名\n\n5月8日，国际金价出现下跌。现货黄金一度跌超1.5%，截止发稿，下跌0.28%，报3354美元/盎司。\n\nCOMEX黄金一度跌近2%，截至发稿，下跌1%，报3358.1美元/盎司。\n\n**黄金最激进预期：年内冲击4000美元**\n\n值得注意的是，据财联社消息，美国银行的大宗商品分析师对黄金的前景越来越乐观。在美银周三（5月7日）发布的一份报告中，Michael Widmer领导的贵金属团队预计，金价在今年下半年触及每盎司4,000美元的可能性越来越大。\n\n美银的这一最新预测是目前华尔街各大银行中最激进的预测之一。\n\n今年3月，Widmer和他的团队曾预测，到2027年，金价将达到3500美元。而在不到一个月的时间内，金价就达到了这一目标。\n\n**年轻人开始租三金结婚**\n\n此外，国内各大品牌金饰品价格仍然上涨中，周大福、周六福、潮宏基、周大生、六福珠宝等金饰品价格均涨至1040元/克。\n\n据央视网报道，近段时间，金价居高不下，准备结婚的准新人想要凑齐心仪的“三金”首饰是一笔不小的开销。于是，一些年轻人选择以租赁的方式完成婚俗仪式。龙凤盘旋的足金颈链、刻着双喜的手镯、甚至还有黄金制作而成的“结婚证书”……在广州市白云区的这家金饰店，老板专门为可供租赁的“三金”配饰开了个专柜。老板说，随着金价走高，如今，越来越多的人愿意接受这种租“三金”的方式。\n\n据北京商报报道，近期，部分有购置结婚“三金”需求的消费者选择了自己打金或租赁金饰。一家主营金饰租赁业务的商家向北京商报记者表示，租“三金”是近期才出现的形式，“五一”假期是结婚高峰期，不少热门款式被订购空了。上述商家介绍称，租赁黄金首饰的价格一般在30元/克，押金则按黄金大盘价格收取。\n\n**老铺黄金股价涨了16倍**\n\n值得注意的是，随着金价攀升，“黄金界爱马仕”老铺黄金股价迎来大涨。自去年6月份上市以来，老铺黄金凭借快速增长的业绩接连大涨，截至目前，股价涨幅达到1605%。\n\n5月8日，老铺黄金(06181.HK)高开后回落，截至收盘，公司股价报698港元/股，股价上涨1.90%。\n\n公开信息显示，老铺黄金于2024年6月28日在香港挂牌上市，当时以每股40.50港元的IPO发行价募资10.42亿港元。\n\n今年开年，老铺黄金凭借其火热的销售势头成为市场“顶流”：排队5小时才能买到、黄牛日赚1万等消息频繁刷屏。\n\n4月28日，老铺黄金披露2024年度报告。报告期内，公司实现营业总收入85.26亿元，同比增长167.94%；归母净利润14.73亿元，同比增长253.86%；经营活动产生的现金流量净额为-12.28亿元，上年同期为-2919.6万元。\n\n那近期是入手黄金的好时机吗？职业黄金投资分析师吕超认为，从长期投资角度看，黄金具有货币属性和避险属性，能在经济不稳定时起到保值作用。若投资者资产配置中黄金占比较低，可考虑逢低买入。从短期投资角度看，黄金价格波动受多种因素影响，如美元走势、国际贸易局势、美联储政策等。目前金价虽有回调，但这些不确定因素仍存在，若后续出现新的催化剂，如地缘政治紧张、经济数据下滑等，金价可能再次上涨。\n\n**免责声明：本文内容与数据仅供参考，不构成投资建议，使用前核实。据此操作，风险自担。**\n\n**编辑｜陈柯名 杜波 盖源源**\n\n**校对｜金冥羽**\n\n每日经济新闻综合自央视网、财联社、北京商报等\n\n****推荐阅读↓****\n\n****最新！9连发，于东来再回应质疑：可以邀请柴怼怼、宋清辉等来考察！刚刚，永辉超市发公开信支持****\n\n****盘前涨超10%！极氪传来大消息：将被吉利私有化，并于纽交所退市！李书福最新回应****\n\n****最新！巴方称已击落3架印度无人机！两国战机均未侵入对方领空，巴方公布对印回击细节！中方：对印度的军事行动表示遗憾****\n\n---\n\n以上就是文章的核心内容。', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=290, title='围绕国家战略需求 上海电气与<em>西门子</em>中国签署战略协议', content='看起来采集该网页时出现了问题，提示“Failed to wait for the message endpoint”。这可能是由于网络问题或目标网站的加载问题导致的。你可以稍后再尝试采集，或者检查是否有其他限制（如防火墙、网站反爬机制等）影响了采集过程。如果有其他问题，请告诉我！', source_url=None, keywords='西门子', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=285, title='国信证券发布<em class="highlight">老板</em>电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放', content='以下是该网页的正文内容：\n\n---\n\n# 国信证券发布老板电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放\n\n每日经济新闻\n\n2025-05-09 14:47:56\n\n每经AI快讯，国信证券5月9日发布研报称，给予老板电器（002508.SZ，最新价：19.82元）优于大市评级。评级理由主要包括：1）烟灶主品类增长稳健，二三品类有所承压；2）零售渠道增长较好；3）名气品牌持续成长，盈利扭亏；4）投入加大，盈利有所下降。风险提示：行业竞争加剧；原材料价格大幅上涨；行业需求恢复不及预期。\n\n---\n\n如需转载，请与《每日经济新闻》报社联系。  \n未经报社授权，严禁转载或镜像，违者必究。', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[]), KeywordDataResponseModel(id=284, title='国信证券发布<em>老板</em>电器研报：地产压力下经营较为稳健，期待以旧换新需求进一步释放', content='国信证券发布研报指出，在地产行业压力下，老板电器经营较为稳健，期待以旧换新需求进一步释放。报告给予老板电器优于大市的评级，主要理由包括：\n\n1. 烟灶主品类增长稳健，但二三品类有所承压。\n2. 零售渠道增长较好。\n3. 名气品牌持续成长，并实现盈利扭亏。\n4. 投入加大导致盈利有所下降。\n\n报告同时提示了风险因素，包括行业竞争加剧、原材料价格大幅上涨以及行业需求恢复不及预期等。\n\n（来源：每日经济新闻）', source_url=None, keywords='老板', source='品牌', time='2025-06-20 15:00:38', platform_name=None, selected=False, sentiment='positive', views=0, comments=0, images=[])] page_num=1 page_size=10 total=504 has_next=True
2025-06-22 16:18:11.538 | 56383c1bcae3492d930e754ad645b057 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:18:11.617 | 34ede4ef30514027b3863879889afbfc | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:18:11.622 | e41f29065d4d413e9958944aa3fef137 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:18:11.627 | bce820188dec41d0ac46a07c659b1f4f | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:18:11.646 | 1c795228e4254ef096702e58c476cc57 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:18:11.884 | b0234866b4b34ef8b8efe5e3a66048f6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: None, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-22 16:18:11.909 | b0234866b4b34ef8b8efe5e3a66048f6 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-22 16:18:11.909 | b0234866b4b34ef8b8efe5e3a66048f6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-22 16:18:11.910 | b0234866b4b34ef8b8efe5e3a66048f6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=None, pageNum=1, pageSize=10
2025-06-22 16:18:11.910 | b0234866b4b34ef8b8efe5e3a66048f6 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: None, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-22 16:18:11.935 | b0234866b4b34ef8b8efe5e3a66048f6 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-22 16:18:11.935 | b0234866b4b34ef8b8efe5e3a66048f6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 5 条
2025-06-22 16:18:12.068 | b0234866b4b34ef8b8efe5e3a66048f6 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: None, 总计: 5, 待处理: 3, 已处理: 1, 紧急: 1, 负面: 4, 正面: 1
2025-06-22 16:18:12.068 | b0234866b4b34ef8b8efe5e3a66048f6 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-22 16:18:12.069 | b0234866b4b34ef8b8efe5e3a66048f6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=5 pending_count=3 processed_count=1 urgent_count=1 negative_count=4 positive_count=1
2025-06-22 16:18:12.069 | b0234866b4b34ef8b8efe5e3a66048f6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: None, 返回数据结构: schemes=5, records=list, statistics=True, settings=False
2025-06-22 16:18:12.568 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 15, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-22 16:18:12.594 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-22 16:18:12.594 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-22 16:18:12.594 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=15, pageNum=1, pageSize=10
2025-06-22 16:18:12.595 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 15, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-22 16:18:12.595 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 15
2025-06-22 16:18:12.621 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-22 16:18:12.622 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 1 条
2025-06-22 16:18:12.771 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 15, 总计: 1, 待处理: 1, 已处理: 0, 紧急: 0, 负面: 1, 正面: 0
2025-06-22 16:18:12.771 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 15
2025-06-22 16:18:12.771 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=1 pending_count=1 processed_count=0 urgent_count=0 negative_count=1 positive_count=0
2025-06-22 16:18:12.823 | 345db61ec8c5400ca8b3c68413a9b9a1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-22 16:18:12.823 | 345db61ec8c5400ca8b3c68413a9b9a1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-22 16:18:12.824 | 345db61ec8c5400ca8b3c68413a9b9a1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-22 16:18:12.824 | 345db61ec8c5400ca8b3c68413a9b9a1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-22 16:18:12.825 | 345db61ec8c5400ca8b3c68413a9b9a1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-22 16:18:12.825 | 345db61ec8c5400ca8b3c68413a9b9a1 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-22 16:18:12.825 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 15
2025-06-22 16:18:12.826 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 15 的预警设置成功
2025-06-22 16:18:12.826 | 345db61ec8c5400ca8b3c68413a9b9a1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 15, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-22 16:18:13.145 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 14, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-22 16:18:13.167 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-22 16:18:13.168 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-22 16:18:13.168 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=14, pageNum=1, pageSize=10
2025-06-22 16:18:13.169 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 14, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-22 16:18:13.169 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 14
2025-06-22 16:18:13.192 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-22 16:18:13.192 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 4 条
2025-06-22 16:18:13.323 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 14, 总计: 4, 待处理: 2, 已处理: 1, 紧急: 1, 负面: 3, 正面: 1
2025-06-22 16:18:13.324 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 14
2025-06-22 16:18:13.324 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=4 pending_count=2 processed_count=1 urgent_count=1 negative_count=3 positive_count=1
2025-06-22 16:18:13.369 | b896843cbeee4ac09f6547673a5bead5 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-22 16:18:13.369 | b896843cbeee4ac09f6547673a5bead5 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-22 16:18:13.369 | b896843cbeee4ac09f6547673a5bead5 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-22 16:18:13.369 | b896843cbeee4ac09f6547673a5bead5 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-22 16:18:13.369 | b896843cbeee4ac09f6547673a5bead5 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-22 16:18:13.370 | b896843cbeee4ac09f6547673a5bead5 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-22 16:18:13.370 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 14
2025-06-22 16:18:13.371 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 14 的预警设置成功
2025-06-22 16:18:13.371 | b896843cbeee4ac09f6547673a5bead5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 14, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-22 16:18:14.279 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:42 - 收到前端数据请求 - 方案ID: 18, 页码: 1, 页大小: 10, 搜索文本: None, ID筛选: None, 状态筛选: None
2025-06-22 16:18:14.304 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:54 - 获取启用预警方案列表成功
2025-06-22 16:18:14.305 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:46 - 获取到 5 个启用的预警方案
2025-06-22 16:18:14.305 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:57 - 构建查询参数: schemeId=18, pageNum=1, pageSize=10
2025-06-22 16:18:14.306 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:44 - DAO层查询条件构建 - 方案ID: 18, 记录ID: None, 搜索文本: None, 状态筛选: None
2025-06-22 16:18:14.306 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.dao.warning_record_dao:get_warning_record_list:54 - 添加方案ID筛选条件: 18
2025-06-22 16:18:14.331 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-22 16:18:14.332 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:68 - 查询到预警记录: 0 条
2025-06-22 16:18:14.479 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.dao.warning_record_dao:get_warning_statistics:232 - 预警统计数据 - 方案ID: 18, 总计: 0, 待处理: 0, 已处理: 0, 紧急: 0, 负面: 0, 正面: 0
2025-06-22 16:18:14.480 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: 18
2025-06-22 16:18:14.480 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:72 - 获取统计信息: total_count=0 pending_count=0 processed_count=0 urgent_count=0 negative_count=0 positive_count=0
2025-06-22 16:18:14.531 | a954060616d7424286e04dedbc32bfc3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 platform_types 已经是 list 类型，直接使用
2025-06-22 16:18:14.531 | a954060616d7424286e04dedbc32bfc3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 match_objects 已经是 list 类型，直接使用
2025-06-22 16:18:14.532 | a954060616d7424286e04dedbc32bfc3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 publish_regions 已经是 list 类型，直接使用
2025-06-22 16:18:14.532 | a954060616d7424286e04dedbc32bfc3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 ip_areas 已经是 list 类型，直接使用
2025-06-22 16:18:14.533 | a954060616d7424286e04dedbc32bfc3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 media_categories 已经是 list 类型，直接使用
2025-06-22 16:18:14.533 | a954060616d7424286e04dedbc32bfc3 | DEBUG    | module_warning.dao.warning_settings_dao:safe_json_parse:144 - 字段 article_categories 已经是 list 类型，直接使用
2025-06-22 16:18:14.533 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.service.warning_settings_service:get_warning_settings_services:42 - 获取预警设置成功，方案ID: 18
2025-06-22 16:18:14.533 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:79 - 获取方案 18 的预警设置成功
2025-06-22 16:18:14.533 | a954060616d7424286e04dedbc32bfc3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:97 - 获取预警中心前端数据成功，方案ID: 18, 返回数据结构: schemes=5, records=list, statistics=True, settings=True
2025-06-22 16:18:15.374 | 46c0976ef62546148f87f0e9bf82ed9a | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:388 - 成功创建 4 个菜单分类
2025-06-22 16:18:15.375 | 46c0976ef62546148f87f0e9bf82ed9a | INFO     | module_admin.controller.report_controller:get_report_menu_categories:139 - 获取报告中心菜单分类成功，共 4 项
2025-06-22 16:18:15.599 | 753e00767a274de888ef68c93441b952 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:52 - === 报告中心方案列表请求 ===
2025-06-22 16:18:15.600 | 753e00767a274de888ef68c93441b952 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:53 - 用户ID: 1
2025-06-22 16:18:15.600 | 753e00767a274de888ef68c93441b952 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:54 - 页码: 1
2025-06-22 16:18:15.601 | 753e00767a274de888ef68c93441b952 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 页大小: 10
2025-06-22 16:18:15.601 | 753e00767a274de888ef68c93441b952 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 模板类型: None
2025-06-22 16:18:15.601 | 753e00767a274de888ef68c93441b952 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:57 - 搜索关键词: None
2025-06-22 16:18:15.602 | 753e00767a274de888ef68c93441b952 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:58 - 方案类型ID: None
2025-06-22 16:18:15.606 | c80b27126f6e4c7db41f344e13f3fbfd | INFO     | module_admin.controller.report_controller:get_scheme_type_list:116 - 获取方案类型列表成功
2025-06-22 16:18:15.711 | 753e00767a274de888ef68c93441b952 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-06-22 16:18:15.737 | 753e00767a274de888ef68c93441b952 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 16
2025-06-22 16:18:15.737 | 753e00767a274de888ef68c93441b952 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=16
2025-06-22 16:18:15.738 | 753e00767a274de888ef68c93441b952 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:67 - 获取报告中心方案列表成功，共 16 条记录
2025-06-22 16:18:15.738 | 753e00767a274de888ef68c93441b952 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:68 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-06-22 16:18:16.889 | 81ff1e5bfd0448549076680b1dbdce5a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:52 - === 报告中心方案列表请求 ===
2025-06-22 16:18:16.889 | 81ff1e5bfd0448549076680b1dbdce5a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:53 - 用户ID: 1
2025-06-22 16:18:16.890 | 81ff1e5bfd0448549076680b1dbdce5a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:54 - 页码: 1
2025-06-22 16:18:16.890 | 81ff1e5bfd0448549076680b1dbdce5a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 页大小: 10
2025-06-22 16:18:16.890 | 81ff1e5bfd0448549076680b1dbdce5a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 模板类型: None
2025-06-22 16:18:16.891 | 81ff1e5bfd0448549076680b1dbdce5a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:57 - 搜索关键词: None
2025-06-22 16:18:16.891 | 81ff1e5bfd0448549076680b1dbdce5a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:58 - 方案类型ID: None
2025-06-22 16:18:16.983 | 81ff1e5bfd0448549076680b1dbdce5a | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 1 条方案记录
2025-06-22 16:18:17.006 | 81ff1e5bfd0448549076680b1dbdce5a | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 1, 总数: 1
2025-06-22 16:18:17.006 | 81ff1e5bfd0448549076680b1dbdce5a | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=1, total=1
2025-06-22 16:18:17.007 | 81ff1e5bfd0448549076680b1dbdce5a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:67 - 获取报告中心方案列表成功，共 1 条记录
2025-06-22 16:18:17.007 | 81ff1e5bfd0448549076680b1dbdce5a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:68 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 1
2025-06-22 16:18:17.154 | 4e9bf061a01e4cdd9e4dde4515c04340 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:52 - === 报告中心方案列表请求 ===
2025-06-22 16:18:17.155 | 4e9bf061a01e4cdd9e4dde4515c04340 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:53 - 用户ID: 1
2025-06-22 16:18:17.155 | 4e9bf061a01e4cdd9e4dde4515c04340 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:54 - 页码: 1
2025-06-22 16:18:17.155 | 4e9bf061a01e4cdd9e4dde4515c04340 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 页大小: 10
2025-06-22 16:18:17.156 | 4e9bf061a01e4cdd9e4dde4515c04340 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 模板类型: None
2025-06-22 16:18:17.156 | 4e9bf061a01e4cdd9e4dde4515c04340 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:57 - 搜索关键词: None
2025-06-22 16:18:17.156 | 4e9bf061a01e4cdd9e4dde4515c04340 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:58 - 方案类型ID: None
2025-06-22 16:18:17.261 | 4e9bf061a01e4cdd9e4dde4515c04340 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 1 条方案记录
2025-06-22 16:18:17.286 | 4e9bf061a01e4cdd9e4dde4515c04340 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 1, 总数: 1
2025-06-22 16:18:17.286 | 4e9bf061a01e4cdd9e4dde4515c04340 | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=1, total=1
2025-06-22 16:18:17.286 | 4e9bf061a01e4cdd9e4dde4515c04340 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:67 - 获取报告中心方案列表成功，共 1 条记录
2025-06-22 16:18:17.287 | 4e9bf061a01e4cdd9e4dde4515c04340 | INFO     | module_admin.controller.report_controller:get_report_scheme_list:68 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 1
2025-06-22 16:18:17.381 | 8a882ca0499e4a5ba7b682220c90b08a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:52 - === 报告中心方案列表请求 ===
2025-06-22 16:18:17.381 | 8a882ca0499e4a5ba7b682220c90b08a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:53 - 用户ID: 1
2025-06-22 16:18:17.382 | 8a882ca0499e4a5ba7b682220c90b08a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:54 - 页码: 1
2025-06-22 16:18:17.382 | 8a882ca0499e4a5ba7b682220c90b08a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 页大小: 10
2025-06-22 16:18:17.382 | 8a882ca0499e4a5ba7b682220c90b08a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 模板类型: None
2025-06-22 16:18:17.383 | 8a882ca0499e4a5ba7b682220c90b08a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:57 - 搜索关键词: None
2025-06-22 16:18:17.383 | 8a882ca0499e4a5ba7b682220c90b08a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:58 - 方案类型ID: None
2025-06-22 16:18:17.486 | 8a882ca0499e4a5ba7b682220c90b08a | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 1 条方案记录
2025-06-22 16:18:17.512 | 8a882ca0499e4a5ba7b682220c90b08a | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 1, 总数: 1
2025-06-22 16:18:17.513 | 8a882ca0499e4a5ba7b682220c90b08a | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=1, total=1
2025-06-22 16:18:17.513 | 8a882ca0499e4a5ba7b682220c90b08a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:67 - 获取报告中心方案列表成功，共 1 条记录
2025-06-22 16:18:17.513 | 8a882ca0499e4a5ba7b682220c90b08a | INFO     | module_admin.controller.report_controller:get_report_scheme_list:68 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 1
2025-06-22 16:20:16.878 | 0929bcb0e95d4719a848186a9f2a0e90 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:20:17.024 | 3bbf190017a44865b6a736f6ff47d2a0 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:20:17.610 | 7f94c9fbe39146b59597e917ffd02abe | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:20:17.799 | f65589208dd447f6a2a1356d0aa9186e | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:20:17.884 | 9e157fe31da74bb593309b26cecf8b53 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:20:17.896 | 4b142a43b2824d7bace11b7bd7141438 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:20:18.187 | 76f3d050d8504cee81d2d78a095afae7 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:20:18.201 | fa280f6c8ed94f158813f64c6650d14f | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:20:18.586 | 99be0808182b4266a96ced777234ed97 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:20:18.613 | 25fba0a55cc44b98b43cd8ff8748f291 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:20:18.650 | faaff5d0ebb5400d956cace02220193b | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:20:18.733 | 2a1bcc8679944b13aee0eeb49431474f | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:20:19.318 | 708a6191a07b4fb3a2fce048bb2526fd | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:20:19.419 | 12c984bcdfe944868409a09106da48e4 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:20:19.685 | 3232410b5e5a41df87c6dbeef1a41d91 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:20:19.944 | 4208c8c5b85d4f7798f06ff47a3b35fd | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:20:20.343 | 851f239c7107437dbc054930409a47e4 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:20:20.623 | c12c07a6d4d54a67bbde687a3b5492e2 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:20:21.030 | 034d8095b3ce411b9e73c4f9a7bc08d5 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:20:21.064 | ce33e12170db4199a0ba608181449baf | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:20:23.819 | cade8c0e13a34633bd719040e6bd367e | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:20:24.223 | f2094bcd9ea2484084dbed26d885a893 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:20:24.574 | d48748e0cd8b4c43a96a7dedac1302af | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:20:24.738 | 6b9b3e9314c440779dddb56e00168f88 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:20:24.827 | f0c5cb9ed4b149998f86132c4c38fd2e | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:20:25.537 | 16bd59ef953b4ec4b5ec13ecd07d3055 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:20:31.120 | 79a64cfa89bc4f96b94d73972b327515 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:20:31.519 | 255e5b649f5e41ff9d273bea5dceebd9 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:20:31.822 | e90dd82a14a64af6b09efff2788d76eb | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:20:31.903 | aa5b18294d4347f5887d9917389d40a2 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:23:45.393 | b342bbec658342a6beafeb617cac6d67 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:23:45.477 | 61261b9b3c6c4b6ab36db6cd0ace5e8a | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:23:46.203 | 7b055d6b6af14851acf1b47e2e900bbd | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:23:46.271 | 066aa8719c424f6db7af5ce0cae2ba5d | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:23:46.475 | 1d78a0da03aa4b3f9363070d023b824b | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:23:47.456 | 89b1ae18cbbc48619766a16e002cd610 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:23:47.540 | 66be4e347c524d9793aead045c8f7b43 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:23:48.352 | c483c44d3eea4b9bafcc3fc0400fec21 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:23:48.356 | 87b4eda668b643eca293079ab5fd2661 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:23:48.488 | d6efd462bd0548cba2f6c1637bc659c3 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:23:48.712 | 6657999a855f4468b64bb27a4eeba67d | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:23:49.119 | 22a08f3001d44841b6f825d528952b39 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:23:49.520 | e0132e0d4bc648908b1e6f4560188561 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:23:49.542 | 691838f0a98b450d84941c388d7af176 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:23:49.610 | b1641465dc0a429594620bb880e516b5 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:28:52.345 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-06-22 16:28:52.346 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-22 16:28:53.584 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-22 16:28:53.584 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-22 16:28:53.588 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-22 16:28:54.458 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-22 16:28:54.975 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-22 16:28:54.975 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI启动成功
2025-06-22 16:29:03.255 | 902ff8b6c133454c9e227b02baa30404 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 16:29:03.428 | 730dfa737e3d4cb9a9eb8a620f146170 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-22 16:29:04.039 | 7d3db24ef0aa42ce832d41e8f3e25967 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:29:04.170 | 604f46d0f92c4bf0ba621c53c2f757bd | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:29:04.263 | 48894cfe9528408e876e327a8b8f2f07 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:29:04.264 | ff10311bc87448bbacba459bc051a244 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:29:04.285 | 40674e268baf470b89ceaa1260a1e910 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:29:09.260 | ce25040c16f445b589a4cc2b89be8da8 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:29:09.659 | ed373a6efdfe4674a473c568f37ba0ef | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:29:10.033 | 21bc5bd1e83241219f35fce743354bc9 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:29:10.060 | 6e906c41c1cc463f982273748c59b490 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:29:10.233 | 670be2bdb3aa4231950aacd9f7af476b | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:29:10.382 | 89a3193a194b4e748863737064525ddc | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:29:10.897 | 5ecaaa3738d64c8da6de3a8e7338178b | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:29:11.303 | ef4b406bbd6e4e04b3769f84acacfc49 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:29:11.374 | 1294f7f4faef4120ac667bf34b2b5f80 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:29:11.378 | b47cef07d11c41fd9341fdf945316766 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:29:11.459 | 6f9daac26a6947769e5c7f86aa1863c1 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:29:11.528 | 14af10038d4c4158b6d9c6dfb714e012 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:29:11.721 | 99ceea6b4cb344e49dcead1697cea503 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:29:11.852 | aaddd277053049918ae19bfd15b12900 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:29:11.870 | c07fd60ef0394309bed1327229f979d7 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:29:11.877 | 48755d2b8944445480d9c1827ff736ab | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:29:12.200 | 38a823d6ea704f9e8a232cbd6ed12030 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:29:12.289 | c444170aeac74c26ae5a11e0a5ab5b6a | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:29:12.304 | 5e72df0042f04c89872aa832d8d4aee0 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:29:12.312 | 92fcbf5a54ef4929b76b889f4133601a | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:29:13.536 | 789225c6857d4aaa8ab04bf6590d7476 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:29:14.004 | d598f8a9b02240acbba5e39c0f2bbc33 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:29:15.524 | 24956c7289b44a0bab6f82906dbb87ae | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:29:15.618 | ba0bf685349243eb8a26436c2a47a119 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:29:15.662 | 2ea82acf3981450a825dfcdadd8b80c2 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:29:16.004 | fe5075ac4b8f4749b5df3ceeba398b47 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:29:16.102 | dcf3717fada9422eb323e3bff81ee692 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:29:17.153 | c91bd3191ade47b3908d583e46863d19 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:29:17.176 | 0f6e66cd76594bf6bffdb2be0df8fd56 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:29:17.181 | 6c318cbd6d1c4f4ebf98ce2e46f6be10 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:29:49.156 | 4948a739f4b14dbb884a078d56fe6c70 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 16:30:42.648 | 7c86412666014057aca6e526c7ab7d94 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:30:42.735 | 6f2633f624a14fbb87cc6e0628c8fb50 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:30:43.514 | 7cd1ea7f74414272bd8a30fa5088cafd | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:30:43.533 | 7acc61f1f3d84d43bd183e3d38420fff | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:30:43.739 | b175a41b5d9343a082e15a1a49a319bc | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:30:46.459 | 09d21f20c9754dc496e373f10d719cc8 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:30:46.542 | 4326f7560ce343f68534ccc46240f8f1 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:30:47.236 | 71a9111800e245628d8fbdc2d0a3c311 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:30:47.252 | eea643d015ea462595231c65087df893 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:30:47.515 | da096fcd76bf4047a4d8e61ffbc9f167 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:31:33.364 | 2b7c0ce17e0241749bdad3c1422e6da7 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:31:33.449 | 2fbf1eda7be842e1b731eed30e995ad2 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:31:34.102 | 6bc09a950f2545989e1a5b87d04c5c1d | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:31:34.110 | 3d59a7d15ad14544a673cbeab6dc624a | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:31:34.443 | 5c9b38d0c7fb48139a8df01bf53fae09 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:31:35.037 | 926137ccf0b248d1a869573379b8e1c9 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:31:35.437 | 6d2e55c4792747ae82bd3d0fa57d24e8 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:31:35.723 | c50d72c4396042aaa59acb8ee9b95869 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:31:35.872 | 3fccb8ef51cc455684ca0c1e2f18c5fb | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:31:36.175 | 8445952a860f44d6adf0bf6da156c1b2 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:31:37.064 | 71fed6cbeca94b1eb1c766639a94d54b | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:31:37.455 | fbf75065b15642fb9ce6a3e2f2eaa2e6 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:31:37.721 | 025008cc35f0463ea3bd72dbf5b13479 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:31:37.986 | 8c51980f01be46b3b13888f88a92a43c | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:31:38.122 | 49c5585764d8415786d120b75b4b96be | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:31:42.484 | b8a804d1b65443619f55ef90e197c86d | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:31:42.886 | 8604461a4b384b92a669a6ab6fdc9de9 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:31:43.137 | 09752e3ef90e4e57bd0bad79c647dc31 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:31:43.450 | 7133477b5c704a0880e45f3c7ad44beb | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:31:43.574 | c9ed120e54624a00897619f9e26a3e71 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:31:59.974 | 7689cafb2b7a482f9c4d08f584bc8086 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:32:00.374 | 57d5b0dc55a04bc994c1cc14d092ce67 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:32:00.697 | 577a07df012a40689895c1ae423a8662 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:32:00.960 | 99f9505072a74ed695a1b575e450054a | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:32:01.036 | e6c2033fcd4c47009c34432d6943a1c3 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:32:05.632 | 03a6d41816a0438fa54a1aa7129b8859 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:32:06.025 | 4caa9691c62644e992f55769702862ea | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:32:06.315 | c67e59bfafbe46eaa592062085a770d5 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:32:06.503 | fd77d3c4c3dc47a3b4f5613fcee0801c | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:32:06.668 | e2125515da314bcd90ba7b7039ce1878 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:32:13.487 | 60b37cf8d2494308b3fd57101d6d75be | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:32:13.889 | 4c6c0088ad504f11bc763033c82c2240 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:32:14.174 | 2e0632f3e2864400ba30729701ba971d | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:32:14.409 | b19e3c3bce1448b683a994aabd82ea9f | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:32:14.552 | b9d551464793454fb6536f61449920eb | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:33:19.462 | 8914a6bfb6544550872b9e0daa17503c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 16:33:19.638 | 3cfcd5cbbadf416bb4fafc7b93925866 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-22 16:33:20.187 | f875672780984bacadb8d8db651379cf | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:33:20.388 | 446c96d653cd4edcb7aeaf8bc3be566e | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:33:20.416 | 4c5d3f1f2b684c78a5573e5859a67fe1 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:33:20.418 | c89128d8e5414bdc93b6c0a19f7a80ef | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:33:20.440 | 75c8cef9cce74dc48a466aa3d50c2c67 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:33:23.845 | 2dc2d89f0c2e4f0d896159b00e491341 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:33:23.992 | 00984f0d38bc4a6eb5dc4df62bd3e89f | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:33:24.267 | 6da6f0e5e463412c9c9fb8a826647cc5 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:33:24.269 | 6e52d5c7426c4191b745dfe2540a4c72 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:33:24.288 | 9db040c89ba74a7e9389ba2f316a4a91 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:33:25.348 | 426ff610bd5748979bf11f6f028b2624 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:33:25.750 | 9963a1f952bb4b1d82ae87a1f0686c5a | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:33:26.100 | 944d2a7d166d4384af2bad7b0e6cda10 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:33:26.235 | c63bdf64cceb4331838273ea8ceddaef | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:33:26.430 | 132cc3bd68be43ef919468521ff47014 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:33:28.289 | 55e4ee9f2e91427983fb8e6c185f98d6 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:33:28.679 | cbe507767fbf4463a0dfc6a9a13df93c | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:33:28.705 | dd486b3065cd4c238a42b71906c537fb | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:33:28.711 | a5780d1116e245ba96a0a62ad24426b0 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:33:28.734 | 6dc90665148b4a4d93ff724638e33c20 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:33:31.143 | 5e03b6f31c484cd2a66a6f3871c59221 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:33:31.541 | b99c452d5d8b4879a85f781b0aedba6f | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:33:31.566 | 58db67be9463472391e8f77bdec0c4fe | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:33:31.575 | ec87b768649f4c4b9f83590abb8bce6e | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:33:31.596 | f5080c91346542b5a7521f5fb809680e | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:33:32.884 | 6a90b05daabb4d188d31b00618f118ad | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:33:33.284 | fb3703a671c5420f82079cded9c0d0d0 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:33:33.632 | 291cc378c8d845aaa4229476d797d47f | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:33:33.648 | a4555232e8c14ece90068898bd47327f | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:33:33.944 | 9a8cbfd65ace4340bc76c3dee6943267 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:33:36.539 | 1e9012b61b0e4aec80d0f09baaf87b4b | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:33:36.936 | d1a1f57bd1f7460387f1c62b2212d012 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:33:37.301 | 4a5db108c83d45288dd6e1c515537de6 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:33:37.491 | aa46dc93fcad4101b90967fba9e8a524 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:33:37.688 | dc60c2fb6c8f492c824a96cc4d33bb7c | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:33:43.304 | 9b08f56bf56a4ca48f4f6eb72f73bcd9 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 16:33:43.503 | 4ac815ce568f4f6680095aeab6921aba | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-22 16:33:44.340 | 29c45dca95314dd99e731bfb3fc57631 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:33:44.395 | 1708c5f3f2d14a6aa1e5805d40c0cb0a | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:33:44.423 | 15352c5e34cb4490a3b7a5c4338ea42b | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:33:44.500 | dab16ceffd6e46c98cfea8fc2e14e975 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:33:44.507 | c3c2f65b724d4090b7e78cb2717d46d7 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:33:47.085 | 1140f207e94b4308a8c3fcbc0a418fc3 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:33:47.173 | 26f783687b7a41d5ae21c9884b6ea364 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:33:47.768 | 2ed8036e53c947328ff4c1094e833c54 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:33:48.180 | 40201794948841b6b21c431e8b50fcaa | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:33:48.285 | a1e907e07e424e1a9447c0558680c87d | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:33:51.094 | 054e1ec2b51440b5b5ef4e172042746c | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:33:51.183 | 391b092c0f3d40379afa7a5665c294f8 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:33:51.845 | f93965490ef34cf9b44c8937b2a7d75d | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:33:51.983 | 360bf625458e4b58b95035faa7f6fe26 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:33:52.016 | bf773cff94b74d39924a90b3db960fac | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:33:58.560 | 72cb7126f5224186ad4140fd6ccdef09 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:33:58.647 | 5b0c6961cffe465d981f35c478d7b92b | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:33:59.273 | 7774d6b352a04d48b78e5ee89a5e051a | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:33:59.354 | a6d0b9a68a2e421a908efde4dbdfba90 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:33:59.571 | 6c6bee504f65425abc32a57a549257d1 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:34:37.940 | 9e278825bff64beda2599feffcbc1071 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 16:34:38.114 | cb5df13802ed42fa9cf883f042071684 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-22 16:34:38.802 | d2c9d9178e1f487a8acf0108524257f6 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:34:38.940 | db50bf24a89f4d7e99a0cae664b2e7c6 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:34:39.033 | 2b1c23e9173d4a4bb6d26906349951aa | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:34:39.035 | b04930de55604767b804c8323be644d2 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:34:39.054 | 7b365f28dad4463ba3c9a74e2ee8a8f7 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:34:40.935 | 4ec6ddab386c4c8cb522e78ce6ac0cac | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:34:41.333 | b7517d41f16247508765904f979db913 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:34:41.641 | bd7ea24cb7e74f7aa46f73e0e5967b71 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:34:41.729 | 56137b799b1c4fcb93e2e8fee0e090c4 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:34:41.973 | f7ddaf4449964d589daec71b35a9ef09 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:34:43.472 | 2d3bcf04b6584885ac6b27fce106b2f9 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:34:43.865 | c3b3288442e44a3882b9713c28e14a30 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:34:44.215 | 3d2f92cf6fef4fd297ce5efeb1d931ce | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:34:44.284 | e9e97d676b1346ac8c3088da79cc7cf9 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:34:44.510 | fb2b68083e0e434a85e7f33c1b9c6207 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:34:47.030 | fdf7376a76e74eb6a651936d7bb2cb01 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:34:47.436 | 87d20546b7424e57b889a88418807c2d | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:34:47.745 | 082fc34acff84ef9816a8e78216f31a8 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:34:47.893 | 48e3d01a285f4d22951f3ec3df4390f6 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:34:48.119 | f44e1268fe6942a98f7f7486376811da | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:41:03.385 | 974f2254e53d499da8e8bf915dd52bde | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:41:03.776 | 173eb8ab05c84a66b43e10c269421494 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:41:03.897 | dab7b99646204703b2fcec5a1f73d065 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:41:04.173 | 585f4cee1f0440d39d66bc537683d4eb | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:41:04.195 | fbf9223e84ba43169857665a51432c33 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:41:04.289 | 9a20ce07f90b47559b405e71819e0828 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:41:04.780 | c01e6039c3364ef88d6090e9d3aaaa3a | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:41:04.791 | fe84a09608fd4c28bc3ce2811a2e7c2c | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:41:04.914 | e649abf7a5964ab1935b428eabc0b839 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:41:04.967 | 2fad9efb0bc24e64a63ecf7ca290cc41 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:41:05.239 | d11a586c243740b6866c3107250679de | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:41:05.628 | e1ff4d8c877441c5b57c76cc8e2688f7 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:41:06.031 | af18d76774784c9d8e6816661eef554f | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:41:06.105 | 763c92f76d3c4718a35ccc21e24ff4aa | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:41:06.137 | 3b28dd283d464a4c855ef0a0bf0c9c53 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:41:09.388 | 9506c0fcabd14e9381cc260416cce160 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:41:09.784 | 5ac6bbc425bb418f9292586575e34cbb | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:41:10.172 | d75f91ef3ce54df4bd593a023f349c51 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:41:10.229 | bfe5a78e50504b83b2c6d2275d405857 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:41:10.275 | 4f9f64451fd548608d5e85f5dbc754b8 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:41:15.567 | 6bdd7fc0db08447e9adaf5d0faa29841 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 16:41:15.743 | 4f5abbe7a5784ff9a7ba278f3416b8a2 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-22 16:41:16.564 | 3638564176ff4a178bf66f16e41936e1 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:41:16.623 | 4a6541b803b543399590f5415e9c665e | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:41:16.638 | a6f91e600b2a4b34a4b8c6ea9cd568d9 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:41:16.646 | 3dbe553b7ff648f2a9a269e28464a662 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:41:16.648 | c89484f21c7d477cb2f9c04a533e9fdc | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:41:19.006 | c465ba4c85904443906af6da933c0c9d | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:41:19.394 | 98962fdfa615414d960614e4d941267f | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:41:19.421 | 44c73ab1bb09475badcf9d73cee27c5f | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:41:19.423 | 986d88fb335c4bdabec8b1ea35ae6653 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:41:19.436 | 8c09516d009842d6ada91af8194fdc18 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:41:25.304 | 42bab82d0bc445eab4b54e8f2bd3cab8 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:41:25.699 | 4c8f1f42f80b47bd92cd85e19aad6ea4 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:41:25.989 | 0917a1b857c7444487ab809b6c368332 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:41:26.249 | 5b3c1e80dfd54f6c8e71dc086e51b6c2 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:41:26.457 | c2803e1260f948ebb530fe4ba3b79f6f | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:42:57.487 | 7af1abc8913c4cf8b724c017e76b65b5 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:42:57.573 | 1190e8fdc9ff45dabfda0029c6a2015e | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:42:58.319 | 1b2ad80e54154828836decca4eec5ebe | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:42:58.574 | 130ab8fb85a74c9bb57ca0eaa8b966c4 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:42:58.795 | acdebb4c61084f26931518d3853fa4b6 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:42:59.736 | b63185b59d1645d1abec7b637440e000 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:42:59.824 | c59d2ceae04045b5afdedb13517c0563 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:43:00.659 | 85ca862d2c134a6188387a3996893558 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:43:00.781 | 30fc7caea9524fdea710423825bd5415 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 16:43:00.804 | b55bdb442d4d41a1bf2b848619cd8e96 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:43:35.885 | da18284fbdac4b69992950121a5bfd99 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 16:43:35.974 | fdc0b2f5db374c7ba77f3b333cf69880 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 16:43:37.021 | 453e00ef069346d3904498db5380338e | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 16:43:37.131 | bcb4518700c042ed85f817966ee9e654 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 16:43:37.283 | 0b19a414017240ada019cbcaf40d220e | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 17:05:48.228 | fd34683addd04a3e87216c25ea198b4d | ERROR    | exceptions.handle:exception_handler:70 - (asyncmy.errors.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.email, sys_user.phonenumber, sys_user.sex, sys_user.avatar, sys_user.password, sys_user.status, sys_user.del_flag, sys_user.login_ip, sys_user.login_date, sys_user.create_by, sys_user.create_time, sys_user.update_by, sys_user.update_time, sys_user.remark 
FROM sys_user 
WHERE sys_user.status = %s AND sys_user.del_flag = %s AND sys_user.user_id = %s]
[parameters: ('0', '0', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):

  File "asyncmy\\connection.pyx", line 658, in asyncmy.connection.Connection._read_bytes
    data = await self._reader.readexactly(num_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\streams.py", line 758, in readexactly
    raise self._exception
          │    └ ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)
          └ <StreamReader exception=ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None) transport=<_SelectorSocketTranspo...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\selector_events.py", line 1005, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           │    │          │    └ 262144
           │    │          └ <_SelectorSocketTransport closed fd=1196>
           │    └ None
           └ <_SelectorSocketTransport closed fd=1196>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x000001CA3B619A80>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x000001CA3BD2BB60>
    └ <sqlalchemy.engine.base.Connection object at 0x000001CA5FAF3290>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('0', '0', 1)
    │      │       └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x000001CA3BC6B2E0>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001CA5FC82CF0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 95, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('0', '0', 1)
           │    │      │    │              └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x000001CA3BC6B420>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001CA5FC82CF0>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001CA5FC82CF0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000001CA5FBCEC40>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x000001CA5FDE8D80 (otid=0x000001CA39F22520) dead>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000001CA5FBCEC40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 107, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('0', '0', 1)
                   │    │               └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001CA5FC82CF0>
  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 619, in read_packet
    packet_header = await self._read_bytes(4)
  File "asyncmy\\connection.pyx", line 660, in _read_bytes
    raise errors.OperationalError(
          │      └ <class 'asyncmy.errors.OperationalError'>
          └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\asyncmy\\erro...

asyncmy.errors.OperationalError: (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 332
               │     └ 3
               └ <function _main at 0x000001CA36C15300>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 332
           │    └ <function BaseProcess._bootstrap at 0x000001CA3691C4A0>
           └ <SpawnProcess name='SpawnProcess-1' parent=12420 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001CA369339C0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12420 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001CA36912E40>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12420 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12420 started>
    │    └ <function subprocess_started at 0x000001CA38DDBB00>
    └ <SpawnProcess name='SpawnProcess-1' parent=12420 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=944, family=2, type=1, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001CA5EEACAD0>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=944, family=2, type=1, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x000001CA38DDACA0>
           │       │   └ <uvicorn.server.Server object at 0x000001CA5EEACAD0>
           │       └ <function run at 0x000001CA38A73BA0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001CA5EF9A940>
           │      └ <function Runner.run at 0x000001CA38AA7880>
           └ <asyncio.runners.Runner object at 0x000001CA5EEAD550>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-pac...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001CA38AA5300>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001CA5EEAD550>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 706, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001CA38AA5260>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 677, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001CA38AA7060>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2034, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001CA38604F40>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001CA5EEAF380>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001CA5F...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001C...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001CA5EEAC1A0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001CA5EEAF380>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001CA5F...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001C...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001CA5F...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001C...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001CA5F3E8980>
          └ <fastapi.applications.FastAPI object at 0x000001CA5EEAC1A0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001CA5FD98F40>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001C...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x000001CA5F3E8830>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001CA5F3E8980>

  File "D:\thinktank\thinktankapi\middlewares\trace_middleware\middle.py", line 47, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x000001CA5FD98C20>
          │    │   │      └ <function RequestResponseCycle.receive at 0x000001CA5FD9ACA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x000001CA5F3E86E0>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x000001CA5F3E8830>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x000001CA5FD98C20>
          │         │      └ <function RequestResponseCycle.receive at 0x000001CA5FD9ACA0>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x000001CA5FB04B00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x000001CA5C6F1B20>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x000001CA5FB04B00>
          │    │   │      └ <function RequestResponseCycle.receive at 0x000001CA5FD9ACA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001CA5F3E8590>
          └ <starlette.middleware.gzip.GZipResponder object at 0x000001CA5FB04B00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x000001CA5FB04B00>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x000001CA5FD9ACA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001CA5F3E8440>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001CA5F3E8590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x000001CA5FB04B00>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x000001CA5FD9ACA0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001CA5FD9B6B0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001CA5EDB6300>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001CA5F3E8440>
          └ <function wrap_app_handling_exceptions at 0x000001CA39D77C40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001CA5FD9AA20>
          │   │      └ <function RequestResponseCycle.receive at 0x000001CA5FD9ACA0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001CA5EDB6300>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001CA5FD9AA20>
          │    │                │      └ <function RequestResponseCycle.receive at 0x000001CA5FD9ACA0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001CA5EDB6300>>
          └ <fastapi.routing.APIRouter object at 0x000001CA5EDB6300>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001CA5FD9AA20>
          │     │      │      └ <function RequestResponseCycle.receive at 0x000001CA5FD9ACA0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x000001CA39DAD440>
          └ APIRoute(path='/getInfo', name='get_login_user_info', methods=['GET'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001CA5FD9AA20>
          │    │   │      └ <function RequestResponseCycle.receive at 0x000001CA5FD9ACA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001CA5EEC62A0>
          └ APIRoute(path='/getInfo', name='get_login_user_info', methods=['GET'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001CA5FD9AA20>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x000001CA5FD9ACA0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001CA5FD9B430>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001CA5FD9BE20>
          └ <function wrap_app_handling_exceptions at 0x000001CA39D77C40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001CA5FD99DA0>
          │   │      └ <function RequestResponseCycle.receive at 0x000001CA5FD9ACA0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001CA5FD9BE20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001CA5FD9B430>
                     └ <function get_request_handler.<locals>.app at 0x000001CA5EEC6200>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 291, in app
    solved_result = await solve_dependencies(
                          └ <function solve_dependencies at 0x000001CA39D74C20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\dependencies\utils.py", line 638, in solve_dependencies
    solved = await call(**solved_result.values)
                   │      │             └ {'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMSIsInVzZXJfbmFtZSI6ImFkbWluIiwiZGVwdF9uYW1lIjoiXHU3ODE0XHU1M...
                   │      └ SolvedDependency(values={'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMSIsInVzZXJfbmFtZSI6ImFkbWluIiwiZGVwd...
                   └ <bound method LoginService.get_current_user of <class 'module_admin.service.login_service.LoginService'>>

  File "D:\thinktank\thinktankapi\module_admin\service\login_service.py", line 212, in get_current_user
    query_user = await UserDao.get_user_by_id(query_db, user_id=token_data.user_id)
                       │       │              │                 │          └ 1
                       │       │              │                 └ TokenData(user_id=1)
                       │       │              └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001CA60F749B0>
                       │       └ <classmethod(<function UserDao.get_user_by_id at 0x000001CA5C911120>)>
                       └ <class 'module_admin.dao.user_dao.UserDao'>

  File "D:\thinktank\thinktankapi\module_admin\dao\user_dao.py", line 89, in get_user_by_id
    await db.execute(
          │  └ <function AsyncSession.execute at 0x000001CA3BBE42C0>
          └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001CA60F749B0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 463, in execute
    result = await greenlet_spawn(
                   └ <function greenlet_spawn at 0x000001CA3A0C4680>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x000001CA5FDE8D80 (otid=0x000001CA39F22520) dead>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           │    └ <function Session._execute_internal at 0x000001CA3B9140E0>
           └ <sqlalchemy.orm.session.Session object at 0x000001CA5FAE24E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                   │      │                 └ <classmethod(<function AbstractORMCompileState.orm_execute_statement at 0x000001CA3B7B0AE0>)>
                   │      └ <class 'sqlalchemy.orm.context.ORMSelectCompileState'>
                   └ typing.Any
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
             │    └ <function Connection.execute at 0x000001CA3B5723E0>
             └ <sqlalchemy.engine.base.Connection object at 0x000001CA5FAF3290>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
           └ <bound method ClauseElement._execute_on_connection of <sqlalchemy.sql.selectable.Select object at 0x000001CA5FAE3D40>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           │          └ <function Connection._execute_clauseelement at 0x000001CA3B572700>
           └ <sqlalchemy.engine.base.Connection object at 0x000001CA5FAF3290>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
          │    └ <function Connection._execute_context at 0x000001CA3B5728E0>
          └ <sqlalchemy.engine.base.Connection object at 0x000001CA5FAF3290>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           │    └ <function Connection._exec_single_context at 0x000001CA3B572980>
           └ <sqlalchemy.engine.base.Connection object at 0x000001CA5FAF3290>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    │    └ <function Connection._handle_dbapi_exception at 0x000001CA3B572C00>
    └ <sqlalchemy.engine.base.Connection object at 0x000001CA5FAF3290>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ OperationalError(2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
          │                    │              └ (<class 'asyncmy.errors.OperationalError'>, OperationalError(2013, 'Lost connection to MySQL server during query ([WinError 1...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ OperationalError("(asyncmy.errors.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x000001CA3B619A80>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x000001CA3BD2BB60>
    └ <sqlalchemy.engine.base.Connection object at 0x000001CA5FAF3290>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('0', '0', 1)
    │      │       └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x000001CA3BC6B2E0>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001CA5FC82CF0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 95, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('0', '0', 1)
           │    │      │    │              └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x000001CA3BC6B420>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001CA5FC82CF0>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001CA5FC82CF0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000001CA5FBCEC40>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x000001CA5FDE8D80 (otid=0x000001CA39F22520) dead>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000001CA5FBCEC40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 107, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('0', '0', 1)
                   │    │               └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001CA5FC82CF0>
  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 619, in read_packet
    packet_header = await self._read_bytes(4)
  File "asyncmy\\connection.pyx", line 660, in _read_bytes
    raise errors.OperationalError(
          │      └ <class 'asyncmy.errors.OperationalError'>
          └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\asyncmy\\erro...

sqlalchemy.exc.OperationalError: (asyncmy.errors.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.email, sys_user.phonenumber, sys_user.sex, sys_user.avatar, sys_user.password, sys_user.status, sys_user.del_flag, sys_user.login_ip, sys_user.login_date, sys_user.create_by, sys_user.create_time, sys_user.update_by, sys_user.update_time, sys_user.remark 
FROM sys_user 
WHERE sys_user.status = %s AND sys_user.del_flag = %s AND sys_user.user_id = %s]
[parameters: ('0', '0', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-22 17:05:48.310 | a256ef4516b442f5b734423244549f38 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-22 17:05:48.368 | 9e13cd4619974f7da9891105c485d348 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为09236e25-71cc-4e0b-903f-85b890b0975e的会话获取图片验证码成功
2025-06-22 17:05:59.565 | 66451be6517141ea9b22c875048f654f | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-22 17:06:00.017 | a4436d521737466f8355852c511fbef8 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 17:06:00.620 | 0b9d4ac741b0444b96f4f0ff2f5d6087 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-22 17:06:04.094 | f6d1701f3c1d48b889fa2d7fa4431d8c | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 17:06:04.146 | 57fb24f373c440f5aaa2d5214f146933 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 17:06:04.170 | 8f73076887a84c1e8f7c2ebc80db0868 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 17:06:04.178 | 81dd7b97c01549cc9e8dd13abba2bd6b | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 17:06:04.193 | 4bb66e456c7a4d18897e4151461d7dcf | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 17:06:07.871 | 9b4aa591d15c46299521a4699a14ad13 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 17:06:07.972 | 794fca25f7c942498323be6c042436b7 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 17:06:08.626 | 75f8d003ba444fd9b9b6a05e13e00e67 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 17:06:08.661 | 4fed0218adf24b6eabc3d0397e9370b8 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 17:06:09.279 | 5fe5a5b17af24d88b8b0e40cf9277eaf | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 17:06:11.584 | c29dc980ba1e4f5d99cb6b5b612d0cea | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 17:06:11.695 | a588ab23026b4a398775a2e5fff09de9 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 17:06:12.308 | ae354bef7b644b80bf5fc32a01e19f90 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 17:06:12.366 | bc81d75da34740559989c60b123474f6 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 17:06:12.928 | 330e0b68780346f59d39694cd0556920 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 17:09:23.636 | 3c06c5649b444926998a9de5abe769b3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 17:09:23.845 | 29275e8d60524725bfecbf0c793dbbbd | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-22 17:09:24.423 | 463cabc656544cc2ba0cec2115369e66 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 17:09:24.558 | c5d195704dc04c8e884c74f3aaee4266 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 17:09:24.635 | 8a4eb400a70c4dc3a90b066b7c9ee14c | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 17:09:24.643 | 3c3d2174432949cf87e9b8a6157285c1 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 17:09:24.657 | 7c76b591f9fa494488b9d70d2c282020 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 17:10:06.158 | 2d94db07a0264be790f0a86d3d12deab | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 17:10:06.344 | ad08104b1bf84139933395e58e45ee5f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-22 17:10:07.067 | 53eb0fa8d6c94833b76aabf19d3b0fd0 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 17:10:07.115 | 3d29854dfbda4633b4262719a6727ea5 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 17:10:07.142 | 615259e38fdb45438e7b77cbaa24e00f | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 17:10:07.147 | 77f3600f22ef46e89504676055617505 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 17:10:07.176 | b2ba4655403d4ccfa09079abc24af46b | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 17:10:37.537 | 525e3d8124974783acfb1317f91be2f4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 17:10:37.731 | 486b29d270304bea93372e03de3fecb3 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-22 17:10:38.383 | e0031918c0e34c1da2f3f76510342472 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 17:10:38.440 | 91be742acc0640209ddcb8ebaf8849f6 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 17:10:38.442 | 48b0b4ed05aa44588397580ab0199507 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 17:10:38.449 | da735e16dd544281be1e161ebe737e1e | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 17:10:38.471 | ae3ec3e84acb4978be2a28e68119588d | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 17:12:24.111 | 64b38d8eae6b41ccbac2f0865e2d43d4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-22 17:12:24.296 | 3125db743944440d82660879ab422232 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-22 17:12:24.762 | 5ee3da78db4f4f4d80982617f6a52074 | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 17:12:24.910 | cacda29f36e8489f9c33789929189c60 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 17:12:24.972 | 79cf18974d434f76bba5af601c5380a2 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 17:12:24.974 | 022883cbd0714f329cad18e1a4fda693 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 17:12:24.993 | ecd7021d04394ac8ba3fdb7a4ebbc219 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 17:12:40.630 | 5ba4e84368d74c78a71b55cdc3ebd9dd | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 17:12:41.047 | d120f9c66e8949afbc5912729a03e0a7 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 17:12:41.067 | e4ce770bca614696a39dcf7ab46671cf | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 17:12:41.069 | fb59ddd85d8a406a9427e167046cde77 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 17:12:41.095 | 7166cf42505744eab857bc6af00b32ca | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 17:12:41.379 | 40dc8f5557534d5987e558826cccf44f | INFO     | module_admin.controller.api_mapping_controller:get_comprehensive_spread_analysis:180 - 获取综合传播分析成功，方案ID: 1
2025-06-22 17:12:41.474 | 2a1c4fe462994c5590c99cc846407ca4 | INFO     | module_admin.controller.api_mapping_controller:get_emotion_statistics:200 - 获取情感统计数据成功
2025-06-22 17:12:41.807 | 6b23704c52bb4cb4acab670397cd1634 | INFO     | module_admin.controller.api_mapping_controller:get_event_news_statistics:260 - 获取事件新闻关联统计数据成功
2025-06-22 17:12:41.809 | 2cf8c545ddce406e8448283fb89075b6 | INFO     | module_admin.controller.api_mapping_controller:get_event_statistics:240 - 获取事件统计数据成功
2025-06-22 17:12:41.829 | 3b0abbde586648f28c3c29d2f6100f56 | INFO     | module_admin.controller.api_mapping_controller:get_hot_news_extension_statistics:220 - 获取热点新闻扩展统计数据成功
2025-06-22 17:24:38.202 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-22 17:24:38.223 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-22 17:24:45.859 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-22 17:24:45.863 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
