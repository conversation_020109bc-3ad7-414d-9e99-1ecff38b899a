from datetime import datetime
from sqlalchemy import and_, or_, desc, asc, func, select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from module_opinion.entity.do.opinion_task_do import OpinionTask
from module_opinion.entity.vo.opinion_task_vo import OpinionTaskPageQueryModel, OpinionTaskModel
from utils.page_util import PageUtil


class OpinionTaskDao:
    """
    舆情任务数据访问层
    """

    @classmethod
    async def get_opinion_task_list(
        cls, db: AsyncSession, query_object: OpinionTaskPageQueryModel, is_page: bool = False
    ):
        """
        根据查询参数获取舆情任务列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 舆情任务列表信息对象
        """
        # 使用物理删除，不需要默认的软删除过滤条件
        query = select(OpinionTask)

        # 详细记录查询条件，用于诊断用户数据隔离问题
        from utils.log_util import logger
        logger.info(f'[DAO查询] 开始构建舆情任务查询条件')
        logger.info(f'[DAO查询] 查询对象: {query_object}')
        logger.info(f'[DAO查询] 用户ID: {query_object.user_id} (类型: {type(query_object.user_id)})')
        logger.info(f'[DAO查询] 使用物理删除，无需软删除过滤条件')

        # 构建查询条件
        if query_object.requirement_id:
            logger.info(f'[DAO查询] 添加需求ID过滤条件: {query_object.requirement_id}')
            query = query.where(OpinionTask.requirement_id == query_object.requirement_id)

        # 关键的用户ID过滤条件 - 确保用户数据隔离
        if query_object.user_id is not None:
            logger.info(f'[DAO查询] 添加用户ID过滤条件: {query_object.user_id} - 这是用户数据隔离的关键')
            query = query.where(OpinionTask.user_id == query_object.user_id)
        else:
            logger.warning(f'[DAO查询] 用户ID为None，未添加用户过滤条件 - 这可能导致数据泄露！')

        # 筛选条件计数和记录
        filter_count = 0
        applied_filters = []

        if query_object.task_name and query_object.task_name.strip():
            logger.info(f'[DAO查询] 添加任务名称过滤条件: {query_object.task_name}')
            query = query.where(OpinionTask.task_name.like(f'%{query_object.task_name.strip()}%'))
            filter_count += 1
            applied_filters.append(f'任务名称包含"{query_object.task_name}"')

        if query_object.task_type and query_object.task_type.strip():
            logger.info(f'[DAO查询] 添加任务类型过滤条件: {query_object.task_type}')
            query = query.where(OpinionTask.task_type == query_object.task_type.strip())
            filter_count += 1
            applied_filters.append(f'任务类型={query_object.task_type}')

        # 新增：支持任务描述筛选（用于关键词搜索）
        if hasattr(query_object, 'task_description') and query_object.task_description and query_object.task_description.strip():
            logger.info(f'[DAO查询] 添加任务描述过滤条件: {query_object.task_description}')
            query = query.where(OpinionTask.task_description.like(f'%{query_object.task_description.strip()}%'))
            filter_count += 1
            applied_filters.append(f'任务描述包含"{query_object.task_description}"')

        if query_object.frequency:
            logger.info(f'[DAO查询] 添加执行频率过滤条件: {query_object.frequency}')
            query = query.where(OpinionTask.frequency == query_object.frequency)
            filter_count += 1
            applied_filters.append(f'执行频率={query_object.frequency}')

        if query_object.status is not None:
            logger.info(f'[DAO查询] 添加状态过滤条件: {query_object.status}')
            query = query.where(OpinionTask.status == query_object.status)
            filter_count += 1
            applied_filters.append(f'状态={query_object.status}')

        # 如果明确指定了 is_enabled 参数，则添加过滤条件
        if query_object.is_enabled is not None:
            logger.info(f'[DAO查询] 添加启用状态过滤条件: {query_object.is_enabled}')
            query = query.where(OpinionTask.is_enabled == query_object.is_enabled)
            filter_count += 1
            applied_filters.append(f'启用状态={query_object.is_enabled}')

        if query_object.create_time_start:
            logger.info(f'[DAO查询] 添加创建时间开始过滤条件: {query_object.create_time_start}')
            query = query.where(OpinionTask.create_time >= query_object.create_time_start)
            filter_count += 1
            applied_filters.append(f'创建时间>={query_object.create_time_start}')

        if query_object.create_time_end:
            logger.info(f'[DAO查询] 添加创建时间结束过滤条件: {query_object.create_time_end}')
            query = query.where(OpinionTask.create_time <= query_object.create_time_end)
            filter_count += 1
            applied_filters.append(f'创建时间<={query_object.create_time_end}')

        # 记录筛选条件汇总
        if filter_count > 0:
            logger.info(f'🔍 共应用了 {filter_count} 个筛选条件: {", ".join(applied_filters)}')
        else:
            logger.info('🔍 未应用任何筛选条件，将返回全部数据')

        # 排序
        query = query.order_by(desc(OpinionTask.create_time))

        # 记录最终的SQL查询（用于调试）
        logger.info(f'[DAO查询] 最终查询SQL: {str(query)}')

        if is_page:
            # 分页查询
            logger.info(f'[DAO查询] 执行分页查询 - 页码: {query_object.page_num}, 页大小: {query_object.page_size}')
            result = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page=True)
            logger.info(f'[DAO查询] 分页查询结果 - 总记录数: {result.total}, 当前页记录数: {len(result.records)}')
            return result
        else:
            # 不分页查询
            logger.info(f'[DAO查询] 执行不分页查询')
            result = await db.execute(query)
            records = result.scalars().all()
            logger.info(f'[DAO查询] 不分页查询结果 - 记录数: {len(records)}')
            return records

    @classmethod
    async def get_opinion_task_by_id(cls, db: AsyncSession, task_id: int):
        """
        根据ID获取舆情任务详细信息

        :param db: orm对象
        :param task_id: 任务ID
        :return: 舆情任务信息对象
        """
        query = select(OpinionTask).where(OpinionTask.id == task_id)
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def add_opinion_task_dao(cls, db: AsyncSession, task: OpinionTaskModel):
        """
        新增舆情任务数据库操作

        :param db: orm对象
        :param task: 舆情任务对象
        :return: 新增的舆情任务对象
        """
        db_task = OpinionTask(**task.model_dump(exclude={'id'}))
        db.add(db_task)
        await db.flush()
        await db.refresh(db_task)
        return db_task

    @classmethod
    async def edit_opinion_task_dao(cls, db: AsyncSession, task: dict):
        """
        编辑舆情任务数据库操作

        :param db: orm对象
        :param task: 需要更新的舆情任务字典
        :return:
        """
        task_id = task.pop('id')  # 移除id，避免更新主键
        result = await db.execute(
            update(OpinionTask).where(OpinionTask.id == task_id).values(**task)
        )
        return result.rowcount  # 返回受影响的行数

    @classmethod
    async def delete_opinion_task_dao(cls, db: AsyncSession, task_ids: List[int]):
        """
        删除舆情任务数据库操作（物理删除）

        :param db: orm对象
        :param task_ids: 舆情任务ID列表
        :return: 删除的记录数量
        """
        from sqlalchemy import delete

        # 执行物理删除
        result = await db.execute(
            delete(OpinionTask)
            .where(OpinionTask.id.in_(task_ids))
        )

        return result.rowcount  # 返回实际删除的记录数量

    @classmethod
    async def update_report_oss_url(cls, db: AsyncSession, task_id: int, report_oss_url: str):
        """
        更新舆情任务的报告OSS URL

        :param db: orm对象
        :param task_id: 任务ID
        :param report_oss_url: 报告OSS访问URL
        :return: 受影响的行数
        """
        result = await db.execute(
            update(OpinionTask)
            .where(OpinionTask.id == task_id)
            .values(report_oss_url=report_oss_url, update_time=datetime.now())
        )
        return result.rowcount

    @classmethod
    async def update_push_url(cls, db: AsyncSession, task_id: int, push_url: str):
        """
        更新舆情任务的推送URL

        :param db: orm对象
        :param task_id: 任务ID
        :param push_url: 推送URL
        :return: 受影响的行数
        """
        result = await db.execute(
            update(OpinionTask)
            .where(OpinionTask.id == task_id)
            .values(push_url=push_url, update_time=datetime.now())
        )
        return result.rowcount

    @classmethod
    async def get_tasks_by_requirement(cls, db: AsyncSession, requirement_id: int, user_id: int = None):
        """
        根据需求ID获取任务列表（支持用户过滤）

        :param db: orm对象
        :param requirement_id: 需求ID
        :param user_id: 用户ID，可选，用于用户级别隔离
        :return: 任务列表
        """
        query = select(OpinionTask).where(
            OpinionTask.requirement_id == requirement_id
        )

        # 如果提供了用户ID，添加用户过滤条件
        if user_id is not None:
            query = query.where(OpinionTask.user_id == user_id)

        query = query.order_by(desc(OpinionTask.create_time))

        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def update_task_status(cls, db: AsyncSession, task_id: int, status: int):
        """
        更新任务状态

        :param db: orm对象
        :param task_id: 任务ID
        :param status: 新状态
        :return:
        """
        await db.execute(
            update(OpinionTask)
            .where(OpinionTask.id == task_id)
            .values(status=status, update_time=datetime.now())
        )

    @classmethod
    async def update_task_execution_time(cls, db: AsyncSession, task_id: int, execution_time: datetime):
        """
        更新任务执行时间

        :param db: orm对象
        :param task_id: 任务ID
        :param execution_time: 执行时间
        :return:
        """
        await db.execute(
            update(OpinionTask)
            .where(OpinionTask.id == task_id)
            .values(execution_time=execution_time, update_time=datetime.now())
        )

    @classmethod
    async def get_pending_tasks(cls, db: AsyncSession, limit: int = 100):
        """
        获取待执行的任务

        :param db: orm对象
        :param limit: 限制数量
        :return: 待执行任务列表
        """
        query = select(OpinionTask).where(
            and_(
                OpinionTask.status == 'pending',  # 待执行
                OpinionTask.next_execute_time <= datetime.now(),
                OpinionTask.is_enabled == 1
            )
        ).order_by(asc(OpinionTask.next_execute_time)).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_task_statistics(cls, db: AsyncSession, requirement_id: Optional[int] = None):
        """
        获取任务统计信息

        :param db: orm对象
        :param requirement_id: 需求ID（可选）
        :return: 统计信息
        """
        base_query = select(OpinionTask)
        
        if requirement_id:
            base_query = base_query.where(OpinionTask.requirement_id == requirement_id)

        # 总任务数
        total_query = select(func.count(OpinionTask.id)).select_from(base_query.subquery())
        total_result = await db.execute(total_query)
        total_count = total_result.scalar()

        # 各状态任务数
        status_query = select(
            OpinionTask.status,
            func.count(OpinionTask.id).label('count')
        ).select_from(base_query.subquery()).group_by(OpinionTask.status)

        status_result = await db.execute(status_query)
        status_counts = {row.status: row.count for row in status_result.fetchall()}

        return {
            'total_count': total_count,
            'pending_count': status_counts.get('pending', 0),      # 待执行
            'running_count': status_counts.get('running', 0),      # 执行中
            'completed_count': status_counts.get('completed', 0),    # 已完成
            'failed_count': status_counts.get('failed', 0),       # 执行失败
            'cancelled_count': status_counts.get('cancelled', 0)     # 已取消
        }

    @classmethod
    async def check_task_name_unique(cls, db: AsyncSession, requirement_id: int, task_name: str, task_id: Optional[int] = None):
        """
        检查任务名称在需求下是否唯一

        :param db: orm对象
        :param requirement_id: 需求ID
        :param task_name: 任务名称
        :param task_id: 任务ID（编辑时排除自己）
        :return: 是否唯一
        """
        query = select(func.count(OpinionTask.id)).where(
            and_(
                OpinionTask.requirement_id == requirement_id,
                OpinionTask.task_name == task_name
            )
        )

        if task_id:
            query = query.where(OpinionTask.id != task_id)

        result = await db.execute(query)
        count = result.scalar()
        return count == 0

    @classmethod
    async def check_task_exists_by_content(cls, db: AsyncSession, requirement_id: int, task_type: str, push_url: str):
        """
        检查基于内容的任务是否已存在

        :param db: orm对象
        :param requirement_id: 需求ID
        :param task_type: 任务类型
        :param push_url: 推送URL
        :return: 已存在的任务对象或None
        """
        query = select(OpinionTask).where(
            and_(
                OpinionTask.requirement_id == requirement_id,
                OpinionTask.task_type == task_type,
                OpinionTask.push_url == push_url
            )
        ).order_by(desc(OpinionTask.create_time))

        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def update_sentiment_counts(
        cls,
        db: AsyncSession,
        task_id: int,
        positive_count: int,
        negative_count: int,
        neutral_count: int
    ):
        """
        更新任务的情感统计数据

        :param db: orm对象
        :param task_id: 任务ID
        :param positive_count: 正面情感数量
        :param negative_count: 负面情感数量
        :param neutral_count: 中性情感数量
        :return: 更新的行数
        """
        result = await db.execute(
            update(OpinionTask)
            .where(OpinionTask.id == task_id)
            .values(
                positive_count=positive_count,
                negative_count=negative_count,
                neutral_count=neutral_count,
                update_time=datetime.now()
            )
        )
        return result.rowcount
