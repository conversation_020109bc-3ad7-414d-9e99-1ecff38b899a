import request from '@/utils/request'

// 获取舆情分析模板列表
export function getOpinionTemplateList(query) {
  return request({
    url: '/opinion/template/list',
    method: 'get',
    params: query
  })
}

// 获取用于选择的模板列表
export function getTemplatesForSelection() {
  return request({
    url: '/opinion/template/selection',
    method: 'get'
  })
}

// 获取模板分类列表
export function getTemplateCategories() {
  return request({
    url: '/opinion/template/categories',
    method: 'get'
  })
}

// 获取舆情分析模板详情
export function getOpinionTemplateDetail(templateId) {
  return request({
    url: `/opinion/template/${templateId}`,
    method: 'get'
  })
}

// 新增舆情分析模板
export function addOpinionTemplate(data) {
  return request({
    url: '/opinion/template/add',
    method: 'post',
    data: data
  })
}

// 编辑舆情分析模板
export function editOpinionTemplate(data) {
  return request({
    url: '/opinion/template/edit',
    method: 'put',
    data: data
  })
}

// 删除舆情分析模板
export function deleteOpinionTemplate(ids) {
  return request({
    url: '/opinion/template/delete',
    method: 'delete',
    data: {
      ids: ids
    }
  })
}

// 更新模板使用次数
export function updateTemplateUsage(templateId, increment = 1) {
  return request({
    url: '/opinion/template/usage',
    method: 'put',
    data: {
      template_id: templateId,
      increment: increment
    }
  })
}
