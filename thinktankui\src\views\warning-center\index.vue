<template>
  <div class="app-container">
    <div class="page-container">
      <!-- 左侧导航栏 -->
      <div class="left-sidebar" :class="{ 'collapsed': sidebarCollapsed }">
        <div class="sidebar-header">
          <el-button type="warning" class="new-scheme-btn" @click="createNewScheme">
            <i class="el-icon-plus"></i> 新建方案
          </el-button>
          <div class="sidebar-btn" @click="toggleSidebar">
            <i class="el-icon-s-fold"></i>
          </div>
        </div>

        <div class="sidebar-search">
          <el-input
            v-model="sidebarSearchText"
            placeholder="搜索"
            prefix-icon="el-icon-search"
            size="small"
            @input="searchSidebar"
          ></el-input>
        </div>

        <div class="sidebar-menu">
          <el-menu
            :default-active="activeMenuItem"
            class="sidebar-menu-list"
            @select="handleMenuSelect"
          >
            <template v-for="(item, index) in menuCategories">
              <!-- 使用唯一的key -->
              <el-menu-item
                v-if="item.isItem"
                :key="'item-' + item.name"
                :index="item.name"
                :class="{ 'active-menu-item': activeMenuItem === item.name }"
              >
                <i :class="item.icon" v-if="item.icon"></i>
                <span>{{ item.name }}</span>
              </el-menu-item>

              <!-- 如果是子菜单 -->
              <el-submenu
                v-else
                :key="'submenu-' + item.name"
                :index="item.name"
              >
                <template slot="title">
                  <i :class="item.icon" v-if="item.icon"></i>
                  <span>{{ item.name }}({{ item.count }})</span>
                </template>
                <!-- 子菜单项 -->
                <el-menu-item
                  v-for="child in item.children"
                  :key="child.name"
                  :index="child.name"
                >
                  {{ child.name }}
                </el-menu-item>
              </el-submenu>
            </template>
          </el-menu>
        </div>
      </div>

      <!-- 右侧内容区 -->
      <div class="right-content">
        <!-- 主体内容 -->
        <div class="main-content">
          <!-- 标题和操作区 -->
          <div class="title-area">
            <div class="title">
              <div style="width: 100%; text-align: left;">
                <h2><i class="el-icon-warning-outline" style="color: #E6A23C; margin-right: 8px;"></i>{{ activeMenuItem || '请选择方案' }}<i class="el-icon-edit-outline"></i></h2>
                <div class="tabs" style="text-align: left; margin-top: 10px; margin-left: 0;">
                  <el-button type="text" icon="el-icon-user">接收人设置</el-button>
                  <el-button type="text" icon="el-icon-bell" @click="openWarningDialog">预警设置</el-button>
                  <el-button type="text" icon="el-icon-data-analysis" @click="openKeywordDialog">关键词设置</el-button>
                </div>
              </div>
            </div>
            <div class="actions">
              <el-switch v-model="autoRefresh" active-text="预警开关"></el-switch>
              <div>
                <el-button type="primary" size="small">人工预警</el-button>
                <el-button type="primary" size="small" @click="openAutoWarningDialog">自动预警</el-button>
              </div>
            </div>
          </div>

          <!-- 调试信息 (临时) -->
          <!-- <div style="background: #f0f0f0; padding: 10px; margin-bottom: 10px; border-radius: 4px; font-size: 12px;">
            <strong>🔍 调试信息:</strong><br>
            statisticsData: {{ statisticsData }}<br>
            activeSchemeId: {{ activeSchemeId }}<br>
            activeMenuItem: {{ activeMenuItem }}
          </div> -->

          <!-- 统计数据卡片区 -->
          <div class="statistics-area" v-if="statisticsData">
            <el-row :gutter="20">
              <el-col :span="4">
                <div class="stat-card">
                  <div class="stat-number">{{ statisticsData.total_count || 0 }}</div>
                  <div class="stat-label">总计</div>
                </div>
              </el-col>
              <el-col :span="4">
                <div class="stat-card pending">
                  <div class="stat-number">{{ statisticsData.pending_count || 0 }}</div>
                  <div class="stat-label">待处理</div>
                </div>
              </el-col>
              <el-col :span="4">
                <div class="stat-card processed">
                  <div class="stat-number">{{ statisticsData.processed_count || 0 }}</div>
                  <div class="stat-label">已处理</div>
                </div>
              </el-col>
              <el-col :span="4">
                <div class="stat-card urgent">
                  <div class="stat-number">{{ statisticsData.urgent_count || 0 }}</div>
                  <div class="stat-label">紧急</div>
                </div>
              </el-col>
              <el-col :span="4">
                <div class="stat-card negative">
                  <div class="stat-number">{{ statisticsData.negative_count || 0 }}</div>
                  <div class="stat-label">负面</div>
                </div>
              </el-col>
              <el-col :span="4">
                <div class="stat-card positive">
                  <div class="stat-number">{{ statisticsData.positive_count || 0 }}</div>
                  <div class="stat-label">正面</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 表格区域 -->
          <div class="table-area">
            <div class="table-toolbar">
              <div class="left-tools">
                <el-checkbox
                  v-model="allSelected"
                  :indeterminate="indeterminate"
                  @change="handleSelectAll">
                  全选
                </el-checkbox>
                <el-button type="text" icon="el-icon-star-off" @click="batchToggleFavorite" :disabled="multipleSelection.length === 0">收藏</el-button>
                <el-button type="text" icon="el-icon-message" @click="batchSendMessage" :disabled="multipleSelection.length === 0">消息</el-button>
                <el-button type="text" icon="el-icon-download" @click="batchDownload" :disabled="multipleSelection.length === 0">下载</el-button>
              </div>
              <div class="right-tools">
                <span>共计{{total}}条</span>
                <el-button type="text" icon="el-icon-download" @click="exportData">导出下载</el-button>
                <el-dropdown @command="handleColumnCommand">
                  <span class="el-dropdown-link">
                    字段<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="title" :class="{ 'is-disabled': !visibleColumns.title }">
                      <el-checkbox v-model="visibleColumns.title">标题/摘要</el-checkbox>
                    </el-dropdown-item>
                    <el-dropdown-item command="source" :class="{ 'is-disabled': !visibleColumns.source }">
                      <el-checkbox v-model="visibleColumns.source">来源类型</el-checkbox>
                    </el-dropdown-item>
                    <el-dropdown-item command="platform" :class="{ 'is-disabled': !visibleColumns.platform }">
                      <el-checkbox v-model="visibleColumns.platform">平台类型</el-checkbox>
                    </el-dropdown-item>
                    <el-dropdown-item command="time" :class="{ 'is-disabled': !visibleColumns.time }">
                      <el-checkbox v-model="visibleColumns.time">发布时间</el-checkbox>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <div class="date-range">
                  <span>2023/04/23 08:00:00 - 2023/04/25 01:00:00</span>
                </div>
                <el-dropdown @command="handleStatusFilter">
                  <span class="el-dropdown-link">
                    {{ (statusOptions.find(item => item.value === statusFilter) || {}).label || '全部' }}<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      v-for="option in statusOptions"
                      :key="option.value"
                      :command="option.value"
                      :class="{ 'is-active': statusFilter === option.value }">
                      {{ option.label }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-input
                  placeholder="搜索"
                  prefix-icon="el-icon-search"
                  v-model="searchText"
                  style="width: 200px;"
                  clearable
                  @input="handleSearch"
                  @clear="handleSearchClear"
                ></el-input>
              </div>
            </div>

            <el-table
              ref="table"
              :data="tableData"
              style="width: 100%"
              v-loading="loading"
              element-loading-text="加载中..."
              @selection-change="handleSelectionChange">
              <el-table-column
                type="selection"
                width="55">
              </el-table-column>
              <el-table-column
                label=""
                width="120">
                <template slot-scope="scope">
                  <el-button type="text" icon="el-icon-star-off" @click="toggleFavorite(scope.row)" :class="{ 'is-favorite': scope.row.isFavorite }"></el-button>
                  <el-button type="text" icon="el-icon-message" @click="sendMessage(scope.row)"></el-button>
                  <el-button type="text" icon="el-icon-download" @click="downloadRecord(scope.row)"></el-button>
                </template>
              </el-table-column>
              <el-table-column
                v-if="visibleColumns.title"
                prop="title"
                label="标题/摘要"
                show-overflow-tooltip>
              </el-table-column>
              <el-table-column
                v-if="visibleColumns.source"
                prop="source"
                label="来源类型"
                width="100">
                <template slot-scope="scope">
                  <el-tag size="mini" type="danger">{{ scope.row.source }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                v-if="visibleColumns.platform"
                prop="platform"
                label="平台类型"
                width="100">
              </el-table-column>
              <el-table-column
                v-if="visibleColumns.time"
                prop="time"
                label="发布时间"
                width="150">
              </el-table-column>
              <el-table-column
                label="操作"
                width="200">
                <template slot-scope="scope">
                  <el-button type="text" size="mini" icon="el-icon-view" @click="viewRecord(scope.row)">查看</el-button>
                  <el-button type="text" size="mini" icon="el-icon-edit" @click="editRecord(scope.row)">编辑</el-button>
                  <el-button type="text" size="mini" icon="el-icon-delete" @click="deleteRecord(scope.row)">删除</el-button>
                  <el-dropdown @command="handleMoreAction" trigger="click">
                    <el-button type="text" size="mini">
                      更多<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="{ action: 'share', row: scope.row }">
                        <i class="el-icon-share"></i> 分享
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'copy', row: scope.row }">
                        <i class="el-icon-document-copy"></i> 复制链接
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'export', row: scope.row }">
                        <i class="el-icon-download"></i> 导出
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total>0"
              :total="total"
              :page.sync="currentPage"
              :limit.sync="pageSize"
              @pagination="getList"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 预警设置抽屉 -->
    <el-drawer
      title="预警设置"
      :visible.sync="warningDialogVisible"
      direction="rtl"
      size="30%"
      :before-close="closeWarningDialog"
      custom-class="warning-drawer">
      <div class="warning-drawer-content">
        <!-- 平台类型 -->
        <div class="warning-section">
          <h3>{{ warningSettings.platformType.title }}</h3>
          <div class="warning-options">
            <el-checkbox
              v-for="(option, index) in warningSettings.platformType.options"
              :key="'platform-' + index"
              v-model="option.checked"
              @change="option.value === 'all' && handleAllCheckbox(warningSettings.platformType)">
              {{ option.label }}
            </el-checkbox>
          </div>
        </div>

        <!-- 内容属性 -->
        <div class="warning-section">
          <h3>{{ warningSettings.contentProperty.title }}</h3>
          <div class="warning-options">
            <el-radio-group v-model="warningSettings.contentProperty.value">
              <el-radio
                v-for="(option, index) in warningSettings.contentProperty.options"
                :key="'content-property-' + index"
                :label="option.value">
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <!-- 信息类型 -->
        <div class="warning-section">
          <h3>{{ warningSettings.infoType.title }}</h3>
          <div class="warning-options">
            <el-radio-group v-model="warningSettings.infoType.value">
              <el-radio
                v-for="(option, index) in warningSettings.infoType.options"
                :key="'info-type-' + index"
                :label="option.value">
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <!-- 匹配对象 -->
        <div class="warning-section">
          <h3>{{ warningSettings.matchObject.title }}</h3>
          <div class="warning-options">
            <el-checkbox
              v-model="warningSettings.matchObject.allChecked"
              @change="handleMatchObjectAll">
              全部
            </el-checkbox>
            <el-checkbox
              v-for="(option, index) in warningSettings.matchObject.options"
              :key="'match-object-' + index"
              v-model="option.checked"
              :disabled="warningSettings.matchObject.allChecked">
              {{ option.label }}
            </el-checkbox>
          </div>
        </div>

        <!-- 匹配方式 -->
        <div class="warning-section">
          <h3>{{ warningSettings.matchMethod.title }}</h3>
          <div class="warning-options">
            <el-radio-group v-model="warningSettings.matchMethod.value">
              <el-radio
                v-for="(option, index) in warningSettings.matchMethod.options"
                :key="'match-method-' + index"
                :label="option.value">
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <!-- 发布地区 -->
        <div class="warning-section">
          <h3>{{ warningSettings.publishRegion.title }}</h3>
          <div class="region-section">
            <div class="region-input">
              <el-input
                placeholder="添加发布地区"
                size="small"
                style="width: 200px;"
                v-model="publishRegionInput">
                <i slot="suffix" class="el-icon-location"></i>
              </el-input>
            </div>
            <div class="region-tags" v-if="warningSettings.publishRegion.regions.length > 0">
              <el-tag
                v-for="(region, index) in warningSettings.publishRegion.regions"
                :key="'region-' + index"
                size="small"
                closable
                @close="removePublishRegion(region.name)">
                {{ region.name }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- IP属地 -->
        <div class="warning-section">
          <h3>{{ warningSettings.ipArea.title }}</h3>
          <div class="region-section">
            <div class="region-input">
              <el-input
                placeholder="添加IP属地"
                size="small"
                style="width: 200px;"
                v-model="ipAreaInput">
                <i slot="suffix" class="el-icon-location"></i>
              </el-input>
            </div>
            <div class="region-tags" v-if="warningSettings.ipArea.areas.length > 0">
              <el-tag
                v-for="(area, index) in warningSettings.ipArea.areas"
                :key="'ip-area-' + index"
                size="small"
                closable
                @close="removeIpArea(area.name)">
                {{ area.name }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 媒体类别 -->
        <div class="warning-section category-section" @click="openMediaCategoryDialog">
          <div class="category-header">
            <h3>{{ warningSettings.mediaCategory.title }}</h3>
            <div class="category-count">
              <span>(已选{{ warningSettings.mediaCategory.count }}个)</span>
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>

        <!-- 文章类别 -->
        <div class="warning-section category-section" @click="openArticleCategoryDialog">
          <div class="category-header">
            <h3>{{ warningSettings.articleCategory.title }}</h3>
            <div class="category-count">
              <span>(已选{{ warningSettings.articleCategory.count }}个)</span>
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="drawer-footer">
          <el-button @click="closeWarningDialog">取消</el-button>
          <el-button type="primary" @click="saveWarningSettings">确定</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 关键词设置抽屉 -->
    <el-drawer
      title="文本词设置"
      :visible.sync="keywordDialogVisible"
      direction="rtl"
      size="30%"
      :before-close="closeKeywordDialog"
      custom-class="keyword-drawer">
      <div class="keyword-drawer-content">
        <!-- 允许词 -->
        <div class="keyword-section">
          <h3>允许词</h3>
          <el-input
            type="textarea"
            :rows="8"
            placeholder="允许词：对文本进行筛选，命中文本的内容会被允许通过"
            v-model="keywordSettings.allowWords">
          </el-input>
        </div>

        <!-- 拒绝词 -->
        <div class="keyword-section">
          <h3>拒绝词</h3>
          <el-input
            type="textarea"
            :rows="8"
            placeholder="拒绝词：对文本进行筛选，命中文本的内容会被拒绝通过"
            v-model="keywordSettings.rejectWords">
          </el-input>
        </div>

        <!-- 底部按钮 -->
        <div class="drawer-footer">
          <el-button @click="closeKeywordDialog">取消</el-button>
          <el-button type="primary" @click="saveKeywordSettings">确定</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 自动预警设置抽屉 -->
    <el-drawer
      title="预警设置"
      :visible.sync="autoWarningDialogVisible"
      direction="rtl"
      size="30%"
      :before-close="closeAutoWarningDialog"
      custom-class="auto-warning-drawer">
      <div class="auto-warning-drawer-content">
        <h3 class="auto-warning-title">自动预警设置</h3>

        <!-- 预警时间 -->
        <div class="auto-warning-section">
          <div class="section-label">预警时间</div>
          <div class="time-range-selector">
            <el-select v-model="autoWarningSettings.timeRange.startHour" placeholder="小时" size="small">
              <el-option
                v-for="h in 24"
                :key="'start-hour-' + h"
                :label="(h - 1).toString().padStart(2, '0')"
                :value="(h - 1).toString().padStart(2, '0')">
              </el-option>
            </el-select>
            <span class="time-separator">:</span>
            <el-select v-model="autoWarningSettings.timeRange.startMinute" placeholder="分钟" size="small">
              <el-option
                v-for="m in 60"
                :key="'start-minute-' + m"
                :label="(m - 1).toString().padStart(2, '0')"
                :value="(m - 1).toString().padStart(2, '0')">
              </el-option>
            </el-select>
          </div>
          <div class="time-range-note">
            <span class="note-text">预警时间段开始时间到结束时间</span>
          </div>
        </div>

        <!-- 预警平台 -->
        <div class="auto-warning-section">
          <div class="section-label">预警平台</div>
          <div class="platform-checkboxes">
            <el-checkbox v-model="autoWarningSettings.platforms.weibo">微博</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.platforms.wechat">微信</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.platforms.website">网站</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.platforms.douyin">抖音</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.platforms.redbook">小红书</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.platforms.bilibili">B站</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.platforms.zhihu">知乎</el-checkbox>
          </div>
        </div>

        <!-- 预警类型 -->
        <div class="auto-warning-section">
          <div class="section-label">预警类型</div>
          <div class="warning-type-selector">
            <el-radio v-model="autoWarningSettings.warningType" label="negative">负面</el-radio>
          </div>
        </div>

        <!-- 处理方式 -->
        <div class="auto-warning-section">
          <div class="section-label">处理方式</div>
          <div class="process-method-selector">
            <el-radio v-model="autoWarningSettings.processMethod" label="all">全部预警</el-radio>
            <el-radio v-model="autoWarningSettings.processMethod" label="onlyAlert">仅告警 <span class="note-text">(只对符合条件的)</span></el-radio>
          </div>
          <div class="process-switch">
            <span class="switch-label">只对重要账号 <span class="note-text">(对方粉丝大于10万或认证账号)</span></span>
            <el-switch v-model="importantAccountOnly"></el-switch>
          </div>
        </div>

        <!-- 优先级别 -->
        <div class="auto-warning-section">
          <div class="section-label">优先级别</div>
          <div class="priority-selector">
            <el-radio v-model="autoWarningSettings.priority" label="normal">正常</el-radio>
            <el-radio v-model="autoWarningSettings.priority" label="urgent">紧急</el-radio>
          </div>
        </div>

        <!-- 处理方式 -->
        <div class="auto-warning-section">
          <div class="section-label">处理方式</div>
          <div class="handle-method-selector">
            <el-radio v-model="autoWarningSettings.handleMethod" label="auto">自动</el-radio>
            <el-radio v-model="autoWarningSettings.handleMethod" label="manual">人工</el-radio>
          </div>
        </div>

        <!-- 告知方式 -->
        <div class="auto-warning-section">
          <div class="section-label">告知方式</div>
          <div class="notify-method-checkboxes">
            <el-checkbox v-model="autoWarningSettings.notifyMethods.sms">短信</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.notifyMethods.email">邮件</el-checkbox>
            <el-checkbox v-model="autoWarningSettings.notifyMethods.wechatNotify">微信通知</el-checkbox>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="drawer-footer">
          <el-button @click="closeAutoWarningDialog">取消</el-button>
          <el-button type="primary" @click="saveAutoWarningSettings">确定</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 详情查看弹窗 -->
    <el-dialog
      title="预警记录详情"
      :visible.sync="detailDialogVisible"
      width="60%"
      :before-close="closeDetailDialog">
      <div class="detail-content" v-if="currentRecord">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="标题">{{ currentRecord.title }}</el-descriptions-item>
          <el-descriptions-item label="来源类型">{{ currentRecord.source }}</el-descriptions-item>
          <el-descriptions-item label="平台类型">{{ currentRecord.platform }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ currentRecord.time }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentRecord.status)">{{ getStatusLabel(currentRecord.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="关键词">{{ currentRecord.keywords || '无' }}</el-descriptions-item>
          <el-descriptions-item label="内容" :span="2">
            <div class="content-text">{{ currentRecord.content || currentRecord.title }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentRecord.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDetailDialog">关闭</el-button>
        <el-button type="primary" @click="editFromDetail">编辑</el-button>
      </div>
    </el-dialog>

    <!-- 编辑表单弹窗 -->
    <el-dialog
      title="编辑预警记录"
      :visible.sync="editDialogVisible"
      width="50%"
      :before-close="closeEditDialog">
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="内容">
          <el-input type="textarea" :rows="4" v-model="editForm.content" placeholder="请输入预警内容"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="editForm.status" placeholder="请选择状态">
            <el-option label="待处理" :value="0"></el-option>
            <el-option label="已处理" :value="1"></el-option>
            <el-option label="紧急" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" :rows="3" v-model="editForm.remark" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeEditDialog">取消</el-button>
        <el-button type="primary" @click="saveEdit">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 导入分页组件（如果需要）
// import Pagination from "@/components/Pagination";
import {
  getActiveWarningSchemes,
  listWarningRecord,
  getWarningSettings,
  saveWarningSettings,
  getWarningStatistics,
  addWarningScheme,
  getWarningFrontendData,
  initWarningSchemeData,
  updateWarningRecordStatus,
  updateWarningRecord,
  delWarningRecord,
  exportWarningRecord,
  batchProcessRecords
} from "@/api/warning/index";

export default {
  name: 'InfoSummary',
  // 注册组件（如果需要）
  // components: {
  //   Pagination
  // },
  data() {
    return {
      originalTopNav: undefined, // 存储原始的topNav状态
      loading: false, // 加载状态
      autoRefresh: true,
      searchText: '',
      searchTimer: null,
      idFilter: '', // ID筛选输入框
      idFilterTimer: null, // ID筛选防抖定时器
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 预警设置弹窗相关数据
      publishRegionInput: '',
      ipAreaInput: '',
      warningDialogVisible: false,
      // 关键词设置抽屉相关数据
      keywordDialogVisible: false,
      keywordSettings: {
        allowWords: '允许词：对文本进行筛选，命中文本的内容会被允许通过',
        rejectWords: '拒绝词：对文本进行筛选，命中文本的内容会被拒绝通过'
      },
      // 自动预警设置抽屉相关数据
      autoWarningDialogVisible: false,
      importantAccountOnly: true,
      autoWarningSettings: {
        timeRange: {
          startHour: '06',
          startMinute: '00',
          endHour: '18',
          endMinute: '00'
        },
        platforms: {
          weibo: true,
          wechat: true,
          website: true,
          douyin: true,
          redbook: true,
          bilibili: false,
          zhihu: false
        },
        warningType: 'negative', // negative, positive, all
        processMethod: 'all', // all, onlyAlert
        priority: 'normal', // normal, urgent
        handleMethod: 'auto', // auto, manual
        notifyMethods: {
          sms: true,
          email: false,
          wechatNotify: true
        }
      },
      warningSettings: {
        platformType: {
          title: '平台类型',
          options: [
            { label: '全部', value: 'all', checked: true },
            { label: '网页', value: 'webpage', checked: true },
            { label: '微信', value: 'wechat', checked: true },
            { label: '微博', value: 'weibo', checked: true },
            { label: '头条号', value: 'toutiao', checked: true },
            { label: 'APP', value: 'app', checked: true },
            { label: '视频', value: 'video', checked: true },
            { label: '论坛', value: 'forum', checked: true },
            { label: '报刊', value: 'newspaper', checked: true },
            { label: '问答', value: 'qa', checked: true }
          ]
        },
        contentProperty: {
          title: '内容属性',
          value: 'all', // all, yes, no
          options: [
            { label: '全部', value: 'all' },
            { label: '是', value: 'yes' },
            { label: '不是', value: 'no' }
          ]
        },
        infoType: {
          title: '信息类型',
          value: 'noncomment', // all, noncomment, comment
          options: [
            { label: '全部', value: 'all' },
            { label: '非评论', value: 'noncomment' },
            { label: '评论', value: 'comment' }
          ]
        },
        matchObject: {
          title: '匹配对象',
          allChecked: true,
          options: [
            { label: '标题匹配', value: 'title', checked: false },
            { label: '正文匹配', value: 'content', checked: false },
            { label: '音频/图片匹配', value: 'media', checked: false },
            { label: '原文匹配', value: 'original', checked: false }
          ]
        },
        matchMethod: {
          title: '匹配方式',
          value: 'exact', // exact, fuzzy
          options: [
            { label: '精准', value: 'exact' },
            { label: '模糊', value: 'fuzzy' }
          ]
        },
        publishRegion: {
          title: '发布地区',
          regions: [
            { name: '全部', value: 'all' }
          ]
        },
        ipArea: {
          title: 'IP属地',
          areas: [
            { name: '全部', value: 'all' }
          ]
        },
        mediaCategory: {
          title: '媒体类别',
          count: 0
        },
        articleCategory: {
          title: '文章类别',
          count: 0
        }
      },
      // 侧边栏数据
      sidebarCollapsed: false,
      sidebarSearchText: '',
      activeMenuItem: '',
      activeSchemeId: null,
      menuCategories: [],
      originalMenuCategories: [], // 存储原始数据用于搜索
      tableData: [],
      multipleSelection: [],
      // 表格状态管理
      allSelected: false,
      indeterminate: false,
      visibleColumns: {
        id: true, // 默认显示ID列
        title: true,
        source: true,
        platform: true,
        time: true
      },
      statusFilter: 'all',
      statusOptions: [
        { label: '全部', value: 'all' },
        { label: '待处理', value: 'pending' },
        { label: '已处理', value: 'processed' },
        { label: '紧急', value: 'urgent' }
      ],
      // 弹窗状态
      detailDialogVisible: false,
      editDialogVisible: false,
      currentRecord: null,
      editForm: {
        id: null,
        scheme_id: null,    // 使用下划线格式
        warning_type: '',   // 使用下划线格式
        content: '',
        status: 0,
        remark: ''
      },
      // 统计数据 - 提供默认结构确保统计卡片能够显示
      statisticsData: {
        total_count: 0,
        pending_count: 0,
        processed_count: 0,
        urgent_count: 0,
        negative_count: 0,
        positive_count: 0
      }
    };
  },
  mounted() {
    // 隐藏顶部导航栏
    this.originalTopNav = this.$store.state.settings.topNav
    this.$store.dispatch('settings/changeSetting', {
      key: 'topNav',
      value: false
    })
  },
  created() {
    this.initPageData();
  },
  beforeDestroy() {
    // 恢复顶部导航栏设置
    if (this.originalTopNav !== undefined) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'topNav',
        value: this.originalTopNav
      })
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
      // 更新全选状态
      this.updateSelectAllStatus();
    },

    // 更新全选状态
    updateSelectAllStatus() {
      const selectedCount = this.multipleSelection.length;
      const totalCount = this.tableData.length;

      if (selectedCount === 0) {
        this.allSelected = false;
        this.indeterminate = false;
      } else if (selectedCount === totalCount) {
        this.allSelected = true;
        this.indeterminate = false;
      } else {
        this.allSelected = false;
        this.indeterminate = true;
      }
    },

    // 全选/取消全选
    handleSelectAll(checked) {
      if (checked) {
        this.$refs.table.toggleAllSelection();
      } else {
        this.$refs.table.clearSelection();
      }
    },
    // 初始化页面数据
    async initPageData() {
      try {
        this.loading = true;
        // 获取启用的预警方案列表
        await this.loadWarningSchemes();
        // 如果有方案，默认选择第一个
        if (this.menuCategories.length > 0) {
          const firstScheme = this.menuCategories[0];
          this.activeMenuItem = firstScheme.name;
          this.activeSchemeId = firstScheme.id;
          // 加载该方案的数据
          await this.loadSchemeData(firstScheme.id);
        }
      } catch (error) {
        console.error('初始化页面数据失败:', error);
        this.$message.error('加载数据失败，请刷新页面重试');
      } finally {
        this.loading = false;
      }
    },
    // 加载预警方案列表
    async loadWarningSchemes() {
      try {
        console.log('🔍 开始加载预警方案列表');
        const response = await getWarningFrontendData({
          pageNum: 1,     // 修复：使用驼峰命名匹配后端模型别名
          pageSize: 10    // 修复：使用驼峰命名匹配后端模型别名
        });

        console.log('🔍 预警方案API响应:', response);

        if (response.code === 200 && response.data && response.data.schemes) {
          console.log('🔍 获取到的方案数据:', response.data.schemes);

          this.menuCategories = response.data.schemes.map(scheme => ({
            id: scheme.id,
            name: scheme.schemeName,
            count: 0, // 后续可以加载统计数据
            isItem: true,
            icon: 'el-icon-office-building',
            isActive: scheme.isActive
          }));
          this.originalMenuCategories = [...this.menuCategories];

          console.log('🔍 处理后的方案列表:', this.menuCategories);
        } else {
          console.warn('⚠️ 预警方案API响应异常:', response);
          this.$message.warning('未获取到预警方案数据');
        }
      } catch (error) {
        console.error('❌ 加载预警方案失败:', error);
        this.$message.error('加载预警方案失败：' + (error.message || '网络错误'));
      }
    },
    // 加载指定方案的数据
    async loadSchemeData(schemeId) {
      try {
        // 设置loading状态
        this.loading = true;

        // 清空之前的数据状态
        this.tableData = [];
        this.statisticsData = {
          total_count: 0,
          pending_count: 0,
          processed_count: 0,
          urgent_count: 0,
          negative_count: 0,
          positive_count: 0
        };

        console.log('🔍 开始加载方案数据，方案ID:', schemeId);
        console.log('🔍 请求参数:', {
          schemeId: schemeId,           // 修复：使用驼峰命名匹配后端模型别名
          pageNum: this.currentPage,    // 修复：使用驼峰命名匹配后端模型别名
          pageSize: this.pageSize,      // 修复：使用驼峰命名匹配后端模型别名
          searchText: this.searchText   // 修复：使用驼峰命名匹配后端模型别名
        });

        // 构建查询参数（使用驼峰命名匹配后端模型别名）
        const queryParams = {
          schemeId: schemeId,         // 修复：使用驼峰命名匹配后端模型别名
          pageNum: this.currentPage,  // 修复：使用驼峰命名匹配后端模型别名
          pageSize: this.pageSize,    // 修复：使用驼峰命名匹配后端模型别名
          searchText: this.searchText // 修复：使用驼峰命名匹配后端模型别名
        };

        // 如果有ID筛选，添加ID参数
        if (this.idFilter && this.idFilter.trim()) {
          queryParams.id = parseInt(this.idFilter.trim());
        }

        // 如果有状态筛选，添加状态参数
        if (this.statusFilter && this.statusFilter !== 'all') {
          const statusValue = this.getStatusValue(this.statusFilter);
          if (statusValue !== undefined) {
            queryParams.status = statusValue;
            console.log('🔍 添加状态筛选参数:', this.statusFilter, '→', statusValue);
          }
        }

        console.log('🔍 查询参数:', queryParams);

        // 使用前端专用接口获取完整数据
        const response = await getWarningFrontendData(queryParams);

        console.log('🔍 API响应:', response);

        if (response.code === 200) {
          const data = response.data;
          console.log('🔍 响应数据结构:', data);
          console.log('🔍 statistics字段:', data.statistics);

          // 更新表格数据 - 处理分页数据结构
          if (data.records) {
            let recordsArray = [];

            // 检查是否是分页数据结构
            if (data.records.rows && Array.isArray(data.records.rows)) {
              recordsArray = data.records.rows;
              this.total = data.records.total || 0;
              console.log('🔍 使用分页数据结构，总记录数:', this.total);
            } else if (Array.isArray(data.records)) {
              recordsArray = data.records;
              this.total = recordsArray.length;
              console.log('🔍 使用数组数据结构，记录数:', this.total);
            }

            this.tableData = recordsArray.map(record => ({
              id: record.id,
              title: record.content || '无标题',
              content: record.content || '无内容',
              source: this.getWarningTypeLabel(record.warningType),
              platform: this.getPlatformLabel(record.schemeId),
              time: this.formatTime(record.createTime),
              status: record.status || 'pending',
              keywords: record.keywords,
              remark: record.remark,
              isFavorite: record.isFavorite || false
            }));

            console.log('🔍 表格数据更新完成，记录数:', this.tableData.length);
          } else {
            console.warn('⚠️ 响应中没有records字段');
            this.tableData = [];
            this.total = 0;
          }

          // 更新统计数据 - 增强处理逻辑
          if (data.statistics) {
            console.log('🔍 准备更新统计数据:', data.statistics);
            this.updateStatistics(data.statistics);
          } else {
            console.warn('⚠️ 响应中没有statistics字段，保持默认统计数据');
          }

          // 更新设置数据
          if (data.settings) {
            this.updateWarningSettings(data.settings);
          }
        } else {
          console.error('❌ API响应错误:', response);
          this.$message.error(response.msg || '获取方案数据失败');
        }
      } catch (error) {
        console.error('❌ 加载方案数据失败:', error);
        this.$message.error('加载方案数据失败：' + (error.message || '网络错误'));
      } finally {
        // 确保loading状态被清除
        this.loading = false;
      }
    },
    getList() {
      // 重新加载当前方案的数据
      if (this.activeSchemeId) {
        this.loadSchemeData(this.activeSchemeId);
      }
    },
    // 侧边栏相关方法
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
    },
    handleMenuSelect(index) {
      console.log('🔄 方案切换开始，选中方案名称:', index);

      // 找到对应的方案ID
      const selectedScheme = this.menuCategories.find(item => item.name === index);
      console.log('🔄 找到的方案信息:', selectedScheme);

      if (selectedScheme && selectedScheme.id) {
        // 检查是否切换到了不同的方案
        if (this.activeSchemeId === selectedScheme.id) {
          console.log('🔄 选择的是当前方案，无需切换');
          return;
        }

        // 更新活动状态
        this.activeMenuItem = index;
        this.activeSchemeId = selectedScheme.id;

        // 重置分页到第一页
        this.currentPage = 1;

        console.log('🔄 开始加载新方案数据，方案ID:', selectedScheme.id);

        // 加载该方案的数据
        this.loadSchemeData(selectedScheme.id);
      } else {
        console.error('❌ 未找到对应的方案信息:', index);
        this.$message.error('方案信息错误，请刷新页面重试');
      }
    },
    async createNewScheme() {
      try {
        const { value: schemeName } = await this.$prompt('请输入方案名称', '新建预警方案', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^.{1,50}$/,
          inputErrorMessage: '方案名称长度应在1-50个字符之间'
        });

        if (schemeName) {
          const response = await addWarningScheme({
            schemeName: schemeName,
            schemeType: 'default',
            description: '',
            isActive: true
          });

          if (response.code === 200) {
            this.$message.success('新建方案成功');
            // 重新加载方案列表
            await this.loadWarningSchemes();
          } else {
            this.$message.error(response.msg || '新建方案失败');
          }
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('新建方案失败:', error);
          this.$message.error('新建方案失败');
        }
      }
    },
    searchSidebar() {
      // 侧边栏搜索逻辑
      if (!this.sidebarSearchText.trim()) {
        // 如果搜索框为空，恢复原始数据
        this.menuCategories = [...this.originalMenuCategories];
      } else {
        // 根据搜索关键词过滤方案
        const keyword = this.sidebarSearchText.toLowerCase();
        this.menuCategories = this.originalMenuCategories.filter(item =>
          item.name.toLowerCase().includes(keyword)
        );
      }
    },
    // 打开预警设置弹窗
    openWarningDialog() {
      this.warningDialogVisible = true;
    },
    // 关闭预警设置弹窗
    closeWarningDialog() {
      this.warningDialogVisible = false;
    },
    // 打开关键词设置抽屉
    openKeywordDialog() {
      this.keywordDialogVisible = true;
    },
    // 关闭关键词设置抽屉
    closeKeywordDialog() {
      this.keywordDialogVisible = false;
    },
    // 保存预警设置
    async saveWarningSettings() {
      try {
        if (!this.activeSchemeId) {
          this.$message.error('请先选择一个预警方案');
          return;
        }

        // 构建设置数据
        const settingsData = {
          schemeId: this.activeSchemeId,
          platformTypes: this.warningSettings.platformType.options
            .filter(opt => opt.checked)
            .map(opt => opt.value),
          contentProperty: this.warningSettings.contentProperty.value,
          infoType: this.warningSettings.infoType.value,
          matchObjects: this.warningSettings.matchObject.options
            .filter(opt => opt.checked)
            .map(opt => opt.value),
          matchMethod: this.warningSettings.matchMethod.value,
          publishRegions: this.warningSettings.publishRegion.regions.map(r => r.name),
          ipAreas: this.warningSettings.ipArea.areas.map(a => a.name),
          allowWords: this.keywordSettings.allowWords,
          rejectWords: this.keywordSettings.rejectWords
        };

        const response = await saveWarningSettings(settingsData);
        if (response.code === 200) {
          this.$message.success('预警设置保存成功');
          this.closeWarningDialog();
        } else {
          this.$message.error(response.msg || '保存失败');
        }
      } catch (error) {
        console.error('保存预警设置失败:', error);
        this.$message.error('保存预警设置失败');
      }
    },

    // 处理全部复选框
    handleAllCheckbox(section) {
      const allOption = section.options.find(opt => opt.value === 'all');
      if (allOption && allOption.checked) {
        // 如果全部被选中，则选中所有选项
        section.options.forEach(opt => {
          opt.checked = true;
        });
      }
    },

    // 处理匹配对象全部复选框
    handleMatchObjectAll(checked) {
      this.warningSettings.matchObject.allChecked = checked;
      if (checked) {
        // 如果全部被选中，则取消选中其他选项
        this.warningSettings.matchObject.options.forEach(opt => {
          opt.checked = false;
        });
      }
    },

    // 添加发布地区
    addPublishRegion(region) {
      if (region && !this.warningSettings.publishRegion.regions.some(r => r.name === region)) {
        this.warningSettings.publishRegion.regions.push({ name: region, value: region });
      }
    },

    // 删除发布地区
    removePublishRegion(region) {
      const index = this.warningSettings.publishRegion.regions.findIndex(r => r.name === region);
      if (index !== -1) {
        this.warningSettings.publishRegion.regions.splice(index, 1);
      }
    },

    // 添加IP属地
    addIpArea(area) {
      if (area && !this.warningSettings.ipArea.areas.some(a => a.name === area)) {
        this.warningSettings.ipArea.areas.push({ name: area, value: area });
      }
    },

    // 删除IP属地
    removeIpArea(area) {
      const index = this.warningSettings.ipArea.areas.findIndex(a => a.name === area);
      if (index !== -1) {
        this.warningSettings.ipArea.areas.splice(index, 1);
      }
    },

    // 打开媒体类别对话框
    openMediaCategoryDialog() {
      this.$message({
        message: '媒体类别功能待实现',
        type: 'info'
      });
    },

    // 打开文章类别对话框
    openArticleCategoryDialog() {
      this.$message({
        message: '文章类别功能待实现',
        type: 'info'
      });
    },

    // 保存关键词设置
    saveKeywordSettings() {
      // 这里可以添加保存关键词设置的逻辑
      console.log('保存关键词设置:', this.keywordSettings);
      this.$message({
        message: '关键词设置保存成功',
        type: 'success'
      });
      this.closeKeywordDialog();
    },

    // 打开自动预警设置抽屉
    openAutoWarningDialog() {
      this.autoWarningDialogVisible = true;
    },

    // 关闭自动预警设置抽屉
    closeAutoWarningDialog() {
      this.autoWarningDialogVisible = false;
    },

    // 保存自动预警设置
    async saveAutoWarningSettings() {
      try {
        // 构建设置数据
        const settingsData = {
          schemeId: this.activeSchemeId,
          autoWarningSettings: this.autoWarningSettings
        };

        const response = await saveWarningSettings(settingsData);
        if (response.code === 200) {
          this.$message.success('自动预警设置保存成功');
          this.closeAutoWarningDialog();
        } else {
          this.$message.error(response.msg || '保存失败');
        }
      } catch (error) {
        console.error('保存自动预警设置失败:', error);
        this.$message.error('保存自动预警设置失败');
      }
    },

    // 辅助方法：获取预警类型标签
    getWarningTypeLabel(type) {
      const typeMap = {
        'negative': '负面',
        'positive': '正面',
        'neutral': '中性'
      };
      return typeMap[type] || '未知';
    },

    // 辅助方法：获取平台标签
    getPlatformLabel(schemeId) {
      // 根据方案ID或其他逻辑返回平台类型
      const platformMap = {
        4: '微博',
        5: '微信',
        6: '网站',
        7: 'APP'
      };
      return platformMap[schemeId] || 'APP';
    },

    // 辅助方法：格式化时间
    formatTime(timeString) {
      if (!timeString) return '';
      const date = new Date(timeString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    // 辅助方法：更新统计数据
    updateStatistics(statistics) {
      console.log('📊 updateStatistics被调用，接收参数:', statistics);
      console.log('📊 当前statisticsData状态:', this.statisticsData);

      // 参数验证
      if (!statistics || typeof statistics !== 'object') {
        console.warn('⚠️ updateStatistics接收到无效的statistics参数:', statistics);
        console.log('📊 保持当前statisticsData不变');
        return;
      }

      try {
        // 更新统计数据对象，确保所有字段都有默认值
        this.statisticsData = {
          total_count: Number(statistics.total_count) || 0,
          pending_count: Number(statistics.pending_count) || 0,
          processed_count: Number(statistics.processed_count) || 0,
          urgent_count: Number(statistics.urgent_count) || 0,
          negative_count: Number(statistics.negative_count) || 0,
          positive_count: Number(statistics.positive_count) || 0
        };

        console.log('📊 更新后的statisticsData:', this.statisticsData);

        // 同时更新表格的总数
        this.total = Number(statistics.total_count) || 0;

        console.log('📊 统计数据更新完成，total:', this.total);
      } catch (error) {
        console.error('❌ 更新统计数据时发生错误:', error);
        console.log('📊 保持当前statisticsData不变');
      }
    },

    // 辅助方法：本地更新统计数据（用于编辑后立即更新）
    updateLocalStatistics(oldStatus, newStatus) {
      console.log('📊 updateLocalStatistics被调用，状态变化:', oldStatus, '→', newStatus);

      if (oldStatus === newStatus) {
        console.log('📊 状态未变化，无需更新统计数据');
        return;
      }

      try {
        // 状态变化映射表
        const statusChangeMap = {
          '0->1': { pending_count: -1, processed_count: +1 },
          '0->2': { pending_count: -1, urgent_count: +1 },
          '1->0': { processed_count: -1, pending_count: +1 },
          '1->2': { processed_count: -1, urgent_count: +1 },
          '2->0': { urgent_count: -1, pending_count: +1 },
          '2->1': { urgent_count: -1, processed_count: +1 }
        };

        const changeKey = `${oldStatus}->${newStatus}`;
        const changes = statusChangeMap[changeKey];

        if (changes) {
          // 应用统计数据变化
          Object.keys(changes).forEach(key => {
            this.statisticsData[key] = Math.max(0, this.statisticsData[key] + changes[key]);
          });

          console.log('📊 本地统计数据更新完成:', this.statisticsData);
        } else {
          console.warn('⚠️ 未知的状态变化:', changeKey);
        }
      } catch (error) {
        console.error('❌ 本地更新统计数据时发生错误:', error);
      }
    },

    // ==================== 表格操作方法 ====================

    // 批量收藏/取消收藏
    async batchToggleFavorite() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请先选择要操作的记录');
        return;
      }

      try {
        const recordIds = this.multipleSelection.map(item => item.id);

        // 调用批量收藏的API
        const response = await batchProcessRecords(recordIds, 'toggle_favorite');

        if (response.code === 200) {
          // 更新本地状态
          this.multipleSelection.forEach(record => {
            record.isFavorite = !record.isFavorite;
          });
          this.$message.success('批量收藏操作成功');
          // 重新加载数据以确保同步
          this.getList();
        } else {
          this.$message.error(response.msg || '批量收藏操作失败');
        }
      } catch (error) {
        console.error('批量收藏失败:', error);
        this.$message.error('批量收藏操作失败');
      }
    },

    // 批量发送消息
    async batchSendMessage() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请先选择要操作的记录');
        return;
      }

      this.$message({
        message: `已选择 ${this.multipleSelection.length} 条记录发送消息`,
        type: 'info'
      });
    },

    // 批量下载
    async batchDownload() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请先选择要操作的记录');
        return;
      }

      try {
        const recordIds = this.multipleSelection.map(item => item.id);
        // 这里可以调用批量下载的API
        this.$message.success(`开始下载 ${recordIds.length} 条记录`);
      } catch (error) {
        console.error('批量下载失败:', error);
        this.$message.error('批量下载失败');
      }
    },

    // 导出数据
    async exportData() {
      try {
        if (!this.activeSchemeId) {
          this.$message.warning('请先选择一个预警方案');
          return;
        }

        // 构建符合后端模型的参数（使用驼峰命名）
        const exportParams = {
          schemeId: this.activeSchemeId,  // 修复：使用驼峰命名匹配后端模型别名
          searchText: this.searchText,    // 修复：使用驼峰命名匹配后端模型别名
          pageNum: 1,                     // 添加必要的分页参数
          pageSize: 10000                 // 设置大的页面大小以获取所有数据
        };

        // 如果有状态筛选，添加到参数中
        if (this.statusFilter && this.statusFilter !== 'all') {
          // 将状态筛选映射为后端认识的格式
          const statusMap = {
            'pending': 0,
            'processed': 1,
            'urgent': 2
          };
          if (statusMap[this.statusFilter] !== undefined) {
            exportParams.status = statusMap[this.statusFilter];
          }
        }

        // 显示导出进度提示
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍候...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        try {
          // 调用导出API
          const response = await exportWarningRecord(exportParams);

          // 处理文件下载
          const blob = new Blob([response], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          });

          // 创建下载链接
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;

          // 生成文件名
          const now = new Date();
          const timestamp = now.getFullYear() +
            String(now.getMonth() + 1).padStart(2, '0') +
            String(now.getDate()).padStart(2, '0') + '_' +
            String(now.getHours()).padStart(2, '0') +
            String(now.getMinutes()).padStart(2, '0') +
            String(now.getSeconds()).padStart(2, '0');

          link.download = `预警记录导出_${timestamp}.xlsx`;

          // 触发下载
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // 释放URL对象
          window.URL.revokeObjectURL(url);

          this.$message.success('导出成功！文件已开始下载');
        } finally {
          loading.close();
        }
      } catch (error) {
        console.error('导出失败:', error);
        // 提供更详细的错误信息
        if (error.response && error.response.status === 422) {
          this.$message.error('导出参数验证失败，请检查筛选条件');
        } else {
          this.$message.error('导出失败，请稍后重试');
        }
      }
    },

    // 处理字段显示控制
    handleColumnCommand(command) {
      // 字段显示控制已通过v-model直接绑定，这里可以添加额外逻辑
      console.log('字段显示控制:', command, this.visibleColumns);
    },

    // 处理状态筛选
    handleStatusFilter(status) {
      console.log('🔄 状态筛选切换:', this.statusFilter, '→', status);
      this.statusFilter = status;
      // 重置到第一页
      this.currentPage = 1;
      // 重新加载数据
      this.getList();
    },

    // 处理搜索
    handleSearch() {
      // 防抖处理
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.currentPage = 1; // 重置到第一页
        this.getList();
      }, 500);
    },

    // 清空搜索
    handleSearchClear() {
      this.searchText = '';
      this.currentPage = 1;
      this.getList();
    },

    // 处理ID筛选
    handleIdFilter() {
      // 防抖处理
      clearTimeout(this.idFilterTimer);
      this.idFilterTimer = setTimeout(() => {
        this.currentPage = 1; // 重置到第一页
        this.getList();
      }, 500);
    },

    // 清空ID筛选
    handleIdFilterClear() {
      this.idFilter = '';
      this.currentPage = 1;
      this.getList();
    },

    // 单行收藏切换
    async toggleFavorite(row) {
      try {
        // 调用单个收藏的API
        const response = await updateWarningRecordStatus(row.id, {
          isFavorite: !row.isFavorite
        });

        if (response.code === 200) {
          // 更新本地状态
          row.isFavorite = !row.isFavorite;
          this.$message.success(row.isFavorite ? '已收藏' : '已取消收藏');
        } else {
          this.$message.error(response.msg || '收藏操作失败');
        }
      } catch (error) {
        console.error('收藏操作失败:', error);
        this.$message.error('收藏操作失败');
      }
    },

    // 发送消息
    sendMessage(row) {
      this.$message({
        message: `发送消息给记录: ${row.title}`,
        type: 'info'
      });
    },

    // 下载单条记录
    downloadRecord(row) {
      this.$message({
        message: `开始下载记录: ${row.title}`,
        type: 'success'
      });
    },

    // 查看记录详情
    viewRecord(row) {
      this.currentRecord = { ...row };
      this.detailDialogVisible = true;
    },

    // 编辑记录
    editRecord(row) {
      this.currentRecord = { ...row };
      this.editForm = {
        id: row.id,
        scheme_id: this.activeSchemeId,  // 使用下划线格式
        warning_type: row.source || 'negative', // 使用下划线格式
        content: row.content || row.title,
        status: this.getStatusValue(row.status),
        remark: row.remark || ''
      };
      this.editDialogVisible = true;
    },

    // 删除记录
    async deleteRecord(row) {
      try {
        await this.$confirm(`确定要删除记录"${row.title}"吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        // 调用删除API
        const response = await delWarningRecord([row.id]);

        if (response.code === 200) {
          // 从表格中移除
          const index = this.tableData.findIndex(item => item.id === row.id);
          if (index !== -1) {
            this.tableData.splice(index, 1);
            this.total--;
          }
          this.$message.success('删除成功');
          // 重新加载数据以确保同步
          this.getList();
        } else {
          this.$message.error(response.msg || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error);
          this.$message.error('删除失败');
        }
      }
    },

    // 处理更多操作
    handleMoreAction(command) {
      const { action, row } = command;

      switch (action) {
        case 'share':
          this.shareRecord(row);
          break;
        case 'copy':
          this.copyRecordLink(row);
          break;
        case 'export':
          this.exportSingleRecord(row);
          break;
        default:
          console.log('未知操作:', action);
      }
    },

    // 分享记录
    shareRecord(row) {
      this.$message({
        message: `分享记录: ${row.title}`,
        type: 'success'
      });
    },

    // 复制记录链接
    copyRecordLink(row) {
      const link = `${window.location.origin}/warning/record/${row.id}`;

      // 创建临时输入框复制链接
      const input = document.createElement('input');
      input.value = link;
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);

      this.$message.success('链接已复制到剪贴板');
    },

    // 导出单条记录
    exportSingleRecord(row) {
      this.$message({
        message: `导出记录: ${row.title}`,
        type: 'success'
      });
    },

    // 关闭详情弹窗
    closeDetailDialog() {
      this.detailDialogVisible = false;
      this.currentRecord = null;
    },

    // 从详情页面进入编辑
    editFromDetail() {
      console.log('editFromDetail 被调用');
      console.log('currentRecord:', this.currentRecord);
      console.log('activeSchemeId:', this.activeSchemeId);

      // 先保存当前记录的引用，避免在关闭弹窗时被清空
      const recordToEdit = { ...this.currentRecord };
      this.closeDetailDialog();
      this.editRecord(recordToEdit);
    },

    // 关闭编辑弹窗
    closeEditDialog() {
      this.editDialogVisible = false;
      this.currentRecord = null;
      this.editForm = {
        id: null,
        scheme_id: null,  // 使用下划线格式
        warning_type: '', // 使用下划线格式
        content: '',
        status: 0,
        remark: ''
      };
    },

    // 保存编辑
    async saveEdit() {
      try {
        if (!this.editForm.content.trim()) {
          this.$message.warning('请输入内容');
          return;
        }

        if (!this.editForm.scheme_id) {  // 使用下划线格式
          this.$message.warning('缺少预警方案ID');
          return;
        }

        // 记录编辑前的状态，用于统计数据更新
        const oldRecord = this.tableData.find(item => item.id === this.editForm.id);
        const oldStatus = oldRecord ? oldRecord.status : null;
        const newStatus = this.editForm.status;

        console.log('📊 编辑前状态:', oldStatus, '编辑后状态:', newStatus);

        // 调用更新API
        const response = await updateWarningRecord(this.editForm);

        if (response.code === 200) {
          // 更新本地数据
          const index = this.tableData.findIndex(item => item.id === this.editForm.id);
          if (index !== -1) {
            this.tableData[index] = {
              ...this.tableData[index],
              title: this.editForm.content, // 使用content作为title显示
              content: this.editForm.content,
              status: this.editForm.status,
              remark: this.editForm.remark
            };
          }

          // 立即更新本地统计数据
          if (oldStatus !== null && oldStatus !== newStatus) {
            this.updateLocalStatistics(oldStatus, newStatus);
          }

          this.$message.success('保存成功');
          this.closeEditDialog();

          // 延迟重新加载数据以确保同步，但不影响用户体验
          setTimeout(() => {
            this.getList();
          }, 1000);
        } else {
          this.$message.error(response.msg || '保存失败');
        }
      } catch (error) {
        console.error('保存失败:', error);
        this.$message.error('保存失败');
      }
    },

    // 获取状态标签
    getStatusLabel(status) {
      const statusMap = {
        'pending': '待处理',
        'processed': '已处理',
        'urgent': '紧急'
      };
      return statusMap[status] || '未知';
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'pending': 'warning',
        'processed': 'success',
        'urgent': 'danger',
        0: 'warning',
        1: 'success',
        2: 'danger'
      };
      return typeMap[status] || 'info';
    },

    // 获取状态值（字符串转数字）
    getStatusValue(status) {
      const statusMap = {
        'pending': 0,
        'processed': 1,
        'urgent': 2
      };
      return typeof status === 'number' ? status : (statusMap[status] || 0);
    },

    // ==================== 原有方法 ====================

    // 辅助方法：更新预警设置
    updateWarningSettings(settings) {
      if (settings) {
        // 更新平台类型设置
        if (settings.platformTypes) {
          this.warningSettings.platformType.options.forEach(option => {
            option.checked = settings.platformTypes.includes(option.value);
          });
        }

        // 更新其他设置
        if (settings.contentProperty) {
          this.warningSettings.contentProperty.value = settings.contentProperty;
        }
        if (settings.infoType) {
          this.warningSettings.infoType.value = settings.infoType;
        }
        if (settings.matchMethod) {
          this.warningSettings.matchMethod.value = settings.matchMethod;
        }

        // 更新关键词设置
        if (settings.allowWords) {
          this.keywordSettings.allowWords = settings.allowWords;
        }
        if (settings.rejectWords) {
          this.keywordSettings.rejectWords = settings.rejectWords;
        }

        // 更新自动预警设置
        if (settings.autoWarningSettings) {
          this.autoWarningSettings = { ...this.autoWarningSettings, ...settings.autoWarningSettings };
        }
      }
    }
  }
};
</script>

<style scoped>
.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-container {
  display: flex;
  height: 100%;
}

/* 左侧导航栏样式 */
.left-sidebar {
  width: 200px;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  transition: width 0.3s;
}

/* 折叠状态的侧边栏 */
.left-sidebar.collapsed {
  width: 64px;
}

.left-sidebar.collapsed .sidebar-search,
.left-sidebar.collapsed .el-menu-item span,
.left-sidebar.collapsed .el-submenu__title span {
  display: none;
}

.left-sidebar.collapsed .new-scheme-btn {
  padding: 8px 0;
  font-size: 0;
}

.left-sidebar.collapsed .new-scheme-btn i {
  font-size: 16px;
  margin: 0;
}

.sidebar-header {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.new-scheme-btn {
  flex: 1;
  font-size: 12px;
  padding: 8px 10px;
}

.sidebar-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  cursor: pointer;
  color: #909399;
}

.sidebar-search {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.sidebar-menu {
  flex: 1;
  overflow-y: auto;
}

.sidebar-menu-list {
  border-right: none;
}

.active-menu-item {
  background-color: #ecf5ff !important;
  color: #409EFF !important;
}

/* 菜单图标样式 */
::v-deep .el-menu-item i,
::v-deep .el-submenu__title i {
  margin-right: 8px;
  font-size: 16px;
  width: 16px;
  text-align: center;
}

::v-deep .el-menu-item i {
  color: #606266;
}

::v-deep .el-submenu__title i {
  color: #909399;
}

::v-deep .el-menu-item.is-active i,
::v-deep .active-menu-item i {
  color: #409EFF;
}

/* 右侧内容区样式 */
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 50px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
}

.nav-items {
  display: flex;
}

.nav-item {
  padding: 0 15px;
  line-height: 50px;
  cursor: pointer;
  position: relative;
}

.nav-item.active {
  color: #409EFF;
  font-weight: bold;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #409EFF;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-info span {
  margin-left: 8px;
  font-size: 14px;
  color: #606266;
}

.main-content {
  flex: 1;
  padding: 20px;
  background-color: #f5f7fa;
  overflow-y: auto;
}

.title-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
}

/* 统计数据卡片样式 */
.statistics-area {
  margin-bottom: 20px;
}

.stat-card {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-card.pending .stat-number {
  color: #E6A23C;
}

.stat-card.processed .stat-number {
  color: #67C23A;
}

.stat-card.urgent .stat-number {
  color: #F56C6C;
}

.stat-card.negative .stat-number {
  color: #F56C6C;
}

.stat-card.positive .stat-number {
  color: #67C23A;
}

.title {
  display: flex;
  align-items: center;
  width: 100%;
}

.title h2 {
  margin: 0;
  font-size: 18px;
  margin-right: 20px;
  display: flex;
  align-items: center;
  text-align: left;
}

.title h2 i {
  margin-left: 5px;
  font-size: 16px;
  color: #909399;
  cursor: pointer;
}

.tabs {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}

.tabs .el-button {
  margin-right: 15px;
  padding-left: 0;
}

.actions {
  display: flex;
  align-items: center;
}

.actions .el-button {
  margin-left: 15px;
}

.table-area {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.left-tools, .right-tools {
  display: flex;
  align-items: center;
}

.left-tools > * {
  margin-right: 10px;
}

.right-tools > * {
  margin-left: 15px;
}

.date-range {
  font-size: 12px;
  color: #606266;
}

.el-dropdown-link {
  cursor: pointer;
  color: #606266;
}

.el-table {
  margin-bottom: 20px;
}

/* 覆盖Element UI的一些默认样式 */
::v-deep .el-menu-item, ::v-deep .el-submenu__title {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

::v-deep .el-submenu .el-menu-item {
  height: 36px;
  line-height: 36px;
  padding: 0 20px 0 40px;
}

/* 预警设置抽屉样式 */
.warning-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    font-size: 16px;
    font-weight: 500;
  }

  .el-drawer__body {
    padding: 0;
  }
}

.warning-drawer-content {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  position: relative;
  padding-bottom: 70px; /* 为底部按钮留出空间 */
}

.warning-section {
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.warning-section h3 {
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
  color: #333;
}

.warning-options {
  display: flex;
  flex-wrap: wrap;
}

.warning-options .el-checkbox {
  margin-right: 10px;
  margin-bottom: 10px;
  font-size: 13px;
}

.warning-options .el-radio {
  margin-right: 15px;
  margin-bottom: 10px;
  font-size: 13px;
}

.region-section {
  padding: 5px 0;
}

.region-input {
  margin-bottom: 10px;
}

.region-tags {
  display: flex;
  flex-wrap: wrap;
}

.region-tags .el-tag {
  margin-right: 10px;
  margin-bottom: 10px;
}

.category-section {
  cursor: pointer;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-count {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 13px;
}

.category-count i {
  margin-left: 5px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px 20px;
  background-color: #fff;
  border-top: 1px solid #eee;
  text-align: right;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

/* 关键词设置抽屉样式 */
.keyword-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    font-size: 16px;
    font-weight: 500;
  }

  .el-drawer__body {
    padding: 0;
  }
}

.keyword-drawer-content {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  position: relative;
  padding-bottom: 70px; /* 为底部按钮留出空间 */
}

.keyword-section {
  margin-bottom: 20px;
}

.keyword-section h3 {
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
  color: #333;
}

/* 自动预警设置抽屉样式 */
.auto-warning-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    font-size: 16px;
    font-weight: 500;
  }

  .el-drawer__body {
    padding: 0;
  }
}

.auto-warning-drawer-content {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  position: relative;
  padding-bottom: 70px; /* 为底部按钮留出空间 */
}

.auto-warning-title {
  font-size: 16px;
  margin-bottom: 20px;
  font-weight: 500;
  color: #333;
}

.auto-warning-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.section-label {
  font-size: 14px;
  margin-bottom: 10px;
  color: #666;
}

.time-range-selector {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.time-separator {
  margin: 0 5px;
}

.time-range-note {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.platform-checkboxes {
  display: flex;
  flex-wrap: wrap;
}

.platform-checkboxes .el-checkbox {
  margin-right: 15px;
  margin-bottom: 10px;
}

.warning-type-selector,
.process-method-selector,
.priority-selector,
.handle-method-selector {
  display: flex;
  flex-wrap: wrap;
}

.warning-type-selector .el-radio,
.process-method-selector .el-radio,
.priority-selector .el-radio,
.handle-method-selector .el-radio {
  margin-right: 20px;
  margin-bottom: 10px;
}

.process-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding: 5px 0;
}

.switch-label {
  font-size: 13px;
  color: #666;
}

.note-text {
  font-size: 12px;
  color: #999;
}

.notify-method-checkboxes {
  display: flex;
  flex-wrap: wrap;
}

.notify-method-checkboxes .el-checkbox {
  margin-right: 20px;
  margin-bottom: 10px;
}

/* 表格相关样式 */
.is-favorite {
  color: #f39c12 !important;
}

.is-favorite:hover {
  color: #e67e22 !important;
}

.content-text {
  max-height: 200px;
  overflow-y: auto;
  line-height: 1.6;
  word-break: break-word;
}

.detail-content {
  padding: 10px 0;
}

.dialog-footer {
  text-align: right;
}

/* 下拉菜单样式 */
.el-dropdown-menu .is-active {
  background-color: #ecf5ff;
  color: #409EFF;
}

.el-dropdown-menu .is-disabled {
  opacity: 0.6;
}

/* 工具栏按钮禁用状态 */
.left-tools .el-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
