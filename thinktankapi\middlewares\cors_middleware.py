from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware


def add_cors_middleware(app: FastAPI):
    """
    添加跨域中间件

    :param app: FastAPI对象
    :return:
    """
    # 前端页面url
    origins = [
        # 开发环境
        'http://localhost:80',
        'http://127.0.0.1:80',
        'http://localhost:81',
        'http://127.0.0.1:81',
        'http://localhost:8080',  # Vue开发服务器默认端口
        'http://127.0.0.1:8080',
        'http://***********:81',  # 网络地址
        # 生产环境
        'https://thinks.jingangai.cn',
        'http://thinks.jingangai.cn',
        'https://thinksapi.jingangai.cn',
    ]

    # 后台api允许跨域
    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )
