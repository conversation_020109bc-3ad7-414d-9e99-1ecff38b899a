from datetime import datetime
from sqlalchemy import and_, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from typing import List, Optional
from module_admin.entity.do.emotion_analysis_summary_do import EmotionAnalysisSummary
from module_admin.entity.do.emotion_definition_do import EmotionDefinition
from module_admin.entity.vo.emotion_analysis_vo import EmotionAnalysisQueryModel
from utils.page_util import PageUtil


class EmotionAnalysisDao:
    """
    情感分析模块数据库操作层
    """

    @classmethod
    async def get_emotion_analysis_summary_by_scheme(
        cls, db: AsyncSession, scheme_id: int, date_start: Optional[datetime] = None, date_end: Optional[datetime] = None
    ) -> List[EmotionAnalysisSummary]:
        """
        根据方案ID获取情感分析汇总数据

        :param db: orm对象
        :param scheme_id: 方案ID
        :param date_start: 开始时间
        :param date_end: 结束时间
        :return: 情感分析汇总数据列表
        """
        query = select(EmotionAnalysisSummary).options(
            selectinload(EmotionAnalysisSummary.emotion_definition)
        ).where(EmotionAnalysisSummary.scheme_id == scheme_id)
        
        if date_start:
            query = query.where(EmotionAnalysisSummary.date_range_start >= date_start)
        if date_end:
            query = query.where(EmotionAnalysisSummary.date_range_end <= date_end)
            
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_emotion_statistics_by_scheme(
        cls, db: AsyncSession, scheme_id: int, date_start: Optional[datetime] = None, date_end: Optional[datetime] = None
    ) -> List[dict]:
        """
        根据方案ID获取情感统计数据（聚合）

        :param db: orm对象
        :param scheme_id: 方案ID
        :param date_start: 开始时间
        :param date_end: 结束时间
        :return: 情感统计数据
        """
        query = select(
            EmotionDefinition.emotion_name,
            EmotionDefinition.emotion_code,
            EmotionDefinition.emotion_color,
            func.sum(EmotionAnalysisSummary.count).label('total_count')
        ).join(
            EmotionAnalysisSummary, EmotionDefinition.id == EmotionAnalysisSummary.emotion_id
        ).where(
            EmotionAnalysisSummary.scheme_id == scheme_id
        )
        
        if date_start:
            query = query.where(EmotionAnalysisSummary.date_range_start >= date_start)
        if date_end:
            query = query.where(EmotionAnalysisSummary.date_range_end <= date_end)
            
        query = query.group_by(
            EmotionDefinition.id,
            EmotionDefinition.emotion_name,
            EmotionDefinition.emotion_code,
            EmotionDefinition.emotion_color
        ).order_by(EmotionDefinition.sort_order)
        
        result = await db.execute(query)
        return [dict(row._mapping) for row in result]

    @classmethod
    async def get_emotion_trend_data(
        cls, db: AsyncSession, scheme_id: int, days: int = 30
    ) -> List[dict]:
        """
        获取情感趋势数据

        :param db: orm对象
        :param scheme_id: 方案ID
        :param days: 天数
        :return: 趋势数据
        """
        query = select(
            EmotionAnalysisSummary.date_range_start,
            EmotionDefinition.emotion_name,
            EmotionDefinition.emotion_code,
            EmotionDefinition.emotion_color,
            EmotionAnalysisSummary.count
        ).join(
            EmotionDefinition, EmotionDefinition.id == EmotionAnalysisSummary.emotion_id
        ).where(
            EmotionAnalysisSummary.scheme_id == scheme_id
        ).order_by(
            EmotionAnalysisSummary.date_range_start,
            EmotionDefinition.sort_order
        )
        
        result = await db.execute(query)
        return [dict(row._mapping) for row in result]

    @classmethod
    async def get_all_emotion_definitions(cls, db: AsyncSession) -> List[EmotionDefinition]:
        """
        获取所有情感定义

        :param db: orm对象
        :return: 情感定义列表
        """
        query = select(EmotionDefinition).where(EmotionDefinition.status == '0').order_by(EmotionDefinition.sort_order)
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def check_data_exists(cls, db: AsyncSession, scheme_id: int) -> bool:
        """
        检查指定方案是否有数据

        :param db: orm对象
        :param scheme_id: 方案ID
        :return: 是否存在数据
        """
        query = select(func.count(EmotionAnalysisSummary.id)).where(EmotionAnalysisSummary.scheme_id == scheme_id)
        result = await db.execute(query)
        count = result.scalar()
        return count > 0
