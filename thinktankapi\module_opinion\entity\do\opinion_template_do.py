from datetime import datetime
from sqlalchemy import BigInteger, Column, DateTime, Integer, String, Text
from config.database import Base


class OpinionTemplate(Base):
    """
    舆情分析模板表
    """

    __tablename__ = 'opinion_template'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    template_name = Column(String(200), nullable=False, comment='模板名称')
    template_category = Column(String(100), nullable=False, comment='模板分类')
    entity_keyword = Column(String(500), nullable=False, comment='实体关键词')
    specific_requirement = Column(Text, nullable=False, comment='具体需求描述')
    template_description = Column(Text, nullable=True, comment='模板描述')
    priority = Column(String(20), default='medium', comment='优先级：high, medium, low')
    max_keywords_limit = Column(Integer, default=5, comment='最大关键词选择数量')
    template_tags = Column(String(500), nullable=True, comment='模板标签，多个标签用逗号分隔')
    usage_count = Column(Integer, default=0, comment='使用次数统计')
    is_system_template = Column(Integer, default=0, comment='是否系统模板：0-用户自定义，1-系统预设')
    is_active = Column(Integer, default=1, comment='是否激活：0-否，1-是')
    is_deleted = Column(Integer, default=0, comment='是否删除：0-否，1-是')
    sort_order = Column(Integer, default=0, comment='排序顺序')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), default='', comment='备注信息')
