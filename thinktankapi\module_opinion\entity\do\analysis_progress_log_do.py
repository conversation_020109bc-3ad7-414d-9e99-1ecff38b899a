from datetime import datetime
from sqlalchemy import Column, BigInteger, String, Text, Integer, DateTime, JSON, Index
from config.database import Base


class AnalysisProgressLog(Base):
    """
    分析进度日志数据对象
    """
    __tablename__ = 'analysis_progress_log'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    task_id = Column(String(64), nullable=False, comment='分析任务ID')
    requirement_id = Column(BigInteger, nullable=False, comment='需求ID，关联opinion_requirement表')
    user_id = Column(BigInteger, nullable=True, comment='用户ID')
    log_level = Column(String(20), nullable=False, default='info', comment='日志级别：info, success, warning, error')
    log_message = Column(Text, nullable=False, comment='日志消息内容')
    progress_percentage = Column(Integer, default=0, comment='进度百分比：0-100')
    step_name = Column(String(100), nullable=True, comment='当前步骤名称')
    step_status = Column(String(20), default='running', comment='步骤状态：running, completed, failed')
    execution_time = Column(DateTime, nullable=True, comment='执行时间')
    additional_data = Column(JSON, nullable=True, comment='附加数据，JSON格式')
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')

    # 索引
    idx_task_id = Index('idx_task_id', task_id)
    idx_requirement_id = Index('idx_requirement_id', requirement_id)
    idx_user_id = Index('idx_user_id', user_id)
    idx_create_time = Index('idx_create_time', create_time)
    idx_log_level = Index('idx_log_level', log_level)
    idx_step_status = Index('idx_step_status', step_status)


class AnalysisTask(Base):
    """
    分析任务数据对象
    """
    __tablename__ = 'analysis_task'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    task_id = Column(String(64), nullable=False, unique=True, comment='分析任务唯一ID')
    requirement_id = Column(BigInteger, nullable=False, comment='需求ID，关联opinion_requirement表')
    user_id = Column(BigInteger, nullable=True, comment='用户ID')
    task_name = Column(String(200), nullable=False, comment='任务名称')
    task_status = Column(String(20), nullable=False, default='pending', comment='任务状态：pending, running, completed, failed, cancelled')
    progress_percentage = Column(Integer, default=0, comment='总体进度百分比：0-100')
    start_time = Column(DateTime, nullable=True, comment='开始时间')
    end_time = Column(DateTime, nullable=True, comment='结束时间')
    duration = Column(Integer, nullable=True, comment='执行时长（秒）')
    analysis_config = Column(JSON, nullable=True, comment='分析配置，JSON格式')
    result_summary = Column(JSON, nullable=True, comment='结果摘要，JSON格式')
    error_message = Column(Text, nullable=True, comment='错误信息')
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now, comment='更新时间')

    # 索引
    idx_requirement_id = Index('idx_requirement_id', requirement_id)
    idx_user_id = Index('idx_user_id', user_id)
    idx_task_status = Index('idx_task_status', task_status)
    idx_create_time = Index('idx_create_time', create_time)
