from fastapi import APIRouter, Depends, Request, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from config.get_db import get_db
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_opinion.service.opinion_requirement_service import OpinionRequirementService
from module_opinion.service.requirement_keyword_service import RequirementKeywordService
from module_opinion.service.opinion_task_service import OpinionTaskService
from module_opinion.service.requirement_data_source_service import RequirementDataSourceService
from module_opinion.service.external_api_service import ExternalApiService
from module_opinion.service.push_report_service import PushReportService
from module_opinion.entity.vo.opinion_requirement_vo import (
    OpinionRequirementPageQueryModel,
    CreateOpinionRequirementModel,
    UpdateOpinionRequirementModel,
    DeleteOpinionRequirementModel
)
from module_opinion.entity.vo.requirement_keyword_vo import (
    RequirementKeywordPageQueryModel,
    GenerateKeywordsModel,
    SelectKeywordsModel
)
from module_opinion.entity.vo.opinion_task_vo import (
    OpinionTaskPageQueryModel,
    CreateOpinionTaskModel,
    UpdateOpinionTaskModel,
    UpdateTaskStatusModel,
    UpdateSentimentCountsModel,
    DeleteOpinionTaskModel
)
from module_opinion.entity.vo.requirement_data_source_vo import (
    RequirementDataSourcePageQueryModel,
    CreateRequirementDataSourceModel,
    UpdateRequirementDataSourceModel,
    DeleteRequirementDataSourceModel,
    BatchCreateDataSourceModel
)
from module_opinion.entity.vo.online_search_vo import (
    OnlineSearchRequestModel,
    AnalysisStartRequestModel
)
from module_opinion.entity.vo.push_report_vo import (
    PushReportRequestModel,
    PushReportResponseModel
)
from module_opinion.entity.vo.analysis_progress_vo import (
    CreateAnalysisTaskModel,
    CreateProgressLogModel,
    AnalysisProgressQueryModel,
    AnalysisProgressResponseModel
)
from module_opinion.service.analysis_progress_service import AnalysisProgressService
from utils.response_util import ResponseUtil
from utils.log_util import logger
from exceptions.exception import ServiceException
from utils.page_util import PageResponseModel


# 舆情分析接口（需要认证）- 从公开接口改为需要认证的接口
publicOpinionAnalysisController = APIRouter(prefix='/public/opinion-analysis', dependencies=[Depends(LoginService.get_current_user)])


@publicOpinionAnalysisController.get('/requirements')
async def get_public_requirement_list(
    request: Request,
    requirement_page_query: OpinionRequirementPageQueryModel = Depends(OpinionRequirementPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取舆情需求列表（需要认证）
    """
    try:
        # 从认证用户中获取用户ID，确保数据权限隔离
        requirement_page_query.user_id = current_user.user.user_id

        # 添加日志输出，检查接收到的参数
        logger.info(f'🔍 后端接收到的查询参数: {requirement_page_query.model_dump()}')
        logger.info(f'🔍 需求名称: {requirement_page_query.requirement_name}')
        logger.info(f'🔍 实体关键词: {requirement_page_query.entity_keyword}')
        logger.info(f'🔍 分析状态: {requirement_page_query.analysis_status}')
        logger.info(f'🔍 用户ID: {requirement_page_query.user_id}')
        logger.info(f'🔍 页码: {requirement_page_query.page_num}')
        logger.info(f'🔍 每页大小: {requirement_page_query.page_size}')

        # 检查是否有筛选条件
        has_filters = bool(requirement_page_query.requirement_name or
                          requirement_page_query.entity_keyword or
                          requirement_page_query.analysis_status is not None or
                          requirement_page_query.status is not None)
        logger.info(f'🔍 是否包含筛选条件: {has_filters}')

        requirement_list_result = await OpinionRequirementService.get_opinion_requirement_list_services(
            query_db, requirement_page_query, is_page=True
        )

        # 添加详细的返回数据日志
        logger.info(f'🔍 Service层返回的数据类型: {type(requirement_list_result)}')
        if hasattr(requirement_list_result, 'model_dump'):
            logger.info(f'🔍 Service层返回的数据model_dump: {requirement_list_result.model_dump()}')

        logger.info('获取舆情需求列表成功')
        # 使用 model_content 参数，这样会调用 model_dump(by_alias=True) 并直接合并到响应中
        response_result = ResponseUtil.success(model_content=requirement_list_result)
        logger.info(f'🔍 最终API响应结构: {response_result.body.decode("utf-8") if hasattr(response_result, "body") else "无法获取响应体"}')
        return response_result
    except Exception as e:
        logger.error(f'获取舆情需求列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取舆情需求列表失败: {str(e)}')


@publicOpinionAnalysisController.get('/keywords/categories')
async def get_public_keyword_categories(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取关键词分类列表（公开接口）
    """
    try:
        categories_result = await RequirementKeywordService.get_keyword_categories_services(query_db)
        logger.info('获取关键词分类列表成功')
        return ResponseUtil.success(data=categories_result)
    except Exception as e:
        logger.error(f'获取关键词分类列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取关键词分类列表失败: {str(e)}')


@publicOpinionAnalysisController.post('/requirements')
async def create_public_requirement(
    request: Request,
    add_requirement: CreateOpinionRequirementModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
):
    """
    创建舆情需求（公开接口）
    """
    try:
        # 使用默认用户ID（1）作为创建者，因为这是公开接口
        default_user_id = 1
        add_requirement_result = await OpinionRequirementService.add_opinion_requirement_services(
            query_db, add_requirement, default_user_id
        )
        logger.info('创建舆情需求成功')
        return ResponseUtil.success(data=add_requirement_result)
    except Exception as e:
        # 详细的错误日志
        logger.error(f'创建舆情需求失败: {str(e)}')
        logger.error(f'异常类型: {type(e).__name__}')
        logger.error(f'异常详情: {repr(e)}')
        if hasattr(e, 'message'):
            logger.error(f'异常消息: {e.message}')

        # 构建错误响应消息
        error_msg = str(e) if str(e) else f'创建舆情需求时发生未知错误: {type(e).__name__}'
        return ResponseUtil.error(msg=f'创建舆情需求失败: {error_msg}')


# 内部接口（需要认证）
internalOpinionAnalysisController = APIRouter(prefix='/system/opinion-analysis', dependencies=[Depends(LoginService.get_current_user)])


@internalOpinionAnalysisController.get('/requirements', response_model=PageResponseModel)
async def get_requirement_list(
    request: Request,
    requirement_page_query: OpinionRequirementPageQueryModel = Depends(OpinionRequirementPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取舆情需求列表
    """
    try:
        requirement_list_result = await OpinionRequirementService.get_opinion_requirement_list_services(
            query_db, requirement_page_query, is_page=True
        )
        logger.info('获取舆情需求列表成功')
        return ResponseUtil.success(model_content=requirement_list_result)
    except Exception as e:
        logger.error(f'获取舆情需求列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取舆情需求列表失败: {str(e)}')


@internalOpinionAnalysisController.get('/requirements/select')
async def get_requirement_list_for_select(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取需求列表用于下拉选择
    """
    try:
        requirement_list_result = await OpinionRequirementService.get_requirement_list_for_select_services(
            query_db, current_user.user.user_id
        )
        logger.info('获取需求选择列表成功')
        return ResponseUtil.success(data=requirement_list_result)
    except Exception as e:
        logger.error(f'获取需求选择列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取需求选择列表失败: {str(e)}')


@internalOpinionAnalysisController.get('/requirements/{requirement_id}')
async def get_requirement_detail(
    request: Request,
    requirement_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取舆情需求详细信息
    """
    try:
        requirement_detail_result = await OpinionRequirementService.get_opinion_requirement_detail_services(
            query_db, requirement_id
        )
        logger.info(f'获取需求ID为{requirement_id}的详细信息成功')
        return ResponseUtil.success(data=requirement_detail_result)
    except Exception as e:
        logger.error(f'获取需求详细信息失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取需求详细信息失败: {str(e)}')


@internalOpinionAnalysisController.post('/requirements')
async def create_requirement(
    request: Request,
    add_requirement: CreateOpinionRequirementModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    创建舆情需求
    """
    try:
        add_requirement_result = await OpinionRequirementService.add_opinion_requirement_services(
            query_db, add_requirement, current_user.user.user_id
        )
        logger.info('创建舆情需求成功')
        return ResponseUtil.success(data=add_requirement_result)
    except Exception as e:
        logger.error(f'创建舆情需求失败: {str(e)}')
        return ResponseUtil.error(msg=f'创建舆情需求失败: {str(e)}')


@internalOpinionAnalysisController.put('/requirements/{requirement_id}')
async def update_requirement(
    request: Request,
    requirement_id: int,
    edit_requirement: UpdateOpinionRequirementModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新舆情需求
    """
    try:
        # 确保ID一致
        edit_requirement.id = requirement_id
        edit_requirement_result = await OpinionRequirementService.edit_opinion_requirement_services(
            query_db, edit_requirement, current_user.user.user_id
        )
        logger.info(f'更新需求ID为{requirement_id}的信息成功')
        return ResponseUtil.success(data=edit_requirement_result)
    except Exception as e:
        logger.error(f'更新舆情需求失败: {str(e)}')
        return ResponseUtil.error(msg=f'更新舆情需求失败: {str(e)}')


@internalOpinionAnalysisController.delete('/requirements')
async def delete_requirements(
    request: Request,
    delete_requirement: DeleteOpinionRequirementModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除舆情需求
    """
    try:
        delete_requirement_result = await OpinionRequirementService.delete_opinion_requirement_services(
            query_db, delete_requirement
        )
        logger.info('删除舆情需求成功')
        return ResponseUtil.success(data=delete_requirement_result)
    except Exception as e:
        logger.error(f'删除舆情需求失败: {str(e)}')
        return ResponseUtil.error(msg=f'删除舆情需求失败: {str(e)}')


@internalOpinionAnalysisController.get('/requirements/export')
async def export_requirements(
    request: Request,
    requirement_page_query: OpinionRequirementPageQueryModel = Depends(OpinionRequirementPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    导出舆情需求列表
    """
    try:
        from fastapi.responses import StreamingResponse
        import io
        import pandas as pd
        from datetime import datetime

        # 获取所有符合条件的需求数据（不分页）
        requirement_list_result = await OpinionRequirementService.get_opinion_requirement_list_services(
            query_db, requirement_page_query, is_page=False
        )

        # 准备导出数据
        export_data = []
        for req in requirement_list_result:
            # 状态映射
            status_map = {0: '禁用', 1: '启用', 2: '已完成'}
            analysis_status_map = {0: '未开始', 1: '分析中', 2: '已完成', 3: '失败'}
            priority_map = {'high': '高', 'medium': '中', 'low': '低'}

            export_data.append({
                'ID': req.id,
                '需求名称': req.requirement_name,
                '实体关键词': req.entity_keyword,
                '具体需求': req.specific_requirement,
                '状态': status_map.get(req.status, '未知'),
                '分析状态': analysis_status_map.get(req.analysis_status, '未知'),
                '优先级': priority_map.get(req.priority, '未知'),
                '当前步骤': req.current_step,
                '已选关键词数': req.selected_keywords_count,
                '最大关键词限制': req.max_keywords_limit,
                '创建时间': req.create_time.strftime('%Y-%m-%d %H:%M:%S') if req.create_time else '',
                '更新时间': req.update_time.strftime('%Y-%m-%d %H:%M:%S') if req.update_time else '',
                '创建者': req.create_by,
                '备注': req.remark
            })

        # 创建DataFrame
        df = pd.DataFrame(export_data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='分析记录', index=False)

        output.seek(0)

        # 生成文件名
        filename = f"分析记录_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        logger.info(f'导出舆情需求列表成功，共 {len(export_data)} 条记录')

        return StreamingResponse(
            io.BytesIO(output.read()),
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={'Content-Disposition': f'attachment; filename="{filename}"'}
        )
    except Exception as e:
        logger.error(f'导出舆情需求列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'导出舆情需求列表失败: {str(e)}')


@internalOpinionAnalysisController.get('/keywords')
async def get_requirement_keywords(
    request: Request,
    keyword_page_query: RequirementKeywordPageQueryModel = Depends(RequirementKeywordPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取需求关键词列表
    """
    try:
        keyword_list_result = await RequirementKeywordService.get_requirement_keyword_list_services(
            query_db, keyword_page_query, is_page=True
        )
        logger.info('获取需求关键词列表成功')
        return ResponseUtil.success(model_content=keyword_list_result)
    except Exception as e:
        logger.error(f'获取需求关键词列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取需求关键词列表失败: {str(e)}')


@internalOpinionAnalysisController.post('/keywords/generate')
async def generate_keywords(
    request: Request,
    generate_request: GenerateKeywordsModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    生成关联词
    """
    try:
        generate_result = await RequirementKeywordService.generate_keywords_services(
            query_db, generate_request, current_user.user.user_id
        )
        logger.info(f'为需求ID {generate_request.requirement_id} 生成关联词成功')
        return ResponseUtil.success(data=generate_result)
    except Exception as e:
        logger.error(f'生成关联词失败: {str(e)}')
        return ResponseUtil.error(msg=f'生成关联词失败: {str(e)}')


@internalOpinionAnalysisController.post('/keywords/select')
async def select_keywords(
    request: Request,
    select_request: SelectKeywordsModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    选择关键词
    """
    try:
        select_result = await RequirementKeywordService.select_keywords_services(
            query_db, select_request, current_user.user.user_id
        )
        logger.info(f'为需求ID {select_request.requirement_id} 选择关键词成功')
        return ResponseUtil.success(data=select_result)
    except Exception as e:
        logger.error(f'选择关键词失败: {str(e)}')
        return ResponseUtil.error(msg=f'选择关键词失败: {str(e)}')


@internalOpinionAnalysisController.get('/keywords/selected/{requirement_id}')
async def get_selected_keywords(
    request: Request,
    requirement_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取已选择的关键词
    """
    try:
        selected_keywords_result = await RequirementKeywordService.get_selected_keywords_services(
            query_db, requirement_id
        )
        logger.info(f'获取需求ID {requirement_id} 的已选择关键词成功')
        return ResponseUtil.success(data=selected_keywords_result)
    except Exception as e:
        logger.error(f'获取已选择关键词失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取已选择关键词失败: {str(e)}')


@internalOpinionAnalysisController.get('/keywords/categories')
async def get_keyword_categories(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取关键词分类列表
    """
    try:
        categories_result = await RequirementKeywordService.get_keyword_categories_services(query_db)
        logger.info('获取关键词分类列表成功')
        return ResponseUtil.success(data=categories_result)
    except Exception as e:
        logger.error(f'获取关键词分类列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取关键词分类列表失败: {str(e)}')


# 任务管理相关API
@internalOpinionAnalysisController.get('/tasks')
async def get_task_list(
    request: Request,
    task_page_query: OpinionTaskPageQueryModel = Depends(OpinionTaskPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取舆情任务列表（用户级别隔离）
    """
    try:
        # 详细日志记录用户认证信息，用于诊断环境差异问题
        logger.info(f'[用户认证] 当前用户对象: {current_user}')
        logger.info(f'[用户认证] 用户ID: {current_user.user.user_id} (类型: {type(current_user.user.user_id)})')
        logger.info(f'[用户认证] 用户名: {current_user.user.user_name}')

        # 验证用户ID的有效性
        if current_user.user.user_id is None:
            logger.error('[用户认证] 用户ID为None，认证可能失败')
            return ResponseUtil.error(msg='用户认证失败，请重新登录')

        if not isinstance(current_user.user.user_id, int) or current_user.user.user_id <= 0:
            logger.error(f'[用户认证] 用户ID无效: {current_user.user.user_id}')
            return ResponseUtil.error(msg='用户认证信息无效')

        # 记录原始查询参数
        logger.info(f'[查询参数] 原始查询对象: {task_page_query}')
        logger.info(f'[查询参数] 原始user_id: {task_page_query.user_id}')

        # 创建查询对象的副本，避免修改原对象，确保并发安全
        # 使用 Pydantic 的 model_copy() 方法创建深拷贝，防止并发请求间的数据污染
        safe_task_query = task_page_query.model_copy(update={'user_id': current_user.user.user_id})

        # 记录安全查询对象信息
        logger.info(f'[查询参数] 安全查询对象user_id: {safe_task_query.user_id}')
        logger.info(f'[查询参数] 确认用户数据隔离 - 只查询用户ID为{safe_task_query.user_id}的数据')

        task_list_result = await OpinionTaskService.get_opinion_task_list_services(
            query_db, safe_task_query, is_page=True
        )

        # 记录查询结果统计信息
        total_records = task_list_result.total if hasattr(task_list_result, 'total') else len(task_list_result.records) if hasattr(task_list_result, 'records') else 0
        logger.info(f'[查询结果] 用户ID为{current_user.user.user_id}的舆情任务列表查询成功，共{total_records}条记录')
        # 手动构建返回数据，避免model_dump问题
        result_data = {
            'records': task_list_result.records,
            'pageNum': task_list_result.page_num,
            'pageSize': task_list_result.page_size,
            'total': task_list_result.total,
            'hasNext': task_list_result.has_next
        }
        return ResponseUtil.success(dict_content=result_data)
    except Exception as e:
        logger.error(f'获取舆情任务列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取舆情任务列表失败: {str(e)}')


@internalOpinionAnalysisController.get('/tasks/check-exists')
async def check_task_exists(
    request: Request,
    requirement_id: int,
    task_type: str,
    push_url: str,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    检查任务是否已存在
    """
    try:
        check_result = await OpinionTaskService.check_task_exists_services(
            query_db, requirement_id, task_type, push_url
        )
        logger.info(f'检查任务是否存在: {check_result}')
        return ResponseUtil.success(data=check_result)
    except Exception as e:
        logger.error(f'检查任务是否存在失败: {str(e)}')
        return ResponseUtil.error(msg=f'检查任务是否存在失败: {str(e)}')


@internalOpinionAnalysisController.get('/tasks/{task_id}')
async def get_task_detail(
    request: Request,
    task_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取舆情任务详细信息（用户级别隔离）
    """
    try:
        task_detail_result = await OpinionTaskService.get_opinion_task_detail_services(
            query_db, task_id, current_user.user.user_id
        )
        logger.info(f'用户ID为{current_user.user.user_id}获取任务ID为{task_id}的详细信息成功')
        return ResponseUtil.success(data=task_detail_result)
    except Exception as e:
        logger.error(f'获取任务详细信息失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取任务详细信息失败: {str(e)}')


@internalOpinionAnalysisController.post('/tasks')
async def create_task(
    request: Request,
    add_task: CreateOpinionTaskModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    创建舆情任务
    """
    try:
        add_task_result = await OpinionTaskService.add_opinion_task_services(
            query_db, add_task, current_user.user.user_id
        )
        logger.info('创建舆情任务成功')
        return ResponseUtil.success(data=add_task_result)
    except Exception as e:
        logger.error(f'创建舆情任务失败: {str(e)}')
        return ResponseUtil.error(msg=f'创建舆情任务失败: {str(e)}')



@internalOpinionAnalysisController.put('/tasks/{task_id}')
async def update_task(
    request: Request,
    task_id: int,
    edit_task: UpdateOpinionTaskModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新舆情任务
    """
    try:
        # 确保ID一致
        edit_task.id = task_id
        edit_task_result = await OpinionTaskService.edit_opinion_task_services(
            query_db, edit_task, current_user.user.user_id
        )
        logger.info(f'更新任务ID为{task_id}的信息成功')
        return ResponseUtil.success(data=edit_task_result)
    except Exception as e:
        logger.error(f'更新舆情任务失败: {str(e)}')
        return ResponseUtil.error(msg=f'更新舆情任务失败: {str(e)}')


@internalOpinionAnalysisController.put('/tasks/{task_id}/status')
async def update_task_status(
    request: Request,
    task_id: int,
    update_status: UpdateTaskStatusModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新任务状态（用户级别隔离）
    """
    try:
        # 确保ID一致
        update_status.task_id = task_id
        update_result = await OpinionTaskService.update_task_status_services(
            query_db, update_status, current_user.user.user_id
        )
        logger.info(f'用户ID为{current_user.user.user_id}更新任务ID为{task_id}的状态成功')
        return ResponseUtil.success(data=update_result)
    except Exception as e:
        logger.error(f'更新任务状态失败: {str(e)}')
        return ResponseUtil.error(msg=f'更新任务状态失败: {str(e)}')


@internalOpinionAnalysisController.put('/tasks/{task_id}/sentiment-counts')
async def update_task_sentiment_counts(
    request: Request,
    task_id: int,
    sentiment_data: UpdateSentimentCountsModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新任务情感统计数据（用户级别隔离）
    """
    try:
        # 确保ID一致
        sentiment_data.task_id = task_id
        update_result = await OpinionTaskService.update_sentiment_counts_services(
            query_db,
            task_id,
            sentiment_data.positive_count,
            sentiment_data.negative_count,
            sentiment_data.neutral_count
        )
        logger.info(f'用户ID为{current_user.user.user_id}更新任务ID为{task_id}的情感统计数据成功')
        return ResponseUtil.success(data=update_result)
    except Exception as e:
        logger.error(f'更新任务情感统计数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'更新任务情感统计数据失败: {str(e)}')


@internalOpinionAnalysisController.delete('/tasks/{task_id}')
async def delete_task(
    request: Request,
    task_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除舆情任务（物理删除，用户级别隔离）
    """
    try:
        logger.info(f'接收到删除任务请求 - 用户ID: {current_user.user.user_id}, 任务ID: {task_id}')

        # 验证任务ID的有效性
        if task_id <= 0:
            logger.error(f'无效的任务ID: {task_id}')
            return ResponseUtil.error(msg='无效的任务ID')

        delete_result = await OpinionTaskService.delete_opinion_task_services(
            query_db, [task_id], current_user.user.user_id
        )

        logger.info(f'用户ID为{current_user.user.user_id}成功删除任务ID为{task_id}')
        return ResponseUtil.success(data=delete_result)
    except ServiceException as e:
        logger.error(f'删除舆情任务业务异常: {str(e)}')
        return ResponseUtil.error(msg=str(e))
    except Exception as e:
        logger.error(f'删除舆情任务系统异常: {str(e)}')
        return ResponseUtil.error(msg=f'删除舆情任务失败: {str(e)}')


@internalOpinionAnalysisController.delete('/tasks')
async def batch_delete_tasks(
    request: Request,
    delete_tasks: DeleteOpinionTaskModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    批量删除舆情任务（物理删除，用户级别隔离）
    """
    try:
        logger.info(f'接收到批量删除任务请求 - 用户ID: {current_user.user.user_id}, 任务IDs: {delete_tasks.ids}')

        # 验证任务ID列表的有效性
        if not delete_tasks.ids:
            logger.error('批量删除请求中没有提供任务ID')
            return ResponseUtil.error(msg='请选择要删除的任务')

        # 验证所有任务ID都是有效的正整数
        invalid_ids = [task_id for task_id in delete_tasks.ids if not isinstance(task_id, int) or task_id <= 0]
        if invalid_ids:
            logger.error(f'包含无效的任务ID: {invalid_ids}')
            return ResponseUtil.error(msg=f'包含无效的任务ID: {invalid_ids}')

        delete_result = await OpinionTaskService.delete_opinion_task_services(
            query_db, delete_tasks.ids, current_user.user.user_id
        )

        logger.info(f'用户ID为{current_user.user.user_id}成功批量删除任务，任务IDs: {delete_tasks.ids}')
        return ResponseUtil.success(data=delete_result)
    except ServiceException as e:
        logger.error(f'批量删除舆情任务业务异常: {str(e)}')
        return ResponseUtil.error(msg=str(e))
    except Exception as e:
        logger.error(f'批量删除舆情任务系统异常: {str(e)}')
        return ResponseUtil.error(msg=f'批量删除舆情任务失败: {str(e)}')


@internalOpinionAnalysisController.get('/requirements/{requirement_id}/tasks')
async def get_tasks_by_requirement(
    request: Request,
    requirement_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    根据需求ID获取任务列表（用户级别隔离）
    """
    try:
        tasks_result = await OpinionTaskService.get_tasks_by_requirement_services(
            query_db, requirement_id, current_user.user.user_id
        )
        logger.info(f'用户ID为{current_user.user.user_id}获取需求ID为{requirement_id}的任务列表成功')
        return ResponseUtil.success(data=tasks_result)
    except Exception as e:
        logger.error(f'获取需求任务列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取需求任务列表失败: {str(e)}')


@internalOpinionAnalysisController.get('/tasks/statistics')
async def get_task_statistics(
    request: Request,
    requirement_id: Optional[int] = Query(None),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取任务统计信息
    """
    try:
        statistics_result = await OpinionTaskService.get_task_statistics_services(query_db, requirement_id)
        logger.info('获取任务统计信息成功')
        return ResponseUtil.success(data=statistics_result)
    except Exception as e:
        logger.error(f'获取任务统计信息失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取任务统计信息失败: {str(e)}')


# 数据源管理相关API
@internalOpinionAnalysisController.get('/data-sources')
async def get_data_source_list(
    request: Request,
    data_source_page_query: RequirementDataSourcePageQueryModel = Depends(RequirementDataSourcePageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取需求数据源列表
    """
    try:
        # 自动添加当前用户过滤条件
        data_source_page_query.created_by = str(current_user.user.user_id)

        data_source_list_result = await RequirementDataSourceService.get_requirement_data_source_list_services(
            query_db, data_source_page_query, is_page=True
        )
        logger.info('获取需求数据源列表成功')
        return ResponseUtil.success(model_content=data_source_list_result)
    except Exception as e:
        logger.error(f'获取需求数据源列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取需求数据源列表失败: {str(e)}')


@internalOpinionAnalysisController.get('/data-sources/{data_source_id}')
async def get_data_source_detail(
    request: Request,
    data_source_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取需求数据源详细信息
    """
    try:
        data_source_detail_result = await RequirementDataSourceService.get_requirement_data_source_detail_services(query_db, data_source_id)
        logger.info(f'获取数据源ID为{data_source_id}的详细信息成功')
        return ResponseUtil.success(data=data_source_detail_result)
    except Exception as e:
        logger.error(f'获取数据源详细信息失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取数据源详细信息失败: {str(e)}')


@internalOpinionAnalysisController.post('/data-sources')
async def create_data_source(
    request: Request,
    add_data_source: CreateRequirementDataSourceModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    创建需求数据源
    """
    try:
        add_data_source_result = await RequirementDataSourceService.add_requirement_data_source_services(
            query_db, add_data_source, current_user.user.user_id
        )
        logger.info('创建需求数据源成功')
        return ResponseUtil.success(data=add_data_source_result)
    except Exception as e:
        logger.error(f'创建需求数据源失败: {str(e)}')
        return ResponseUtil.error(msg=f'创建需求数据源失败: {str(e)}')


@internalOpinionAnalysisController.post('/data-sources/batch')
async def batch_create_data_sources(
    request: Request,
    batch_add: BatchCreateDataSourceModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    批量创建数据源
    """
    try:
        batch_add_result = await RequirementDataSourceService.batch_add_data_sources_services(
            query_db, batch_add, current_user.user.user_id
        )
        logger.info('批量创建数据源成功')
        return ResponseUtil.success(data=batch_add_result)
    except Exception as e:
        logger.error(f'批量创建数据源失败: {str(e)}')
        return ResponseUtil.error(msg=f'批量创建数据源失败: {str(e)}')


@internalOpinionAnalysisController.put('/data-sources/{data_source_id}')
async def update_data_source(
    request: Request,
    data_source_id: int,
    edit_data_source: UpdateRequirementDataSourceModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新需求数据源
    """
    try:
        # 确保ID一致
        edit_data_source.id = data_source_id
        edit_data_source_result = await RequirementDataSourceService.edit_requirement_data_source_services(
            query_db, edit_data_source, current_user.user.user_id
        )
        logger.info(f'更新数据源ID为{data_source_id}的信息成功')
        return ResponseUtil.success(data=edit_data_source_result)
    except Exception as e:
        logger.error(f'更新需求数据源失败: {str(e)}')
        return ResponseUtil.error(msg=f'更新需求数据源失败: {str(e)}')


@internalOpinionAnalysisController.delete('/data-sources')
async def delete_data_sources(
    request: Request,
    delete_data_source: DeleteRequirementDataSourceModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除需求数据源
    """
    try:
        delete_result = await RequirementDataSourceService.delete_requirement_data_source_services(query_db, delete_data_source)
        logger.info('删除需求数据源成功')
        return ResponseUtil.success(data=delete_result)
    except Exception as e:
        logger.error(f'删除需求数据源失败: {str(e)}')
        return ResponseUtil.error(msg=f'删除需求数据源失败: {str(e)}')


@internalOpinionAnalysisController.get('/requirements/{requirement_id}/data-sources')
async def get_data_sources_by_requirement(
    request: Request,
    requirement_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    根据需求ID获取数据源列表
    """
    try:
        data_sources_result = await RequirementDataSourceService.get_data_sources_by_requirement_services(query_db, requirement_id)
        logger.info(f'获取需求ID为{requirement_id}的数据源列表成功')
        return ResponseUtil.success(data=data_sources_result)
    except Exception as e:
        logger.error(f'获取需求数据源列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取需求数据源列表失败: {str(e)}')


@internalOpinionAnalysisController.get('/data-sources/statistics')
async def get_data_source_statistics(
    request: Request,
    requirement_id: Optional[int] = Query(None),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取数据源统计信息
    """
    try:
        statistics_result = await RequirementDataSourceService.get_data_source_statistics_services(query_db, requirement_id)
        logger.info('获取数据源统计信息成功')
        return ResponseUtil.success(data=statistics_result)
    except Exception as e:
        logger.error(f'获取数据源统计信息失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取数据源统计信息失败: {str(e)}')


@internalOpinionAnalysisController.get('/data-sources/defaults')
async def get_default_data_sources(
    request: Request,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取默认数据源配置
    """
    try:
        default_sources_result = await RequirementDataSourceService.get_default_data_sources_services()
        logger.info('获取默认数据源配置成功')
        return ResponseUtil.success(data=default_sources_result)
    except Exception as e:
        logger.error(f'获取默认数据源配置失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取默认数据源配置失败: {str(e)}')


# ==================== 联网搜索相关接口 ====================

@publicOpinionAnalysisController.post('/online-search')
async def perform_online_search(
    request: Request,
    search_request: OnlineSearchRequestModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
):
    """
    执行联网搜索（公开接口）
    """
    try:
        search_result = await ExternalApiService.perform_online_search(
            query_db,
            search_request.entity_keyword,
            search_request.specific_requirement,
            search_request.selected_keywords
        )
        logger.info('联网搜索执行成功')
        return ResponseUtil.success(data=search_result)
    except ServiceException as e:
        logger.error(f'联网搜索失败: {str(e)}')
        return ResponseUtil.error(msg=str(e))
    except Exception as e:
        logger.error(f'联网搜索失败: {str(e)}')
        return ResponseUtil.error(msg=f'联网搜索失败: {str(e)}')


@publicOpinionAnalysisController.post('/start-analysis')
async def start_analysis(
    request: Request,
    analysis_request: AnalysisStartRequestModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
):
    """
    开始分析（公开接口）- 保持原有同步行为以兼容现有前端
    """
    try:
        logger.info(f'开始分析，需求ID: {analysis_request.requirement_id}')

        analysis_results = {}

        # 检查是否需要执行联网搜索
        if analysis_request.enable_online_search:
            logger.info('执行联网搜索...')
            online_search_result = await ExternalApiService.perform_online_search(
                query_db,
                analysis_request.entity_keyword,
                analysis_request.specific_requirement,
                analysis_request.selected_keywords
            )
            analysis_results['online_search'] = online_search_result

        # 检查是否需要执行自定义数据源搜索
        if analysis_request.enable_custom_data_source:
            logger.info('执行自定义数据源搜索...')
            from module_opinion.service.custom_data_source_service import CustomDataSourceService

            custom_search_result = await CustomDataSourceService.crawl_custom_data_sources(
                query_db,
                analysis_request.requirement_id,
                analysis_request.entity_keyword,
                analysis_request.specific_requirement,
                analysis_request.selected_keywords
            )
            analysis_results['custom_data_source'] = custom_search_result

        # 验证至少执行了一种搜索
        if not analysis_results:
            logger.warning('没有执行任何搜索操作')
            return ResponseUtil.failure(msg='请至少选择一种数据源类型')

        # 返回分析结果
        result = {
            'requirement_id': analysis_request.requirement_id,
            'analysis_results': analysis_results,
            'status': 'completed',
            'message': '分析完成'
        }

        logger.info('分析执行成功')
        return ResponseUtil.success(data=result)

    except ServiceException as e:
        logger.error(f'分析执行失败: {str(e)}')
        return ResponseUtil.error(msg=str(e))
    except Exception as e:
        logger.error(f'分析执行失败: {str(e)}')
        return ResponseUtil.error(msg=f'分析执行失败: {str(e)}')


# ==================== 报告推送相关接口 ====================

@publicOpinionAnalysisController.post('/push-report')
async def push_report(
    request: Request,
    push_request: PushReportRequestModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
):
    """
    推送报告到目标URL（公开接口）
    """
    try:
        logger.info(f'开始推送报告到: {push_request.target_url}')

        # 调用推送服务
        push_result = await PushReportService.push_report(
            query_db,
            push_request,
            user_id=None  # 公开接口暂不传用户ID
        )

        if push_result.success:
            logger.info(f'报告推送成功，推送ID: {push_result.push_id}')
            return ResponseUtil.success(data=push_result.model_dump())
        else:
            logger.error(f'报告推送失败: {push_result.error_details}')
            return ResponseUtil.error(msg=push_result.message, data=push_result.model_dump())

    except Exception as e:
        logger.error(f'推送报告接口异常: {str(e)}')
        return ResponseUtil.error(msg=f'推送报告失败: {str(e)}')


@publicOpinionAnalysisController.get('/tasks/statistics')
async def get_public_task_statistics(
    request: Request,
    requirement_id: Optional[int] = Query(None),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取任务统计信息（公开接口）
    """
    try:
        statistics_result = await OpinionTaskService.get_task_statistics_services(query_db, requirement_id)
        logger.info('获取任务统计信息成功（公开接口）')
        return ResponseUtil.success(data=statistics_result)
    except Exception as e:
        logger.error(f'获取任务统计信息失败（公开接口）: {str(e)}')
        return ResponseUtil.error(msg=f'获取任务统计信息失败: {str(e)}')


@publicOpinionAnalysisController.get('/data-sources')
async def get_public_data_source_list(
    request: Request,
    data_source_page_query: RequirementDataSourcePageQueryModel = Depends(RequirementDataSourcePageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取需求数据源列表（公开接口）
    """
    try:
        # 自动添加当前用户过滤条件
        data_source_page_query.created_by = str(current_user.user.user_id)

        data_source_list_result = await RequirementDataSourceService.get_requirement_data_source_list_services(
            query_db, data_source_page_query, is_page=True
        )
        logger.info('获取需求数据源列表成功（公开接口）')
        return ResponseUtil.success(model_content=data_source_list_result)
    except Exception as e:
        logger.error(f'获取需求数据源列表失败（公开接口）: {str(e)}')
        return ResponseUtil.error(msg=f'获取需求数据源列表失败: {str(e)}')


@publicOpinionAnalysisController.delete('/data-sources/{data_source_id}')
async def delete_public_data_source(
    request: Request,
    data_source_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    删除需求数据源（公开接口）
    """
    try:
        # 创建删除模型
        delete_model = DeleteRequirementDataSourceModel(ids=[data_source_id])
        delete_result = await RequirementDataSourceService.delete_requirement_data_source_services(query_db, delete_model)
        logger.info(f'删除需求数据源成功（公开接口）: {data_source_id}')
        return ResponseUtil.success(data=delete_result)
    except Exception as e:
        logger.error(f'删除需求数据源失败（公开接口）: {str(e)}')
        return ResponseUtil.error(msg=f'删除需求数据源失败: {str(e)}')


@publicOpinionAnalysisController.get('/data-sources/statistics')
async def get_public_data_source_statistics(
    request: Request,
    requirement_id: Optional[int] = Query(None),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取数据源统计信息（公开接口）
    """
    try:
        statistics_result = await RequirementDataSourceService.get_data_source_statistics_services(query_db, requirement_id)
        logger.info('获取数据源统计信息成功（公开接口）')
        return ResponseUtil.success(data=statistics_result)
    except Exception as e:
        logger.error(f'获取数据源统计信息失败（公开接口）: {str(e)}')
        return ResponseUtil.error(msg=f'获取数据源统计信息失败: {str(e)}')


@publicOpinionAnalysisController.get('/report/{requirement_id}')
async def get_public_report_data(
    request: Request,
    requirement_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取公开报告数据（用于推送链接访问）
    """
    try:
        # 获取需求信息
        requirement = await OpinionRequirementService.get_requirement_by_id_services(query_db, requirement_id)
        if not requirement:
            return ResponseUtil.failure(msg='需求不存在')

        # 获取相关的分析结果数据
        # 这里可以从缓存或数据库中获取最近的分析结果
        # 暂时返回基础的需求信息，后续可以扩展

        report_data = {
            'requirement_id': requirement_id,
            'requirement_name': requirement.requirement_name,
            'entity_keyword': requirement.entity_keyword,
            'specific_requirement': requirement.specific_requirement,
            'analysis_results': {
                'total_articles': 0,
                'sentiment': {
                    'positive': 0,
                    'neutral': 0,
                    'negative': 0
                },
                'online_search': [],
                'custom_sources': []
            }
        }

        logger.info(f'获取公开报告数据成功，需求ID: {requirement_id}')
        return ResponseUtil.success(data=report_data)
    except Exception as e:
        logger.error(f'获取公开报告数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取公开报告数据失败: {str(e)}')


@publicOpinionAnalysisController.delete('/requirements')
async def delete_requirements_public(
    request: Request,
    delete_requirement: DeleteOpinionRequirementModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
):
    """
    删除舆情需求（公开接口）
    """
    try:
        delete_requirement_result = await OpinionRequirementService.delete_opinion_requirement_services(
            query_db, delete_requirement
        )
        logger.info('删除舆情需求成功')
        return ResponseUtil.success(data=delete_requirement_result)
    except Exception as e:
        logger.error(f'删除舆情需求失败: {str(e)}')
        return ResponseUtil.error(msg=f'删除舆情需求失败: {str(e)}')


@publicOpinionAnalysisController.get('/requirements/export')
async def export_requirements_public(
    request: Request,
    requirement_name: Optional[str] = Query(default=None, description='需求名称'),
    entity_keyword: Optional[str] = Query(default=None, description='实体关键词'),
    analysis_status: Optional[int] = Query(default=None, description='分析状态'),
    create_time_start: Optional[str] = Query(default=None, description='创建时间开始'),
    create_time_end: Optional[str] = Query(default=None, description='创建时间结束'),
    query_db: AsyncSession = Depends(get_db),
):
    """
    导出舆情需求列表（公开接口）
    """
    try:
        from fastapi.responses import StreamingResponse
        import io
        import csv
        from sqlalchemy import select
        from module_opinion.entity.do.opinion_requirement_do import OpinionRequirement

        # 构建查询条件
        query = select(OpinionRequirement).where(OpinionRequirement.is_deleted == 0)

        if requirement_name:
            query = query.where(OpinionRequirement.requirement_name.like(f'%{requirement_name}%'))

        if entity_keyword:
            query = query.where(OpinionRequirement.entity_keyword.like(f'%{entity_keyword}%'))

        if analysis_status is not None:
            query = query.where(OpinionRequirement.analysis_status == analysis_status)

        if create_time_start:
            query = query.where(OpinionRequirement.create_time >= create_time_start)

        if create_time_end:
            query = query.where(OpinionRequirement.create_time <= create_time_end)

        # 添加排序
        query = query.order_by(OpinionRequirement.create_time.desc())

        # 执行查询
        result = await query_db.execute(query)
        requirements = result.scalars().all()

        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)

        # 写入表头
        writer.writerow(['ID', '需求名称', '关键词', '具体需求', '分析状态', '创建时间'])

        # 写入数据
        for req in requirements:
            status_map = {0: '未开始', 1: '分析中', 2: '已完成', 3: '失败'}

            writer.writerow([
                req.id,
                req.requirement_name,
                req.entity_keyword,
                req.specific_requirement,
                status_map.get(req.analysis_status, '未知'),
                req.create_time.strftime('%Y-%m-%d %H:%M:%S') if req.create_time else ''
            ])

        # 准备响应
        output.seek(0)

        def iter_csv():
            yield output.getvalue().encode('utf-8-sig')  # 使用utf-8-sig确保Excel正确显示中文

        logger.info(f'导出舆情需求列表成功，导出数量: {len(requirements)}')

        return StreamingResponse(
            iter_csv(),
            media_type='text/csv',
            headers={'Content-Disposition': 'attachment; filename="analysis_records.csv"'}
        )
    except Exception as e:
        logger.error(f'导出舆情需求列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'导出舆情需求列表失败: {str(e)}')


@internalOpinionAnalysisController.post('/push-report')
async def push_report_internal(
    request: Request,
    push_request: PushReportRequestModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    推送报告到目标URL（内部接口，需要认证）
    """
    try:
        logger.info(f'用户{current_user.user.user_id}开始推送报告到: {push_request.target_url}')

        # 调用推送服务
        push_result = await PushReportService.push_report(
            query_db,
            push_request,
            user_id=current_user.user.user_id
        )

        if push_result.success:
            logger.info(f'报告推送成功，推送ID: {push_result.push_id}')
            return ResponseUtil.success(data=push_result.model_dump())
        else:
            logger.error(f'报告推送失败: {push_result.error_details}')
            return ResponseUtil.error(msg=push_result.message, data=push_result.model_dump())

    except Exception as e:
        logger.error(f'推送报告接口异常: {str(e)}')
        return ResponseUtil.error(msg=f'推送报告失败: {str(e)}')


# ==================== 分析进度相关接口 ====================

@publicOpinionAnalysisController.post('/analysis-progress/create-task')
async def create_analysis_task(
    request: Request,
    task_data: CreateAnalysisTaskModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
):
    """
    创建分析任务（公开接口）
    """
    try:
        task_id = await AnalysisProgressService.create_analysis_task(query_db, task_data)
        logger.info(f'创建分析任务成功，任务ID: {task_id}')
        return ResponseUtil.success(data={'task_id': task_id})
    except Exception as e:
        logger.error(f'创建分析任务失败: {str(e)}')
        return ResponseUtil.error(msg=f'创建分析任务失败: {str(e)}')


@publicOpinionAnalysisController.post('/analysis-progress/add-log')
async def add_progress_log(
    request: Request,
    log_data: CreateProgressLogModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
):
    """
    添加进度日志（公开接口）
    """
    try:
        log = await AnalysisProgressService.add_progress_log(query_db, log_data)
        logger.debug(f'添加进度日志成功: {log_data.task_id}')
        return ResponseUtil.success(data=log.model_dump())
    except Exception as e:
        logger.error(f'添加进度日志失败: {str(e)}')
        return ResponseUtil.error(msg=f'添加进度日志失败: {str(e)}')


@publicOpinionAnalysisController.get('/analysis-progress/{task_id}')
async def get_analysis_progress(
    request: Request,
    task_id: str,
    page_num: int = Query(default=1, description='页码'),
    page_size: int = Query(default=50, description='每页大小'),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取分析进度（公开接口）
    """
    try:
        progress = await AnalysisProgressService.get_analysis_progress(
            query_db, task_id, page_num, page_size
        )
        logger.info(f'获取分析进度成功: {task_id}')
        return ResponseUtil.success(data=progress.model_dump())
    except Exception as e:
        logger.error(f'获取分析进度失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取分析进度失败: {str(e)}')


@publicOpinionAnalysisController.post('/analysis-progress/{task_id}/complete')
async def complete_analysis_task(
    request: Request,
    task_id: str,
    result_summary: dict = Body(default=None),
    query_db: AsyncSession = Depends(get_db),
):
    """
    完成分析任务（公开接口）
    """
    try:
        success = await AnalysisProgressService.complete_analysis_task(
            query_db, task_id, result_summary
        )
        if success:
            logger.info(f'完成分析任务成功: {task_id}')
            return ResponseUtil.success(data={'task_id': task_id, 'status': 'completed'})
        else:
            return ResponseUtil.error(msg='任务不存在或状态异常')
    except Exception as e:
        logger.error(f'完成分析任务失败: {str(e)}')
        return ResponseUtil.error(msg=f'完成分析任务失败: {str(e)}')


@publicOpinionAnalysisController.post('/analysis-progress/{task_id}/cancel')
async def cancel_analysis_task(
    request: Request,
    task_id: str,
    query_db: AsyncSession = Depends(get_db),
):
    """
    取消分析任务（公开接口）
    """
    try:
        success = await AnalysisProgressService.cancel_analysis_task(query_db, task_id)
        if success:
            logger.info(f'取消分析任务成功: {task_id}')
            return ResponseUtil.success(data={'task_id': task_id, 'status': 'cancelled'})
        else:
            return ResponseUtil.error(msg='任务不存在或状态异常')
    except Exception as e:
        logger.error(f'取消分析任务失败: {str(e)}')
        return ResponseUtil.error(msg=f'取消分析任务失败: {str(e)}')


@publicOpinionAnalysisController.post('/generate-report')
async def generate_and_upload_report(
    request: Request,
    report_request: dict = Body(...),
    query_db: AsyncSession = Depends(get_db),
):
    """
    生成分析报告并上传到OSS（公开接口）
    """
    try:
        logger.info(f'开始生成报告并上传到OSS，需求ID: {report_request.get("requirement_id")}')

        # 调用报告生成服务
        from module_opinion.service.report_html_generator_service import ReportHtmlGeneratorService

        requirement_name = report_request.get('requirement_name', '舆情分析报告')
        entity_keyword = report_request.get('entity_keyword', '')

        # 生成报告HTML并上传到OSS
        page_id, report_oss_url = await ReportHtmlGeneratorService.generate_report_html(
            report_request.get('report_data', {}),
            report_request.get('analysis_results', {}),
            requirement_name,
            entity_keyword
        )

        # 如果有需求ID，更新数据库中的报告URL
        requirement_id = report_request.get('requirement_id')
        if requirement_id and report_oss_url.startswith('https://'):
            try:
                # 查找与该需求相关的任务并更新OSS URL
                from module_opinion.service.opinion_task_service import OpinionTaskService
                tasks = await OpinionTaskService.get_tasks_by_requirement_services(query_db, requirement_id)

                if tasks and len(tasks) > 0:
                    # 更新最新的任务记录
                    task_id = tasks[0].get('id')
                    if task_id:
                        await OpinionTaskService.save_report_oss_url_services(
                            query_db, task_id, report_oss_url
                        )
                        logger.info(f'报告OSS URL已保存到任务 {task_id}: {report_oss_url}')
            except Exception as db_error:
                logger.error(f'保存报告OSS URL到数据库失败: {str(db_error)}')
                # 不影响主流程，继续返回结果

        logger.info(f'报告生成并上传OSS成功: {page_id} -> {report_oss_url}')
        return ResponseUtil.success(data={
            'page_id': page_id,
            'report_oss_url': report_oss_url,
            'message': '报告生成并上传成功'
        })
    except Exception as e:
        logger.error(f'生成报告并上传到OSS失败: {str(e)}')
        return ResponseUtil.error(msg=f'生成报告并上传失败: {str(e)}')


@publicOpinionAnalysisController.put('/tasks/{task_id}/sentiment-counts')
async def update_public_task_sentiment_counts(
    request: Request,
    task_id: int,
    sentiment_data: UpdateSentimentCountsModel = Body(...),
    query_db: AsyncSession = Depends(get_db),
):
    """
    更新任务情感统计数据（公开接口）
    """
    try:
        # 确保ID一致
        sentiment_data.task_id = task_id
        update_result = await OpinionTaskService.update_sentiment_counts_services(
            query_db,
            task_id,
            sentiment_data.positive_count,
            sentiment_data.negative_count,
            sentiment_data.neutral_count
        )
        logger.info(f'更新任务ID为{task_id}的情感统计数据成功（公开接口）')
        return ResponseUtil.success(data=update_result)
    except Exception as e:
        logger.error(f'更新任务情感统计数据失败（公开接口）: {str(e)}')
        return ResponseUtil.error(msg=f'更新任务情感统计数据失败: {str(e)}')
