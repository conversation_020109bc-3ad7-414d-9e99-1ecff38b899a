{"name": "vfadmin", "version": "1.6.2", "description": "金刚舆情智库系统", "author": "insistence", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/insistence2022/RuoYi-Vue-FastAPI.git"}, "dependencies": {"@antv/g2plot": "^2.4.31", "@riophae/vue-treeselect": "0.4.0", "ant-design-vue": "^1.7.8", "axios": "0.28.1", "clipboard": "2.0.8", "core-js": "3.37.1", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "element-ui": "2.15.14", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "nprogress": "0.2.0", "quill": "2.0.2", "screenfull": "5.0.2", "sortablejs": "1.10.2", "splitpanes": "2.4.1", "viser-vue": "^2.4.8", "vue": "^2.7.16", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-echarts": "^7.0.3", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-import": "^1.13.8", "chalk": "4.1.0", "compression-webpack-plugin": "6.1.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "less": "^3.13.1", "less-loader": "^5.0.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "^2.7.16", "webpack-theme-color-replacer": "^1.3.26"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}