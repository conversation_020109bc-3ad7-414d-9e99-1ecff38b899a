import request from '@/utils/request'

// 获取信源人员列表
export function getSourcePersonnelList(params) {
  return request({
    url: '/api/source-monitoring/personnel/list',
    method: 'get',
    params
  })
}

// 获取信源媒体列表
export function getSourceMediaList(params) {
  return request({
    url: '/api/source-monitoring/media/list',
    method: 'get',
    params
  })
}

// 获取媒体监控列表
export function getMediaMonitoringList(params) {
  return request({
    url: '/api/source-monitoring/monitoring/list',
    method: 'get',
    params
  })
}

// 获取媒体反馈列表
export function getMediaFeedbackList(params) {
  return request({
    url: '/api/source-monitoring/feedback/list',
    method: 'get',
    params
  })
}

// 获取监控统计数据
export function getMonitoringStatistics() {
  return request({
    url: '/api/source-monitoring/statistics',
    method: 'get'
  })
}

// 健康检查
export function healthCheck() {
  return request({
    url: '/api/source-monitoring/health',
    method: 'get'
  })
}

// 简化接口 - 兼容现有前端
export function getSimpleSourcePersonnelList() {
  return request({
    url: '/api/source-monitoring/simple/personnel',
    method: 'get'
  })
}

export function getSimpleSourceMediaList() {
  return request({
    url: '/api/source-monitoring/simple/media',
    method: 'get'
  })
}

export function getSimpleMediaMonitoringList() {
  return request({
    url: '/api/source-monitoring/simple/monitoring',
    method: 'get'
  })
}

export function getSimpleMediaFeedbackList() {
  return request({
    url: '/api/source-monitoring/simple/feedback',
    method: 'get'
  })
}
