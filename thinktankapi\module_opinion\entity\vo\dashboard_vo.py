from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict


class RecentAnalysisRecordModel(BaseModel):
    """
    最新分析记录模型
    """
    model_config = ConfigDict(from_attributes=True)

    id: Optional[int] = Field(default=None, description='分析任务ID')
    requirement_name: Optional[str] = Field(default=None, description='需求名称')
    status: Optional[str] = Field(default=None, description='任务状态：pending, running, completed, failed, cancelled')
    entity_keyword: Optional[str] = Field(default=None, description='实体关键词')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    positive_count: Optional[int] = Field(default=0, description='正面情感数量')
    neutral_count: Optional[int] = Field(default=0, description='中性情感数量')
    negative_count: Optional[int] = Field(default=0, description='负面情感数量')
    task_name: Optional[str] = Field(default=None, description='任务名称')
    progress_percentage: Optional[int] = Field(default=0, description='进度百分比')
    report_oss_url: Optional[str] = Field(default=None, description='报告OSS访问URL')


class OpinionTrendDataModel(BaseModel):
    """
    舆情趋势数据模型
    """
    date: str = Field(..., description='日期，格式：YYYY-MM-DD')
    positive_count: int = Field(default=0, description='正面情感数量')
    neutral_count: int = Field(default=0, description='中性情感数量')
    negative_count: int = Field(default=0, description='负面情感数量')
    total_count: int = Field(default=0, description='总数量')


class OpinionTrendResponseModel(BaseModel):
    """
    舆情趋势响应模型
    """
    trend_data: List[OpinionTrendDataModel] = Field(default_factory=list, description='趋势数据列表')
    date_labels: List[str] = Field(default_factory=list, description='日期标签列表')
    positive_series: List[int] = Field(default_factory=list, description='正面情感数据系列')
    neutral_series: List[int] = Field(default_factory=list, description='中性情感数据系列')
    negative_series: List[int] = Field(default_factory=list, description='负面情感数据系列')


class RecentAnalysisRecordsResponseModel(BaseModel):
    """
    最新分析记录响应模型
    """
    model_config = ConfigDict(from_attributes=True)

    records: List[RecentAnalysisRecordModel] = Field(default=[], description='最新分析记录列表')
    total_count: int = Field(default=0, description='总记录数')


class DashboardStatisticsModel(BaseModel):
    """
    Dashboard统计数据模型
    """
    model_config = ConfigDict(from_attributes=True)

    today_tasks: int = Field(default=0, description='今日分析任务数量')
    tasks_growth: float = Field(default=0.0, description='今日任务相比昨日的增长百分比')
    negative_count: int = Field(default=0, description='负面舆情监测数量')
    negative_change: float = Field(default=0.0, description='负面舆情相比昨日的变化百分比')
    completed_count: int = Field(default=0, description='已完成任务数量')
    completed_change: float = Field(default=0.0, description='已完成任务相比昨日的变化百分比')
    pending_count: int = Field(default=0, description='待完成任务数量')
    pending_change: float = Field(default=0.0, description='待完成任务相比昨日的变化百分比')
    total_count: int = Field(default=0, description='总任务数量')
    running_count: int = Field(default=0, description='执行中任务数量')
    failed_count: int = Field(default=0, description='失败任务数量')

    # 新增用户统计字段
    total_analysis: int = Field(default=0, description='用户总分析次数')
    today_analysis: int = Field(default=0, description='用户今日分析次数')
    remaining_count: int = Field(default=0, description='用户剩余分析次数')
    today_remaining: int = Field(default=0, description='用户今日剩余次数')

    # 新增会员相关字段
    package_name: str = Field(default='', description='当前套餐名称')
    package_limit: int = Field(default=0, description='套餐分析次数限制')
    usage_percentage: float = Field(default=0.0, description='使用百分比')
