from datetime import datetime, time
from sqlalchemy import and_, delete, desc, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_warning.entity.do.warning_record_do import WarningRecord
from module_warning.entity.vo.warning_vo import WarningRecordModel, WarningRecordPageQueryModel
from utils.page_util import PageUtil


class WarningRecordDao:
    """
    预警记录管理模块数据库操作层
    """

    @classmethod
    async def get_warning_record_by_id(cls, db: AsyncSession, record_id: int):
        """
        根据预警记录id获取预警记录详细信息

        :param db: orm对象
        :param record_id: 预警记录id
        :return: 预警记录信息对象
        """
        record_info = (await db.execute(select(WarningRecord).where(WarningRecord.id == record_id))).scalars().first()

        return record_info

    @classmethod
    async def get_warning_record_list(cls, db: AsyncSession, query_object: WarningRecordPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取预警记录列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 预警记录列表信息对象
        """
        query = select(WarningRecord)
        
        # 构建查询条件
        conditions = []

        # 添加调试日志
        from utils.log_util import logger
        logger.info(f'DAO层查询条件构建 - 方案ID: {query_object.scheme_id}, 记录ID: {query_object.id}, 搜索文本: {query_object.search_text}, 状态筛选: {query_object.status}')

        # ID精确匹配查询（优先级最高）
        if query_object.id:
            conditions.append(WarningRecord.id == query_object.id)
            logger.info(f'添加ID筛选条件: {query_object.id}')

        # 方案ID筛选（重要：确保方案筛选正常工作）
        if query_object.scheme_id:
            conditions.append(WarningRecord.scheme_id == query_object.scheme_id)
            logger.info(f'添加方案ID筛选条件: {query_object.scheme_id}')

        if query_object.warning_type:
            conditions.append(WarningRecord.warning_type == query_object.warning_type)
        if query_object.status is not None:
            conditions.append(WarningRecord.status == query_object.status)
            logger.info(f'添加状态筛选条件: {query_object.status}')
        if query_object.search_text:
            # 增强搜索逻辑：支持ID、内容和关键词搜索
            search_conditions = [
                WarningRecord.content.like(f'%{query_object.search_text}%'),
                WarningRecord.keywords.like(f'%{query_object.search_text}%')
            ]

            # 如果搜索文本是纯数字，也尝试匹配ID
            if query_object.search_text.isdigit():
                search_conditions.append(WarningRecord.id == int(query_object.search_text))

            conditions.append(or_(*search_conditions))
        if query_object.begin_time:
            begin_time = datetime.strptime(query_object.begin_time, '%Y-%m-%d')
            begin_time = datetime.combine(begin_time.date(), time.min)
            conditions.append(WarningRecord.create_time >= begin_time)
        if query_object.end_time:
            end_time = datetime.strptime(query_object.end_time, '%Y-%m-%d')
            end_time = datetime.combine(end_time.date(), time.max)
            conditions.append(WarningRecord.create_time <= end_time)

        if conditions:
            query = query.where(and_(*conditions))

        # 排序
        query = query.order_by(desc(WarningRecord.create_time))

        if is_page:
            # 分页查询
            return await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size)
        else:
            # 不分页查询
            result = await db.execute(query)
            return result.scalars().all()

    @classmethod
    async def add_warning_record_dao(cls, db: AsyncSession, record: WarningRecordModel):
        """
        新增预警记录数据库操作

        :param db: orm对象
        :param record: 预警记录对象
        :return:
        """
        db_record = WarningRecord(**record.model_dump(exclude_unset=True))
        db.add(db_record)
        await db.flush()

        return db_record

    @classmethod
    async def edit_warning_record_dao(cls, db: AsyncSession, record: dict):
        """
        编辑预警记录数据库操作

        :param db: orm对象
        :param record: 需要更新的预警记录字典
        :return:
        """
        from utils.log_util import logger

        record_id = record.get('id')  # 获取ID用于WHERE条件
        if not record_id:
            raise ValueError("记录ID不能为空")

        # 创建更新数据的副本，排除ID字段
        update_data = {k: v for k, v in record.items() if k != 'id'}

        logger.info(f'DAO层更新数据: record_id={record_id}, update_data={update_data}')

        try:
            result = await db.execute(
                update(WarningRecord)
                .where(WarningRecord.id == record_id)
                .values(**update_data)
            )
            logger.info(f'数据库更新结果: 影响行数={result.rowcount}')

            if result.rowcount == 0:
                raise ValueError(f"没有找到ID为{record_id}的记录进行更新")

        except Exception as e:
            logger.error(f'数据库更新操作失败: {type(e).__name__}: {str(e)}')
            raise

    @classmethod
    async def delete_warning_record_dao(cls, db: AsyncSession, record_ids: list):
        """
        删除预警记录数据库操作

        :param db: orm对象
        :param record_ids: 预警记录ID列表
        :return:
        """
        await db.execute(delete(WarningRecord).where(WarningRecord.id.in_(record_ids)))

    @classmethod
    async def delete_warning_record_by_scheme_id(cls, db: AsyncSession, scheme_id: int):
        """
        根据方案ID删除预警记录数据库操作

        :param db: orm对象
        :param scheme_id: 预警方案ID
        :return:
        """
        await db.execute(delete(WarningRecord).where(WarningRecord.scheme_id == scheme_id))

    @classmethod
    async def get_warning_statistics(cls, db: AsyncSession, scheme_id: int = None):
        """
        获取预警统计数据

        :param db: orm对象
        :param scheme_id: 方案ID，可选
        :return: 统计数据
        """
        from utils.log_util import logger

        # 构建基础WHERE条件
        base_conditions = []
        if scheme_id:
            base_conditions.append(WarningRecord.scheme_id == scheme_id)

        # 总数量 - 直接查询，避免子查询
        total_query = select(func.count(WarningRecord.id))
        if base_conditions:
            total_query = total_query.where(and_(*base_conditions))
        total_result = await db.execute(total_query)
        total_count = total_result.scalar() or 0

        # 待处理数量 (status = 0)
        pending_conditions = base_conditions + [WarningRecord.status == 0]
        pending_query = select(func.count(WarningRecord.id))
        if pending_conditions:
            pending_query = pending_query.where(and_(*pending_conditions))
        pending_result = await db.execute(pending_query)
        pending_count = pending_result.scalar() or 0

        # 已处理数量 (status = 1)
        processed_conditions = base_conditions + [WarningRecord.status == 1]
        processed_query = select(func.count(WarningRecord.id))
        if processed_conditions:
            processed_query = processed_query.where(and_(*processed_conditions))
        processed_result = await db.execute(processed_query)
        processed_count = processed_result.scalar() or 0

        # 紧急数量 (status = 2)
        urgent_conditions = base_conditions + [WarningRecord.status == 2]
        urgent_query = select(func.count(WarningRecord.id))
        if urgent_conditions:
            urgent_query = urgent_query.where(and_(*urgent_conditions))
        urgent_result = await db.execute(urgent_query)
        urgent_count = urgent_result.scalar() or 0

        # 负面预警数量 (warning_type = 'negative')
        negative_conditions = base_conditions + [WarningRecord.warning_type == 'negative']
        negative_query = select(func.count(WarningRecord.id))
        if negative_conditions:
            negative_query = negative_query.where(and_(*negative_conditions))
        negative_result = await db.execute(negative_query)
        negative_count = negative_result.scalar() or 0

        # 正面预警数量 (warning_type = 'positive')
        positive_conditions = base_conditions + [WarningRecord.warning_type == 'positive']
        positive_query = select(func.count(WarningRecord.id))
        if positive_conditions:
            positive_query = positive_query.where(and_(*positive_conditions))
        positive_result = await db.execute(positive_query)
        positive_count = positive_result.scalar() or 0

        # 记录统计结果用于调试
        logger.info(f'预警统计数据 - 方案ID: {scheme_id}, 总计: {total_count}, 待处理: {pending_count}, '
                   f'已处理: {processed_count}, 紧急: {urgent_count}, 负面: {negative_count}, 正面: {positive_count}')

        return {
            'total_count': total_count,
            'pending_count': pending_count,
            'processed_count': processed_count,
            'urgent_count': urgent_count,  # 修复：使用实际查询结果
            'negative_count': negative_count,
            'positive_count': positive_count
        }

    @classmethod
    async def update_warning_record_status(cls, db: AsyncSession, record_id: int, status: int, update_by: str):
        """
        更新预警记录状态

        :param db: orm对象
        :param record_id: 预警记录ID
        :param status: 新状态
        :param update_by: 更新者
        :return:
        """
        await db.execute(
            update(WarningRecord)
            .where(WarningRecord.id == record_id)
            .values(status=status, update_by=update_by, update_time=datetime.now())
        )
