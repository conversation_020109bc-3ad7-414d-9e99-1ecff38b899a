import request from '@/utils/request'

// Dashboard API接口

/**
 * 获取最新分析记录
 * @param {number} limit 返回记录数量，默认3条，最大10条
 * @param {number} user_id 用户ID，用于过滤当前用户的分析记录
 * @returns {Promise}
 */
export function getRecentAnalysisRecords(limit = 3, user_id = null) {
  const params = {
    limit: limit
  }

  // 如果提供了用户ID，添加到参数中
  if (user_id) {
    params.user_id = user_id
  }

  return request({
    url: '/api/dashboard/recent-analysis',
    method: 'get',
    params: params
  })
}

/**
 * 获取最新分析记录（内部接口，需要认证）
 * @param {number} limit 返回记录数量，默认3条，最大10条
 * @param {number} user_id 用户ID，用于过滤当前用户的分析记录
 * @returns {Promise}
 */
export function getInternalRecentAnalysisRecords(limit = 3, user_id = null) {
  const params = {
    limit: limit
  }

  // 如果提供了用户ID，添加到参数中
  if (user_id) {
    params.user_id = user_id
  }

  return request({
    url: '/system/dashboard/recent-analysis',
    method: 'get',
    params: params
  })
}

/**
 * 获取Dashboard统计数据
 * @param {number} user_id 用户ID，用于过滤当前用户的统计数据
 * @returns {Promise}
 */
export function getDashboardStatistics(user_id = null) {
  const params = {}

  // 如果提供了用户ID，添加到参数中
  if (user_id) {
    params.user_id = user_id
  }

  return request({
    url: '/api/dashboard/statistics',
    method: 'get',
    params: params
  })
}

/**
 * 获取舆情分析趋势数据
 * @param {number} days 天数，默认7天，最大30天
 * @param {number} user_id 用户ID，用于过滤当前用户的趋势数据
 * @returns {Promise}
 */
export function getOpinionTrendData(days = 7, user_id = null) {
  const params = {
    days: days
  }

  // 如果提供了用户ID，添加到参数中
  if (user_id) {
    params.user_id = user_id
  }

  return request({
    url: '/api/dashboard/opinion-trend',
    method: 'get',
    params: params
  })
}

/**
 * 获取Dashboard统计数据（内部接口，需要认证）
 * @returns {Promise}
 */
export function getInternalDashboardStatistics() {
  return request({
    url: '/system/dashboard/statistics',
    method: 'get'
  })
}

/**
 * 获取用户Dashboard信息（用户信息和会员信息）
 * @param {number} user_id 用户ID
 * @returns {Promise}
 */
export function getUserDashboardInfo(user_id = null) {
  const params = {}

  // 如果提供了用户ID，添加到参数中
  if (user_id) {
    params.user_id = user_id
  }

  return request({
    url: '/api/dashboard/user-info',
    method: 'get',
    params: params
  })
}

/**
 * 获取用户当前月份的分析记录数量（备用统计）
 * @param {number} user_id 用户ID
 * @returns {Promise}
 */
export function getCurrentMonthAnalysisCount(user_id = null) {
  const params = {}

  // 如果提供了用户ID，添加到参数中
  if (user_id) {
    params.user_id = user_id
  }

  return request({
    url: '/api/dashboard/current-month-analysis-count',
    method: 'get',
    params: params
  })
}
