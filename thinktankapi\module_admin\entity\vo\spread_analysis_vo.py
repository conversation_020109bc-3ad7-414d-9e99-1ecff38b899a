from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class SummaryCardModel(BaseModel):
    """
    汇总卡片模型
    """
    title: str = Field(..., description="卡片标题")
    value: str = Field(..., description="卡片数值")
    color: str = Field(..., description="卡片颜色")


class KeywordCloudModel(BaseModel):
    """
    关键词云模型
    """
    name: str = Field(..., description="关键词名称")
    value: int = Field(..., description="关键词权重")


class PlatformDistributionModel(BaseModel):
    """
    平台分布模型
    """
    name: str = Field(..., description="平台名称")
    value: float = Field(..., description="占比值")
    count: int = Field(default=0, description="数量")


class MediaTypeDistributionModel(BaseModel):
    """
    媒体类型分布模型
    """
    name: str = Field(..., description="媒体类型名称")
    value: int = Field(..., description="数量")


class SentimentDistributionModel(BaseModel):
    """
    情感分布模型
    """
    sentiment: str = Field(..., description="情感类型")
    count: int = Field(..., description="数量")
    percentage: float = Field(..., description="百分比")


class TrendDataModel(BaseModel):
    """
    趋势数据模型
    """
    date: str = Field(..., description="日期")
    count: int = Field(..., description="数量")
    positive: Optional[int] = Field(None, description="正面数量")
    negative: Optional[int] = Field(None, description="负面数量")
    neutral: Optional[int] = Field(None, description="中性数量")


class ComprehensiveSpreadAnalysisModel(BaseModel):
    """
    综合传播分析响应模型
    """
    summary_cards: List[SummaryCardModel] = Field(default=[], description="汇总卡片")
    keywords_cloud: List[KeywordCloudModel] = Field(default=[], description="关键词云")
    platform_distribution: List[PlatformDistributionModel] = Field(default=[], description="平台分布")
    media_type_distribution: List[MediaTypeDistributionModel] = Field(default=[], description="媒体类型分布")
    media_volume_distribution: List[PlatformDistributionModel] = Field(default=[], description="媒体声量分布")
    sentiment_distribution: List[SentimentDistributionModel] = Field(default=[], description="情感分布")
    trend_data: List[TrendDataModel] = Field(default=[], description="趋势数据")
    raw_data: Optional[Dict[str, Any]] = Field(None, description="原始数据")


class SpreadAnalysisQueryModel(BaseModel):
    """
    传播分析查询模型
    """
    scheme_id: int = Field(..., description="方案ID")
    time_range: str = Field(default="today", description="时间范围")
    date_start: Optional[str] = Field(None, description="开始日期")
    date_end: Optional[str] = Field(None, description="结束日期")
    platform_types: Optional[List[str]] = Field(None, description="平台类型列表")
    sentiment_types: Optional[List[str]] = Field(None, description="情感类型列表")


class EmotionStatisticsModel(BaseModel):
    """
    情感统计模型
    """
    total_count: int = Field(default=0, description="总数量")
    positive_count: int = Field(default=0, description="正面数量")
    negative_count: int = Field(default=0, description="负面数量")
    neutral_count: int = Field(default=0, description="中性数量")
    positive_rate: float = Field(default=0.0, description="正面比例")
    negative_rate: float = Field(default=0.0, description="负面比例")
    neutral_rate: float = Field(default=0.0, description="中性比例")
    emotion_map: Optional[Dict[str, int]] = Field(None, description="情感映射")


class HotNewsExtensionStatisticsModel(BaseModel):
    """
    热点新闻扩展统计模型
    """
    total_news: int = Field(default=0, description="总新闻数")
    hot_news: int = Field(default=0, description="热点新闻数")
    trending_topics: int = Field(default=0, description="趋势话题数")
    coverage_platforms: int = Field(default=0, description="覆盖平台数")
    engagement_rate: float = Field(default=0.0, description="参与率")
    top_keywords: List[Dict[str, Any]] = Field(default=[], description="热门关键词")
    platform_distribution: Dict[str, float] = Field(default={}, description="平台分布")


class EventStatisticsModel(BaseModel):
    """
    事件统计模型
    """
    total_events: int = Field(default=0, description="总事件数")
    active_events: int = Field(default=0, description="活跃事件数")
    resolved_events: int = Field(default=0, description="已解决事件数")
    high_priority: int = Field(default=0, description="高优先级事件数")
    medium_priority: int = Field(default=0, description="中优先级事件数")
    low_priority: int = Field(default=0, description="低优先级事件数")
    event_types: Dict[str, int] = Field(default={}, description="事件类型分布")
    timeline_data: List[TrendDataModel] = Field(default=[], description="时间线数据")


class EventNewsStatisticsModel(BaseModel):
    """
    事件新闻关联统计模型
    """
    total_associations: int = Field(default=0, description="总关联数")
    verified_associations: int = Field(default=0, description="已验证关联数")
    pending_review: int = Field(default=0, description="待审核数")
    rejected_associations: int = Field(default=0, description="被拒绝关联数")
    accuracy_rate: float = Field(default=0.0, description="准确率")
    coverage_completeness: float = Field(default=0.0, description="覆盖完整性")
    association_types: Dict[str, int] = Field(default={}, description="关联类型分布")
    source_distribution: Dict[str, float] = Field(default={}, description="来源分布")
    trend_data: List[TrendDataModel] = Field(default=[], description="趋势数据")
