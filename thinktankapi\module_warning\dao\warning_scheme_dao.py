from datetime import datetime, time
from sqlalchemy import and_, delete, desc, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_warning.entity.do.warning_scheme_do import WarningScheme
from module_warning.entity.vo.warning_vo import WarningSchemeModel, WarningSchemePageQueryModel
from utils.page_util import PageUtil


class WarningSchemeDao:
    """
    预警方案管理模块数据库操作层
    """

    @classmethod
    async def get_warning_scheme_by_id(cls, db: AsyncSession, scheme_id: int):
        """
        根据预警方案id获取预警方案详细信息

        :param db: orm对象
        :param scheme_id: 预警方案id
        :return: 预警方案信息对象
        """
        scheme_info = (await db.execute(select(WarningScheme).where(WarningScheme.id == scheme_id))).scalars().first()

        return scheme_info

    @classmethod
    async def get_warning_scheme_by_name(cls, db: AsyncSession, scheme_name: str):
        """
        根据预警方案名称获取预警方案信息

        :param db: orm对象
        :param scheme_name: 预警方案名称
        :return: 预警方案信息对象
        """
        scheme_info = (await db.execute(select(WarningScheme).where(WarningScheme.scheme_name == scheme_name))).scalars().first()

        return scheme_info

    @classmethod
    async def get_warning_scheme_list(cls, db: AsyncSession, query_object: WarningSchemePageQueryModel, is_page: bool = False):
        """
        根据查询参数获取预警方案列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 预警方案列表信息对象
        """
        query = select(WarningScheme)
        
        # 构建查询条件
        conditions = []
        
        if query_object.scheme_name:
            conditions.append(WarningScheme.scheme_name.like(f'%{query_object.scheme_name}%'))
        if query_object.scheme_type:
            conditions.append(WarningScheme.scheme_type == query_object.scheme_type)
        if query_object.is_active is not None:
            conditions.append(WarningScheme.is_active == query_object.is_active)
        if query_object.begin_time:
            begin_time = datetime.strptime(query_object.begin_time, '%Y-%m-%d')
            begin_time = datetime.combine(begin_time.date(), time.min)
            conditions.append(WarningScheme.create_time >= begin_time)
        if query_object.end_time:
            end_time = datetime.strptime(query_object.end_time, '%Y-%m-%d')
            end_time = datetime.combine(end_time.date(), time.max)
            conditions.append(WarningScheme.create_time <= end_time)

        if conditions:
            query = query.where(and_(*conditions))

        # 排序
        query = query.order_by(desc(WarningScheme.create_time))

        if is_page:
            # 分页查询
            return await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size)
        else:
            # 不分页查询
            result = await db.execute(query)
            return result.scalars().all()

    @classmethod
    async def get_active_warning_schemes(cls, db: AsyncSession):
        """
        获取所有启用的预警方案

        :param db: orm对象
        :return: 启用的预警方案列表
        """
        query = select(WarningScheme).where(WarningScheme.is_active == True).order_by(WarningScheme.scheme_name)
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def add_warning_scheme_dao(cls, db: AsyncSession, scheme: WarningSchemeModel):
        """
        新增预警方案数据库操作

        :param db: orm对象
        :param scheme: 预警方案对象
        :return:
        """
        db_scheme = WarningScheme(**scheme.model_dump(exclude_unset=True))
        db.add(db_scheme)
        await db.flush()

        return db_scheme

    @classmethod
    async def edit_warning_scheme_dao(cls, db: AsyncSession, scheme: dict):
        """
        编辑预警方案数据库操作

        :param db: orm对象
        :param scheme: 需要更新的预警方案字典
        :return:
        """
        scheme_id = scheme.get('id')  # 获取ID用于WHERE条件
        if not scheme_id:
            raise ValueError("方案ID不能为空")

        # 创建更新数据的副本，排除ID字段
        update_data = {k: v for k, v in scheme.items() if k != 'id'}

        await db.execute(
            update(WarningScheme)
            .where(WarningScheme.id == scheme_id)
            .values(**update_data)
        )

    @classmethod
    async def delete_warning_scheme_dao(cls, db: AsyncSession, scheme_ids: list):
        """
        删除预警方案数据库操作

        :param db: orm对象
        :param scheme_ids: 预警方案ID列表
        :return:
        """
        await db.execute(delete(WarningScheme).where(WarningScheme.id.in_(scheme_ids)))

    @classmethod
    async def check_scheme_name_unique(cls, db: AsyncSession, scheme_name: str, scheme_id: int = None):
        """
        检查预警方案名称唯一性

        :param db: orm对象
        :param scheme_name: 预警方案名称
        :param scheme_id: 预警方案ID（编辑时排除自身）
        :return: 是否唯一
        """
        query = select(WarningScheme).where(WarningScheme.scheme_name == scheme_name)
        
        if scheme_id:
            query = query.where(WarningScheme.id != scheme_id)
            
        result = await db.execute(query)
        existing_scheme = result.scalars().first()
        
        return existing_scheme is None

    @classmethod
    async def toggle_scheme_status(cls, db: AsyncSession, scheme_id: int, is_active: bool, update_by: str):
        """
        切换预警方案状态

        :param db: orm对象
        :param scheme_id: 预警方案ID
        :param is_active: 是否启用
        :param update_by: 更新者
        :return:
        """
        await db.execute(
            update(WarningScheme)
            .where(WarningScheme.id == scheme_id)
            .values(is_active=is_active, update_by=update_by, update_time=datetime.now())
        )

    @classmethod
    async def get_scheme_count_by_status(cls, db: AsyncSession):
        """
        获取不同状态的方案数量统计

        :param db: orm对象
        :return: 统计数据
        """
        # 总数量
        total_result = await db.execute(select(func.count(WarningScheme.id)))
        total_count = total_result.scalar() or 0

        # 启用数量
        active_result = await db.execute(select(func.count(WarningScheme.id)).where(WarningScheme.is_active == True))
        active_count = active_result.scalar() or 0

        # 停用数量
        inactive_count = total_count - active_count

        return {
            'total_count': total_count,
            'active_count': active_count,
            'inactive_count': inactive_count
        }
