-- =====================================================
-- 报告中心核心表数据插入脚本
-- 创建时间: 2025-06-13
-- 说明: 为报告中心页面提供丰富的测试数据
-- =====================================================

-- 1. 方案数据已存在，跳过插入
-- 当前数据库中已有ID为11-36的方案数据，无需重复插入

-- 2. 为所有方案添加配置数据（包括现有的和新增的）
INSERT INTO scheme_config (scheme_id, monitoring_keywords, excluded_keywords, material_limit, create_time, update_time, create_by, update_by) VALUES
-- 现有方案的配置
(11, '方太,品牌监控,舆情分析', '广告,推广', 100, NOW(), NOW(), 'admin', 'admin'),
(12, '厨电,行业监控,市场分析', '无关内容', 150, NOW(), NOW(), 'admin', 'admin'),
(13, '竞品,分析监控,对手研究', '机密信息', 120, NOW(), NOW(), 'admin', 'admin'),
(14, '产品,口碑监控,用户反馈', '恶意评论', 100, NOW(), NOW(), 'admin', 'admin'),
-- 新增方案的配置
(15, '方太,官方微博,品牌动态', '广告,推广', 100, NOW(), NOW(), 'admin', 'admin'),
(16, '方太,品牌口碑,用户评价', '恶意评论,水军', 150, NOW(), NOW(), 'admin', 'admin'),
(17, '厨电,市场趋势,行业分析', '无关内容', 200, NOW(), NOW(), 'admin', 'admin'),
(18, '智能厨电,AI厨电,物联网', '技术细节', 120, NOW(), NOW(), 'admin', 'admin'),
(19, '老板电器,竞品分析,市场份额', '内部信息', 150, NOW(), NOW(), 'admin', 'admin'),
(20, '华帝,品牌动态,产品策略', '商业机密', 100, NOW(), NOW(), 'admin', 'admin'),
(21, '方太燃气灶,用户反馈,产品评价', '恶意差评', 90, NOW(), NOW(), 'admin', 'admin'),
(22, '方太油烟机,市场口碑,用户体验', '竞品攻击', 110, NOW(), NOW(), 'admin', 'admin'),
(23, '方太,官方微博,品牌动态', '广告,推广', 100, NOW(), NOW(), 'admin', 'admin'),
(24, '方太,品牌口碑,用户评价', '恶意评论,水军', 150, NOW(), NOW(), 'admin', 'admin'),
(25, '方太,新品,产品发布', '谣言,假消息', 80, NOW(), NOW(), 'admin', 'admin'),
(26, '厨电,市场趋势,行业分析', '无关内容', 200, NOW(), NOW(), 'admin', 'admin'),
(27, '智能厨电,AI厨电,物联网', '技术细节', 120, NOW(), NOW(), 'admin', 'admin'),
(28, '厨电消费,购买行为,用户偏好', '价格敏感', 100, NOW(), NOW(), 'admin', 'admin'),
(29, '老板电器,竞品分析,市场份额', '内部信息', 150, NOW(), NOW(), 'admin', 'admin'),
(30, '华帝,品牌动态,产品策略', '商业机密', 100, NOW(), NOW(), 'admin', 'admin'),
(31, '美的厨电,产品线,营销策略', '内部文件', 180, NOW(), NOW(), 'admin', 'admin'),
(32, '海尔厨电,市场定位,发展策略', '机密信息', 120, NOW(), NOW(), 'admin', 'admin'),
(33, '方太燃气灶,用户反馈,产品评价', '恶意差评', 90, NOW(), NOW(), 'admin', 'admin'),
(34, '方太油烟机,市场口碑,用户体验', '竞品攻击', 110, NOW(), NOW(), 'admin', 'admin'),
(35, '方太蒸烤箱,用户体验,产品反馈', '使用问题', 80, NOW(), NOW(), 'admin', 'admin'),
(36, '方太洗碗机,市场销售,用户评价', '安装问题', 100, NOW(), NOW(), 'admin', 'admin');

-- 3. 为所有方案添加统计数据
INSERT INTO scheme_summary_statistic (scheme_id, all_total, media_count, negative_count, today_count, start_time, end_time, refresh_time, create_time, update_time, create_by, update_by) VALUES
(11, 1250, 45, 23, 15, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(12, 890, 32, 12, 8, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(13, 2100, 78, 45, 25, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(14, 1680, 56, 34, 18, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(15, 980, 28, 15, 12, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(16, 1450, 52, 28, 20, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(17, 750, 22, 8, 6, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(18, 3200, 95, 67, 35, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(19, 1890, 64, 32, 22, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(20, 1320, 48, 25, 16, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(21, 2450, 82, 56, 28, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(22, 1680, 58, 38, 19, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(23, 2890, 96, 72, 42, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(24, 1950, 68, 45, 24, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(25, 1120, 38, 22, 14, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(26, 1580, 54, 31, 18, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(27, 890, 26, 12, 9, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(28, 1340, 46, 28, 16, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(29, 2150, 75, 48, 26, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(30, 1780, 62, 35, 21, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(31, 2980, 98, 78, 45, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(32, 2240, 76, 52, 28, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(33, 1420, 48, 26, 17, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(34, 1890, 64, 38, 22, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(35, 1150, 38, 18, 12, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin'),
(36, 1650, 56, 32, 19, '2025-06-01 00:00:00', '2025-06-13 23:59:59', NOW(), NOW(), NOW(), 'admin', 'admin');

-- 4. 添加更多报告模板数据
INSERT INTO report_template (name, template_type, description, template_content, is_active, create_by, create_time, update_time) VALUES
('品牌-社交媒体分析', 'normal', '品牌在社交媒体平台的表现分析模板', '{"sections":["概述","数据统计","趋势分析","建议"]}', 1, 'admin', NOW(), NOW()),
('品牌-危机公关', 'normal', '品牌危机公关事件分析和应对模板', '{"sections":["事件概述","影响评估","应对策略","效果跟踪"]}', 1, 'admin', NOW(), NOW()),
('行业-市场研究', 'normal', '行业市场研究和分析报告模板', '{"sections":["市场概况","竞争格局","发展趋势","机会分析"]}', 1, 'admin', NOW(), NOW()),
('产品-用户反馈', 'normal', '产品用户反馈收集和分析模板', '{"sections":["反馈概述","问题分类","改进建议","跟进计划"]}', 1, 'admin', NOW(), NOW()),
('竞品-SWOT分析', 'competitor', '竞争对手SWOT分析报告模板', '{"sections":["竞品概述","优势分析","劣势分析","机会威胁","策略建议"]}', 1, 'admin', NOW(), NOW()),
('竞品-产品对比', 'competitor', '竞品产品功能和价格对比模板', '{"sections":["产品概述","功能对比","价格分析","市场定位","竞争策略"]}', 1, 'admin', NOW(), NOW()),
('竞品-营销策略', 'competitor', '竞品营销策略分析报告模板', '{"sections":["营销概述","渠道分析","推广策略","效果评估","借鉴建议"]}', 1, 'admin', NOW(), NOW());

-- 5. 为所有方案添加关键词设置（warning_settings表）
INSERT INTO warning_settings (scheme_id, allow_words, reject_words, create_by, create_time, update_time) VALUES
(11, '方太,品牌,监控,舆情,正面,好评,推荐', '广告,推广,营销号,水军,恶意,攻击', 'admin', NOW(), NOW()),
(12, '厨电,行业,监控,市场,趋势,发展', '无关,垃圾,广告,推广', 'admin', NOW(), NOW()),
(13, '竞品,分析,监控,对手,策略', '机密,内部,保密,泄露', 'admin', NOW(), NOW()),
(14, '产品,口碑,监控,用户,反馈', '恶意,差评,黑粉,攻击', 'admin', NOW(), NOW()),
(15, '方太,微博,官方,动态,发布', '谣言,假消息,恶意传播', 'admin', NOW(), NOW()),
(16, '方太,口碑,评价,用户,体验', '水军,刷评,虚假,恶意', 'admin', NOW(), NOW()),
(17, '厨电,市场,趋势,行业,分析', '个人观点,主观,猜测', 'admin', NOW(), NOW()),
(18, '智能厨电,AI,物联网,技术', '技术细节,专业术语', 'admin', NOW(), NOW()),
(19, '老板电器,竞品,市场份额,策略', '内部信息,商业机密', 'admin', NOW(), NOW()),
(20, '华帝,品牌,产品,营销', '商业机密,内部文件', 'admin', NOW(), NOW()),
(21, '方太,燃气灶,用户,反馈,评价', '恶意差评,竞品攻击', 'admin', NOW(), NOW()),
(22, '方太,油烟机,口碑,体验', '恶意评论,水军刷评', 'admin', NOW(), NOW()),
(23, '方太,微博,官方,动态,发布', '谣言,假消息,恶意传播', 'admin', NOW(), NOW()),
(24, '方太,口碑,评价,用户,体验', '水军,刷评,虚假,恶意', 'admin', NOW(), NOW()),
(25, '方太,新品,发布,产品,上市', '泄露,内部,机密,未发布', 'admin', NOW(), NOW()),
(26, '厨电,市场,趋势,行业,分析', '个人观点,主观,猜测', 'admin', NOW(), NOW()),
(27, '智能厨电,AI,物联网,技术', '技术细节,专业术语', 'admin', NOW(), NOW()),
(28, '厨电,消费者,购买,行为,偏好', '价格敏感,个人隐私', 'admin', NOW(), NOW()),
(29, '老板电器,竞品,市场份额,策略', '内部信息,商业机密', 'admin', NOW(), NOW()),
(30, '华帝,品牌,产品,营销', '商业机密,内部文件', 'admin', NOW(), NOW()),
(31, '美的,厨电,产品线,营销', '内部文件,商业机密', 'admin', NOW(), NOW()),
(32, '海尔,厨电,市场,定位,策略', '机密信息,内部资料', 'admin', NOW(), NOW()),
(33, '方太,燃气灶,用户,反馈,评价', '恶意差评,竞品攻击', 'admin', NOW(), NOW()),
(34, '方太,油烟机,口碑,体验', '恶意评论,水军刷评', 'admin', NOW(), NOW()),
(35, '方太,蒸烤箱,用户,体验,反馈', '使用问题,技术故障', 'admin', NOW(), NOW()),
(36, '方太,洗碗机,市场,销售,评价', '安装问题,售后投诉', 'admin', NOW(), NOW())
ON DUPLICATE KEY UPDATE
allow_words = VALUES(allow_words),
reject_words = VALUES(reject_words),
update_time = NOW();

-- 6. 查询验证数据插入结果
SELECT '方案数据统计' as table_name, COUNT(*) as count FROM scheme
UNION ALL
SELECT '方案配置数据统计', COUNT(*) FROM scheme_config  
UNION ALL
SELECT '统计数据统计', COUNT(*) FROM scheme_summary_statistic
UNION ALL
SELECT '模板数据统计', COUNT(*) FROM report_template
UNION ALL
SELECT '关键词设置统计', COUNT(*) FROM warning_settings WHERE allow_words IS NOT NULL;
