from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.dao.spread_analysis_dao import SpreadAnalysisDao
from module_admin.entity.vo.spread_analysis_vo import (
    ComprehensiveSpreadAnalysisModel,
    SummaryCardModel,
    KeywordCloudModel,
    PlatformDistributionModel,
    MediaTypeDistributionModel,
    SentimentDistributionModel,
    TrendDataModel,
    EmotionStatisticsModel,
    HotNewsExtensionStatisticsModel,
    EventStatisticsModel,
    EventNewsStatisticsModel
)
from utils.log_util import logger


class SpreadAnalysisService:
    """
    传播分析服务层
    """

    @classmethod
    async def get_comprehensive_spread_analysis(cls, db: AsyncSession, scheme_id: int, 
                                              time_range: str = "today", date_start: str = None, 
                                              date_end: str = None, platform_types: list = None, 
                                              sentiment_types: list = None) -> ComprehensiveSpreadAnalysisModel:
        """
        获取综合传播分析数据
        """
        try:
            # 获取基础统计数据
            keyword_data = await SpreadAnalysisDao.get_keyword_data_statistics(
                db, time_range, date_start, date_end, platform_types, sentiment_types
            )

            # 计算汇总卡片数据
            summary_cards = await cls._generate_summary_cards(keyword_data)

            # 获取关键词云数据
            keywords_cloud = await cls._generate_keywords_cloud(db, time_range)

            # 获取平台分布数据
            platform_distribution = await cls._generate_platform_distribution(db, time_range)

            # 获取媒体类型分布数据
            media_type_distribution = await cls._generate_media_type_distribution(db, time_range)

            # 获取媒体声量分布数据（复用平台分布逻辑）
            media_volume_distribution = platform_distribution

            # 获取情感分布数据
            sentiment_distribution = await cls._generate_sentiment_distribution(db, time_range)

            # 获取趋势数据
            trend_data = await cls._generate_trend_data(db)

            # 构建原始数据
            raw_data = {
                "emotion_stats": await cls._get_emotion_stats_map(keyword_data),
                "sentiment_distribution": sentiment_distribution
            }

            return ComprehensiveSpreadAnalysisModel(
                summary_cards=summary_cards,
                keywords_cloud=keywords_cloud,
                platform_distribution=platform_distribution,
                media_type_distribution=media_type_distribution,
                media_volume_distribution=media_volume_distribution,
                sentiment_distribution=sentiment_distribution,
                trend_data=trend_data,
                raw_data=raw_data
            )

        except Exception as e:
            logger.error(f"获取综合传播分析数据失败: {e}")
            # 返回空数据而不是抛出异常
            return ComprehensiveSpreadAnalysisModel()

    @classmethod
    async def _generate_summary_cards(cls, keyword_data: List) -> List[SummaryCardModel]:
        """
        生成汇总卡片数据
        """
        try:
            total_count = len(keyword_data)
            positive_count = sum(1 for item in keyword_data if getattr(item, 'sentiment', None) == 'positive')
            negative_count = sum(1 for item in keyword_data if getattr(item, 'sentiment', None) == 'negative')
            neutral_count = sum(1 for item in keyword_data if getattr(item, 'sentiment', None) == 'neutral')
            
            # 计算媒体总数（去重web字段）
            media_count = len(set(getattr(item, 'web', '') for item in keyword_data if getattr(item, 'web', '')))

            return [
                SummaryCardModel(title="信息总量", value=str(total_count), color="#409EFF"),
                SummaryCardModel(title="正面声量", value=str(positive_count), color="#67C23A"),
                SummaryCardModel(title="中性声量", value=str(neutral_count), color="#E6A23C"),
                SummaryCardModel(title="负面声量", value=str(negative_count), color="#F56C6C"),
                SummaryCardModel(title="媒体总数", value=str(media_count), color="#909399")
            ]
        except Exception as e:
            logger.error(f"生成汇总卡片数据失败: {e}")
            return []

    @classmethod
    async def _generate_keywords_cloud(cls, db: AsyncSession, time_range: str) -> List[KeywordCloudModel]:
        """
        生成关键词云数据
        """
        try:
            keyword_stats = await SpreadAnalysisDao.get_keyword_cloud_data(db, time_range, 20)
            
            keywords_cloud = []
            for stat in keyword_stats:
                if stat.keyword:
                    keywords_cloud.append(KeywordCloudModel(
                        name=stat.keyword,
                        value=stat.count * 100  # 放大数值以便更好地显示
                    ))
            
            return keywords_cloud
        except Exception as e:
            logger.error(f"生成关键词云数据失败: {e}")
            return []

    @classmethod
    async def _generate_platform_distribution(cls, db: AsyncSession, time_range: str) -> List[PlatformDistributionModel]:
        """
        生成平台分布数据
        """
        try:
            platform_stats = await SpreadAnalysisDao.get_platform_distribution_statistics(db, time_range)
            
            total_count = sum(stat.count for stat in platform_stats)
            if total_count == 0:
                return []

            platform_distribution = []
            for stat in platform_stats:
                if stat.platform:
                    percentage = (stat.count / total_count) * 100
                    platform_distribution.append(PlatformDistributionModel(
                        name=cls._get_platform_display_name(stat.platform),
                        value=round(percentage, 2),
                        count=stat.count
                    ))
            
            return platform_distribution
        except Exception as e:
            logger.error(f"生成平台分布数据失败: {e}")
            return []

    @classmethod
    async def _generate_media_type_distribution(cls, db: AsyncSession, time_range: str) -> List[MediaTypeDistributionModel]:
        """
        生成媒体类型分布数据
        """
        try:
            platform_stats = await SpreadAnalysisDao.get_platform_distribution_statistics(db, time_range)
            
            media_type_distribution = []
            for stat in platform_stats:
                if stat.platform:
                    media_type_distribution.append(MediaTypeDistributionModel(
                        name=cls._get_platform_display_name(stat.platform),
                        value=stat.count
                    ))
            
            return media_type_distribution
        except Exception as e:
            logger.error(f"生成媒体类型分布数据失败: {e}")
            return []

    @classmethod
    async def _generate_sentiment_distribution(cls, db: AsyncSession, time_range: str) -> List[SentimentDistributionModel]:
        """
        生成情感分布数据
        """
        try:
            sentiment_stats = await SpreadAnalysisDao.get_sentiment_distribution_statistics(db, time_range)
            
            total_count = sum(stat.count for stat in sentiment_stats)
            if total_count == 0:
                return []

            sentiment_distribution = []
            for stat in sentiment_stats:
                if stat.sentiment:
                    percentage = (stat.count / total_count) * 100
                    sentiment_distribution.append(SentimentDistributionModel(
                        sentiment=stat.sentiment,
                        count=stat.count,
                        percentage=round(percentage, 2)
                    ))
            
            return sentiment_distribution
        except Exception as e:
            logger.error(f"生成情感分布数据失败: {e}")
            return []

    @classmethod
    async def _generate_trend_data(cls, db: AsyncSession, days: int = 7) -> List[TrendDataModel]:
        """
        生成趋势数据
        """
        try:
            trend_stats = await SpreadAnalysisDao.get_trend_data(db, days)
            
            trend_data = []
            for stat in trend_stats:
                trend_data.append(TrendDataModel(
                    date=stat.date.strftime('%Y-%m-%d'),
                    count=stat.count,
                    positive=stat.positive,
                    negative=stat.negative,
                    neutral=stat.neutral
                ))
            
            return trend_data
        except Exception as e:
            logger.error(f"生成趋势数据失败: {e}")
            return []

    @classmethod
    async def _get_emotion_stats_map(cls, keyword_data: List) -> Dict[str, int]:
        """
        获取情感统计映射
        """
        try:
            emotion_map = {}
            for item in keyword_data:
                sentiment = getattr(item, 'sentiment', 'neutral')
                if sentiment == 'positive':
                    emotion_map['1'] = emotion_map.get('1', 0) + 1
                elif sentiment == 'negative':
                    emotion_map['3'] = emotion_map.get('3', 0) + 1
                else:
                    emotion_map['2'] = emotion_map.get('2', 0) + 1
            
            return emotion_map
        except Exception as e:
            logger.error(f"获取情感统计映射失败: {e}")
            return {}

    @classmethod
    def _get_platform_display_name(cls, platform_type: str) -> str:
        """
        获取平台显示名称
        """
        platform_mapping = {
            '新闻': '新闻',
            '科技': '科技',
            '调研': '调研',
            '企业动态': '企业动态',
            '产品': '产品',
            '微博': '微博',
            '微信': '微信',
            '视频': '视频',
            '论坛': '论坛',
            '电商': '电商',
            '问答': '问答',
            '负面舆情': '负面舆情',
            '品牌': '品牌'
        }
        return platform_mapping.get(platform_type, platform_type)

    @classmethod
    async def get_emotion_statistics(cls, db: AsyncSession) -> EmotionStatisticsModel:
        """
        获取情感统计数据
        """
        try:
            emotion_stats = await SpreadAnalysisDao.get_emotion_statistics(db)

            if not emotion_stats:
                return EmotionStatisticsModel()

            total_count = emotion_stats.total_count or 0
            positive_count = emotion_stats.positive_count or 0
            negative_count = emotion_stats.negative_count or 0
            neutral_count = emotion_stats.neutral_count or 0

            # 计算比例
            positive_rate = (positive_count / total_count * 100) if total_count > 0 else 0
            negative_rate = (negative_count / total_count * 100) if total_count > 0 else 0
            neutral_rate = (neutral_count / total_count * 100) if total_count > 0 else 0

            return EmotionStatisticsModel(
                total_count=total_count,
                positive_count=positive_count,
                negative_count=negative_count,
                neutral_count=neutral_count,
                positive_rate=round(positive_rate, 1),
                negative_rate=round(negative_rate, 1),
                neutral_rate=round(neutral_rate, 1),
                emotion_map={
                    '1': positive_count,
                    '2': neutral_count,
                    '3': negative_count
                }
            )
        except Exception as e:
            logger.error(f"获取情感统计数据失败: {e}")
            return EmotionStatisticsModel()

    @classmethod
    async def get_hot_news_extension_statistics(cls, db: AsyncSession, time_range: str = "today") -> HotNewsExtensionStatisticsModel:
        """
        获取热点新闻扩展统计数据
        """
        try:
            # 基于现有keyword_data表模拟热点新闻扩展统计
            keyword_data = await SpreadAnalysisDao.get_keyword_data_statistics(db, time_range)

            total_news = len(keyword_data)
            # 模拟热点新闻数量（假设热度分数高的为热点新闻）
            hot_news = max(1, total_news // 8)  # 约12.5%为热点新闻
            trending_topics = max(1, total_news // 20)  # 约5%为趋势话题

            # 统计平台数量
            platforms = set(getattr(item, 'web', '') for item in keyword_data if getattr(item, 'web', ''))
            coverage_platforms = len(platforms)

            # 模拟参与率
            engagement_rate = min(85.0, max(50.0, 60.0 + (total_news / 100)))

            # 生成热门关键词
            keyword_stats = await SpreadAnalysisDao.get_keyword_cloud_data(db, time_range, 5)
            top_keywords = []
            for stat in keyword_stats:
                if stat.keyword:
                    top_keywords.append({
                        "keyword": stat.keyword,
                        "count": stat.count,
                        "trend": "up" if stat.count > 10 else "stable"
                    })

            # 生成平台分布
            platform_stats = await SpreadAnalysisDao.get_platform_distribution_statistics(db, time_range)
            total_platform_count = sum(stat.count for stat in platform_stats)
            platform_distribution = {}

            if total_platform_count > 0:
                for stat in platform_stats:
                    if stat.platform:
                        percentage = (stat.count / total_platform_count) * 100
                        platform_name = cls._get_platform_display_name(stat.platform)
                        platform_distribution[platform_name.lower()] = round(percentage, 1)

            return HotNewsExtensionStatisticsModel(
                total_news=total_news,
                hot_news=hot_news,
                trending_topics=trending_topics,
                coverage_platforms=coverage_platforms,
                engagement_rate=round(engagement_rate, 1),
                top_keywords=top_keywords,
                platform_distribution=platform_distribution
            )
        except Exception as e:
            logger.error(f"获取热点新闻扩展统计数据失败: {e}")
            return HotNewsExtensionStatisticsModel()

    @classmethod
    async def get_event_statistics(cls, db: AsyncSession, time_range: str = "today") -> EventStatisticsModel:
        """
        获取事件统计数据
        """
        try:
            # 基于现有keyword_data表模拟事件统计
            keyword_data = await SpreadAnalysisDao.get_keyword_data_statistics(db, time_range)

            total_events = max(1, len(keyword_data) // 5)  # 模拟事件数量
            active_events = max(1, total_events // 3)
            resolved_events = total_events - active_events

            # 模拟优先级分布
            high_priority = max(1, total_events // 10)
            medium_priority = max(1, total_events // 4)
            low_priority = total_events - high_priority - medium_priority

            # 模拟事件类型分布
            event_types = {
                "brand_crisis": max(1, total_events // 15),
                "product_issue": max(1, total_events // 10),
                "market_change": max(1, total_events // 4),
                "competitor_action": max(1, total_events // 8),
                "industry_trend": max(1, total_events // 3)
            }

            # 生成时间线数据
            trend_data = await cls._generate_trend_data(db, 7)

            return EventStatisticsModel(
                total_events=total_events,
                active_events=active_events,
                resolved_events=resolved_events,
                high_priority=high_priority,
                medium_priority=medium_priority,
                low_priority=low_priority,
                event_types=event_types,
                timeline_data=trend_data
            )
        except Exception as e:
            logger.error(f"获取事件统计数据失败: {e}")
            return EventStatisticsModel()

    @classmethod
    async def get_event_news_statistics(cls, db: AsyncSession, time_range: str = "today") -> EventNewsStatisticsModel:
        """
        获取事件新闻关联统计数据
        """
        try:
            # 基于现有keyword_data表模拟事件新闻关联统计
            keyword_data = await SpreadAnalysisDao.get_keyword_data_statistics(db, time_range)

            total_associations = max(1, len(keyword_data) * 2)  # 模拟关联数量
            verified_associations = max(1, int(total_associations * 0.85))
            pending_review = max(1, int(total_associations * 0.10))
            rejected_associations = total_associations - verified_associations - pending_review

            # 计算准确率和完整性
            accuracy_rate = (verified_associations / total_associations * 100) if total_associations > 0 else 0
            coverage_completeness = min(95.0, max(80.0, accuracy_rate + 10))

            # 模拟关联类型分布
            association_types = {
                "direct_mention": max(1, int(total_associations * 0.6)),
                "indirect_reference": max(1, int(total_associations * 0.3)),
                "related_topic": max(1, int(total_associations * 0.1))
            }

            # 模拟来源分布
            source_distribution = {
                "news_media": 45.6,
                "social_media": 32.1,
                "official_announcement": 12.8,
                "user_generated": 9.5
            }

            # 生成趋势数据
            trend_data = await cls._generate_trend_data(db, 7)

            return EventNewsStatisticsModel(
                total_associations=total_associations,
                verified_associations=verified_associations,
                pending_review=pending_review,
                rejected_associations=rejected_associations,
                accuracy_rate=round(accuracy_rate, 1),
                coverage_completeness=round(coverage_completeness, 1),
                association_types=association_types,
                source_distribution=source_distribution,
                trend_data=trend_data
            )
        except Exception as e:
            logger.error(f"获取事件新闻关联统计数据失败: {e}")
            return EventNewsStatisticsModel()
