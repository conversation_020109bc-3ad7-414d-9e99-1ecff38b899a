-- 创建报告模板表
CREATE TABLE IF NOT EXISTS `report_template` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '模板ID',
    `name` varchar(100) NOT NULL COMMENT '模板名称',
    `template_type` varchar(50) NOT NULL COMMENT '模板类型：normal-普通模板，competitor-竞对模板',
    `description` text COMMENT '模板描述',
    `template_content` longtext COMMENT '模板内容（JSON格式）',
    `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_template_type` (`template_type`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报告模板表';

-- 插入一些示例数据
INSERT INTO `report_template` (`name`, `template_type`, `description`, `template_content`, `is_active`, `create_by`) VALUES
('品牌-热议话题', 'normal', '品牌热议话题报告模板', '{}', 1, 'admin'),
('品牌-舆论', 'normal', '品牌舆论分析报告模板', '{}', 1, 'admin'),
('品牌-竞品', 'competitor', '品牌竞品分析报告模板', '{}', 1, 'admin');
