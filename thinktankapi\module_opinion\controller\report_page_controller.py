import os
from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse
from config.env import UploadConfig
from utils.log_util import logger

# 公开的报告页面控制器，无需认证
publicReportPageController = APIRouter(prefix='/public/report', tags=['公开报告页面'])


@publicReportPageController.get('/view/{page_id}')
async def view_report_page(page_id: str):
    """
    查看报告页面（公开接口，无需认证）
    
    :param page_id: 页面ID
    :return: HTML页面文件
    """
    try:
        # 验证页面ID格式
        if not page_id or not page_id.startswith('report_'):
            raise HTTPException(status_code=404, detail='页面不存在')
        
        # 构建文件路径
        report_dir = os.path.join(UploadConfig.UPLOAD_PATH, 'reports')
        file_path = os.path.join(report_dir, f"{page_id}.html")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.warning(f'报告页面文件不存在: {file_path}')
            raise HTTPException(status_code=404, detail='页面不存在')
        
        # 返回HTML文件，设置正确的Content-Type确保浏览器直接显示
        return FileResponse(
            path=file_path,
            media_type='text/html; charset=utf-8',
            headers={
                'Content-Disposition': 'inline',  # 确保浏览器内联显示而不是下载
                'Cache-Control': 'no-cache'
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f'访问报告页面失败: {str(e)}')
        raise HTTPException(status_code=500, detail='服务器内部错误')


@publicReportPageController.get('/check/{page_id}')
async def check_report_page(page_id: str):
    """
    检查报告页面是否存在（公开接口，无需认证）
    
    :param page_id: 页面ID
    :return: 检查结果
    """
    try:
        # 验证页面ID格式
        if not page_id or not page_id.startswith('report_'):
            return {'exists': False, 'message': '无效的页面ID'}
        
        # 构建文件路径
        report_dir = os.path.join(UploadConfig.UPLOAD_PATH, 'reports')
        file_path = os.path.join(report_dir, f"{page_id}.html")
        
        # 检查文件是否存在
        exists = os.path.exists(file_path)
        
        return {
            'exists': exists,
            'page_id': page_id,
            'message': '页面存在' if exists else '页面不存在'
        }
        
    except Exception as e:
        logger.error(f'检查报告页面失败: {str(e)}')
        return {'exists': False, 'message': '检查失败'}
