2025-05-22 09:49:42.880 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-05-22 09:49:42.881 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-05-22 09:49:46.856 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-05-22 09:49:46.856 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-05-22 09:49:46.863 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-05-22 09:49:47.613 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-05-22 09:49:48.292 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-05-22 09:49:48.292 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-05-22 09:49:48.398 | 817de6739e3c4fdf9b8143afb9119c00 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为03219bdd-8adf-4cff-a93a-b3880bb424de的会话获取图片验证码成功
2025-05-22 09:50:09.686 | 4be7871489b641b2a361f852355a9099 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-22 09:50:09.965 | 08a96e0119de42259fc74753a327bf75 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 09:50:10.227 | 03c590d259aa45ddbbb0ccfd2f454a5d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 09:53:39.652 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-05-22 09:53:39.654 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-05-22 09:53:42.062 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-05-22 09:53:42.062 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-05-22 09:53:42.063 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-05-22 09:53:43.193 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-05-22 09:53:44.571 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-05-22 09:53:44.571 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-05-22 09:54:00.897 | 61e7efbf03314c7fbac758dde4da9930 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 09:54:01.222 | 3c9078bc7c5b4a729e63476fde65cd57 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 09:58:47.859 | 2b011c15074a4c5fbeb693f4ee2e5cf2 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为277a1b8d-a23b-4428-a751-c142b008afb9的会话获取图片验证码成功
2025-05-22 09:58:52.287 | 0bc6cacc8b7b4462947a9b95013c3f26 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为5f71c2ec-ffd6-43c3-a47f-d52d2131d81b的会话获取图片验证码成功
2025-05-22 09:59:01.170 | 63460541ef2b4c8d9df9a54a14c63109 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为11cd0272-37a2-40c7-95c4-ac6812057236的会话获取图片验证码成功
2025-05-22 10:00:50.548 | 6364744b66fa4a8689d8f0c2ff74afcf | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为ddfc75d7-87de-425c-af1e-f7b6593fe421的会话获取图片验证码成功
2025-05-22 10:00:53.981 | 2333673bec1b4359b5e3a79f0d98f318 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-22 10:00:54.392 | 3ae5c494a09e4c4ab439a481b6185577 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 10:00:54.720 | d9d9bda17ae842d2b0fb713d873e298e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 10:01:43.172 | 8c2c92d5ac4243d3921d40226f6fc39a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 10:01:43.677 | 1be555cba3da4974be75cbeef8d32727 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 10:10:31.804 | 002b6f7481b64d3791c79c6239dcd090 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 10:10:32.129 | 46c51f0237ae48da84031649c9b1238f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 10:14:32.952 | 2a33392e2aa54db9b30a075124ab326a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 10:14:33.282 | 8fef0d0b8f1842fd811cb9ac6af6a930 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 10:17:43.034 | 004a8595521e4f5f828e91462b35ce71 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 10:17:43.365 | 7ca9ccab245a4948956ef3cbd6b0496e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 10:29:19.938 | b8f23e5da62d45c6b292d5369478f4a5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 10:29:20.264 | c839d8eab716441da63e97ef51886c18 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 10:30:31.783 | 63fa128648204429ac7ecc821db08555 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 10:30:32.104 | 9ddd44bc276848308c046183fbdebc85 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 10:35:27.522 | 99c4eb9ecc9d42b39f110b1695210d8b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 10:35:27.856 | 507cff6faabd42d5a0d9c9c6ebbada99 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 10:35:36.590 | a31370f2c861435692fd7236d5792be1 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 10:35:36.922 | e556d803775f4dcf9eea7a5629b6b0c4 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 10:42:31.789 | 9cd43fd187d64e2096a8d7079cc21013 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 10:42:32.419 | 9b8f13c65efd4ed39d1cb902b6028d2f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 10:46:58.585 | e2014a4429f14decb589748aaaef4ad1 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 10:46:58.943 | 608bc581ea56433892a4145c74ae0c45 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 10:52:43.052 | 6d54728f895842c69d7a03bdb070d9ca | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 10:52:45.044 | f9d2ac73449d487a8fbeed02cba49a57 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 10:59:35.855 | 5d43714700be43b1a3d9c1f99a2c1e66 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 10:59:36.185 | 0f76e9bb31d944f6855575b50a5d5fa5 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 11:01:18.394 | ca77a28f8a4c46638ac8527dfacd8a51 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 11:01:18.899 | c72888f871324604aac9cee67bcd5611 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 11:07:57.450 | baec997349e843d0a118170b223138e6 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 11:07:57.813 | 104ab66cc26b4bf4957b76265ecae192 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 11:20:55.424 | 62cdf35f0e9041f7b9c40b6edd32fee7 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 11:20:55.837 | 0d329350f858450a8666d46906e3726b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 11:29:10.449 | e8c24d10b6154e379b1e481f236dcbbe | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 11:29:10.766 | 1adb5cc9086c49b58a3ee3d971c33f04 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 11:30:03.038 | 2e5eee7fbdf6414e9b8636a214cbfae8 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为0fae1f95-eca9-46b8-b5bd-e4e8347935e2的会话获取图片验证码成功
2025-05-22 11:43:05.179 | 8296a8ab84b84f5f90f2df39561249a6 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 11:43:06.244 | c2b55f8adf9245279a703ffa233b8d17 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 14:49:06.207 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-05-22 14:49:06.207 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-05-22 14:49:07.291 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-05-22 14:49:07.292 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-05-22 14:49:07.294 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-05-22 14:49:07.936 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-05-22 14:49:08.757 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-05-22 14:49:08.757 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-05-22 14:49:33.653 | 4d76cbd269bf4c38bf9ed82d9e68d1c5 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-05-22 14:49:33.748 | 4883dbee89f04e7e87dff19ea4a7b27c | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-05-22 14:49:33.806 | fa1d1a9a32e94c388200474185da590d | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为0b56b8d6-1823-4506-92df-8c5ce2b44b77的会话获取图片验证码成功
2025-05-22 14:49:43.218 | 60696db8a0504b69b9ac7e019262c717 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-22 14:49:43.510 | 503d3998ae59406c8ba7cc37315eefea | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 14:49:43.768 | 3ff10860a2804448934e34894057737a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 15:07:32.381 | aa43e613a77f4a158a34529ba9253bc3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 15:07:32.728 | 17e526d3ea8442218f491cd6df50f4b9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 15:08:20.996 | 5b83d7fe591f42cf8b6eb9192ba7df4d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 15:08:21.271 | eceaec6534494d8dba26cea6b2583382 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 15:32:11.984 | 597d5070a4334433991a294e2d41c09a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 15:32:12.264 | 280736e69cce4f91b2b4ba7900277fef | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 15:33:06.991 | 7d477e44b39f496c9e3096f38c30dd25 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 15:33:07.249 | b795860db48c45a48e457f4f83d0de7e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 15:36:41.725 | 7488166f0a1743aebf27782c6abb6f1d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 15:36:42.025 | ae8d2bb895dc4d70bd88ccb3dda878ab | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 15:42:53.926 | da50962c59bc4017b691da456493477b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 15:42:54.218 | bfde5433a3d04a5383d961ed73603d92 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 16:06:55.673 | 0d2aed399bdb475986f1663cbd424b5a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 16:06:55.972 | a28789e57736473d996a581ca21843e6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 16:09:17.857 | d4d4548ec52245efa5cb6887818dcbbe | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 16:09:18.146 | 51f06985c88f4be7986d4729e3037380 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 16:11:16.535 | a5fec91b1d874aa2a6aecf8e69469086 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为6efbe6d7-0170-4f39-b6e4-5b6208ec899d的会话获取图片验证码成功
2025-05-22 16:11:27.547 | 03b0aeb834df489fa8d87be10fe684e2 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-22 16:11:27.852 | 24f3146dd3b341d99358b36bdc1a6b22 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 16:11:28.443 | 9185708a57284393bc21ec7b448d1be6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 16:33:00.871 | 98c374d12757405fb05d76acae2ed86e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 16:33:01.192 | 88787f96b4a34a0aa57d82fbdea81745 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 16:41:48.356 | ce2c844000ed48ee989d460f4e32d4d5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 16:41:48.624 | 20dc665174784f0f926497788e52f0e6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 16:49:19.593 | d345b1753879408ca65f6210e2145eae | ERROR    | exceptions.handle:exception_handler:70 - (asyncmy.errors.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.email, sys_user.phonenumber, sys_user.sex, sys_user.avatar, sys_user.password, sys_user.status, sys_user.del_flag, sys_user.login_ip, sys_user.login_date, sys_user.create_by, sys_user.create_time, sys_user.update_by, sys_user.update_time, sys_user.remark 
FROM sys_user 
WHERE sys_user.status = %s AND sys_user.del_flag = %s AND sys_user.user_id = %s]
[parameters: ('0', '0', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):

  File "asyncmy\\connection.pyx", line 658, in asyncmy.connection.Connection._read_bytes
    data = await self._reader.readexactly(num_bytes)

  File "C:\Program Files\Python39\lib\asyncio\streams.py", line 712, in readexactly
    raise self._exception
          │    └ ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)
          └ <StreamReader exception=ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None) transport=<_SelectorSocketTranspo...

  File "C:\Program Files\Python39\lib\asyncio\selector_events.py", line 856, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           │    │          │    └ 262144
           │    │          └ <_SelectorSocketTransport closed fd=2392>
           │    └ None
           └ <_SelectorSocketTransport closed fd=2392>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x0000026A0C169790>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x0000026A0C188DF0>
    └ <sqlalchemy.engine.base.Connection object at 0x0000026A37BCF220>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('0', '0', 1)
    │      │       └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x0000026A0C5E10D0>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026A37BE84A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 95, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('0', '0', 1)
           │    │      │    │              └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x0000026A0C5E11F0>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026A37BE84A0>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026A37BE84A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x0000026A37847540>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x0000026A37BBE100 (otid=0x0000026A0BA2E0F0) dead>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x0000026A37847540>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 107, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('0', '0', 1)
                   │    │               └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026A37BE84A0>
  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 619, in read_packet
    packet_header = await self._read_bytes(4)
  File "asyncmy\\connection.pyx", line 660, in _read_bytes
    raise errors.OperationalError(
          │      └ <class 'asyncmy.errors.OperationalError'>
          └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\asyncmy\\errors.cp39-win_...

asyncmy.errors.OperationalError: (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "C:\Program Files\Python39\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 172
               │     └ 3
               └ <function _main at 0x0000026A0848A700>

  File "C:\Program Files\Python39\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 172
           │    └ <function BaseProcess._bootstrap at 0x0000026A083EC8B0>
           └ <SpawnProcess name='SpawnProcess-1' parent=11312 started>

  File "C:\Program Files\Python39\lib\multiprocessing\process.py", line 315, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000026A083E0EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=11312 started>

  File "C:\Program Files\Python39\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000026A084A1940>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=11312 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=11312 started>
    │    └ <function subprocess_started at 0x0000026A0A804670>
    └ <SpawnProcess name='SpawnProcess-1' parent=11312 started>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=2056, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000026A084A1A60>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=2056, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x0000026A0A7F7700>
           │       │   └ <uvicorn.server.Server object at 0x0000026A084A1A60>
           │       └ <function run at 0x0000026A084B38B0>
           └ <module 'asyncio' from 'C:\\Program Files\\Python39\\lib\\asyncio\\__init__.py'>

  File "C:\Program Files\Python39\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000026A37635A40>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000026A0A04AB80>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\Python39\lib\asyncio\base_events.py", line 629, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000026A0A04AAF0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\Python39\lib\asyncio\base_events.py", line 596, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026A0A04D670>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\Python39\lib\asyncio\base_events.py", line 1890, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026A09FEB280>
    └ <Handle <TaskWakeupMethWrapper object at 0x0000026A37BCFCA0>(<Future finished result=None>)>

  File "C:\Program Files\Python39\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <TaskWakeupMethWrapper object at 0x0000026A37BCFCA0>(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <TaskWakeupMethWrapper object at 0x0000026A37BCFCA0>(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <TaskWakeupMethWrapper object at 0x0000026A37BCFCA0>(<Future finished result=None>)>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000026A3774AE20>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026A37...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000026A084A1E50>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000026A3774AE20>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026A37...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026A37...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000026A378317C0>
          └ <fastapi.applications.FastAPI object at 0x0000026A084A1E50>
> File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000026A3789C280>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000026A37831910>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000026A378317C0>

  File "H:\项目\金刚\舆情\thinktankapi\middlewares\trace_middleware\middle.py", line 47, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000026A37950AF0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000026A37950940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x0000026A37831940>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000026A37831910>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000026A37950AF0>
          │         │      └ <function RequestResponseCycle.receive at 0x0000026A37950940>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000026A378516A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x0000026A352D9160>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x0000026A378516A0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000026A37950940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026A378319A0>
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000026A378516A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000026A378516A0>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000026A37950940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026A378319D0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026A378319A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000026A378516A0>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x0000026A37950940>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026A37851520>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026A373FFB50>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026A378319D0>
          └ <function wrap_app_handling_exceptions at 0x0000026A0B7E2310>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026A37950CA0>
          │   │      └ <function RequestResponseCycle.receive at 0x0000026A37950940>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026A373FFB50>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026A37950CA0>
          │    │                │      └ <function RequestResponseCycle.receive at 0x0000026A37950940>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026A373FFB50>>
          └ <fastapi.routing.APIRouter object at 0x0000026A373FFB50>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026A37950CA0>
          │     │      │      └ <function RequestResponseCycle.receive at 0x0000026A37950940>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x0000026A0B7F8A60>
          └ APIRoute(path='/getInfo', name='get_login_user_info', methods=['GET'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026A37950CA0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000026A37950940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000026A3741B310>
          └ APIRoute(path='/getInfo', name='get_login_user_info', methods=['GET'])
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026A37950CA0>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x0000026A37950940>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000026A37B93AC0>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000026A37B06430>
          └ <function wrap_app_handling_exceptions at 0x0000026A0B7E2310>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026A37A99B80>
          │   │      └ <function RequestResponseCycle.receive at 0x0000026A37950940>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000026A37B06430>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000026A37B93AC0>
                     └ <function get_request_handler.<locals>.app at 0x0000026A3741B1F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\fastapi\routing.py", line 291, in app
    solved_result = await solve_dependencies(
                          └ <function solve_dependencies at 0x0000026A0B7B4EE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\fastapi\dependencies\utils.py", line 638, in solve_dependencies
    solved = await call(**solved_result.values)
                   │      │             └ {'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMSIsInVzZXJfbmFtZSI6ImFkbWluIiwiZGVwdF9uYW1lIjoiXHU3ODE0XHU1M...
                   │      └ SolvedDependency(values={'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMSIsInVzZXJfbmFtZSI6ImFkbWluIiwiZGVwd...
                   └ <bound method LoginService.get_current_user of <class 'module_admin.service.login_service.LoginService'>>

  File "H:\项目\金刚\舆情\thinktankapi\module_admin\service\login_service.py", line 212, in get_current_user
    query_user = await UserDao.get_user_by_id(query_db, user_id=token_data.user_id)
                       │       │              │                 │          └ 1
                       │       │              │                 └ TokenData(user_id=1)
                       │       │              └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000026A37BD1520>
                       │       └ <classmethod object at 0x0000026A364B50A0>
                       └ <class 'module_admin.dao.user_dao.UserDao'>

  File "H:\项目\金刚\舆情\thinktankapi\module_admin\dao\user_dao.py", line 89, in get_user_by_id
    await db.execute(
          │  └ <function AsyncSession.execute at 0x0000026A0C5573A0>
          └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000026A37BD1520>

  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\ext\asyncio\session.py", line 463, in execute
    result = await greenlet_spawn(
                   └ <function greenlet_spawn at 0x0000026A0BA6B9D0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x0000026A37BBE100 (otid=0x0000026A0BA2E0F0) dead>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           │    └ <function Session._execute_internal at 0x0000026A0C477DC0>
           └ <sqlalchemy.orm.session.Session object at 0x0000026A37BD1370>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                   │      │                 └ <classmethod object at 0x0000026A0C30C940>
                   │      └ <class 'sqlalchemy.orm.context.ORMSelectCompileState'>
                   └ typing.Any
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\orm\context.py", line 305, in orm_execute_statement
    result = conn.execute(
             │    └ <function Connection.execute at 0x0000026A0C0AA940>
             └ <sqlalchemy.engine.base.Connection object at 0x0000026A37BCF220>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
           └ <bound method ClauseElement._execute_on_connection of <sqlalchemy.sql.selectable.Select object at 0x0000026A37BD1EB0>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           │          └ <function Connection._execute_clauseelement at 0x0000026A0C0AAC10>
           └ <sqlalchemy.engine.base.Connection object at 0x0000026A37BCF220>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
          │    └ <function Connection._execute_context at 0x0000026A0C0AADC0>
          └ <sqlalchemy.engine.base.Connection object at 0x0000026A37BCF220>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           │    └ <function Connection._exec_single_context at 0x0000026A0C0AAE50>
           └ <sqlalchemy.engine.base.Connection object at 0x0000026A37BCF220>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    │    └ <function Connection._handle_dbapi_exception at 0x0000026A0C0AD0D0>
    └ <sqlalchemy.engine.base.Connection object at 0x0000026A37BCF220>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ OperationalError(2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
          │                    │              └ (<class 'asyncmy.errors.OperationalError'>, OperationalError(2013, 'Lost connection to MySQL server during query ([WinError 1...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ OperationalError("(asyncmy.errors.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x0000026A0C169790>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x0000026A0C188DF0>
    └ <sqlalchemy.engine.base.Connection object at 0x0000026A37BCF220>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('0', '0', 1)
    │      │       └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x0000026A0C5E10D0>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026A37BE84A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 95, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('0', '0', 1)
           │    │      │    │              └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x0000026A0C5E11F0>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026A37BE84A0>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026A37BE84A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x0000026A37847540>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x0000026A37BBE100 (otid=0x0000026A0BA2E0F0) dead>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x0000026A37847540>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 107, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('0', '0', 1)
                   │    │               └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026A37BE84A0>
  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 619, in read_packet
    packet_header = await self._read_bytes(4)
  File "asyncmy\\connection.pyx", line 660, in _read_bytes
    raise errors.OperationalError(
          │      └ <class 'asyncmy.errors.OperationalError'>
          └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\asyncmy\\errors.cp39-win_...

sqlalchemy.exc.OperationalError: (asyncmy.errors.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.email, sys_user.phonenumber, sys_user.sex, sys_user.avatar, sys_user.password, sys_user.status, sys_user.del_flag, sys_user.login_ip, sys_user.login_date, sys_user.create_by, sys_user.create_time, sys_user.update_by, sys_user.update_time, sys_user.remark 
FROM sys_user 
WHERE sys_user.status = %s AND sys_user.del_flag = %s AND sys_user.user_id = %s]
[parameters: ('0', '0', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-22 16:49:19.743 | f2d3bd014b254a3f8e454e7c336c630e | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-05-22 16:49:19.778 | aa6a89c790aa4e7086014544af56cb09 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为1dd4baf6-3d79-4427-b59d-dfcfdcfb98d8的会话获取图片验证码成功
2025-05-22 16:51:36.247 | 5e3c3e1d0dba43d39910eb0a481e6c1a | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为eb7e1d3f-0486-4390-950a-138d68b326f6的会话获取图片验证码成功
2025-05-22 16:52:53.019 | 823ad29b608d46289987aa446f2efcc1 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为9d164341-1967-419b-bc18-bf6eb7499868的会话获取图片验证码成功
2025-05-22 16:57:09.827 | 34495152b1c74701b496150e0b8d6a4e | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为fa23fc95-b19b-46a7-b82c-3363c86d5662的会话获取图片验证码成功
2025-05-22 16:57:14.767 | 7535ea06e16a428b8a73e67730648cd0 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-22 16:57:15.157 | ba2abd0d2c0d42dea443c9758c581b87 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 16:57:15.498 | 4de19fa35456476e9260edadc8a4570f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 16:57:45.321 | d3a065c4a36f4272b0cb89931e646231 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 16:57:45.643 | 64b544dc3cf24c11b2cb76b607a4b2f6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 16:58:30.276 | 4637da9eb20740108ac7b8924513a978 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 16:58:30.924 | b6fb495dbdf74b5c8d4ebd4c91028af9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 16:58:52.456 | b1741b7556294e7c93c21343461ecc0f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 16:58:52.772 | 33fe9334b9734864a090189be8051b55 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 16:59:07.481 | aca8d20fe9a744999d4eecfcc0a613fe | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 16:59:07.790 | 5e0bf9bb3ce44721ba2f7013ce6511e3 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:02:22.537 | 81054cb1d2814135b40d674a5f89fe6a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:02:23.016 | fb35af3d44af41d8b92c3fe7f1601072 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:02:31.955 | cf9d36b1ca674021bab1916db3329217 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:02:32.926 | 51fe772e21d242fdb85f0086c6daf250 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:02:44.344 | a1a314be168c4c4c81f9f7da2918277b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:02:45.539 | e3f1005e4ca4446ebe7867a18c3cf7b0 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:05:48.494 | 121ee56670954be3a646a5fdcf98c6cd | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:05:48.802 | 71a163230ce24cd1ad816340ea55994d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:06:17.415 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-05-22 17:06:17.418 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-05-22 17:06:31.973 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-05-22 17:06:31.975 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-05-22 17:06:33.049 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-05-22 17:06:33.050 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-05-22 17:06:33.052 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-05-22 17:06:33.618 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-05-22 17:06:34.325 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-05-22 17:06:34.325 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-05-22 17:06:45.022 | 5da1ebb3acbe4a9086cd4627ce66aa35 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为84b4d258-7d44-446c-b892-3b5109cdd01a的会话获取图片验证码成功
2025-05-22 17:06:49.384 | c4421bc21f1c46d4bd29ab3f65d1f358 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-22 17:06:49.763 | 64e570967a6948b9b660b7e166a93938 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:06:49.997 | 0e1142e557d64d4491bcdb69c83f1a7d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:10:08.319 | 4a7661182d3a456eb7d8595dfe9f95d9 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:10:08.980 | 4fc61a35d7d14185a82476fd90f6030f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:10:27.027 | ef284480c8ea44b69ab645ee46cda346 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:10:27.441 | 46cf4dabc2bf4224a92e98ac021206e5 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:10:37.108 | 83b552a9839f4477b597aed898604907 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:10:37.429 | bc366164e848461ba00f464604faba0a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:14:53.604 | ef78c0061ab44171b70d75e3d9f06408 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:14:53.836 | 8c0e61f178f54cbcaba2061c4d6e8dcb | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:14:59.896 | 8117e670ce774845a3a2d67688ea8872 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:15:00.249 | 7eced394ac114ee8967d1e1e64193bf3 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:15:30.540 | 7ec5bb8f27344a4c91868c54210c833d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:15:30.768 | 596265c4f1b2448e867ff7d45d29ac41 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:16:01.373 | 85e3da5c7ec84facba3798f955909300 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:16:01.579 | f6f2a685a9724cb9a07453600bb84dcd | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:17:24.553 | 60ca9e484e7344c8a481808a85055aa0 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:17:24.768 | f5cf0e1bb3bd412c876dff40a68c665c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:18:12.789 | 37812e7c495547bfaae855b3f1437416 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:18:13.242 | c3589fb028f64171a7d24d43f042dd64 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:18:56.317 | f52cef8e3bd7434086dcd3b2561cd7f5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:18:56.552 | 517c6e4fd0c6474d9ea71be01bf41ac7 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:19:30.889 | 58d490b8e08143b7bfa59f53319006ed | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:19:31.132 | fd71a463cb9146a6ae9860af024d7395 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:21:11.480 | 404b5d5584da478980aa7f36076d9681 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:21:11.705 | b4774e2b7851431687445a59d3b49645 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:22:42.978 | c14676d9c8264a3c8aa18fb8aaeb114d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:22:43.242 | fd72c5d7ec6848deb6820982f336d7d6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:30:16.529 | b7094244769042abaece1987e4428935 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:30:16.927 | 7181e2cde90047bbbe8584198c585e82 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:33:28.453 | 58fa9d74682147348b98a35ea4029dd5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:33:28.699 | 2151657035da4951a13cef9033b82d25 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:34:09.718 | a6616e0067fd44a38470cbc603b6b568 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:34:09.941 | 2606c9e286164de1b6a8c970bf958bf6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:35:22.761 | 733a03d4d80a4cbe896fa63bc011e912 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:35:24.907 | d7a6f7cfa04d4a0c96714f15ff5a8f2d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:37:29.851 | a5d850ee78244e16a3a3e358e989e647 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:37:30.073 | 29dccde0d9cd47a582d8330530d751b1 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:42:11.849 | 5e58d8d2c990436a97f5fb2029b6ec1c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:42:12.096 | d6cabb6495764374b2f0ad5a43f16ff8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:42:16.058 | 952198c9c5d8451b9eb197ceea4d9533 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:42:16.301 | 4c3fcc0692354f78ac35618823d5752d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:44:20.107 | 4e8ebc0bdce6406ea6a97da78c09b5f5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:44:20.393 | 0c3ed81d2309450e89dbd1480de56df9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:47:49.492 | a5123c3e4d03490dae8bd49a96e0fefe | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:47:49.698 | 3266d6a473ec4532b1cbfe1c3ca53d33 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:48:31.894 | 8ad4dee4410748669d484aeb9868f985 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:48:32.129 | be0e9a69788b4696a5c3d6db98977904 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:48:44.705 | 92390ba378be40db9edb380548a7a109 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:48:44.935 | 022b3301286f4096993abaacf99d5360 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:53:34.795 | 39f4d3d6930b4df6bcf76b028b95103b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 17:53:35.163 | d4e2f57ed844414d884e66d7569d065b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 17:56:19.661 | 34bdab8581d34dda98fd46ddaa8b2a60 | INFO     | module_admin.controller.user_controller:query_detail_system_user_profile:210 - 获取user_id为1的信息成功
2025-05-22 18:01:28.740 | e123dced2b114b7c935054d20a37191d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:01:28.968 | 945fbb6cac6b40de86f10c45b936c0f8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:01:29.637 | a45dff3477734c529480814ebad1fae1 | INFO     | module_admin.controller.user_controller:query_detail_system_user_profile:210 - 获取user_id为1的信息成功
2025-05-22 18:02:19.986 | 09d18c09573e48ae89c9eb5dd75b58bf | INFO     | module_admin.controller.user_controller:query_detail_system_user_profile:210 - 获取user_id为1的信息成功
2025-05-22 18:02:22.535 | b33e3ee36e304046ab324a076cecdc6d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:02:22.749 | f8db28b710e14c2492a7136553fbb6c7 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:02:28.464 | accef96b8353403d988a4029373fbe13 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:02:28.680 | c17b602278854658b10c3a797fd3dc0a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:02:38.201 | 69435482ff6540b18c934a1a4c1dde60 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:02:39.462 | 0afa7e50e81e4f629cab59a5368864f6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:03:18.663 | 960c413260544e35b5a158900a5e0ae9 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:03:18.969 | 3b705827a9fa4aaea99b51f057de76f9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:03:22.999 | 2a6a6d75c03f443abdea90c3f4ee4d25 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:03:23.342 | f49a97f4b95c401b8299bd2f62f4b69d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:03:24.210 | d06f4f41fc8b46bf830f75fa9817125e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:03:24.496 | 18195fc774d34b379288b3757560a1ad | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:03:26.303 | 15682cf3cae940cab0bef9b1ca635b20 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为7e1fb8d1-78f0-4389-bd25-3ba0857eb9cc的会话获取图片验证码成功
2025-05-22 18:03:43.775 | 0407deb8ef4c4d86ad867e3395dc4cee | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:03:44.247 | cc7dd4e26ced48f6834edc6066c20840 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:04:43.870 | 55365716db80424380aa6707407d9267 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:04:44.180 | 22a5a8dd4b0b44cba9d7f7e8572cfe7b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:04:53.993 | 9a3fcda81e8c44e1b6c5ad4884bb0ec6 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:04:54.315 | d7663013fc8c4e72878238fc051e2e9e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:10:46.944 | 16e11834c3c941f8af817d6c844f8470 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:10:47.215 | 1d3dc3469f6349e6bfc79679767adbb7 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:12:22.141 | bc70889bd1e84946b5637cb78caa52d8 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:12:22.412 | 27b0d7d713a248499115d66e6f6b3035 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:17:59.185 | cc12a0896860410a8ec1835cf3551335 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:17:59.666 | 4b4a29e0a303440c8d16de05b72414dd | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:18:25.422 | 9a4410c46a6943bd922ca42638f08638 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:18:25.759 | d0008a307268420bab4fedce3409e0c8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:18:59.399 | ae470173e51b4899912878468bebb4a4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:18:59.651 | b3e3136512374318b8fb6bb95bd184b6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:19:09.326 | c4612ba6358a4363b24b30e21a456620 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:19:09.613 | e02b0942340f4078b4088ef2668eccee | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:22:05.559 | d29449aebec24847a161fa8827d1534f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:22:05.916 | 2deab58fb8be4aeea1ba9f712c715449 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:26:16.794 | 17c3244f43ca45f280db72b1e2abd582 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:26:17.090 | fedc3dd1dcff4c59a91da9d54b7fa5c6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:28:04.748 | 8203b680c5a14a6c8e9727c69d8793b9 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:28:05.011 | 83776b703e274f748129743d075d0192 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:29:33.247 | 9244acc5367244c4a6f9a61d4a936d9a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:29:33.503 | 69fe5d2416f74ab3a7c63502a34db2bb | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:33:11.944 | ae293f83588a48c99f4d9ebacbd2ba0f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:33:12.211 | 6b516a5f483e4a2ab02cd00133ae45ec | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:36:59.277 | 1549caf5e7f3429baa8bbbf8dbee173a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:36:59.548 | 663e0be7f9c9473a9cbfc925fe9cd091 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:47:35.803 | cccbdcc9b9f548ddbe4baf6d7f6d6df9 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:47:36.064 | ae63c29cedc54bfebabd6244b899e509 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:49:32.987 | 659c3574e72847388226d9a25001634b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:49:33.525 | 3125f8e0e42d4dd4b54494c4ffc45d07 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:51:52.991 | 5052faa1cc2f48bc9486fb94889f85ba | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:51:53.257 | 7db436e6872e42b4aec8942023f168d3 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 18:53:12.099 | aa46bda643b24ef4831d8cb239b2edf3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 18:53:15.350 | 2c64e39f6a2b496a8b177942adef8719 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-22 19:02:42.855 | ac55b6a62c8a4135a16154946b71b6f0 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-22 19:02:43.141 | 5626239b8a2b4192ac762ac992ec0924 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
