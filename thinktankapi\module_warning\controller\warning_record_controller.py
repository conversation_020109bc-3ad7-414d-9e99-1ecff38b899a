from datetime import datetime
from fastapi import APIRouter, Depends, Request, Form
from pydantic_validation_decorator import <PERSON><PERSON><PERSON><PERSON><PERSON>s
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.service.login_service import LoginService
from module_warning.service.warning_record_service import WarningRecordService
from module_warning.entity.vo.warning_vo import (
    DeleteWarningRecordModel,
    WarningRecordModel,
    WarningRecordPageQueryModel
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


warningRecordController = APIRouter(prefix='/warning/record', dependencies=[Depends(LoginService.get_current_user)])


@warningRecordController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('warning:record:list'))]
)
async def get_warning_record_list(
    request: Request,
    record_page_query: WarningRecordPageQueryModel = Depends(WarningRecordPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取预警记录列表
    """
    try:
        # 获取分页数据
        record_page_query_result = await WarningRecordService.get_warning_record_list_services(
            query_db, record_page_query, is_page=True
        )
        logger.info('获取预警记录列表成功')
        return ResponseUtil.success(model_content=record_page_query_result)
    except Exception as e:
        logger.error(f'获取预警记录列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警记录列表失败: {str(e)}')


@warningRecordController.post('/export', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:export'))])
@Log(title='预警记录', business_type=BusinessType.EXPORT)
async def export_warning_record(
    request: Request,
    record_page_query: WarningRecordPageQueryModel = Form(),
    query_db: AsyncSession = Depends(get_db),
):
    """
    导出预警记录
    """
    try:
        # 获取所有数据（不分页）
        record_list_result = await WarningRecordService.get_warning_record_list_services(
            query_db, record_page_query, is_page=False
        )
        # 导出为Excel文件
        record_export_result = await WarningRecordService.export_warning_record_list_services(record_list_result)
        logger.info('导出预警记录成功')
        return ResponseUtil.streaming(data=bytes2file_response(record_export_result))
    except Exception as e:
        logger.error(f'导出预警记录失败: {str(e)}')
        return ResponseUtil.error(msg=f'导出预警记录失败: {str(e)}')


@warningRecordController.get(
    '/{record_id}', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:query'))]
)
async def get_warning_record_detail(
    request: Request,
    record_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取预警记录详情
    """
    try:
        record_detail_result = await WarningRecordService.get_warning_record_detail_services(query_db, record_id)
        logger.info(f'获取预警记录详情成功，记录ID: {record_id}')
        return ResponseUtil.success(data=record_detail_result)
    except Exception as e:
        logger.error(f'获取预警记录详情失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警记录详情失败: {str(e)}')


@warningRecordController.post('', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:add'))])
@ValidateFields(validate_model='add_warning_record')
@Log(title='预警记录', business_type=BusinessType.INSERT)
async def add_warning_record(
    request: Request,
    add_record: WarningRecordModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增预警记录
    """
    try:
        add_record.create_by = current_user.user.user_name
        add_record.create_time = datetime.now()
        add_record.update_by = current_user.user.user_name
        add_record.update_time = datetime.now()
        add_record_result = await WarningRecordService.add_warning_record_services(query_db, add_record)
        logger.info(add_record_result.message)
        return ResponseUtil.success(msg=add_record_result.message)
    except Exception as e:
        logger.error(f'新增预警记录失败: {str(e)}')
        return ResponseUtil.error(msg=f'新增预警记录失败: {str(e)}')


@warningRecordController.put('', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:edit'))])
@ValidateFields(validate_model='edit_warning_record')
@Log(title='预警记录', business_type=BusinessType.UPDATE)
async def edit_warning_record(
    request: Request,
    edit_record: WarningRecordModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑预警记录
    """
    try:
        edit_record.update_by = current_user.user.user_name
        edit_record.update_time = datetime.now()
        edit_record_result = await WarningRecordService.edit_warning_record_services(query_db, edit_record)
        logger.info(edit_record_result.message)
        return ResponseUtil.success(msg=edit_record_result.message)
    except Exception as e:
        logger.error(f'编辑预警记录失败: {str(e)}')
        return ResponseUtil.error(msg=f'编辑预警记录失败: {str(e)}')


@warningRecordController.delete('', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:remove'))])
@ValidateFields(validate_model='delete_warning_record')
@Log(title='预警记录', business_type=BusinessType.DELETE)
async def delete_warning_record(
    request: Request,
    delete_record: DeleteWarningRecordModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除预警记录
    """
    try:
        delete_record_result = await WarningRecordService.delete_warning_record_services(query_db, delete_record)
        logger.info(delete_record_result.message)
        return ResponseUtil.success(msg=delete_record_result.message)
    except Exception as e:
        logger.error(f'删除预警记录失败: {str(e)}')
        return ResponseUtil.error(msg=f'删除预警记录失败: {str(e)}')


@warningRecordController.get(
    '/statistics', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:list'))]
)
async def get_warning_statistics(
    request: Request,
    scheme_id: int = None,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取预警统计数据
    """
    try:
        statistics_result = await WarningRecordService.get_warning_statistics_services(query_db, scheme_id)
        logger.info(f'获取预警统计数据成功，方案ID: {scheme_id}')
        return ResponseUtil.success(data=statistics_result)
    except Exception as e:
        logger.error(f'获取预警统计数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警统计数据失败: {str(e)}')


@warningRecordController.put(
    '/{record_id}/status', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:edit'))]
)
@Log(title='预警记录状态', business_type=BusinessType.UPDATE)
async def update_warning_record_status(
    request: Request,
    record_id: int,
    status: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新预警记录状态
    """
    try:
        update_result = await WarningRecordService.update_warning_record_status_services(
            query_db, record_id, status, current_user.user.user_name
        )
        logger.info(update_result.message)
        return ResponseUtil.success(msg=update_result.message)
    except Exception as e:
        logger.error(f'更新预警记录状态失败: {str(e)}')
        return ResponseUtil.error(msg=f'更新预警记录状态失败: {str(e)}')



