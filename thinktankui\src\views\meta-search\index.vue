<template>
  <div class="meta-search-container">
    <!-- API状态提示 -->
    <div v-if="!enableAPI" class="api-status-tip" style="background: #f0f9ff; border: 1px solid #0ea5e9; color: #0369a1; padding: 8px 16px; margin-bottom: 16px; border-radius: 4px; font-size: 14px;">
      <i class="el-icon-info" style="margin-right: 8px;"></i>
      当前使用模拟数据模式，所有搜索结果均为演示数据
      <el-button type="text" size="mini" style="margin-left: 16px; color: #0369a1;" @click="enableAPI = true; loadRealData()">
        启用真实API
      </el-button>
    </div>

    <!-- 顶部搜索区域 -->
    <div class="search-header">
      <div class="search-tabs">
        <div
          class="tab"
          :class="{ active: activeTab === 'fulltext' }"
          @click="switchTab('fulltext')"
        >
          全文检索
        </div>
        <div
          class="tab"
          :class="{ active: activeTab === 'meta' }"
          @click="switchTab('meta')"
        >
          元搜索
        </div>
      </div>

      <div class="search-box">
        <input
          type="text"
          class="search-input"
          v-model="searchKeyword"
          placeholder="请输入搜索关键词"
          @keyup.enter="handleSearch"
        />
        <el-button type="primary" class="search-btn" @click="handleSearch">搜索</el-button>
      </div>
    </div>

    <!-- 全文检索结果 -->
    <div v-if="activeTab === 'fulltext' && hasSearched" class="fulltext-results">
      <!-- 筛选条件区域 -->
      <div class="filter-section">
        <!-- 时间筛选 -->
        <div class="filter-row">
          <span class="filter-label">时间范围:</span>
          <div class="filter-options">
            <el-button
              v-for="time in timeOptions"
              :key="time.value"
              :type="selectedTime === time.value ? 'primary' : ''"
              size="small"
              @click="selectTime(time.value)"
            >
              {{ time.label }}
            </el-button>
          </div>
        </div>

        <!-- 平台筛选 -->
        <div class="filter-row">
          <span class="filter-label">平台类型:</span>
          <div class="filter-options">
            <el-button
              v-for="platform in platformOptions"
              :key="platform.value"
              :type="selectedPlatform === platform.value ? 'primary' : ''"
              size="small"
              @click="selectPlatform(platform.value)"
            >
              {{ platform.label }}
              <span v-if="platform.count" class="count">({{ platform.count }})</span>
            </el-button>
          </div>
        </div>

        <!-- 情感筛选 -->
        <div class="filter-row">
          <span class="filter-label">情感类型:</span>
          <div class="filter-options">
            <el-button
              v-for="emotion in emotionOptions"
              :key="emotion.value"
              :type="selectedEmotion === emotion.value ? 'primary' : ''"
              size="small"
              @click="selectEmotion(emotion.value)"
            >
              {{ emotion.label }}
              <span v-if="emotion.count" class="count">({{ emotion.count }})</span>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 结果统计 -->
      <div class="result-stats">
        <span>共{{ totalResults }}条结果</span>
        <div class="action-buttons">
          <el-button size="small">导出</el-button>
          <el-button type="primary" size="small">分析</el-button>
        </div>
      </div>

      <!-- 搜索结果列表 -->
      <div class="results-list" v-loading="loading">
        <div v-for="(item, index) in searchResults" :key="index" class="result-item">
          <div class="result-header">
            <h3 class="result-title">{{ item.title }}</h3>
            <div class="result-actions">
              <el-button type="text" icon="el-icon-view"></el-button>
            </div>
          </div>

          <div class="result-meta">
            <span class="meta-item">{{ item.source }}</span>
            <span class="meta-item">{{ item.publishTime }}</span>
            <span class="meta-item">{{ item.author }}</span>
            <span class="meta-item">{{ item.platform }}</span>
            <span class="meta-item">阅读量: {{ item.readCount }}</span>
            <span class="meta-item">{{ item.location }}</span>
            <span class="meta-item">{{ item.category }}</span>
          </div>

          <div class="result-content">
            {{ item.content }}
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="totalResults"
          :current-page.sync="currentPage"
          :page-size="pageSize"
          @current-change="handlePageChange"
        ></el-pagination>
      </div>
    </div>

    <!-- 元搜索结果 -->
    <div v-if="activeTab === 'meta' && hasSearched" class="meta-results">
      <!-- 搜索引擎选项卡 -->
      <div class="search-engines">
        <div
          v-for="engine in searchEngines"
          :key="engine.id"
          class="engine-item"
          :class="{ active: engine.id === activeEngine }"
          @click="toggleEngine(engine.id)"
        >
          <div class="checkbox">
            <i class="el-icon-check" v-if="selectedEngines.includes(engine.id)"></i>
          </div>
          <img :src="engine.icon" :alt="engine.name" class="engine-icon" />
          <span class="engine-name">{{ engine.name }}</span>
        </div>
      </div>

      <!-- 搜索结果展示区 -->
      <div class="results-container">
        <!-- 左侧搜索结果 -->
        <div class="result-column" v-if="selectedEngines.includes('bing')">
          <div class="result-header">
            <div class="result-icon bing-icon"></div>
            <span class="result-title">Microsoft Bing</span>
          </div>
          <div class="result-list">
            <div class="result-item" v-for="(item, index) in bingResults" :key="'bing-'+index">
              <h3 class="item-title">
                <a :href="item.link" target="_blank">{{ item.title }}</a>
              </h3>
              <div class="item-url">{{ item.url }}</div>
              <div class="item-desc">{{ item.description }}</div>
            </div>
          </div>
        </div>

        <!-- 中间搜索结果 -->
        <div class="result-column" v-if="selectedEngines.includes('baidu')">
          <div class="result-header">
            <div class="result-icon baidu-icon"></div>
            <span class="result-title">百度搜索</span>
          </div>
          <div class="result-list">
            <div class="result-item" v-for="(item, index) in baiduResults" :key="'baidu-'+index">
              <h3 class="item-title">
                <a :href="item.link" target="_blank">{{ item.title }}</a>
              </h3>
              <div class="item-url">{{ item.url }}</div>
              <div class="item-desc">{{ item.description }}</div>
            </div>
          </div>
        </div>

        <!-- 右侧搜索结果 -->
        <div class="result-column" v-if="selectedEngines.includes('360')">
          <div class="result-header">
            <div class="result-icon so360-icon"></div>
            <span class="result-title">360搜索</span>
          </div>
          <div class="result-list">
            <div class="result-item" v-for="(item, index) in so360Results" :key="'360-'+index">
              <h3 class="item-title">
                <a :href="item.link" target="_blank">{{ item.title }}</a>
              </h3>
              <div class="item-url">{{ item.url }}</div>
              <div class="item-desc">{{ item.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 完全移除API导入，使用纯模拟数据模式
// import {
//   getSimpleFulltextSearch,
//   getSimpleMetaSearch,
//   getSimpleStatistics,
//   getSearchFilters
// } from '@/api/meta-search'

export default {
  name: 'MetaSearch',
  // 禁用字典数据功能，避免自动调用字典API
  dicts: null,
  data() {
    return {
      activeTab: 'fulltext', // 默认显示全文检索
      searchKeyword: '方太',
      hasSearched: false,
      loading: false,
      enableAPI: false, // API开关，默认关闭，使用模拟数据

      // 全文检索相关数据
      selectedTime: '24h',
      selectedPlatform: 'all',
      selectedEmotion: 'all',
      currentPage: 1,
      pageSize: 10,
      totalResults: 10000,

      timeOptions: [
        { label: '24小时', value: '24h' },
        { label: '一周', value: '1w' },
        { label: '半年', value: '6m' },
        { label: '一年', value: '1y' },
        { label: '自定义', value: 'custom' }
      ],

      platformOptions: [
        { label: '全部', value: 'all', count: 10540 },
        { label: '微信', value: 'wechat', count: 1847 },
        { label: '微博', value: 'weibo', count: 2008 },
        { label: '客户端', value: 'app', count: 1748 },
        { label: '论坛', value: 'forum', count: 673 }
      ],

      emotionOptions: [
        { label: '全部', value: 'all' },
        { label: '正面', value: 'positive' },
        { label: '负面', value: 'negative' },
        { label: '中性', value: 'neutral' }
      ],

      searchResults: [
        {
          title: '从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...',
          source: '新华网',
          publishTime: '2022-06-29 20:07:04',
          author: '77人讨论',
          platform: '平台来源',
          readCount: '无',
          location: '无所在地',
          category: '新闻',
          content: '从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...'
        },
        {
          title: '中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文',
          source: '中大论文发表',
          publishTime: '2022-06-29 20:07:04',
          author: '77人讨论',
          platform: '平台来源',
          readCount: '无',
          location: '无所在地',
          category: '论文',
          content: '中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文...'
        },
        {
          title: '转发微博#中#大学生，人情世故。',
          source: '微博',
          publishTime: '2022-06-29 20:07:04',
          author: '77人讨论',
          platform: '微博',
          readCount: '1000',
          location: '北京',
          category: '社交媒体',
          content: '转发微博#中#大学生，人情世故。这是一条关于大学生人际关系的微博内容...'
        }
      ],

      // 元搜索相关数据
      activeEngine: 'bing',
      selectedEngines: ['bing', 'baidu', '360'],
      searchEngines: [
        { id: 'bing', name: 'Microsoft Bing', icon: 'bing-icon' },
        { id: 'baidu', name: '百度搜索', icon: 'baidu-icon' },
        { id: '360', name: '360搜索', icon: 'so360-icon' }
      ],
      bingResults: [
        {
          title: '方太官网_高端全场景厨电',
          url: 'https://www.fotile.com',
          link: 'https://www.fotile.com',
          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'
        },
        {
          title: '【方太集团官网】',
          url: 'https://www.fotile.com/about',
          link: 'https://www.fotile.com/about',
          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'
        },
        {
          title: '不平凡',
          url: 'https://www.fotile.com/product',
          link: 'https://www.fotile.com/product',
          description: '方太高端厨电，专注高端厨房电器20年，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'
        }
      ],
      baiduResults: [
        {
          title: '方太官网_高端全场景厨电',
          url: 'https://www.fotile.com',
          link: 'https://www.fotile.com',
          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'
        },
        {
          title: '方太厨电 高端集成厨房电器品牌',
          url: 'https://www.fotile.com/product',
          link: 'https://www.fotile.com/product',
          description: '全国服务热线：400-315-0000 方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'
        },
        {
          title: '方太 - 百度百科',
          url: 'https://baike.baidu.com/item/方太/1830',
          link: 'https://baike.baidu.com/item/方太/1830',
          description: '方太，是中国高端厨电领导品牌，创立于1996年，总部位于浙江宁波，是一家集研发、生产、销售于一体的现代化企业，主要产品包括吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'
        }
      ],
      so360Results: [
        {
          title: '方太官网_高端全场景厨电',
          url: 'https://www.fotile.com',
          link: 'https://www.fotile.com',
          description: '方太——专业生产高端厨房电器的领导品牌，主营产品：吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器，方太厨电，方太集成烹饪中心...'
        },
        {
          title: '方太厨电旗舰店-天猫',
          url: 'https://fotile.tmall.com',
          link: 'https://fotile.tmall.com',
          description: '方太厨电旗舰店,提供方太油烟机,方太燃气灶,方太消毒柜,方太洗碗机,方太蒸箱,方太烤箱,方太微波炉,方太水槽洗碗机等产品。天猫正品保障,提供...'
        },
        {
          title: '方太集团有限公司',
          url: 'https://www.fotile.com/about',
          link: 'https://www.fotile.com/about',
          description: '方太集团有限公司创立于1996年，总部位于浙江宁波，是一家集研发、生产、销售于一体的现代化企业，主要产品包括吸油烟机、燃气灶、消毒柜、蒸箱、烤箱、洗碗机等厨房电器...'
        }
      ]
    }
  },
  mounted() {
    console.log('Meta-Search页面加载，完全使用模拟数据模式')

    // 立即使用默认数据，确保页面能立即显示
    this.useDefaultFilters()

    // 完全禁用API，只使用模拟数据
    console.log('完全使用模拟数据，无任何API依赖')

    // 如果有默认搜索词，使用模拟数据执行搜索
    if (this.searchKeyword) {
      setTimeout(() => {
        this.handleSearchWithMockData()
      }, 500)
    }

    // 显示欢迎信息
    this.$nextTick(() => {
      this.$message({
        message: '欢迎使用元搜索系统！当前为演示模式，所有数据均为模拟数据。',
        type: 'info',
        duration: 3000
      })
    })
  },
  methods: {
    // 加载真实数据（后台静默加载）
    async loadRealData() {
      if (!this.enableAPI) {
        console.log('API已禁用，跳过真实数据加载')
        return
      }

      try {
        console.log('开始后台加载真实数据...')

        // 静默尝试加载真实数据
        const results = await Promise.allSettled([
          this.loadFiltersWithRetry(1), // 只重试1次
          this.loadStatisticsWithRetry(1)
        ])

        // 检查加载结果
        const successCount = results.filter(result => result.status === 'fulfilled').length
        const failedCount = results.filter(result => result.status === 'rejected').length

        if (successCount > 0) {
          console.log(`成功加载 ${successCount} 个真实数据源`)
        }
        if (failedCount > 0) {
          console.warn(`${failedCount} 个API调用失败，继续使用默认数据`)
        }

        // 如果有默认搜索词，执行搜索
        if (this.searchKeyword) {
          setTimeout(() => {
            this.handleSearch()
          }, 500)
        }

        console.log('真实数据加载完成')
      } catch (error) {
        console.error('加载真实数据失败:', error)
        // 失败不影响页面使用，继续使用默认数据
      }
    },

    // 初始化数据（保留作为备用方法）
    async initializeData() {
      this.loadRealData()
    },

    // 标签页切换
    switchTab(tab) {
      this.activeTab = tab
      if (this.searchKeyword && this.hasSearched) {
        this.handleSearch()
      }
    },

    // 搜索功能
    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.$message.warning('请输入搜索关键词')
        return
      }

      this.loading = true
      this.hasSearched = true

      try {
        if (this.enableAPI) {
          // 启用API时尝试真实搜索
          if (this.activeTab === 'fulltext') {
            await this.performFulltextSearchWithRetry()
          } else if (this.activeTab === 'meta') {
            await this.performMetaSearchWithRetry()
          }
        } else {
          // 禁用API时直接使用模拟数据
          this.handleSearchWithMockData()
        }
      } catch (error) {
        console.error('搜索失败:', error)
        this.$message.error('搜索失败，已显示模拟数据')
        // 确保即使搜索失败也有数据显示
        this.handleSearchWithMockData()
      } finally {
        this.loading = false
      }
    },

    // 使用模拟数据进行搜索
    handleSearchWithMockData() {
      console.log('使用模拟数据进行搜索:', this.searchKeyword)

      if (this.activeTab === 'fulltext') {
        this.useMockSearchResults()
        this.$message.success(`搜索完成，找到模拟数据 ${this.totalResults} 条结果`)
      } else if (this.activeTab === 'meta') {
        this.useMockMetaResults()
        this.$message.success('元搜索完成，显示模拟结果')
      }

      this.hasSearched = true
    },

    // 带重试机制的全文检索搜索
    async performFulltextSearchWithRetry(retries = 1) {
      for (let i = 0; i <= retries; i++) {
        try {
          console.log(`尝试全文检索搜索 (第${i + 1}次)`)
          await this.performFulltextSearch()
          return // 成功则退出重试循环
        } catch (error) {
          console.error(`全文检索搜索失败 (第${i + 1}次):`, error)
          if (i === retries) {
            // 最后一次重试失败，使用模拟数据
            this.useMockSearchResults()
            throw error
          } else {
            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 1000))
          }
        }
      }
    },

    // 执行全文检索搜索（纯模拟版本）
    async performFulltextSearch() {
      try {
        const params = {
          keyword: this.searchKeyword,
          time_range: this.selectedTime,
          platform: this.selectedPlatform,
          emotion: this.selectedEmotion,
          page: this.currentPage,
          size: this.pageSize
        }

        console.log('模拟全文检索请求:', params)
        // 直接使用模拟数据，不调用真实API
        this.useMockSearchResults()
        console.log('模拟全文检索完成')

        // 模拟成功响应
        this.$message.success(`搜索完成，找到 ${this.totalResults} 条模拟结果`)
      } catch (error) {
        console.error('模拟全文检索处理异常:', error)
        // 确保总是有数据显示
        this.useMockSearchResults()
        this.$message.success('搜索完成，显示模拟数据')
      }
    },

    // 带重试机制的元搜索
    async performMetaSearchWithRetry(retries = 1) {
      for (let i = 0; i <= retries; i++) {
        try {
          console.log(`尝试元搜索 (第${i + 1}次)`)
          await this.performMetaSearch()
          return // 成功则退出重试循环
        } catch (error) {
          console.error(`元搜索失败 (第${i + 1}次):`, error)
          if (i === retries) {
            // 最后一次重试失败，使用模拟数据
            this.useMockMetaResults()
            throw error
          } else {
            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 1000))
          }
        }
      }
    },

    // 执行元搜索（纯模拟版本）
    async performMetaSearch() {
      try {
        console.log('模拟元搜索请求:', this.searchKeyword)

        // 直接使用模拟数据，不调用真实API
        this.useMockMetaResults()

        this.$message.success('元搜索完成，显示模拟结果')
      } catch (error) {
        console.error('模拟元搜索处理异常:', error)
        // 确保总是有数据显示
        this.useMockMetaResults()
        this.$message.success('元搜索完成，显示模拟数据')
      }
    },

    // 全文检索筛选方法
    selectTime(value) {
      this.selectedTime = value
      if (this.hasSearched) {
        if (this.enableAPI) {
          this.handleSearch()
        } else {
          // 使用模拟数据重新筛选
          this.handleSearchWithMockData()
        }
      }
    },

    selectPlatform(value) {
      this.selectedPlatform = value
      if (this.hasSearched) {
        if (this.enableAPI) {
          this.handleSearch()
        } else {
          // 使用模拟数据重新筛选
          this.handleSearchWithMockData()
        }
      }
    },

    selectEmotion(value) {
      this.selectedEmotion = value
      if (this.hasSearched) {
        if (this.enableAPI) {
          this.handleSearch()
        } else {
          // 使用模拟数据重新筛选
          this.handleSearchWithMockData()
        }
      }
    },

    handlePageChange(page) {
      this.currentPage = page
      if (this.hasSearched) {
        if (this.enableAPI) {
          this.handleSearch()
        } else {
          // 使用模拟数据重新分页
          this.handleSearchWithMockData()
        }
      }
    },

    // 加载筛选器选项（纯模拟版本）
    async loadFiltersWithRetry(retries = 2) {
      console.log('使用模拟筛选器选项，无需API调用')
      this.useDefaultFilters()
      return Promise.resolve()
    },

    // 加载筛选器选项 (保留原方法作为备用)
    async loadFilters() {
      return this.loadFiltersWithRetry(0) // 不重试的版本
    },

    // 加载统计信息（纯模拟版本）
    async loadStatisticsWithRetry(retries = 2) {
      console.log('使用模拟统计信息，无需API调用')
      return Promise.resolve()
    },

    // 加载统计信息 (保留原方法作为备用)
    async loadStatistics() {
      return this.loadStatisticsWithRetry(0) // 不重试的版本
    },

    // 更新平台选项统计
    updatePlatformOptions(stats) {
      this.platformOptions.forEach(option => {
        if (stats[option.value] !== undefined) {
          option.count = stats[option.value]
        }
      })
    },

    // 更新情感选项统计
    updateEmotionOptions(stats) {
      this.emotionOptions.forEach(option => {
        if (stats[option.value] !== undefined) {
          option.count = stats[option.value]
        }
      })
    },

    // 元搜索引擎切换
    toggleEngine(engineId) {
      // 切换选中状态
      if (this.selectedEngines.includes(engineId)) {
        // 如果已经选中，且不是最后一个选中的引擎，则取消选中
        if (this.selectedEngines.length > 1) {
          this.selectedEngines = this.selectedEngines.filter(id => id !== engineId)
        }
      } else {
        // 如果未选中，则添加到选中列表
        this.selectedEngines.push(engineId)
      }

      // 设置当前活动引擎
      this.activeEngine = engineId
    },

    // 使用默认筛选器选项
    useDefaultFilters() {
      this.platformOptions = [
        { label: '全部', value: 'all', count: 10540 },
        { label: '微信', value: 'wechat', count: 1847 },
        { label: '微博', value: 'weibo', count: 2008 },
        { label: '客户端', value: 'app', count: 1748 },
        { label: '论坛', value: 'forum', count: 673 }
      ]

      this.emotionOptions = [
        { label: '全部', value: 'all', count: 10540 },
        { label: '正面', value: 'positive', count: 3500 },
        { label: '负面', value: 'negative', count: 2000 },
        { label: '中性', value: 'neutral', count: 5040 }
      ]
    },

    // 使用模拟搜索结果
    useMockSearchResults() {
      const keyword = this.searchKeyword || '搜索'
      const sources = ['新华网', '人民网', '央视新闻', '澎湃新闻', '界面新闻', '财经网', '科技日报', '环球时报']
      const platforms = ['微信', '微博', '客户端', '论坛', '网站']
      const locations = ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安']
      const categories = ['新闻', '论文', '社交媒体', '评论', '分析报告']
      const emotions = ['正面', '负面', '中性']

      this.searchResults = []

      // 生成10条模拟数据
      for (let i = 1; i <= 10; i++) {
        const source = sources[Math.floor(Math.random() * sources.length)]
        const platform = platforms[Math.floor(Math.random() * platforms.length)]
        const location = locations[Math.floor(Math.random() * locations.length)]
        const category = categories[Math.floor(Math.random() * categories.length)]
        const emotion = emotions[Math.floor(Math.random() * emotions.length)]

        this.searchResults.push({
          id: i,
          title: `关于${keyword}的重要${category} - ${i}`,
          content: `这是一条关于${keyword}的详细内容，包含了最新的发展动态和深度分析。${keyword}在当前市场环境下展现出了强劲的发展势头，相关数据显示...`,
          source: source,
          author: `${Math.floor(Math.random() * 999) + 100}人讨论`,
          publish_time: `2024-01-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')} ${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:00`,
          platform: platform,
          read_count: String(Math.floor(Math.random() * 50000) + 1000),
          location: location,
          category: category,
          hot_score: Math.round((Math.random() * 9 + 1) * 10) / 10,
          emotion_type: emotion,
          keywords: `${keyword},热点,新闻,分析`
        })
      }

      this.totalResults = 10000 // 模拟大量数据
      console.log('生成了10条模拟搜索结果')
    },

    // 使用模拟元搜索结果
    useMockMetaResults() {
      const keyword = this.searchKeyword || '搜索'
      this.bingResults = [
        {
          title: `${keyword}官网_权威信息平台`,
          url: `https://www.bing.com/search?q=${keyword}`,
          description: `这是关于${keyword}的官方权威信息，提供最新动态、详细介绍和相关资讯...`,
          engine: 'bing'
        }
      ]
      this.baiduResults = [
        {
          title: `${keyword} - 百科全书`,
          url: `https://www.baidu.com/s?wd=${keyword}`,
          description: `${keyword}是一个重要的概念/实体，具有深远的影响和重要意义...`,
          engine: 'baidu'
        }
      ]
      this.so360Results = [
        {
          title: `最新${keyword}新闻资讯`,
          url: `https://www.so.com/s?q=${keyword}`,
          description: `汇集最新的${keyword}相关新闻，实时更新，权威报道...`,
          engine: '360'
        }
      ]
      this.$message.info('使用模拟数据显示元搜索结果')
    }
  }
}
</script>

<style scoped>
.meta-search-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.search-header {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-tabs {
  display: flex;
  margin-bottom: 20px;
}

.tab {
  padding: 8px 16px;
  margin-right: 10px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.tab.active {
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
  font-weight: bold;
}

.search-box {
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  height: 40px;
  padding: 0 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-right: 10px;
  font-size: 14px;
}

.search-btn {
  height: 40px;
}

.search-results {
  display: flex;
  flex-direction: column;
}

.search-engines {
  display: flex;
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.engine-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  margin-right: 15px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
  border: 1px solid #dcdfe6;
  background: #fff;
}

.engine-item:hover {
  border-color: #c6e2ff;
}

.engine-item.active {
  background: #ecf5ff;
  color: #409EFF;
  border-color: #409EFF;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: #409EFF;
}

.engine-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.results-container {
  display: flex;
  gap: 20px;
}

.result-column {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.result-header {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  background: #f5f7fa;
}

.result-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border-radius: 2px;
}

/* 搜索引擎图标样式 */
.bing-icon {
  background: linear-gradient(45deg, #0078d4, #106ebe);
  position: relative;
}

.bing-icon::after {
  content: 'B';
  color: white;
  font-weight: bold;
  font-size: 10px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.baidu-icon {
  background: linear-gradient(45deg, #2932e1, #1e23a7);
  position: relative;
}

.baidu-icon::after {
  content: '百';
  color: white;
  font-weight: bold;
  font-size: 9px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.so360-icon {
  background: linear-gradient(45deg, #01c85f, #00a84f);
  position: relative;
}

.so360-icon::after {
  content: '360';
  color: white;
  font-weight: bold;
  font-size: 7px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.result-title {
  font-weight: bold;
  color: #303133;
}

.result-list {
  padding: 15px;
}

.result-item {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.result-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.item-title {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.item-title a {
  color: #0366d6;
  text-decoration: none;
}

.item-title a:hover {
  text-decoration: underline;
}

.item-url {
  color: #67c23a;
  font-size: 12px;
  margin-bottom: 8px;
}

.item-desc {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

/* 全文检索样式 */
.fulltext-results {
  margin-top: 20px;
}

.filter-section {
  background: white;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 80px;
  color: #666;
  font-size: 14px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.count {
  color: #999;
  font-size: 12px;
}

.result-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 15px 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.results-list {
  background: white;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.results-list .result-item {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.results-list .result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.result-title {
  font-size: 16px;
  color: #333;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  margin-right: 20px;
}

.result-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
  font-size: 12px;
  color: #999;
}

.meta-item {
  white-space: nowrap;
}

.result-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
