from datetime import datetime, timedelta
from typing import List, Optional
from sqlalchemy import select, desc, func, case, and_, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from module_opinion.entity.do.analysis_progress_log_do import AnalysisTask
from module_opinion.entity.do.opinion_requirement_do import OpinionRequirement
from module_opinion.entity.do.opinion_task_do import OpinionTask
from module_admin.entity.do.news_do import News
from utils.log_util import logger


class DashboardDao:
    """
    Dashboard数据访问层
    """

    @classmethod
    async def get_recent_analysis_records(
        cls,
        query_db: AsyncSession,
        limit: int = 3,
        user_id: int = None
    ) -> List[dict]:
        """
        获取最新的分析记录（从opinion_task表获取）

        :param query_db: 数据库会话
        :param limit: 返回记录数量限制，默认3条
        :param user_id: 用户ID，用于过滤特定用户的分析记录
        :return: 最新分析记录列表
        """
        try:
            # 构建查询，关联opinion_task和opinion_requirement表
            query = (
                select(
                    OpinionTask.id,
                    OpinionTask.task_name,
                    OpinionTask.status,
                    OpinionTask.create_time,
                    OpinionTask.execute_count,
                    OpinionTask.success_count,
                    OpinionTask.fail_count,
                    OpinionTask.last_execute_time,
                    OpinionTask.report_oss_url,
                    OpinionTask.positive_count,
                    OpinionTask.negative_count,
                    OpinionTask.neutral_count,
                    OpinionRequirement.requirement_name,
                    OpinionRequirement.entity_keyword
                )
                .select_from(OpinionTask)
                .join(
                    OpinionRequirement,
                    OpinionTask.requirement_id == OpinionRequirement.id
                )
                .where(
                    # 只查询未删除的需求记录和启用的任务
                    OpinionRequirement.is_deleted == 0,
                    OpinionTask.is_enabled == 1,
                    OpinionTask.requirement_id > 0
                )
            )

            # 如果提供了用户ID，添加用户过滤条件
            if user_id is not None:
                query = query.where(
                    and_(
                        OpinionTask.user_id == user_id,
                        OpinionRequirement.user_id == user_id
                    )
                )

            query = query.order_by(desc(OpinionTask.id)).limit(limit)  # 按ID降序排列，确保获取最新的记录

            result = await query_db.execute(query)
            records = result.fetchall()

            # 转换为字典格式并处理统计数据
            analysis_records = []
            for record in records:
                # 使用新添加的情感统计字段
                positive_count = record.positive_count or 0
                negative_count = record.negative_count or 0
                neutral_count = record.neutral_count or 0

                # 计算进度百分比（基于执行统计）
                progress_percentage = 0
                if record.execute_count and record.execute_count > 0:
                    progress_percentage = min(100, int((record.success_count or 0) / record.execute_count * 100))
                elif record.status == 'completed':
                    progress_percentage = 100
                elif record.status in ['running', 'pending']:
                    progress_percentage = 50

                analysis_records.append({
                    'id': record.id,
                    'requirement_name': record.requirement_name,
                    'status': record.status,
                    'entity_keyword': record.entity_keyword,
                    'create_time': record.create_time,
                    'positive_count': positive_count,
                    'neutral_count': neutral_count,
                    'negative_count': negative_count,
                    'task_name': record.task_name,
                    'progress_percentage': progress_percentage,
                    'report_oss_url': record.report_oss_url
                })

            logger.info(f"成功获取最新{len(analysis_records)}条分析记录（从opinion_task表），用户ID: {user_id}")
            return analysis_records

        except Exception as e:
            logger.error(f"获取最新分析记录失败: {str(e)}")
            raise e

    @classmethod
    async def get_analysis_records_count(cls, query_db: AsyncSession, user_id: int = None) -> int:
        """
        获取分析记录总数（从opinion_task表获取）

        :param query_db: 数据库会话
        :param user_id: 用户ID，用于过滤特定用户的分析记录
        :return: 分析记录总数
        """
        try:
            query = (
                select(func.count(OpinionTask.id))
                .select_from(OpinionTask)
                .join(
                    OpinionRequirement,
                    OpinionTask.requirement_id == OpinionRequirement.id
                )
                .where(
                    OpinionRequirement.is_deleted == 0,
                    OpinionTask.is_enabled == 1
                )
            )

            # 如果提供了用户ID，添加用户过滤条件
            if user_id is not None:
                query = query.where(
                    and_(
                        OpinionTask.user_id == user_id,
                        OpinionRequirement.user_id == user_id
                    )
                )

            result = await query_db.execute(query)
            count = result.scalar()

            logger.info(f"分析记录总数（opinion_task表），用户ID: {user_id}: {count}")
            return count or 0

        except Exception as e:
            logger.error(f"获取分析记录总数失败: {str(e)}")
            raise e

    @classmethod
    async def get_dashboard_statistics(cls, query_db: AsyncSession, user_id: int = None) -> dict:
        """
        获取Dashboard统计数据

        :param query_db: 数据库会话
        :param user_id: 用户ID，用于过滤特定用户的统计数据
        :return: Dashboard统计数据字典
        """
        try:
            logger.info("=== Dashboard DAO: 开始获取统计数据 ===")

            # 获取今天和昨天的日期
            today = datetime.now().date()
            yesterday = today - timedelta(days=1)
            today_start = datetime.combine(today, datetime.min.time())
            today_end = datetime.combine(today, datetime.max.time())
            yesterday_start = datetime.combine(yesterday, datetime.min.time())
            yesterday_end = datetime.combine(yesterday, datetime.max.time())

            logger.info(f"=== Dashboard DAO: 时间范围 - 今日: {today_start} 到 {today_end} ===")
            logger.info(f"=== Dashboard DAO: 时间范围 - 昨日: {yesterday_start} 到 {yesterday_end} ===")

            # 1. 获取今日任务数量
            today_tasks_conditions = [
                OpinionTask.create_time >= today_start,
                OpinionTask.create_time <= today_end,
                OpinionTask.is_enabled == 1
            ]

            # 如果提供了用户ID，添加用户过滤条件
            if user_id is not None:
                today_tasks_conditions.append(OpinionTask.user_id == user_id)

            today_tasks_query = select(func.count(OpinionTask.id)).where(
                and_(*today_tasks_conditions)
            )
            logger.info(f"=== Dashboard DAO: 今日任务查询SQL: {today_tasks_query} ===")

            today_tasks_result = await query_db.execute(today_tasks_query)
            today_tasks = today_tasks_result.scalar() or 0
            logger.info(f"=== Dashboard DAO: 今日任务数量: {today_tasks} ===")

            # 2. 获取昨日任务数量
            yesterday_tasks_conditions = [
                OpinionTask.create_time >= yesterday_start,
                OpinionTask.create_time <= yesterday_end,
                OpinionTask.is_enabled == 1
            ]

            # 如果提供了用户ID，添加用户过滤条件
            if user_id is not None:
                yesterday_tasks_conditions.append(OpinionTask.user_id == user_id)

            yesterday_tasks_query = select(func.count(OpinionTask.id)).where(
                and_(*yesterday_tasks_conditions)
            )
            yesterday_tasks_result = await query_db.execute(yesterday_tasks_query)
            yesterday_tasks = yesterday_tasks_result.scalar() or 0

            # 3. 计算任务增长率
            tasks_growth = 0.0
            if yesterday_tasks > 0:
                tasks_growth = ((today_tasks - yesterday_tasks) / yesterday_tasks) * 100
            elif today_tasks > 0:
                tasks_growth = 100.0

            # 4. 获取今日负面舆情数量（暂时使用opinion_task表的negative_count字段）
            try:
                today_negative_conditions = [
                    OpinionTask.create_time >= today_start,
                    OpinionTask.create_time <= today_end,
                    OpinionTask.is_enabled == 1
                ]

                # 如果提供了用户ID，添加用户过滤条件
                if user_id is not None:
                    today_negative_conditions.append(OpinionTask.user_id == user_id)

                today_negative_query = select(func.sum(OpinionTask.negative_count)).where(
                    and_(*today_negative_conditions)
                )
                today_negative_result = await query_db.execute(today_negative_query)
                today_negative = today_negative_result.scalar() or 0
            except Exception as e:
                logger.warning(f"获取今日负面舆情数量失败，使用默认值: {str(e)}")
                today_negative = 0

            # 5. 获取昨日负面舆情数量
            try:
                yesterday_negative_conditions = [
                    OpinionTask.create_time >= yesterday_start,
                    OpinionTask.create_time <= yesterday_end,
                    OpinionTask.is_enabled == 1
                ]

                # 如果提供了用户ID，添加用户过滤条件
                if user_id is not None:
                    yesterday_negative_conditions.append(OpinionTask.user_id == user_id)

                yesterday_negative_query = select(func.sum(OpinionTask.negative_count)).where(
                    and_(*yesterday_negative_conditions)
                )
                yesterday_negative_result = await query_db.execute(yesterday_negative_query)
                yesterday_negative = yesterday_negative_result.scalar() or 0
            except Exception as e:
                logger.warning(f"获取昨日负面舆情数量失败，使用默认值: {str(e)}")
                yesterday_negative = 0

            # 6. 计算负面舆情变化率
            negative_change = 0.0
            if yesterday_negative > 0:
                negative_change = ((today_negative - yesterday_negative) / yesterday_negative) * 100
            elif today_negative > 0:
                negative_change = 100.0

            # 7. 获取各状态任务数量
            status_conditions = [OpinionTask.is_enabled == 1]

            # 如果提供了用户ID，添加用户过滤条件
            if user_id is not None:
                status_conditions.append(OpinionTask.user_id == user_id)

            status_query = select(
                OpinionTask.status,
                func.count(OpinionTask.id).label('count')
            ).where(
                and_(*status_conditions)
            ).group_by(OpinionTask.status)

            status_result = await query_db.execute(status_query)
            status_counts = {row.status: row.count for row in status_result.fetchall()}

            completed_count = status_counts.get('completed', 0)
            pending_count = status_counts.get('pending', 0)
            running_count = status_counts.get('running', 0)
            failed_count = status_counts.get('failed', 0)
            total_count = sum(status_counts.values())

            # 8. 获取昨日各状态任务数量用于计算变化率
            yesterday_status_conditions = [
                OpinionTask.create_time >= yesterday_start,
                OpinionTask.create_time <= yesterday_end,
                OpinionTask.is_enabled == 1
            ]

            # 如果提供了用户ID，添加用户过滤条件
            if user_id is not None:
                yesterday_status_conditions.append(OpinionTask.user_id == user_id)

            yesterday_status_query = select(
                OpinionTask.status,
                func.count(OpinionTask.id).label('count')
            ).where(
                and_(*yesterday_status_conditions)
            ).group_by(OpinionTask.status)

            yesterday_status_result = await query_db.execute(yesterday_status_query)
            yesterday_status_counts = {row.status: row.count for row in yesterday_status_result.fetchall()}

            yesterday_completed = yesterday_status_counts.get('completed', 0)
            yesterday_pending = yesterday_status_counts.get('pending', 0)

            # 9. 计算已完成任务变化率
            completed_change = 0.0
            if yesterday_completed > 0:
                completed_change = ((completed_count - yesterday_completed) / yesterday_completed) * 100
            elif completed_count > 0:
                completed_change = 100.0

            # 10. 计算待完成任务变化率
            pending_change = 0.0
            if yesterday_pending > 0:
                pending_change = ((pending_count - yesterday_pending) / yesterday_pending) * 100
            elif pending_count > 0:
                pending_change = 100.0

            # 11. 获取用户分析统计数据
            user_stats = await cls.get_user_analysis_statistics(query_db, user_id)

            statistics = {
                'today_tasks': today_tasks,
                'tasks_growth': round(tasks_growth, 1),
                'negative_count': today_negative,
                'negative_change': round(negative_change, 1),
                'completed_count': completed_count,
                'completed_change': round(completed_change, 1),
                'pending_count': pending_count,
                'pending_change': round(pending_change, 1),
                'total_count': total_count,
                'running_count': running_count,
                'failed_count': failed_count,
                # 添加用户统计数据
                'total_analysis': user_stats['total_analysis'],
                'today_analysis': user_stats['today_analysis'],
                'remaining_count': user_stats['remaining_count'],
                'today_remaining': user_stats['today_remaining'],
                'package_name': user_stats['package_name'],
                'package_limit': user_stats['package_limit'],
                'usage_percentage': user_stats['usage_percentage']
            }

            logger.info(f"=== Dashboard DAO: 最终统计数据: {statistics} ===")
            logger.info(f"=== Dashboard DAO: 成功获取Dashboard统计数据 ===")
            return statistics

        except Exception as e:
            logger.error(f"获取Dashboard统计数据失败: {str(e)}")
            # 返回默认值
            return {
                'today_tasks': 0,
                'tasks_growth': 0.0,
                'negative_count': 0,
                'negative_change': 0.0,
                'completed_count': 0,
                'completed_change': 0.0,
                'pending_count': 0,
                'pending_change': 0.0,
                'total_count': 0,
                'running_count': 0,
                'failed_count': 0,
                'total_analysis': 0,
                'today_analysis': 0,
                'remaining_count': 0,
                'today_remaining': 0,
                'package_name': '',
                'package_limit': 0,
                'usage_percentage': 0.0
            }

    @classmethod
    async def get_user_analysis_statistics(cls, query_db: AsyncSession, user_id: int = None) -> dict:
        """
        获取用户分析统计数据

        :param query_db: 数据库会话
        :param user_id: 用户ID
        :return: 用户分析统计数据
        """
        try:
            from datetime import datetime, date
            from sqlalchemy import func, and_, case
            from module_opinion.entity.do.opinion_task_do import OpinionTask
            from module_opinion.entity.do.opinion_requirement_do import OpinionRequirement
            from module_bill.entity.do.order_do import UserPackage, UserOrder
            from module_admin.entity.do.user_do import SysUser

            logger.info(f"=== 开始获取用户分析统计数据，用户ID: {user_id} ===")

            if user_id is None:
                logger.warning("用户ID为空，返回默认统计数据")
                return {
                    'total_analysis': 0,
                    'today_analysis': 0,
                    'remaining_count': 0,
                    'today_remaining': 0,
                    'package_name': '基础版',
                    'package_limit': 0,
                    'usage_percentage': 0.0
                }

            # 1. 获取用户总分析次数（基于opinion_requirement表）
            # 首先获取用户套餐有效期信息
            package_validity_info = await cls._get_user_package_validity_period(query_db, user_id)

            # 构建查询条件
            base_conditions = [
                OpinionRequirement.user_id == user_id,
                OpinionRequirement.is_deleted == 0
            ]

            # 如果有套餐有效期，添加时间范围条件
            if package_validity_info['has_valid_package'] and package_validity_info['start_time']:
                base_conditions.append(OpinionRequirement.create_time >= package_validity_info['start_time'])
                if package_validity_info['end_time']:
                    base_conditions.append(OpinionRequirement.create_time <= package_validity_info['end_time'])

            total_query = (
                select(func.count(OpinionRequirement.id))
                .select_from(OpinionRequirement)
                .where(and_(*base_conditions))
            )

            result = await query_db.execute(total_query)
            total_analysis = result.scalar() or 0

            logger.info(f"=== 用户{user_id}的opinion_requirement记录总数: {total_analysis} ===")
            logger.info(f"=== 套餐有效期信息: {package_validity_info} ===")

            # 2. 获取今日分析次数（基于opinion_requirement表）
            today = date.today()
            today_conditions = base_conditions.copy()
            today_conditions.append(func.date(OpinionRequirement.create_time) == today)

            today_query = (
                select(func.count(OpinionRequirement.id))
                .select_from(OpinionRequirement)
                .where(and_(*today_conditions))
            )

            result = await query_db.execute(today_query)
            today_analysis = result.scalar() or 0

            logger.info(f"=== 用户{user_id}今日的opinion_requirement记录数: {today_analysis} ===")

            # 3. 获取用户套餐信息
            package_info = await cls.get_user_package_info(query_db, user_id)

            # 4. 计算剩余次数
            package_limit = package_info['package_limit']
            remaining_count = 0
            today_remaining = 0
            usage_percentage = 0.0

            if package_limit == -1:  # 无限制
                remaining_count = 999999
                today_remaining = 999999
                usage_percentage = 0.0
            elif package_limit > 0:
                remaining_count = max(0, package_limit - total_analysis)
                # 假设每日限制为套餐总限制的1/30 (月均分配)
                daily_limit = max(1, package_limit // 30)
                today_remaining = max(0, daily_limit - today_analysis)
                usage_percentage = round((total_analysis / package_limit) * 100, 1) if package_limit > 0 else 0.0

            statistics = {
                'total_analysis': total_analysis,
                'today_analysis': today_analysis,
                'remaining_count': remaining_count,
                'today_remaining': today_remaining,
                'package_name': package_info['package_name'],
                'package_limit': package_limit,
                'usage_percentage': usage_percentage
            }

            logger.info(f"=== 用户分析统计数据: {statistics} ===")
            return statistics

        except Exception as e:
            logger.error(f"获取用户分析统计数据失败: {str(e)}")
            return {
                'total_analysis': 0,
                'today_analysis': 0,
                'remaining_count': 0,
                'today_remaining': 0,
                'package_name': '基础版',
                'package_limit': 0,
                'usage_percentage': 0.0
            }

    @classmethod
    async def get_user_package_info(cls, query_db: AsyncSession, user_id: int) -> dict:
        """
        获取用户套餐信息
        根据优先级查询：订单套餐 -> VIP等级 -> 默认套餐

        :param query_db: 数据库会话
        :param user_id: 用户ID
        :return: 用户套餐信息 {'package_name': str, 'package_limit': int}
        """
        try:
            from module_bill.entity.do.order_do import UserPackage, UserOrder
            from module_admin.entity.do.user_do import SysUser
            from config.package_config import PackageConfig
            from sqlalchemy import desc
            from datetime import datetime

            logger.info(f"=== 开始获取用户套餐信息，用户ID: {user_id} ===")

            # 优先级1: 根据用户VIP等级获取套餐信息（统一使用vip_level字段）
            package_info = await cls._get_package_from_vip_level(query_db, user_id)
            if package_info:
                return package_info

            # 优先级2: 查询用户最新的有效订单及其套餐信息（仅作为备选）
            package_info = await cls._get_package_from_orders(query_db, user_id)
            if package_info:
                return package_info

            # 优先级3: 使用默认套餐配置
            default_package = PackageConfig.get_default_package()
            logger.info(f"=== 使用默认套餐配置: {default_package} ===")
            return {
                'package_name': default_package['name'],
                'package_limit': default_package['limit']
            }

        except Exception as e:
            logger.error(f"获取用户套餐信息失败: {str(e)}")
            # 异常情况下返回默认配置
            try:
                from config.package_config import PackageConfig
                default_package = PackageConfig.get_default_package()
                return {
                    'package_name': default_package['name'],
                    'package_limit': default_package['limit']
                }
            except:
                # 如果连配置都无法加载，使用硬编码默认值
                return {
                    'package_name': '基础版',
                    'package_limit': 20
                }

    @classmethod
    async def _get_package_from_orders(cls, query_db: AsyncSession, user_id: int) -> dict:
        """
        从用户订单中获取套餐信息

        :param query_db: 数据库会话
        :param user_id: 用户ID
        :return: 套餐信息或None
        """
        try:
            from module_bill.entity.do.order_do import UserPackage, UserOrder
            from sqlalchemy import desc
            from datetime import datetime

            # 查询用户最新的有效订单
            order_query = (
                select(UserOrder, UserPackage)
                .join(UserPackage, UserOrder.package_id == UserPackage.package_id)
                .where(
                    and_(
                        UserOrder.user_id == user_id,
                        UserOrder.payment_status == 'paid',
                        UserOrder.order_status == 'completed'
                    )
                )
                .order_by(desc(UserOrder.payment_time))
                .limit(1)
            )

            result = await query_db.execute(order_query)
            order_package = result.first()

            if order_package:
                order, package = order_package

                # 检查订单是否在有效期内（如果有expire_time）
                if order.expire_time and order.expire_time < datetime.now():
                    logger.info(f"=== 用户订单已过期: {order.expire_time} ===")
                    return None

                logger.info(f"=== 找到有效订单套餐: {package.package_name}, 限制: {package.analysis_limit} ===")
                return {
                    'package_name': package.package_name,
                    'package_limit': package.analysis_limit
                }

            return None

        except Exception as e:
            logger.error(f"从订单获取套餐信息失败: {str(e)}")
            return None

    @classmethod
    async def _get_user_package_validity_period(cls, query_db: AsyncSession, user_id: int) -> dict:
        """
        获取用户套餐有效期信息

        :param query_db: 数据库会话
        :param user_id: 用户ID
        :return: 套餐有效期信息字典
        """
        try:
            from module_bill.entity.do.order_do import UserOrder, UserPackage

            # 查询用户最新的已支付订单
            order_query = (
                select(UserOrder, UserPackage)
                .join(UserPackage, UserOrder.package_id == UserPackage.package_id)
                .where(
                    and_(
                        UserOrder.user_id == user_id,
                        UserOrder.payment_status == 'paid'
                    )
                )
                .order_by(desc(UserOrder.payment_time))
                .limit(1)
            )

            result = await query_db.execute(order_query)
            order_package = result.first()

            if order_package:
                order, package = order_package

                # 检查订单是否仍在有效期内
                current_time = datetime.now()
                is_valid = order.expire_time and order.expire_time > current_time

                return {
                    'has_valid_package': True,
                    'start_time': order.payment_time,
                    'end_time': order.expire_time if is_valid else current_time,
                    'package_name': package.package_name,
                    'is_expired': not is_valid
                }
            else:
                # 没有付费订单，使用VIP等级套餐（无时间限制）
                return {
                    'has_valid_package': False,
                    'start_time': None,
                    'end_time': None,
                    'package_name': 'VIP等级套餐',
                    'is_expired': False
                }

        except Exception as e:
            logger.error(f"获取用户套餐有效期信息失败: {str(e)}")
            # 返回默认值，不限制时间范围
            return {
                'has_valid_package': False,
                'start_time': None,
                'end_time': None,
                'package_name': '默认套餐',
                'is_expired': False
            }

    @classmethod
    async def _get_package_from_vip_level(cls, query_db: AsyncSession, user_id: int) -> dict:
        """
        根据用户VIP等级获取套餐信息

        :param query_db: 数据库会话
        :param user_id: 用户ID
        :return: 套餐信息或None
        """
        try:
            from module_admin.entity.do.user_do import SysUser
            from config.package_config import PackageConfig

            # 查询用户VIP等级
            user_query = select(SysUser).where(SysUser.user_id == user_id)
            user_result = await query_db.execute(user_query)
            user = user_result.scalar_one_or_none()

            if user:
                # 获取用户VIP等级，默认为0（基础版）
                vip_level = getattr(user, 'vip_level', 0) or 0

                # 根据VIP等级获取套餐配置
                vip_package = PackageConfig.get_package_by_vip_level(vip_level)
                logger.info(f"=== 根据VIP等级 {vip_level} 确定套餐: {vip_package} ===")
                return {
                    'package_name': vip_package['name'],
                    'package_limit': vip_package['limit']
                }

            return None

        except Exception as e:
            logger.error(f"从VIP等级获取套餐信息失败: {str(e)}")
            return None

    @classmethod
    async def get_opinion_trend_data(cls, query_db: AsyncSession, days: int = 7) -> List[dict]:
        """
        获取舆情分析趋势数据（近N天）

        :param query_db: 数据库会话
        :param days: 天数，默认7天
        :return: 趋势数据列表
        """
        try:
            from datetime import datetime, timedelta
            from sqlalchemy import func, case, and_
            from module_admin.entity.do.keyword_data_do import KeywordData

            # 计算日期范围
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days-1)

            # 查询每日统计数据
            query = (
                select(
                    func.date(KeywordData.createtime).label('date'),
                    func.count(KeywordData.id).label('total_count'),
                    func.sum(case((KeywordData.sentiment == 'positive', 1), else_=0)).label('positive_count'),
                    func.sum(case((KeywordData.sentiment == 'neutral', 1), else_=0)).label('neutral_count'),
                    func.sum(case((KeywordData.sentiment == 'negative', 1), else_=0)).label('negative_count')
                )
                .where(
                    and_(
                        func.date(KeywordData.createtime) >= start_date,
                        func.date(KeywordData.createtime) <= end_date
                    )
                )
                .group_by(func.date(KeywordData.createtime))
                .order_by('date')
            )

            result = await query_db.execute(query)
            rows = result.fetchall()

            # 转换为字典列表
            trend_data = []
            for row in rows:
                trend_data.append({
                    'date': row.date.strftime('%Y-%m-%d'),
                    'total_count': row.total_count or 0,
                    'positive_count': row.positive_count or 0,
                    'neutral_count': row.neutral_count or 0,
                    'negative_count': row.negative_count or 0
                })

            # 如果某些日期没有数据，补充0值
            all_dates = []
            current_date = start_date
            while current_date <= end_date:
                all_dates.append(current_date.strftime('%Y-%m-%d'))
                current_date += timedelta(days=1)

            # 创建完整的数据集
            complete_data = []
            existing_dates = {item['date'] for item in trend_data}

            for date_str in all_dates:
                if date_str in existing_dates:
                    # 找到对应的数据
                    for item in trend_data:
                        if item['date'] == date_str:
                            complete_data.append(item)
                            break
                else:
                    # 补充空数据
                    complete_data.append({
                        'date': date_str,
                        'total_count': 0,
                        'positive_count': 0,
                        'neutral_count': 0,
                        'negative_count': 0
                    })

            logger.info(f"成功获取{days}天舆情趋势数据，共{len(complete_data)}条记录")
            return complete_data

        except Exception as e:
            logger.error(f"获取舆情趋势数据失败: {str(e)}")
            raise e

    @classmethod
    async def get_current_month_analysis_count(cls, query_db: AsyncSession, user_id: int) -> int:
        """
        获取用户当前月份的分析记录数量（备用统计）

        :param query_db: 数据库会话
        :param user_id: 用户ID
        :return: 当前月份分析记录数量
        """
        try:
            from datetime import datetime
            from sqlalchemy import func, and_, extract
            from module_opinion.entity.do.opinion_requirement_do import OpinionRequirement

            logger.info(f"=== Dashboard DAO: 开始获取用户{user_id}当前月份分析记录数 ===")

            if user_id is None:
                logger.warning("用户ID为空，返回0")
                return 0

            # 获取当前年月
            current_year = datetime.now().year
            current_month = datetime.now().month

            logger.info(f"=== Dashboard DAO: 查询时间范围 - {current_year}年{current_month}月 ===")

            # 查询当前月份的opinion_requirement记录数
            query = (
                select(func.count(OpinionRequirement.id))
                .select_from(OpinionRequirement)
                .where(and_(
                    OpinionRequirement.user_id == user_id,
                    OpinionRequirement.is_deleted == 0,
                    extract('year', OpinionRequirement.create_time) == current_year,
                    extract('month', OpinionRequirement.create_time) == current_month
                ))
            )

            result = await query_db.execute(query)
            monthly_count = result.scalar() or 0

            logger.info(f"=== Dashboard DAO: 用户{user_id}在{current_year}年{current_month}月的分析记录数: {monthly_count} ===")
            logger.info(f"=== Dashboard DAO: 成功获取当前月份分析记录数 ===")

            return monthly_count

        except Exception as e:
            logger.error(f"获取当前月份分析记录数失败: {str(e)}")
            logger.error(f"错误详情: {e}", exc_info=True)
            return 0
