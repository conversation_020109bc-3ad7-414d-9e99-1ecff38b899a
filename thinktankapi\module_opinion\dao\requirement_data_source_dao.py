from datetime import datetime
from sqlalchemy import and_, or_, desc, asc, func, select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from module_opinion.entity.do.requirement_data_source_do import RequirementDataSource
from module_opinion.entity.vo.requirement_data_source_vo import RequirementDataSourcePageQueryModel, RequirementDataSourceModel
from utils.page_util import PageUtil


class RequirementDataSourceDao:
    """
    需求数据源数据访问层
    """

    @classmethod
    async def get_requirement_data_source_list(
        cls, db: AsyncSession, query_object: RequirementDataSourcePageQueryModel, is_page: bool = False
    ):
        """
        根据查询参数获取需求数据源列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 需求数据源列表信息对象
        """
        query = select(RequirementDataSource)
        
        # 构建查询条件
        if query_object.requirement_id:
            query = query.where(RequirementDataSource.requirement_id == query_object.requirement_id)
        if query_object.source_type:
            query = query.where(RequirementDataSource.source_type == query_object.source_type)
        if query_object.source_name:
            query = query.where(RequirementDataSource.source_name.like(f'%{query_object.source_name}%'))
        if query_object.is_selected is not None:
            query = query.where(RequirementDataSource.is_selected == query_object.is_selected)
        if query_object.is_active is not None:
            query = query.where(RequirementDataSource.is_active == query_object.is_active)
        if query_object.created_by:
            query = query.where(RequirementDataSource.create_by == query_object.created_by)

        # 排序
        query = query.order_by(desc(RequirementDataSource.create_time))

        if is_page:
            # 分页查询
            return await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size)
        else:
            # 不分页查询
            result = await db.execute(query)
            return result.scalars().all()

    @classmethod
    async def get_requirement_data_source_by_id(cls, db: AsyncSession, data_source_id: int):
        """
        根据ID获取需求数据源详细信息

        :param db: orm对象
        :param data_source_id: 数据源ID
        :return: 需求数据源信息对象
        """
        query = select(RequirementDataSource).where(RequirementDataSource.id == data_source_id)
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def add_requirement_data_source_dao(cls, db: AsyncSession, data_source: RequirementDataSourceModel):
        """
        新增需求数据源数据库操作

        :param db: orm对象
        :param data_source: 需求数据源对象
        :return: 新增的需求数据源对象
        """
        db_data_source = RequirementDataSource(**data_source.model_dump(exclude={'id'}))
        db.add(db_data_source)
        await db.flush()
        return db_data_source

    @classmethod
    async def batch_add_requirement_data_sources_dao(cls, db: AsyncSession, data_sources: List[RequirementDataSourceModel]):
        """
        批量新增需求数据源数据库操作

        :param db: orm对象
        :param data_sources: 需求数据源对象列表
        :return: 新增的需求数据源对象列表
        """
        db_data_sources = []
        for data_source in data_sources:
            db_data_source = RequirementDataSource(**data_source.model_dump(exclude={'id'}))
            db_data_sources.append(db_data_source)
            db.add(db_data_source)
        
        await db.flush()
        return db_data_sources

    @classmethod
    async def edit_requirement_data_source_dao(cls, db: AsyncSession, data_source: dict):
        """
        编辑需求数据源数据库操作

        :param db: orm对象
        :param data_source: 需要更新的需求数据源字典
        :return:
        """
        await db.execute(
            update(RequirementDataSource).where(RequirementDataSource.id == data_source['id']).values(**data_source)
        )

    @classmethod
    async def delete_requirement_data_source_dao(cls, db: AsyncSession, data_source_ids: List[int]):
        """
        删除需求数据源数据库操作（物理删除）

        :param db: orm对象
        :param data_source_ids: 需求数据源ID列表
        :return:
        """
        await db.execute(
            delete(RequirementDataSource)
            .where(RequirementDataSource.id.in_(data_source_ids))
        )

    @classmethod
    async def get_data_sources_by_requirement(cls, db: AsyncSession, requirement_id: int):
        """
        根据需求ID获取数据源列表

        :param db: orm对象
        :param requirement_id: 需求ID
        :return: 数据源列表
        """
        query = select(RequirementDataSource).where(
            and_(
                RequirementDataSource.requirement_id == requirement_id,
                RequirementDataSource.is_active == 1
            )
        ).order_by(desc(RequirementDataSource.create_time))
        
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_data_sources_by_type(cls, db: AsyncSession, source_type: str, requirement_id: Optional[int] = None):
        """
        根据数据源类型获取数据源列表

        :param db: orm对象
        :param source_type: 数据源类型
        :param requirement_id: 需求ID（可选）
        :return: 数据源列表
        """
        query = select(RequirementDataSource).where(
            and_(
                RequirementDataSource.source_type == source_type,
                RequirementDataSource.is_active == 1
            )
        )
        
        if requirement_id:
            query = query.where(RequirementDataSource.requirement_id == requirement_id)
            
        query = query.order_by(desc(RequirementDataSource.create_time))
        
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def check_data_source_exists(cls, db: AsyncSession, requirement_id: int, source_type: str, source_name: str):
        """
        检查数据源是否已存在

        :param db: orm对象
        :param requirement_id: 需求ID
        :param source_type: 数据源类型
        :param source_name: 数据源名称
        :return: 是否存在
        """
        query = select(func.count(RequirementDataSource.id)).where(
            and_(
                RequirementDataSource.requirement_id == requirement_id,
                RequirementDataSource.source_type == source_type,
                RequirementDataSource.source_name == source_name
            )
        )
        
        result = await db.execute(query)
        count = result.scalar()
        return count > 0

    @classmethod
    async def get_data_source_statistics(cls, db: AsyncSession, requirement_id: Optional[int] = None):
        """
        获取数据源统计信息

        :param db: orm对象
        :param requirement_id: 需求ID（可选）
        :return: 统计信息
        """
        base_query = select(RequirementDataSource)
        
        if requirement_id:
            base_query = base_query.where(RequirementDataSource.requirement_id == requirement_id)

        # 总数据源数
        total_query = select(func.count(RequirementDataSource.id)).select_from(base_query.subquery())
        total_result = await db.execute(total_query)
        total_count = total_result.scalar()

        # 各类型数据源数
        type_query = select(
            RequirementDataSource.source_type,
            func.count(RequirementDataSource.id).label('count')
        ).select_from(base_query.subquery()).group_by(RequirementDataSource.source_type)
        
        type_result = await db.execute(type_query)
        type_counts = {row.source_type: row.count for row in type_result.fetchall()}

        # 各状态数据源数
        status_query = select(
            RequirementDataSource.is_active,
            func.count(RequirementDataSource.id).label('count')
        ).select_from(base_query.subquery()).group_by(RequirementDataSource.is_active)

        status_result = await db.execute(status_query)
        status_counts = {row.is_active: row.count for row in status_result.fetchall()}

        return {
            'total_count': total_count,
            'type_counts': type_counts,
            'status_counts': status_counts,
            'active_count': status_counts.get(1, 0),
            'inactive_count': status_counts.get(0, 0)
        }

    @classmethod
    async def clear_requirement_data_sources(cls, db: AsyncSession, requirement_id: int):
        """
        清空需求下的所有数据源（软删除）

        :param db: orm对象
        :param requirement_id: 需求ID
        :return:
        """
        await db.execute(
            update(RequirementDataSource)
            .where(RequirementDataSource.requirement_id == requirement_id)
            .values(is_active=0, update_time=datetime.now())
        )
