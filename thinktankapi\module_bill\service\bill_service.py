from datetime import datetime, date
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from module_bill.dao.bill_dao import BillDao
from module_bill.entity.vo.bill_vo import (
    BillListQueryModel, 
    BillListResponseModel, 
    UserOrderModel, 
    UserInfoModel,
    UserMembershipInfoModel,
    UserPackageModel
)
from module_bill.entity.do.order_do import UserOrder, UserPackage
from module_admin.entity.do.user_do import SysUser
from utils.log_util import logger


class BillService:
    """
    账单管理服务层
    """

    @staticmethod
    def get_membership_status_by_vip_level(vip_level: int) -> str:
        """
        根据VIP等级获取会员状态（统一使用PackageConfig配置）

        :param vip_level: VIP等级
        :return: 会员状态描述
        """
        try:
            from config.package_config import PackageConfig
            return PackageConfig.get_membership_status(vip_level)
        except Exception as e:
            logger.error(f"获取会员状态失败: {str(e)}")
            # 备用映射
            vip_mapping = {
                0: "基础版会员",
                1: "VIP1会员",
                2: "VIP2会员",
                3: "VIP3会员",
                4: "企业版会员"
            }
            return vip_mapping.get(vip_level, "基础版会员")

    @staticmethod
    async def get_user_orders_page(db: AsyncSession, query_model: BillListQueryModel) -> BillListResponseModel:
        """
        分页查询用户订单
        """
        try:
            orders, total = await BillDao.get_user_orders_page(db, query_model)
            
            # 转换为VO模型
            order_models = []
            for order in orders:
                order_model = UserOrderModel(
                    order_id=order.order_id,
                    order_no=order.order_no,
                    user_id=order.user_id,
                    package_id=order.package_id,
                    package_name=order.package_name,
                    original_price=order.original_price,
                    discount_amount=order.discount_amount,
                    final_price=order.final_price,
                    payment_method=order.payment_method,
                    order_status=order.order_status,
                    payment_status=order.payment_status,
                    payment_time=order.payment_time,
                    expire_time=order.expire_time,
                    transaction_id=order.transaction_id,
                    payment_data=order.payment_data,
                    remark=order.remark,
                    create_time=order.create_time,
                    update_time=order.update_time
                )
                order_models.append(order_model)
            
            return BillListResponseModel(
                total=total,
                rows=order_models,
                page_num=query_model.page_num,
                page_size=query_model.page_size
            )
        except Exception as e:
            logger.error(f"查询用户订单失败: {str(e)}")
            raise e

    @staticmethod
    async def get_user_membership_info(db: AsyncSession, user_id: int) -> UserMembershipInfoModel:
        """
        获取用户会员信息
        """
        try:
            # 获取用户基本信息
            user = await BillDao.get_user_by_id(db, user_id)
            if not user:
                raise ValueError(f"用户不存在: {user_id}")
            
            user_info = UserInfoModel(
                user_id=user.user_id,
                user_name=user.user_name,
                nick_name=user.nick_name,
                email=user.email,
                avatar=user.avatar,
                vip_level=user.vip_level,
                account_balance=user.account_balance,
                total_spent=user.total_spent,
                last_payment_time=user.last_payment_time
            )
            
            # 统一使用 vip_level 判断会员状态
            vip_level = user.vip_level or 0
            membership_status = BillService.get_membership_status_by_vip_level(vip_level)

            logger.info(f"用户{user_id}的VIP等级: {vip_level}, 会员状态: {membership_status}")

            # 获取用户最新的已支付订单（仅用于获取套餐信息和到期时间）
            latest_order = await BillDao.get_latest_paid_order_by_user_id(db, user_id)

            current_package = None
            expire_time = None

            if latest_order:
                # 获取套餐信息
                package = await BillDao.get_package_by_id(db, latest_order.package_id)
                if package:
                    current_package = UserPackageModel(
                        package_id=package.package_id,
                        package_name=package.package_name,
                        package_type=package.package_type,
                        analysis_limit=package.analysis_limit,
                        duration_days=package.duration_days,
                        original_price=package.original_price,
                        current_price=package.current_price,
                        discount_rate=package.discount_rate,
                        features=package.features,
                        is_active=package.is_active,
                        sort_order=package.sort_order,
                        description=package.description,
                        create_time=package.create_time,
                        update_time=package.update_time
                    )

                    # 设置到期时间（如果订单未过期）
                    if latest_order.expire_time and latest_order.expire_time > datetime.now():
                        expire_time = latest_order.expire_time

                    # 会员状态统一由vip_level决定，不再受订单状态影响
                    logger.info(f"用户订单套餐: {package.package_name}, 到期时间: {latest_order.expire_time}")
            
            return UserMembershipInfoModel(
                user_info=user_info,
                current_package=current_package,
                membership_status=membership_status,
                expire_time=expire_time
            )
        except Exception as e:
            logger.error(f"获取用户会员信息失败: {str(e)}")
            raise e

    @staticmethod
    async def get_all_packages(db: AsyncSession) -> List[UserPackageModel]:
        """
        获取所有套餐
        """
        try:
            packages = await BillDao.get_all_packages(db)
            
            package_models = []
            for package in packages:
                package_model = UserPackageModel(
                    package_id=package.package_id,
                    package_name=package.package_name,
                    package_type=package.package_type,
                    analysis_limit=package.analysis_limit,
                    duration_days=package.duration_days,
                    original_price=package.original_price,
                    current_price=package.current_price,
                    discount_rate=package.discount_rate,
                    features=package.features,
                    is_active=package.is_active,
                    sort_order=package.sort_order,
                    description=package.description,
                    create_time=package.create_time,
                    update_time=package.update_time
                )
                package_models.append(package_model)
            
            return package_models
        except Exception as e:
            logger.error(f"获取套餐列表失败: {str(e)}")
            raise e

    @staticmethod
    async def get_order_by_id(db: AsyncSession, order_id: int) -> Optional[UserOrderModel]:
        """
        根据订单ID获取订单信息
        """
        try:
            order = await BillDao.get_order_by_id(db, order_id)
            if not order:
                return None
            
            return UserOrderModel(
                order_id=order.order_id,
                order_no=order.order_no,
                user_id=order.user_id,
                package_id=order.package_id,
                package_name=order.package_name,
                original_price=order.original_price,
                discount_amount=order.discount_amount,
                final_price=order.final_price,
                payment_method=order.payment_method,
                order_status=order.order_status,
                payment_status=order.payment_status,
                payment_time=order.payment_time,
                expire_time=order.expire_time,
                transaction_id=order.transaction_id,
                payment_data=order.payment_data,
                remark=order.remark,
                create_time=order.create_time,
                update_time=order.update_time
            )
        except Exception as e:
            logger.error(f"获取订单信息失败: {str(e)}")
            raise e

    @staticmethod
    async def get_user_orders_by_user_id(db: AsyncSession, user_id: int, limit: int = 10) -> List[UserOrderModel]:
        """
        根据用户ID获取订单列表
        """
        try:
            orders = await BillDao.get_user_orders_by_user_id(db, user_id, limit)
            
            order_models = []
            for order in orders:
                order_model = UserOrderModel(
                    order_id=order.order_id,
                    order_no=order.order_no,
                    user_id=order.user_id,
                    package_id=order.package_id,
                    package_name=order.package_name,
                    original_price=order.original_price,
                    discount_amount=order.discount_amount,
                    final_price=order.final_price,
                    payment_method=order.payment_method,
                    order_status=order.order_status,
                    payment_status=order.payment_status,
                    payment_time=order.payment_time,
                    expire_time=order.expire_time,
                    transaction_id=order.transaction_id,
                    payment_data=order.payment_data,
                    remark=order.remark,
                    create_time=order.create_time,
                    update_time=order.update_time
                )
                order_models.append(order_model)
            
            return order_models
        except Exception as e:
            logger.error(f"获取用户订单列表失败: {str(e)}")
            raise e
