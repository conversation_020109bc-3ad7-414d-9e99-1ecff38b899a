"""
自定义数据源爬取服务
"""

import asyncio
import aiohttp
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from utils.log_util import logger
from exceptions.exception import ServiceException
from module_opinion.dao.requirement_data_source_dao import RequirementDataSourceDao
from module_admin.dao.keyword_data_dao import KeywordDataDao
from module_admin.entity.vo.keyword_data_vo import KeywordDataModel
from datetime import datetime
import re
from urllib.parse import urljoin, urlparse


class CustomDataSourceService:
    """
    自定义数据源爬取服务类
    """
    
    # 请求超时时间（秒）
    REQUEST_TIMEOUT = 30
    
    # 最大重试次数
    MAX_RETRIES = 3
    
    # 用户代理
    USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

    @classmethod
    async def crawl_custom_data_sources(
        cls, 
        query_db: AsyncSession, 
        requirement_id: int,
        entity_keyword: str, 
        specific_requirement: str, 
        selected_keywords: List[str]
    ) -> Dict[str, Any]:
        """
        爬取自定义数据源
        
        :param query_db: 数据库会话
        :param requirement_id: 需求ID
        :param entity_keyword: 实体关键词
        :param specific_requirement: 具体需求
        :param selected_keywords: 选中的关键词
        :return: 爬取结果
        """
        try:
            logger.info(f"开始爬取需求 {requirement_id} 的自定义数据源")
            
            # 获取需求对应的数据源列表
            data_sources = await RequirementDataSourceDao.get_data_sources_by_requirement(
                query_db, requirement_id
            )
            
            if not data_sources:
                logger.warning(f"需求 {requirement_id} 没有配置数据源")
                return {
                    'success': False,
                    'message': '没有配置自定义数据源',
                    'articles': [],
                    'saved_count': 0
                }
            
            logger.info(f"找到 {len(data_sources)} 个数据源")
            
            # 爬取所有数据源
            all_articles = []
            successful_sources = 0
            
            for data_source in data_sources:
                if not data_source.source_url:
                    logger.warning(f"数据源 {data_source.source_name} 没有配置URL")
                    continue
                
                try:
                    logger.info(f"开始爬取数据源: {data_source.source_name} ({data_source.source_url})")
                    
                    articles = await cls._crawl_single_source(
                        data_source.source_url,
                        data_source.source_name,
                        entity_keyword,
                        selected_keywords
                    )
                    
                    if articles:
                        all_articles.extend(articles)
                        successful_sources += 1
                        logger.info(f"从 {data_source.source_name} 获取到 {len(articles)} 条数据")
                    
                except Exception as e:
                    logger.error(f"爬取数据源 {data_source.source_name} 失败: {str(e)}")
                    continue
            
            logger.info(f"爬取完成，共从 {successful_sources}/{len(data_sources)} 个数据源获取到 {len(all_articles)} 条数据")
            
            # 保存爬取结果到数据库
            saved_count = 0
            if all_articles:
                saved_count = await cls._save_crawl_results_to_db(
                    query_db, all_articles, entity_keyword, selected_keywords
                )
            
            return {
                'success': True,
                'message': f'成功爬取 {successful_sources} 个数据源，获取 {len(all_articles)} 条数据',
                'articles': all_articles,
                'saved_count': saved_count,
                'source_count': len(data_sources),
                'successful_sources': successful_sources
            }
            
        except Exception as e:
            logger.error(f"爬取自定义数据源失败: {str(e)}")
            raise ServiceException(f"爬取自定义数据源失败: {str(e)}")

    @classmethod
    async def _crawl_single_source(
        cls, 
        url: str, 
        source_name: str, 
        entity_keyword: str, 
        keywords: List[str]
    ) -> List[Dict[str, Any]]:
        """
        爬取单个数据源
        
        :param url: 数据源URL
        :param source_name: 数据源名称
        :param entity_keyword: 实体关键词
        :param keywords: 关键词列表
        :return: 文章列表
        """
        headers = {
            'User-Agent': cls.USER_AGENT,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        timeout = aiohttp.ClientTimeout(total=cls.REQUEST_TIMEOUT)
        
        for attempt in range(cls.MAX_RETRIES):
            try:
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(url, headers=headers) as response:
                        if response.status == 200:
                            content = await response.text()
                            
                            # 解析网页内容
                            articles = cls._parse_web_content(
                                content, url, source_name, entity_keyword, keywords
                            )
                            
                            return articles
                        else:
                            logger.warning(f"HTTP {response.status} for {url}")
                            
            except asyncio.TimeoutError:
                logger.warning(f"请求超时 (尝试 {attempt + 1}/{cls.MAX_RETRIES}): {url}")
            except Exception as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{cls.MAX_RETRIES}): {url}, 错误: {str(e)}")
            
            if attempt < cls.MAX_RETRIES - 1:
                await asyncio.sleep(2 ** attempt)  # 指数退避
        
        logger.error(f"所有重试都失败了: {url}")
        return []

    @classmethod
    def _parse_web_content(
        cls, 
        content: str, 
        url: str, 
        source_name: str, 
        entity_keyword: str, 
        keywords: List[str]
    ) -> List[Dict[str, Any]]:
        """
        解析网页内容
        
        :param content: 网页内容
        :param url: 网页URL
        :param source_name: 数据源名称
        :param entity_keyword: 实体关键词
        :param keywords: 关键词列表
        :return: 解析后的文章列表
        """
        articles = []
        
        try:
            # 移除HTML标签，提取纯文本
            text_content = re.sub(r'<[^>]+>', ' ', content)
            text_content = re.sub(r'\s+', ' ', text_content).strip()
            
            # 检查是否包含关键词
            all_keywords = [entity_keyword] + keywords
            keyword_matches = []
            
            for keyword in all_keywords:
                if keyword.lower() in text_content.lower():
                    keyword_matches.append(keyword)
            
            # 如果包含关键词，创建文章记录
            if keyword_matches:
                # 提取标题（尝试从title标签中提取）
                title_match = re.search(r'<title[^>]*>([^<]+)</title>', content, re.IGNORECASE)
                title = title_match.group(1).strip() if title_match else f"{source_name} - 相关内容"
                
                # 截取相关内容片段
                content_snippet = cls._extract_relevant_content(text_content, keyword_matches)
                
                article = {
                    'title': title,
                    'content': content_snippet,
                    'url': url,
                    'source': source_name,
                    'publish_time': cls._get_current_timestamp(),
                    'sentiment': cls._analyze_text_sentiment(content_snippet),
                    'matched_keywords': keyword_matches
                }
                
                articles.append(article)
                logger.info(f"从 {source_name} 提取到相关内容，匹配关键词: {', '.join(keyword_matches)}")
            
        except Exception as e:
            logger.error(f"解析网页内容失败: {str(e)}")
        
        return articles

    @classmethod
    def _extract_relevant_content(cls, text: str, keywords: List[str]) -> str:
        """
        提取与关键词相关的内容片段
        
        :param text: 文本内容
        :param keywords: 关键词列表
        :return: 相关内容片段
        """
        # 找到第一个关键词的位置
        first_keyword_pos = len(text)
        for keyword in keywords:
            pos = text.lower().find(keyword.lower())
            if pos != -1 and pos < first_keyword_pos:
                first_keyword_pos = pos
        
        if first_keyword_pos == len(text):
            # 如果没找到关键词，返回开头部分
            return text[:1000] + '...' if len(text) > 1000 else text
        
        # 提取关键词前后的内容
        start = max(0, first_keyword_pos - 200)
        end = min(len(text), first_keyword_pos + 800)
        
        snippet = text[start:end]
        
        # 如果不是从开头开始，添加省略号
        if start > 0:
            snippet = '...' + snippet
        
        # 如果不是到结尾，添加省略号
        if end < len(text):
            snippet = snippet + '...'
        
        return snippet

    @classmethod
    def _analyze_text_sentiment(cls, text: str) -> str:
        """
        简单的文本情感分析
        """
        positive_words = ['好', '优秀', '成功', '增长', '提升', '改善', '积极', '正面', '赞', '支持', '喜欢', '满意']
        negative_words = ['坏', '失败', '下降', '问题', '困难', '危机', '负面', '批评', '反对', '担心', '不满', '失望']
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'

    @classmethod
    def _get_current_timestamp(cls) -> str:
        """
        获取当前时间戳
        """
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    @classmethod
    async def _save_crawl_results_to_db(
        cls, 
        query_db: AsyncSession, 
        articles: List[Dict[str, Any]], 
        entity_keyword: str, 
        selected_keywords: List[str]
    ) -> int:
        """
        保存爬取结果到数据库
        
        :param query_db: 数据库会话
        :param articles: 文章列表
        :param entity_keyword: 实体关键词
        :param selected_keywords: 选中的关键词
        :return: 保存的记录数
        """
        try:
            saved_count = 0
            
            # 将所有关键词合并为一个字符串
            all_keywords = [entity_keyword] + selected_keywords
            keywords_str = ','.join(all_keywords)
            
            logger.info(f"准备保存 {len(articles)} 条爬取结果到数据库")
            
            for i, article in enumerate(articles):
                try:
                    # 数据验证和清理
                    title = article.get('title', f'自定义数据源结果 {i+1}')[:255]
                    content = article.get('content', '')[:10000]
                    url = article.get('url', '')[:255]
                    web = article.get('source', '自定义数据源')[:255]
                    sentiment = article.get('sentiment', 'neutral')
                    
                    # 确保sentiment值有效
                    if sentiment not in ['positive', 'negative', 'neutral']:
                        sentiment = 'neutral'
                    
                    # 创建KeywordDataModel对象
                    keyword_data = KeywordDataModel(
                        title=title,
                        content=content,
                        url=url,
                        keyword=keywords_str,
                        type='custom-data-source',  # 标记为自定义数据源
                        web=web,
                        sentiment=sentiment
                    )
                    
                    # 保存到数据库
                    result = await KeywordDataDao.add_keyword_data_dao(query_db, keyword_data)
                    if result:
                        saved_count += 1
                        logger.info(f"成功保存第 {i+1} 条爬取结果，ID: {result.id}")
                    else:
                        logger.error(f"保存第 {i+1} 条爬取结果失败：数据库操作返回空结果")
                    
                except Exception as e:
                    logger.error(f"保存第 {i+1} 条爬取结果失败: {str(e)}")
                    continue
            
            logger.info(f"爬取结果保存完成，成功保存 {saved_count}/{len(articles)} 条记录")
            return saved_count
            
        except Exception as e:
            logger.error(f"保存爬取结果到数据库失败: {str(e)}")
            return 0
