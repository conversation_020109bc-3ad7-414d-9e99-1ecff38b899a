from datetime import datetime, date
from typing import List, Optional, <PERSON><PERSON>
from sqlalchemy import and_, or_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from module_bill.entity.do.order_do import UserOrder, UserPackage
from module_admin.entity.do.user_do import SysUser
from module_bill.entity.vo.bill_vo import BillListQueryModel


class BillDao:
    """
    账单管理数据访问层
    """

    @staticmethod
    async def get_user_orders_page(db: AsyncSession, query_model: BillListQueryModel) -> Tuple[List[UserOrder], int]:
        """
        分页查询用户订单
        """
        # 构建查询条件
        conditions = []

        if query_model.user_id:
            conditions.append(UserOrder.user_id == query_model.user_id)

        if query_model.payment_status:
            conditions.append(UserOrder.payment_status == query_model.payment_status)

        if query_model.payment_method:
            conditions.append(UserOrder.payment_method == query_model.payment_method)

        if query_model.order_status:
            conditions.append(UserOrder.order_status == query_model.order_status)

        if query_model.start_date:
            conditions.append(UserOrder.create_time >= query_model.start_date)

        if query_model.end_date:
            conditions.append(UserOrder.create_time <= query_model.end_date)

        # 查询总数
        total_query = select(func.count(UserOrder.order_id))
        if conditions:
            total_query = total_query.where(and_(*conditions))
        total_result = await db.execute(total_query)
        total = total_result.scalar()

        # 分页查询
        query = select(UserOrder)
        if conditions:
            query = query.where(and_(*conditions))

        query = query.order_by(desc(UserOrder.create_time))\
                     .offset((query_model.page_num - 1) * query_model.page_size)\
                     .limit(query_model.page_size)

        result = await db.execute(query)
        orders = result.scalars().all()

        return orders, total

    @staticmethod
    async def get_user_by_id(db: AsyncSession, user_id: int) -> Optional[SysUser]:
        """
        根据用户ID获取用户信息
        """
        query = select(SysUser).where(SysUser.user_id == user_id)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def get_package_by_id(db: AsyncSession, package_id: int) -> Optional[UserPackage]:
        """
        根据套餐ID获取套餐信息
        """
        query = select(UserPackage).where(UserPackage.package_id == package_id)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def get_order_by_id(db: AsyncSession, order_id: int) -> Optional[UserOrder]:
        """
        根据订单ID获取订单信息
        """
        query = select(UserOrder).where(UserOrder.order_id == order_id)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def get_order_by_no(db: AsyncSession, order_no: str) -> Optional[UserOrder]:
        """
        根据订单号获取订单信息
        """
        query = select(UserOrder).where(UserOrder.order_no == order_no)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def create_order(db: AsyncSession, order: UserOrder) -> UserOrder:
        """
        创建订单
        """
        db.add(order)
        await db.commit()
        await db.refresh(order)
        return order

    @staticmethod
    async def update_order(db: AsyncSession, order: UserOrder) -> UserOrder:
        """
        更新订单
        """
        await db.commit()
        await db.refresh(order)
        return order

    @staticmethod
    async def get_all_packages(db: AsyncSession) -> List[UserPackage]:
        """
        获取所有启用的套餐
        """
        query = select(UserPackage)\
                .where(UserPackage.is_active == 1)\
                .order_by(UserPackage.sort_order)
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def get_user_orders_by_user_id(db: AsyncSession, user_id: int, limit: int = 10) -> List[UserOrder]:
        """
        根据用户ID获取订单列表
        """
        query = select(UserOrder)\
                .where(UserOrder.user_id == user_id)\
                .order_by(desc(UserOrder.create_time))\
                .limit(limit)
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def get_latest_paid_order_by_user_id(db: AsyncSession, user_id: int) -> Optional[UserOrder]:
        """
        获取用户最新的已支付订单
        """
        query = select(UserOrder)\
                .where(and_(
                    UserOrder.user_id == user_id,
                    UserOrder.payment_status == 'paid'
                ))\
                .order_by(desc(UserOrder.payment_time))\
                .limit(1)
        result = await db.execute(query)
        return result.scalar_one_or_none()
