import os
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from config.env import UploadConfig


def mount_staticfiles(app: FastAPI):
    """
    挂载静态文件
    """
    app.mount(f'{UploadConfig.UPLOAD_PREFIX}', StaticFiles(directory=f'{UploadConfig.UPLOAD_PATH}'), name='profile')

    # 挂载报告页面静态文件
    reports_dir = os.path.join(UploadConfig.UPLOAD_PATH, 'reports')
    if not os.path.exists(reports_dir):
        os.makedirs(reports_dir)
    app.mount('/reports', StaticFiles(directory=reports_dir), name='reports')
