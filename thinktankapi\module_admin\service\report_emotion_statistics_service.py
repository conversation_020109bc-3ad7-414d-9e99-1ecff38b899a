from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from module_admin.dao.report_emotion_statistics_dao import ReportEmotionStatisticsDao
from module_admin.entity.do.report_emotion_statistics_do import ReportEmotionStatisticsDO
from module_admin.entity.vo.report_emotion_statistics_vo import (
    ReportEmotionStatisticsPageQueryModel,
    CreateReportEmotionStatisticsModel,
    UpdateReportEmotionStatisticsModel,
    ReportEmotionStatisticsModel,
    ReportEmotionSummaryModel,
    ReportEmotionTrendModel,
    ReportEmotionComparisonModel,
    BatchCreateReportEmotionStatisticsModel
)
from utils.response_util import ResponseUtil
from utils.page_util import PageResponseModel
from utils.log_util import logger
import json


class ReportEmotionStatisticsService:
    """
    报告情感统计服务层
    """

    @classmethod
    async def get_report_emotion_statistics_list(
        cls,
        query_db: AsyncSession,
        query_object: ReportEmotionStatisticsPageQueryModel
    ) -> PageResponseModel:
        """
        获取报告情感统计分页列表
        
        :param query_db: orm对象
        :param query_object: 查询参数对象
        :return: 分页响应模型
        """
        try:
            # 获取列表数据
            statistics_list = await ReportEmotionStatisticsDao.get_report_emotion_statistics_list(
                query_db, query_object, is_page=True
            )
            
            # 获取总数
            total = await ReportEmotionStatisticsDao.get_report_emotion_statistics_count(
                query_db, query_object
            )
            
            # 转换为响应模型
            statistics_list_result = []
            for statistics in statistics_list:
                statistics_dict = statistics.to_dict()
                statistics_list_result.append(ReportEmotionStatisticsModel(**statistics_dict))
            
            return PageResponseModel(
                rows=statistics_list_result,
                total=total,
                page_num=query_object.page_num,
                page_size=query_object.page_size
            )
            
        except Exception as e:
            logger.error(f'获取报告情感统计列表失败: {str(e)}')
            raise e

    @classmethod
    async def get_report_emotion_statistics_detail(
        cls,
        query_db: AsyncSession,
        report_id: str
    ) -> Dict[str, Any]:
        """
        获取报告情感统计详情
        
        :param query_db: orm对象
        :param report_id: 报告ID
        :return: 统计详情
        """
        try:
            statistics = await ReportEmotionStatisticsDao.get_report_emotion_statistics_detail(
                query_db, report_id
            )
            
            if not statistics:
                return ResponseUtil.failure(msg='报告情感统计不存在')
            
            # 转换为响应模型
            statistics_model = ReportEmotionStatisticsModel(**statistics.to_dict())
            summary_model = ReportEmotionSummaryModel(**statistics.get_emotion_summary())
            
            return ResponseUtil.success(data={
                'statistics': statistics_model,
                'summary': summary_model
            })
            
        except Exception as e:
            logger.error(f'获取报告情感统计详情失败: {str(e)}')
            return ResponseUtil.error(msg=f'获取详情失败: {str(e)}')

    @classmethod
    async def add_report_emotion_statistics(
        cls,
        query_db: AsyncSession,
        add_request: CreateReportEmotionStatisticsModel,
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        新增报告情感统计
        
        :param query_db: orm对象
        :param add_request: 新增请求
        :param user_id: 用户ID
        :return: 新增结果
        """
        try:
            # 检查报告ID是否已存在
            if await ReportEmotionStatisticsDao.check_report_id_exists(query_db, add_request.report_id):
                return ResponseUtil.failure(msg='报告ID已存在')
            
            # 新增统计记录
            new_statistics = await ReportEmotionStatisticsDao.add_report_emotion_statistics(
                query_db, add_request, user_id
            )
            
            await query_db.commit()
            
            logger.info(f'新增报告情感统计成功，报告ID: {add_request.report_id}')
            return ResponseUtil.success(data=ReportEmotionStatisticsModel(**new_statistics.to_dict()))
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f'新增报告情感统计失败: {str(e)}')
            return ResponseUtil.error(msg=f'新增失败: {str(e)}')

    @classmethod
    async def edit_report_emotion_statistics(
        cls,
        query_db: AsyncSession,
        edit_request: UpdateReportEmotionStatisticsModel,
        report_id: str,
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        编辑报告情感统计
        
        :param query_db: orm对象
        :param edit_request: 编辑请求
        :param report_id: 报告ID
        :param user_id: 用户ID
        :return: 编辑结果
        """
        try:
            # 编辑统计记录
            edit_count = await ReportEmotionStatisticsDao.edit_report_emotion_statistics(
                query_db, edit_request, report_id, user_id
            )
            
            if edit_count == 0:
                return ResponseUtil.failure(msg='报告情感统计不存在')
            
            await query_db.commit()
            
            logger.info(f'编辑报告情感统计成功，报告ID: {report_id}')
            return ResponseUtil.success(msg='编辑成功')
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f'编辑报告情感统计失败: {str(e)}')
            return ResponseUtil.error(msg=f'编辑失败: {str(e)}')

    @classmethod
    async def delete_report_emotion_statistics(
        cls,
        query_db: AsyncSession,
        report_ids: List[str]
    ) -> Dict[str, Any]:
        """
        删除报告情感统计
        
        :param query_db: orm对象
        :param report_ids: 报告ID列表
        :return: 删除结果
        """
        try:
            # 删除统计记录
            delete_count = await ReportEmotionStatisticsDao.delete_report_emotion_statistics(
                query_db, report_ids
            )
            
            await query_db.commit()
            
            logger.info(f'删除报告情感统计成功，删除数量: {delete_count}')
            return ResponseUtil.success(msg=f'删除成功，共删除{delete_count}条记录')
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f'删除报告情感统计失败: {str(e)}')
            return ResponseUtil.error(msg=f'删除失败: {str(e)}')

    @classmethod
    async def batch_create_report_emotion_statistics(
        cls,
        query_db: AsyncSession,
        batch_request: BatchCreateReportEmotionStatisticsModel,
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        批量创建报告情感统计
        
        :param query_db: orm对象
        :param batch_request: 批量创建请求
        :param user_id: 用户ID
        :return: 创建结果
        """
        try:
            created_statistics = []
            failed_reports = []
            
            for statistics_data in batch_request.statistics_list:
                try:
                    # 检查报告ID是否已存在
                    if await ReportEmotionStatisticsDao.check_report_id_exists(query_db, statistics_data.report_id):
                        failed_reports.append({
                            'report_id': statistics_data.report_id,
                            'reason': '报告ID已存在'
                        })
                        continue
                    
                    # 创建统计记录
                    new_statistics = await ReportEmotionStatisticsDao.add_report_emotion_statistics(
                        query_db, statistics_data, user_id
                    )
                    created_statistics.append(new_statistics.to_dict())
                    
                except Exception as e:
                    failed_reports.append({
                        'report_id': statistics_data.report_id,
                        'reason': str(e)
                    })
            
            await query_db.commit()
            
            result_data = {
                'created_count': len(created_statistics),
                'failed_count': len(failed_reports),
                'created_statistics': created_statistics,
                'failed_reports': failed_reports
            }
            
            logger.info(f'批量创建报告情感统计完成，成功: {len(created_statistics)}, 失败: {len(failed_reports)}')
            return ResponseUtil.success(data=result_data)
            
        except Exception as e:
            await query_db.rollback()
            logger.error(f'批量创建报告情感统计失败: {str(e)}')
            return ResponseUtil.error(msg=f'批量创建失败: {str(e)}')

    @classmethod
    async def get_emotion_trend_data(
        cls,
        query_db: AsyncSession,
        scheme_id: Optional[int] = None,
        days: int = 7
    ) -> Dict[str, Any]:
        """
        获取情感趋势数据
        
        :param query_db: orm对象
        :param scheme_id: 方案ID
        :param days: 天数
        :return: 趋势数据
        """
        try:
            trend_data = await ReportEmotionStatisticsDao.get_emotion_trend_data(
                query_db, scheme_id, days
            )
            
            trend_models = [ReportEmotionTrendModel(**data) for data in trend_data]
            
            return ResponseUtil.success(data=trend_models)
            
        except Exception as e:
            logger.error(f'获取情感趋势数据失败: {str(e)}')
            return ResponseUtil.error(msg=f'获取趋势数据失败: {str(e)}')

    @classmethod
    async def get_emotion_summary_by_type(
        cls,
        query_db: AsyncSession,
        report_type: str = 'normal',
        days: int = 30
    ) -> Dict[str, Any]:
        """
        根据报告类型获取情感统计摘要
        
        :param query_db: orm对象
        :param report_type: 报告类型
        :param days: 天数
        :return: 统计摘要
        """
        try:
            summary_data = await ReportEmotionStatisticsDao.get_emotion_summary_by_type(
                query_db, report_type, days
            )
            
            return ResponseUtil.success(data=summary_data)
            
        except Exception as e:
            logger.error(f'获取情感统计摘要失败: {str(e)}')
            return ResponseUtil.error(msg=f'获取统计摘要失败: {str(e)}')

    @classmethod
    async def get_emotion_statistics_by_scheme(
        cls,
        query_db: AsyncSession,
        scheme_id: int,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        根据方案ID获取情感统计数据
        
        :param query_db: orm对象
        :param scheme_id: 方案ID
        :param days: 天数
        :return: 统计数据
        """
        try:
            statistics_list = await ReportEmotionStatisticsDao.get_emotion_statistics_by_scheme(
                query_db, scheme_id, days
            )
            
            statistics_models = [
                ReportEmotionStatisticsModel(**statistics.to_dict())
                for statistics in statistics_list
            ]
            
            return ResponseUtil.success(data=statistics_models)
            
        except Exception as e:
            logger.error(f'获取方案情感统计数据失败: {str(e)}')
            return ResponseUtil.error(msg=f'获取统计数据失败: {str(e)}')
