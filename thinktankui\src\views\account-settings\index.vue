<template>
  <div class="account-management">
    <!-- 用户信息卡片 -->
    <div class="user-info-card" v-loading="loading">
      <div class="user-info-left">
        <div class="user-avatar">
          <span>{{ user_info.nick_name ? user_info.nick_name.charAt(0).toUpperCase() : 'U' }}</span>
        </div>
        <div class="user-details">
          <h3>{{ user_info.nick_name || user_info.user_name }}</h3>
          <p v-if="user_info.email" class="user-email">{{ user_info.email }}</p>
          <p v-else class="no-email">未设置邮箱</p>
        </div>
      </div>
      <div class="user-info-right">
        <span v-if="getMembershipStatusText()" class="member-tag">{{ getMembershipStatusText() }}</span>
        <span class="expire-date" v-if="membership_info.expire_time">
          到期时间：{{ formatDate(membership_info.expire_time) }}
        </span>
        <span class="expire-date" v-else-if="shouldShowExpireTime()">
          永久有效
        </span>
        <span class="expire-date" v-else-if="user_info.create_time">
          注册时间：{{ formatDate(user_info.create_time) }}
        </span>
      </div>
    </div>

    <!-- 账户设置 -->
    <el-card class="settings-card" shadow="never">
      <div slot="header" class="card-header">
        <span class="card-title">账户设置</span>
      </div>
      
      <el-form
        ref="userForm"
        :model="user_form"
        :rules="form_rules"
        label-width="80px"
        class="user-form"
      >
        <el-form-item label="用户昵称" prop="nick_name">
          <el-input
            v-model="user_form.nick_name"
            placeholder="请输入用户昵称"
            style="width: 300px;"
          />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="user_form.email"
            placeholder="请输入邮箱"
            style="width: 300px;"
          />
        </el-form-item>

        <el-form-item label="手机号" prop="phonenumber">
          <el-input
            v-model="user_form.phonenumber"
            placeholder="请输入手机号"
            style="width: 300px;"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="saveSettings"
            :loading="saving"
          >
            保存设置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getUserProfile, updateUserProfile } from '@/api/system/user'
import { getBillDashboardData } from '@/api/bill'
import { mapGetters } from 'vuex'

export default {
  name: 'AccountManagement',
  computed: {
    ...mapGetters(['id'])
  },
  data() {
    return {
      loading: false,
      saving: false,
      user_info: {
        user_id: null,
        user_name: '',
        nick_name: '',
        email: '',
        avatar: '',
        phonenumber: '',
        create_time: null,
        role: [],
        vip_level: 0,
        account_balance: 0.00,
        total_spent: 0.00,
        last_payment_time: null
      },
      membership_info: {
        membership_status: '',
        expire_time: null,
        current_package: null
      },
      user_form: {
        nick_name: '',
        email: '',
        phonenumber: ''
      },
      form_rules: {
        nick_name: [
          { required: true, message: '请输入用户昵称', trigger: 'blur' },
          { min: 2, max: 20, message: '用户昵称长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        phonenumber: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getUserInfo()
    this.loadMembershipInfo()
  },
  methods: {
    // 获取用户信息
    async getUserInfo() {
      this.loading = true
      try {
        const response = await getUserProfile()
        if (response.code === 200 && response.data) {
          // 处理API返回的数据结构
          const userData = response.data.data || response.data
          this.user_info = {
            user_id: userData.userId || userData.user_id,
            user_name: userData.userName || userData.user_name,
            nick_name: userData.nickName || userData.nick_name,
            email: userData.email,
            avatar: userData.avatar,
            phonenumber: userData.phonenumber,
            create_time: userData.createTime || userData.create_time,
            role: userData.role || [],
            vip_level: userData.vipLevel || userData.vip_level || 0,
            account_balance: userData.accountBalance || userData.account_balance || 0.00,
            total_spent: userData.totalSpent || userData.total_spent || 0.00,
            last_payment_time: userData.lastPaymentTime || userData.last_payment_time || null
          }

          // 同步到表单
          this.user_form = {
            nick_name: this.user_info.nick_name || '',
            email: this.user_info.email || '',
            phonenumber: this.user_info.phonenumber || ''
          }
        } else {
          this.$message.error(response.msg || '获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.$message.error('获取用户信息失败')
      } finally {
        this.loading = false
      }
    },

    // 加载会员信息
    async loadMembershipInfo() {
      try {
        const userId = this.id || this.$store.getters.id
        if (!userId) {
          console.warn('用户ID未获取到，跳过会员信息加载')
          return
        }

        const response = await getBillDashboardData(userId)
        if (response.code === 200) {
          this.membership_info = response.data.membership_info || {}
        } else {
          console.error('获取会员信息失败:', response.msg)
        }
      } catch (error) {
        console.error('加载会员信息失败:', error)
      }
    },

    // 保存设置
    saveSettings() {
      this.$refs.userForm.validate(async (valid) => {
        if (valid) {
          this.saving = true
          try {
            // 构造符合API要求的数据格式
            const updateData = {
              nickName: this.user_form.nick_name,
              email: this.user_form.email,
              phonenumber: this.user_form.phonenumber
            }

            const response = await updateUserProfile(updateData)
            if (response.code === 200) {
              this.$message.success('保存成功')
              // 更新用户信息显示
              this.user_info.nick_name = this.user_form.nick_name
              this.user_info.email = this.user_form.email
              this.user_info.phonenumber = this.user_form.phonenumber
            } else {
              this.$message.error(response.msg || '保存失败')
            }
          } catch (error) {
            console.error('保存用户信息失败:', error)
            this.$message.error('保存失败')
          } finally {
            this.saving = false
          }
        }
      })
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    },

    // 获取会员状态显示文本
    getMembershipStatusText() {
      const status = this.membership_info.membership_status
      if (!status) {
        return ''  // 没有数据时返回空字符串
      }
      // 统一术语显示
      if (status === '基础版') {
        return '基础版会员'
      }
      if (status === '专业版') {
        return '专业版会员'
      }
      if (status === '企业版') {
        return '企业版会员'
      }
      return status
    },

    // 判断是否应该显示到期时间
    shouldShowExpireTime() {
      const status = this.membership_info.membership_status
      // 如果是普通用户或没有状态，不显示到期时间
      if (!status || status === '普通用户') {
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.account-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 统一的用户信息卡片样式 */
.user-info-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.user-info-left {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #36cfc9;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.user-avatar span {
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.user-details h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #303133;
}

.user-details p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.user-details .user-email {
  color: #909399;
}

.user-details .no-email {
  color: #c0c4cc;
  font-style: italic;
}

.user-info-right {
  text-align: right;
}

.member-tag {
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  display: block;
  margin-bottom: 8px;
}

.expire-date {
  font-size: 14px;
  color: #666;
}

.settings-card {
  .card-header {
    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .user-form {
    padding: 20px 0;
  }
}

::v-deep .el-card__body {
  padding: 20px;
}

::v-deep .el-form-item {
  margin-bottom: 22px;
}

::v-deep .el-form-item__label {
  color: #606266;
  font-weight: 500;
}
</style>