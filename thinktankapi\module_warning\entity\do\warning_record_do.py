from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text, ForeignKey
from config.database import Base


class WarningRecord(Base):
    """
    预警记录表
    """

    __tablename__ = 'warning_record'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='预警记录ID')
    scheme_id = Column(Integer, ForeignKey('warning_scheme.id'), nullable=False, comment='预警方案ID')
    warning_type = Column(String(50), nullable=False, comment='预警类型')
    content = Column(Text, nullable=True, comment='预警内容')
    keywords = Column(Text, nullable=True, comment='关键词')
    status = Column(Integer, nullable=True, default=0, comment='状态')
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), nullable=True, default='', comment='创建者')
    update_by = Column(String(64), nullable=True, default='', comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')
