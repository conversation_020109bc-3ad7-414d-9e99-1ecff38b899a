from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, BigInteger
from sqlalchemy.orm import relationship
from config.database import Base


class SchemeDO(Base):
    """
    方案数据对象
    """
    __tablename__ = 'scheme'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='方案ID')
    user_id = Column(BigInteger, ForeignKey('sys_user.user_id'), nullable=False, comment='用户ID')
    type_id = Column(Integer, ForeignKey('scheme_type.id'), nullable=False, comment='方案类型ID')
    name = Column(String(100), nullable=False, comment='方案名称')
    description = Column(Text, comment='方案描述')
    status = Column(Integer, default=1, comment='状态：0-禁用，1-启用')
    refresh_status = Column(Integer, default=0, comment='刷新状态：0-未刷新，1-刷新中，2-已刷新')
    is_warning = Column(Boolean, default=False, comment='是否开启预警')
    version = Column(String(20), comment='版本号')
    create_time = Column(DateTime, comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')
    create_by = Column(String(64), comment='创建者')
    update_by = Column(String(64), comment='更新者')
    remark = Column(String(500), comment='备注')

    # 关联关系
    scheme_type = relationship("SchemeTypeDO", back_populates="schemes")
    scheme_config = relationship("SchemeConfigDO", back_populates="scheme", uselist=False)
    scheme_statistics = relationship("SchemeSummaryStatisticDO", back_populates="scheme", uselist=False)
    # scheme_news = relationship("SchemeNews", back_populates="scheme")  # 暂时注释掉，避免循环依赖


class SchemeTypeDO(Base):
    """
    方案类型数据对象
    """
    __tablename__ = 'scheme_type'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='类型ID')
    name = Column(String(50), unique=True, nullable=False, comment='类型名称')
    description = Column(Text, comment='类型描述')
    display_order = Column(Integer, default=0, comment='显示顺序')
    is_active = Column(Boolean, default=True, comment='是否激活')
    create_time = Column(DateTime, comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')
    create_by = Column(String(64), comment='创建者')
    update_by = Column(String(64), comment='更新者')
    remark = Column(String(500), comment='备注')

    # 关联关系
    schemes = relationship("SchemeDO", back_populates="scheme_type")


class SchemeConfigDO(Base):
    """
    方案配置数据对象
    """
    __tablename__ = 'scheme_config'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='配置ID')
    scheme_id = Column(Integer, ForeignKey('scheme.id'), unique=True, nullable=False, comment='方案ID')
    monitoring_keywords = Column(Text, comment='监控关键词')
    excluded_keywords = Column(Text, comment='排除关键词')
    material_limit = Column(Integer, default=0, comment='素材限制数量')
    user_condition = Column(Text, comment='用户条件（JSON格式）')
    create_time = Column(DateTime, comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')
    create_by = Column(String(64), comment='创建者')
    update_by = Column(String(64), comment='更新者')
    remark = Column(String(500), comment='备注')

    # 关联关系
    scheme = relationship("SchemeDO", back_populates="scheme_config")


class SchemeSummaryStatisticDO(Base):
    """
    方案汇总统计数据对象
    """
    __tablename__ = 'scheme_summary_statistic'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='统计ID')
    scheme_id = Column(Integer, ForeignKey('scheme.id'), nullable=False, comment='方案ID')
    all_total = Column(Integer, default=0, comment='总数量')
    media_count = Column(Integer, default=0, comment='媒体数量')
    negative_count = Column(Integer, default=0, comment='负面数量')
    today_count = Column(Integer, default=0, comment='今日数量')
    start_time = Column(DateTime, comment='开始时间')
    end_time = Column(DateTime, comment='结束时间')
    refresh_time = Column(DateTime, comment='刷新时间')
    create_time = Column(DateTime, comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')
    create_by = Column(String(64), comment='创建者')
    update_by = Column(String(64), comment='更新者')
    remark = Column(String(500), comment='备注')

    # 关联关系
    scheme = relationship("SchemeDO", back_populates="scheme_statistics")
