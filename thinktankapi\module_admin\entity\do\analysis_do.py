from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from config.database import Base


class EmotionAnalysisSummaryDO(Base):
    """
    情感分析汇总数据对象
    """
    __tablename__ = 'emotion_analysis_summary'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='汇总ID')
    scheme_id = Column(Integer, ForeignKey('scheme.id'), nullable=False, comment='方案ID')
    emotion_id = Column(Integer, nullable=False, comment='情感ID')
    count = Column(Integer, default=0, comment='数量')
    date_range_start = Column(DateTime, comment='日期范围开始')
    date_range_end = Column(DateTime, comment='日期范围结束')
    create_time = Column(DateTime, comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')
    create_by = Column(String(64), comment='创建者')
    update_by = Column(String(64), comment='更新者')
    remark = Column(String(500), comment='备注')


class PlatformAnalysisSummaryDO(Base):
    """
    平台分析汇总数据对象
    """
    __tablename__ = 'platform_analysis_summary'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='汇总ID')
    scheme_id = Column(Integer, ForeignKey('scheme.id'), nullable=False, comment='方案ID')
    platform_id = Column(Integer, nullable=False, comment='平台ID')
    count = Column(Integer, default=0, comment='数量')
    date_range_start = Column(DateTime, comment='日期范围开始')
    date_range_end = Column(DateTime, comment='日期范围结束')
    create_time = Column(DateTime, comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')
    create_by = Column(String(64), comment='创建者')
    update_by = Column(String(64), comment='更新者')
    remark = Column(String(500), comment='备注')
