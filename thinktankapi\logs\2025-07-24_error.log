2025-07-24 10:37:24.796 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-24 10:37:24.797 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-24 10:37:26.692 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-24 10:37:26.692 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-24 10:37:26.703 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-24 10:37:27.457 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-24 10:37:28.073 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-24 10:37:28.073 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-24 10:37:55.616 | 222c3fd07baa47fd95b78099213bb4d7 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为bead05ff-060b-4f9f-9112-d5252f5d2b17的会话获取图片验证码成功
2025-07-24 10:38:21.482 | 913046a26b224bb9943ef19a8ecbc0ac | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-24 10:38:21.774 | a2c9d21aefaa4b8f8160afc47d310cd2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-24 10:38:22.322 | 0cf6dcaef10e4ed6ad0c3e3f305e2350 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-24 10:38:22.993 | 89295813750f4bc6a68e3c42a27e804f | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-24 10:38:23.069 | 89295813750f4bc6a68e3c42a27e804f | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-24 10:38:23.069 | 89295813750f4bc6a68e3c42a27e804f | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-24 10:38:23.412 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-24 10:38:23.412 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-24 10:38:23.412 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-24 10:38:23.412 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-24 00:00:00 到 2025-07-24 23:59:59.999999 ===
2025-07-24 10:38:23.413 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-23 00:00:00 到 2025-07-23 23:59:59.999999 ===
2025-07-24 10:38:23.415 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-24 10:38:23.446 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-24 10:38:23.469 | 6dc7a2cf336143599a26a1ce84c1d292 | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-24 10:38:23.502 | 6dc7a2cf336143599a26a1ce84c1d292 | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 24
2025-07-24 10:38:23.502 | 6dc7a2cf336143599a26a1ce84c1d292 | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 24
2025-07-24 10:38:23.503 | 6dc7a2cf336143599a26a1ce84c1d292 | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-24 10:38:23.599 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-24 10:38:23.874 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-24 10:38:23.874 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 24, 10, 38, 23, 844335), 'package_name': '企业版', 'is_expired': True} ===
2025-07-24 10:38:23.906 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-24 10:38:23.907 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-24 10:38:23.938 | 816afb7e0a914255854734595344d6c7 | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-24 10:38:23.938 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-24 10:38:23.939 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 10:38:23.939 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 10:38:23.940 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-24 10:38:23.940 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 10:38:23.941 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 10:38:23.941 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-24 10:38:23.941 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 10:38:23.941 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-24 10:38:23.942 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 10:38:23.942 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-24 10:38:23.943 | 816afb7e0a914255854734595344d6c7 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x0000025FAF386E00> ===
2025-07-24 10:39:53.351 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 24, 10, 38, 21), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-24 10:39:53.352 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 10:39:53.352 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-24 10:39:53.353 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 10:39:53.353 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-24 10:39:53.354 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-24 10:39:53.354 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-24 10:39:53.354 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-24 10:39:53.355 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 10:39:53.355 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 10:39:53.356 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-24 10:39:53.356 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-24 10:39:53.357 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-24 10:39:53.359 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-24 10:39:53.360 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-24 10:39:53.433 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-24 10:39:53.433 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-24 10:39:53.434 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-24 10:39:53.434 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-24 10:39:53.435 | 20648a33637b4bf0b0bd0fab5bc28b92 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-24 10:40:10.506 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1063 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 10:40:10.507 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.service.push_report_service:push_report:43 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 10:40:10.507 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:80 - 生成HTML - report_data keys: ['requirementName', 'entityKeyword', 'specificRequirement', 'reportOssUrl', 'totalArticles', 'sentiment', 'dataSources', 'taskId', 'createTime', 'status']
2025-07-24 10:40:10.507 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:81 - 生成HTML - analysis_results keys: ['summary', 'reportUrl']
2025-07-24 10:40:10.508 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:82 - 生成HTML - selectedKeywords: []
2025-07-24 10:40:10.508 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:107 - 生成HTML - 提取到 0 篇文章
2025-07-24 10:40:10.508 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:111 - 生成HTML - 提取到 0 个关键词: []
2025-07-24 10:40:10.871 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | utils.oss_util:_get_client:47 - OSS客户端初始化成功
2025-07-24 10:40:11.046 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | utils.oss_util:_generate_signed_domain_url:204 - 生成可直接查看的OSS URL: opinion-reports/report_3e1c43d4af92_1753324810.html
2025-07-24 10:40:11.047 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | utils.oss_util:_upload_html_standard:110 - HTML文件上传OSS成功: opinion-reports/report_3e1c43d4af92_1753324810.html -> https://oss.jingangai.cn/opinion-reports/report_3e1c43d4af92_1753324810.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755916811&Signature=yRTr4ehbkeM10nNwFjkI9SjiaU4%3D
2025-07-24 10:40:11.047 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | utils.oss_util:_upload_html_standard:111 - OSS上传结果 - 状态码: 200, 请求ID: 68819D0AFDDA2E36386AF001
2025-07-24 10:40:11.047 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.service.report_html_generator_service:generate_report_html:45 - 报告HTML页面生成并上传OSS成功: report_3e1c43d4af92_1753324810 -> https://oss.jingangai.cn/opinion-reports/report_3e1c43d4af92_1753324810.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755916811&Signature=yRTr4ehbkeM10nNwFjkI9SjiaU4%3D
2025-07-24 10:40:11.047 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.service.push_report_service:push_report:69 - 报告页面已上传OSS: https://oss.jingangai.cn/opinion-reports/report_3e1c43d4af92_1753324810.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755916811&Signature=yRTr4ehbkeM10nNwFjkI9SjiaU4%3D
2025-07-24 10:40:11.048 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.service.push_report_service:push_report:85 - 报告页面生成成功: https://oss.jingangai.cn/opinion-reports/report_3e1c43d4af92_1753324810.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755916811&Signature=yRTr4ehbkeM10nNwFjkI9SjiaU4%3D
2025-07-24 10:40:11.048 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.service.push_report_service:_generate_mobile_friendly_url:611 - 生成移动端备用URL: http://localhost:9099/dev-api/public/report/view/report_3e1c43d4af92_1753324810
2025-07-24 10:40:11.304 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.service.push_report_service:_validate_report_url:579 - 报告URL验证成功: https://oss.jingangai.cn/opinion-reports/report_3e1c43d4af92_1753324810.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755916811&Signature=yRTr4ehbkeM10nNwFjkI9SjiaU4%3D
2025-07-24 10:40:11.339 | 5d7e46dff5d548f3a40f7b7e484bf909 | WARNING  | module_opinion.service.push_report_service:push_report:166 - 未找到requirement_id 66 对应的任务，跳过OSS URL和情感统计数据保存
2025-07-24 10:40:12.107 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.service.push_report_service:_log_push_result:552 - 推送成功: {"push_id": "push_1753324810507", "target_url": "https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63", "success": true, "response_status": 200, "response_data": {"errcode": 0, "errmsg": "ok"}, "error_message": null, "push_time": "2025-07-24T10:40:10.507208", "user_id": null}
2025-07-24 10:40:12.107 | 5d7e46dff5d548f3a40f7b7e484bf909 | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1073 - 报告推送成功，推送ID: push_1753324810507
2025-07-24 10:42:36.049 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-24 10:42:36.050 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-24 11:03:32.418 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-24 11:03:32.419 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-24 11:03:33.997 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-24 11:03:33.997 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-24 11:03:33.999 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-24 11:03:34.669 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-24 11:03:35.234 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-24 11:03:35.234 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-24 11:03:59.029 | 486359429a2e4a1695867e738f7f5297 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为ea65bc4c-2473-4b8f-84f5-bb783d58c492的会话获取图片验证码成功
2025-07-24 11:04:07.565 | c17e6aa484d14ec5ad41d3344f755ad2 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-24 11:04:07.812 | 3fcf324550fa44f68d42487bd5c578d7 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-24 11:04:08.313 | 3e705a200c4a4d44a5d6258f444587cf | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-24 11:04:08.810 | 875479a11d064bb68daa30c141713b74 | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-24 11:04:08.868 | 875479a11d064bb68daa30c141713b74 | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-24 11:04:08.869 | 875479a11d064bb68daa30c141713b74 | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-24 11:04:09.342 | 9b36da92f46d426380568fb281093190 | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-24 11:04:09.368 | 9b36da92f46d426380568fb281093190 | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 24
2025-07-24 11:04:09.369 | 9b36da92f46d426380568fb281093190 | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 24
2025-07-24 11:04:09.369 | 9b36da92f46d426380568fb281093190 | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-24 11:04:09.477 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-24 11:04:09.478 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-24 11:04:09.479 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-24 11:04:09.479 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-24 00:00:00 到 2025-07-24 23:59:59.999999 ===
2025-07-24 11:04:09.479 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-23 00:00:00 到 2025-07-23 23:59:59.999999 ===
2025-07-24 11:04:09.481 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-24 11:04:09.509 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-24 11:04:09.647 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-24 11:04:09.705 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-24 11:04:09.705 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 24, 11, 4, 9, 677394), 'package_name': '企业版', 'is_expired': True} ===
2025-07-24 11:04:09.735 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-24 11:04:09.735 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-24 11:04:09.762 | a011492927df4544a468e766c7d2350f | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-24 11:04:09.762 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-24 11:04:09.763 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 11:04:09.763 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 11:04:09.763 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-24 11:04:09.764 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 11:04:09.764 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 11:04:09.765 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-24 11:04:09.765 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 11:04:09.765 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-24 11:04:09.766 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 11:04:09.766 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-24 11:04:09.767 | a011492927df4544a468e766c7d2350f | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x0000019ABCA0BBD0> ===
2025-07-24 11:04:12.747 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 24, 11, 4, 7), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-24 11:04:12.747 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 11:04:12.748 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-24 11:04:12.748 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 11:04:12.748 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-24 11:04:12.749 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-24 11:04:12.749 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-24 11:04:12.750 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-24 11:04:12.750 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 11:04:12.751 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 11:04:12.751 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-24 11:04:12.752 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-24 11:04:12.752 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-24 11:04:12.755 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-24 11:04:12.756 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-24 11:04:12.813 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-24 11:04:12.814 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-24 11:04:12.814 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-24 11:04:12.814 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-24 11:04:12.815 | 4cdb087be11a4b9ca772991783898cd1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-24 11:04:31.622 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1063 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 11:04:31.622 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.service.push_report_service:push_report:43 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 11:04:31.687 | 6b492174210d4373bae403143bc642f0 | WARNING  | module_opinion.service.push_report_service:_get_complete_analysis_data:348 - 需求不存在: 60，使用原始数据
2025-07-24 11:04:31.688 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:80 - 生成HTML - report_data keys: ['requirementName', 'entityKeyword', 'specificRequirement', 'reportOssUrl', 'totalArticles', 'sentiment', 'dataSources', 'taskId', 'createTime', 'status']
2025-07-24 11:04:31.688 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:81 - 生成HTML - analysis_results keys: ['summary', 'reportUrl']
2025-07-24 11:04:31.688 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:82 - 生成HTML - selectedKeywords: []
2025-07-24 11:04:31.689 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:107 - 生成HTML - 提取到 0 篇文章
2025-07-24 11:04:31.689 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:111 - 生成HTML - 提取到 0 个关键词: []
2025-07-24 11:04:31.965 | 6b492174210d4373bae403143bc642f0 | INFO     | utils.oss_util:_get_client:47 - OSS客户端初始化成功
2025-07-24 11:04:32.144 | 6b492174210d4373bae403143bc642f0 | INFO     | utils.oss_util:_generate_signed_domain_url:204 - 生成可直接查看的OSS URL: opinion-reports/report_8562d508b447_1753326271.html
2025-07-24 11:04:32.145 | 6b492174210d4373bae403143bc642f0 | INFO     | utils.oss_util:_upload_html_standard:110 - HTML文件上传OSS成功: opinion-reports/report_8562d508b447_1753326271.html -> https://oss.jingangai.cn/opinion-reports/report_8562d508b447_1753326271.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755918272&Signature=cgtAnHe/1Oz67znNzuxfAa2luqY%3D
2025-07-24 11:04:32.145 | 6b492174210d4373bae403143bc642f0 | INFO     | utils.oss_util:_upload_html_standard:111 - OSS上传结果 - 状态码: 200, 请求ID: 6881A2BFC8A45833302E7E98
2025-07-24 11:04:32.145 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.service.report_html_generator_service:generate_report_html:45 - 报告HTML页面生成并上传OSS成功: report_8562d508b447_1753326271 -> https://oss.jingangai.cn/opinion-reports/report_8562d508b447_1753326271.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755918272&Signature=cgtAnHe/1Oz67znNzuxfAa2luqY%3D
2025-07-24 11:04:32.146 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.service.push_report_service:push_report:74 - 报告页面已上传OSS: https://oss.jingangai.cn/opinion-reports/report_8562d508b447_1753326271.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755918272&Signature=cgtAnHe/1Oz67znNzuxfAa2luqY%3D
2025-07-24 11:04:32.146 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.service.push_report_service:push_report:90 - 报告页面生成成功: https://oss.jingangai.cn/opinion-reports/report_8562d508b447_1753326271.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755918272&Signature=cgtAnHe/1Oz67znNzuxfAa2luqY%3D
2025-07-24 11:04:32.146 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.service.push_report_service:_generate_mobile_friendly_url:754 - 生成移动端备用URL: http://localhost:9099/dev-api/public/report/view/report_8562d508b447_1753326271
2025-07-24 11:04:32.350 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.service.push_report_service:_validate_report_url:722 - 报告URL验证成功: https://oss.jingangai.cn/opinion-reports/report_8562d508b447_1753326271.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755918272&Signature=cgtAnHe/1Oz67znNzuxfAa2luqY%3D
2025-07-24 11:04:32.379 | 6b492174210d4373bae403143bc642f0 | WARNING  | module_opinion.service.push_report_service:push_report:171 - 未找到requirement_id 60 对应的任务，跳过OSS URL和情感统计数据保存
2025-07-24 11:04:33.092 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.service.push_report_service:_log_push_result:695 - 推送成功: {"push_id": "push_1753326271622", "target_url": "https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63", "success": true, "response_status": 200, "response_data": {"errcode": 0, "errmsg": "ok"}, "error_message": null, "push_time": "2025-07-24T11:04:31.622963", "user_id": null}
2025-07-24 11:04:33.093 | 6b492174210d4373bae403143bc642f0 | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1073 - 报告推送成功，推送ID: push_1753326271622
2025-07-24 11:12:02.396 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-24 11:12:02.397 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-24 11:39:48.074 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-24 11:39:48.075 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-24 11:39:49.519 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-24 11:39:49.519 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-24 11:39:49.521 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-24 11:39:50.137 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-24 11:39:50.732 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-24 11:39:50.732 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-24 11:40:14.862 | 59b1c0df24b64bceb5cbba3f194c01e9 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为4dcf2b15-b7c6-468d-8986-30aebebbe73e的会话获取图片验证码成功
2025-07-24 11:40:28.750 | 5eac1f8effad4a308f79ff22f2859e40 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-24 11:40:28.963 | 35d9b92cc2df4b1189c3fa8a95459596 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-24 11:40:29.457 | bb2b45d3784f4a6d9bcf592aec989f7c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-24 11:40:29.928 | 24f9cd6795744b7281e98cce59705cfb | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-24 11:40:29.953 | 24f9cd6795744b7281e98cce59705cfb | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 24
2025-07-24 11:40:29.954 | 24f9cd6795744b7281e98cce59705cfb | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 24
2025-07-24 11:40:29.954 | 24f9cd6795744b7281e98cce59705cfb | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-24 11:40:30.204 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-24 11:40:30.204 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-24 11:40:30.205 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-24 11:40:30.205 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-24 00:00:00 到 2025-07-24 23:59:59.999999 ===
2025-07-24 11:40:30.205 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-23 00:00:00 到 2025-07-23 23:59:59.999999 ===
2025-07-24 11:40:30.207 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-24 11:40:30.231 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-24 11:40:30.352 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-24 11:40:30.613 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-24 11:40:30.613 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 24, 11, 40, 30, 588752), 'package_name': '企业版', 'is_expired': True} ===
2025-07-24 11:40:30.627 | a5a439a29df74ca3945f72713803c598 | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-24 11:40:30.641 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-24 11:40:30.642 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-24 11:40:30.667 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-24 11:40:30.668 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-24 11:40:30.668 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 11:40:30.668 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 11:40:30.669 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-24 11:40:30.669 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 11:40:30.670 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 11:40:30.670 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-24 11:40:30.671 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 11:40:30.671 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-24 11:40:30.672 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 11:40:30.672 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-24 11:40:30.673 | 37bb3b0a90034f8cb0d7c43326a72e29 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x0000021A072070B0> ===
2025-07-24 11:40:30.682 | a5a439a29df74ca3945f72713803c598 | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-24 11:40:30.683 | a5a439a29df74ca3945f72713803c598 | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-24 11:40:35.249 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 24, 11, 40, 29), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-24 11:40:35.250 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 11:40:35.250 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-24 11:40:35.250 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 11:40:35.251 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-24 11:40:35.251 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-24 11:40:35.252 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-24 11:40:35.252 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-24 11:40:35.253 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 11:40:35.253 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 11:40:35.253 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-24 11:40:35.254 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-24 11:40:35.254 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-24 11:40:35.257 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-24 11:40:35.258 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-24 11:40:35.313 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-24 11:40:35.314 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-24 11:40:35.314 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-24 11:40:35.314 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-24 11:40:35.315 | 659d0e50008345a786e29adb4ed9ce94 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-24 11:40:45.293 | 3f2b42d415154add8d0e5c367e878b97 | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1063 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 11:40:45.293 | 3f2b42d415154add8d0e5c367e878b97 | INFO     | module_opinion.service.push_report_service:push_report:44 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 11:40:45.293 | 3f2b42d415154add8d0e5c367e878b97 | INFO     | module_opinion.service.push_report_service:push_report:57 - 使用现有报告URL: https://oss.jingangai.cn/opinion-reports/report_700d642f5674_1753152663.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755744663&Signature=P2ovdJx/AyZqQqA5SRhL9e%2BrZtU%3D
2025-07-24 11:40:45.294 | 3f2b42d415154add8d0e5c367e878b97 | INFO     | module_opinion.service.push_report_service:push_report:75 - 报告页面已上传OSS: https://oss.jingangai.cn/opinion-reports/report_700d642f5674_1753152663.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755744663&Signature=P2ovdJx/AyZqQqA5SRhL9e%2BrZtU%3D
2025-07-24 11:40:45.294 | 3f2b42d415154add8d0e5c367e878b97 | INFO     | module_opinion.service.push_report_service:push_report:91 - 报告页面生成成功: https://oss.jingangai.cn/opinion-reports/report_700d642f5674_1753152663.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755744663&Signature=P2ovdJx/AyZqQqA5SRhL9e%2BrZtU%3D
2025-07-24 11:40:45.295 | 3f2b42d415154add8d0e5c367e878b97 | INFO     | module_opinion.service.push_report_service:_generate_mobile_friendly_url:727 - 生成移动端备用URL: http://localhost:9099/dev-api/public/report/view/existing_1753328445293
2025-07-24 11:40:45.571 | 3f2b42d415154add8d0e5c367e878b97 | INFO     | module_opinion.service.push_report_service:_validate_report_url:585 - 报告URL验证成功: https://oss.jingangai.cn/opinion-reports/report_700d642f5674_1753152663.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755744663&Signature=P2ovdJx/AyZqQqA5SRhL9e%2BrZtU%3D
2025-07-24 11:40:45.600 | 3f2b42d415154add8d0e5c367e878b97 | WARNING  | module_opinion.service.push_report_service:push_report:172 - 未找到requirement_id 63 对应的任务，跳过OSS URL和情感统计数据保存
2025-07-24 11:40:46.341 | 3f2b42d415154add8d0e5c367e878b97 | INFO     | module_opinion.service.push_report_service:_log_push_result:558 - 推送成功: {"push_id": "push_1753328445293", "target_url": "https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63", "success": true, "response_status": 200, "response_data": {"errcode": 0, "errmsg": "ok"}, "error_message": null, "push_time": "2025-07-24T11:40:45.293470", "user_id": null}
2025-07-24 11:40:46.341 | 3f2b42d415154add8d0e5c367e878b97 | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1073 - 报告推送成功，推送ID: push_1753328445293
2025-07-24 11:41:17.157 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1063 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 11:41:17.157 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.service.push_report_service:push_report:44 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 11:41:17.158 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.service.push_report_service:_get_analysis_data_from_db:619 - 开始从数据库获取需求ID 59 的分析数据
2025-07-24 11:41:17.182 | 940c2474abfb47a3aa48f9c296bf4a8c | WARNING  | module_opinion.service.push_report_service:_get_analysis_data_from_db:625 - 未找到需求ID 59 相关的任务
2025-07-24 11:41:17.183 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:80 - 生成HTML - report_data keys: ['requirementName', 'entityKeyword', 'specificRequirement', 'reportOssUrl', 'totalArticles', 'sentiment', 'dataSources', 'taskId', 'createTime', 'status']
2025-07-24 11:41:17.183 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:81 - 生成HTML - analysis_results keys: ['summary', 'reportUrl']
2025-07-24 11:41:17.184 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:82 - 生成HTML - selectedKeywords: []
2025-07-24 11:41:17.184 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:107 - 生成HTML - 提取到 0 篇文章
2025-07-24 11:41:17.184 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:111 - 生成HTML - 提取到 0 个关键词: []
2025-07-24 11:41:17.424 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | utils.oss_util:_get_client:47 - OSS客户端初始化成功
2025-07-24 11:41:17.605 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | utils.oss_util:_generate_signed_domain_url:204 - 生成可直接查看的OSS URL: opinion-reports/report_f3b5f44657b1_1753328477.html
2025-07-24 11:41:17.605 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | utils.oss_util:_upload_html_standard:110 - HTML文件上传OSS成功: opinion-reports/report_f3b5f44657b1_1753328477.html -> https://oss.jingangai.cn/opinion-reports/report_f3b5f44657b1_1753328477.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755920477&Signature=xLdk6th4epZJTbhdiOGI2ZqMpMI%3D
2025-07-24 11:41:17.605 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | utils.oss_util:_upload_html_standard:111 - OSS上传结果 - 状态码: 200, 请求ID: 6881AB5CB2C82636374EB52F
2025-07-24 11:41:17.605 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.service.report_html_generator_service:generate_report_html:45 - 报告HTML页面生成并上传OSS成功: report_f3b5f44657b1_1753328477 -> https://oss.jingangai.cn/opinion-reports/report_f3b5f44657b1_1753328477.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755920477&Signature=xLdk6th4epZJTbhdiOGI2ZqMpMI%3D
2025-07-24 11:41:17.606 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.service.push_report_service:push_report:75 - 报告页面已上传OSS: https://oss.jingangai.cn/opinion-reports/report_f3b5f44657b1_1753328477.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755920477&Signature=xLdk6th4epZJTbhdiOGI2ZqMpMI%3D
2025-07-24 11:41:17.606 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.service.push_report_service:push_report:91 - 报告页面生成成功: https://oss.jingangai.cn/opinion-reports/report_f3b5f44657b1_1753328477.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755920477&Signature=xLdk6th4epZJTbhdiOGI2ZqMpMI%3D
2025-07-24 11:41:17.606 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.service.push_report_service:_generate_mobile_friendly_url:727 - 生成移动端备用URL: http://localhost:9099/dev-api/public/report/view/report_f3b5f44657b1_1753328477
2025-07-24 11:41:17.802 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.service.push_report_service:_validate_report_url:585 - 报告URL验证成功: https://oss.jingangai.cn/opinion-reports/report_f3b5f44657b1_1753328477.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755920477&Signature=xLdk6th4epZJTbhdiOGI2ZqMpMI%3D
2025-07-24 11:41:17.828 | 940c2474abfb47a3aa48f9c296bf4a8c | WARNING  | module_opinion.service.push_report_service:push_report:172 - 未找到requirement_id 59 对应的任务，跳过OSS URL和情感统计数据保存
2025-07-24 11:41:18.340 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.service.push_report_service:_log_push_result:558 - 推送成功: {"push_id": "push_1753328477157", "target_url": "https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63", "success": true, "response_status": 200, "response_data": {"errcode": 0, "errmsg": "ok"}, "error_message": null, "push_time": "2025-07-24T11:41:17.157795", "user_id": null}
2025-07-24 11:41:18.340 | 940c2474abfb47a3aa48f9c296bf4a8c | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1073 - 报告推送成功，推送ID: push_1753328477157
2025-07-24 12:09:13.423 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-24 12:09:13.424 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-24 12:09:15.674 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-24 12:09:15.674 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-24 12:09:15.677 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-24 12:09:16.377 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-24 12:09:17.049 | 97f170a688684f458649abb5278ee1f1 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为113ae28c-8463-4707-86a0-0baa2fe4161f的会话获取图片验证码成功
2025-07-24 12:09:17.119 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-24 12:09:17.119 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-24 12:09:40.911 | b6cf9c91a11f48cb859992c41264c079 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-24 12:09:41.116 | 0dba3b8907c9495d873a08ac5fc6ec1a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-24 12:09:41.612 | 1ea4ca9d0de64b649dca830942b79be2 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-24 12:09:42.084 | 59b19d9d64dc47e8a89ffff2fe0ff5d2 | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-24 12:09:42.134 | 59b19d9d64dc47e8a89ffff2fe0ff5d2 | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-24 12:09:42.134 | 59b19d9d64dc47e8a89ffff2fe0ff5d2 | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-24 12:09:42.345 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-24 12:09:42.345 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-24 12:09:42.345 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-24 12:09:42.345 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-24 00:00:00 到 2025-07-24 23:59:59.999999 ===
2025-07-24 12:09:42.345 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-23 00:00:00 到 2025-07-23 23:59:59.999999 ===
2025-07-24 12:09:42.346 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-24 12:09:42.366 | a5dd1b9a934b4f2eb37142b617467b2c | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-24 12:09:42.370 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-24 12:09:42.389 | a5dd1b9a934b4f2eb37142b617467b2c | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 24
2025-07-24 12:09:42.390 | a5dd1b9a934b4f2eb37142b617467b2c | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 24
2025-07-24 12:09:42.390 | a5dd1b9a934b4f2eb37142b617467b2c | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-24 12:09:42.490 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-24 12:09:42.541 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-24 12:09:42.541 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 24, 12, 9, 42, 516934), 'package_name': '企业版', 'is_expired': True} ===
2025-07-24 12:09:42.566 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-24 12:09:42.566 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-24 12:09:42.590 | 45253af25a3a41359febe6e65d6101fe | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-24 12:09:42.591 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-24 12:09:42.591 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 12:09:42.591 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 12:09:42.591 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-24 12:09:42.591 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 12:09:42.592 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 12:09:42.592 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-24 12:09:42.592 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 12:09:42.592 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-24 12:09:42.593 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 12:09:42.593 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-24 12:09:42.593 | 45253af25a3a41359febe6e65d6101fe | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x0000021A07273DD0> ===
2025-07-24 12:09:58.214 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 24, 12, 9, 41), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-24 12:09:58.214 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 12:09:58.215 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-24 12:09:58.215 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 12:09:58.215 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-24 12:09:58.215 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-24 12:09:58.215 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-24 12:09:58.215 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-24 12:09:58.215 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 12:09:58.216 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 12:09:58.216 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-24 12:09:58.216 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-24 12:09:58.216 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-24 12:09:58.217 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-24 12:09:58.218 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-24 12:09:58.270 | 4903b7828be24ac89d3251bb839b12aa | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-24 12:09:58.270 | 4903b7828be24ac89d3251bb839b12aa | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-24 12:09:58.270 | 4903b7828be24ac89d3251bb839b12aa | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-24 12:09:58.270 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-24 12:09:58.270 | 4903b7828be24ac89d3251bb839b12aa | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-24 12:10:24.908 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1063 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 12:10:24.908 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.service.push_report_service:push_report:44 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 12:10:24.908 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.service.push_report_service:_get_analysis_data_from_db:619 - 开始从数据库获取需求ID 66 的分析数据
2025-07-24 12:10:24.932 | 5a7c9f81baf84dce983da6165925fbbb | WARNING  | module_opinion.service.push_report_service:_get_analysis_data_from_db:625 - 未找到需求ID 66 相关的任务
2025-07-24 12:10:24.933 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:80 - 生成HTML - report_data keys: ['totalArticles', 'totalKeywords', 'dataSources', 'sentiment', 'onlineSearchCount', 'customSourceCounts', 'requirementName', 'entityKeyword', 'specificRequirement', 'selectedKeywords', 'reportPageUrl', 'pushTime', 'analysisResults', 'reportOssUrl', 'taskId', 'createTime', 'status']
2025-07-24 12:10:24.933 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:81 - 生成HTML - analysis_results keys: ['requirement_id', 'analysis_results', 'status', 'message']
2025-07-24 12:10:24.933 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:82 - 生成HTML - selectedKeywords: ['舆情分析报告查看任务']
2025-07-24 12:10:24.934 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:107 - 生成HTML - 提取到 0 篇文章
2025-07-24 12:10:24.934 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:111 - 生成HTML - 提取到 1 个关键词: ['舆情分析报告查看任务']
2025-07-24 12:10:25.084 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | utils.oss_util:_generate_signed_domain_url:204 - 生成可直接查看的OSS URL: opinion-reports/report_5e036e321d18_1753330224.html
2025-07-24 12:10:25.085 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | utils.oss_util:_upload_html_standard:110 - HTML文件上传OSS成功: opinion-reports/report_5e036e321d18_1753330224.html -> https://oss.jingangai.cn/opinion-reports/report_5e036e321d18_1753330224.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755922225&Signature=CIsDgcotPDzXsitAV/ITIEismvA%3D
2025-07-24 12:10:25.085 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | utils.oss_util:_upload_html_standard:111 - OSS上传结果 - 状态码: 200, 请求ID: 6881B230AB8D903936DC9CBA
2025-07-24 12:10:25.085 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.service.report_html_generator_service:generate_report_html:45 - 报告HTML页面生成并上传OSS成功: report_5e036e321d18_1753330224 -> https://oss.jingangai.cn/opinion-reports/report_5e036e321d18_1753330224.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755922225&Signature=CIsDgcotPDzXsitAV/ITIEismvA%3D
2025-07-24 12:10:25.085 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.service.push_report_service:push_report:75 - 报告页面已上传OSS: https://oss.jingangai.cn/opinion-reports/report_5e036e321d18_1753330224.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755922225&Signature=CIsDgcotPDzXsitAV/ITIEismvA%3D
2025-07-24 12:10:25.085 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.service.push_report_service:push_report:91 - 报告页面生成成功: https://oss.jingangai.cn/opinion-reports/report_5e036e321d18_1753330224.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755922225&Signature=CIsDgcotPDzXsitAV/ITIEismvA%3D
2025-07-24 12:10:25.085 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.service.push_report_service:_generate_mobile_friendly_url:727 - 生成移动端备用URL: http://localhost:9099/dev-api/public/report/view/report_5e036e321d18_1753330224
2025-07-24 12:10:25.305 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.service.push_report_service:_validate_report_url:585 - 报告URL验证成功: https://oss.jingangai.cn/opinion-reports/report_5e036e321d18_1753330224.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755922225&Signature=CIsDgcotPDzXsitAV/ITIEismvA%3D
2025-07-24 12:10:25.331 | 5a7c9f81baf84dce983da6165925fbbb | WARNING  | module_opinion.service.push_report_service:push_report:172 - 未找到requirement_id 66 对应的任务，跳过OSS URL和情感统计数据保存
2025-07-24 12:10:26.310 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.service.push_report_service:_log_push_result:558 - 推送成功: {"push_id": "push_1753330224908", "target_url": "https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63", "success": true, "response_status": 200, "response_data": {"errcode": 0, "errmsg": "ok"}, "error_message": null, "push_time": "2025-07-24T12:10:24.908550", "user_id": null}
2025-07-24 12:10:26.311 | 5a7c9f81baf84dce983da6165925fbbb | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1073 - 报告推送成功，推送ID: push_1753330224908
2025-07-24 12:11:52.690 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-24 12:11:52.691 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-24 14:05:56.491 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-24 14:05:56.492 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-24 14:05:58.329 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-24 14:05:58.329 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-24 14:05:58.336 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-24 14:05:59.020 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-24 14:05:59.719 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-24 14:05:59.720 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-24 14:06:25.375 | 78787f600eb84aef96e965ad6a04b161 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为0ccabd44-12bb-49cb-8677-cbb5046d6c11的会话获取图片验证码成功
2025-07-24 14:06:35.106 | 089e4cbabc0a41668fb5f3291e482096 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-24 14:06:35.369 | 56d07d43287446c1b10dd9131bc22c60 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-24 14:06:35.906 | 6f73982d1de542de8fa31556c793c6e4 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-24 14:06:36.633 | 609c1ceaddfe489da2e73a9202d6b703 | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-24 14:06:36.666 | 609c1ceaddfe489da2e73a9202d6b703 | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 24
2025-07-24 14:06:36.667 | 609c1ceaddfe489da2e73a9202d6b703 | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 24
2025-07-24 14:06:36.667 | 609c1ceaddfe489da2e73a9202d6b703 | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-24 14:06:36.924 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-24 14:06:36.925 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-24 14:06:36.925 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-24 14:06:36.925 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-24 00:00:00 到 2025-07-24 23:59:59.999999 ===
2025-07-24 14:06:36.926 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-23 00:00:00 到 2025-07-23 23:59:59.999999 ===
2025-07-24 14:06:36.928 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-24 14:06:36.957 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-24 14:06:37.107 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-24 14:06:37.281 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-24 14:06:37.281 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 24, 14, 6, 37, 249424), 'package_name': '企业版', 'is_expired': True} ===
2025-07-24 14:06:37.311 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-24 14:06:37.313 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-24 14:06:37.342 | 92dfae01321746fcbb72985ac11a7015 | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-24 14:06:37.343 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-24 14:06:37.344 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 14:06:37.344 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 14:06:37.344 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-24 14:06:37.345 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 14:06:37.345 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 14:06:37.346 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-24 14:06:37.346 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 14:06:37.347 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-24 14:06:37.347 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 14:06:37.347 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-24 14:06:37.348 | 92dfae01321746fcbb72985ac11a7015 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x0000018A3C945FD0> ===
2025-07-24 14:06:37.350 | 782e00b4042d447caacfb7ba1d30380c | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-24 14:06:37.432 | 782e00b4042d447caacfb7ba1d30380c | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-24 14:06:37.433 | 782e00b4042d447caacfb7ba1d30380c | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-24 14:07:16.169 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 24, 14, 6, 35), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-24 14:07:16.169 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 14:07:16.169 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-24 14:07:16.170 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 14:07:16.170 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-24 14:07:16.170 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-24 14:07:16.170 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-24 14:07:16.170 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-24 14:07:16.171 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 14:07:16.171 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 14:07:16.171 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-24 14:07:16.171 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-24 14:07:16.171 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-24 14:07:16.173 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-24 14:07:16.174 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-24 14:07:16.234 | e5af8ed80962474ba313fbeb986261f7 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-24 14:07:16.234 | e5af8ed80962474ba313fbeb986261f7 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-24 14:07:16.234 | e5af8ed80962474ba313fbeb986261f7 | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-24 14:07:16.235 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-24 14:07:16.235 | e5af8ed80962474ba313fbeb986261f7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-24 14:09:26.644 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 24, 14, 6, 35), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-24 14:09:26.644 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 14:09:26.645 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-24 14:09:26.645 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 14:09:26.645 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-24 14:09:26.645 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-24 14:09:26.645 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-24 14:09:26.645 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-24 14:09:26.646 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 14:09:26.646 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 14:09:26.646 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-24 14:09:26.646 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-24 14:09:26.647 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-24 14:09:26.648 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-24 14:09:26.649 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-24 14:09:26.730 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-24 14:09:26.730 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-24 14:09:26.730 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-24 14:09:26.730 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-24 14:09:26.731 | 0b4cc7d48ddd46dda730082f6d3524a7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-24 14:09:48.354 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1063 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 14:09:48.354 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.service.push_report_service:push_report:43 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 14:09:48.354 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:80 - 生成HTML - report_data keys: ['requirementName', 'entityKeyword', 'specificRequirement', 'reportOssUrl', 'reportPageUrl', 'totalArticles', 'sentiment', 'dataSources', 'taskId', 'createTime', 'status', 'pushTime']
2025-07-24 14:09:48.354 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:81 - 生成HTML - analysis_results keys: ['summary', 'reportUrl', 'requirement_id', 'requirement_name']
2025-07-24 14:09:48.354 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:82 - 生成HTML - selectedKeywords: []
2025-07-24 14:09:48.355 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:107 - 生成HTML - 提取到 0 篇文章
2025-07-24 14:09:48.355 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:111 - 生成HTML - 提取到 0 个关键词: []
2025-07-24 14:09:48.592 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | utils.oss_util:_get_client:47 - OSS客户端初始化成功
2025-07-24 14:09:49.218 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | utils.oss_util:_generate_signed_domain_url:204 - 生成可直接查看的OSS URL: opinion-reports/report_fd4e0395ca74_1753337388.html
2025-07-24 14:09:49.218 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | utils.oss_util:_upload_html_standard:110 - HTML文件上传OSS成功: opinion-reports/report_fd4e0395ca74_1753337388.html -> https://oss.jingangai.cn/opinion-reports/report_fd4e0395ca74_1753337388.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755929389&Signature=duL9QhvK8x2yX9ZYZZOBdKUdn6k%3D
2025-07-24 14:09:49.219 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | utils.oss_util:_upload_html_standard:111 - OSS上传结果 - 状态码: 200, 请求ID: 6881CE2C2AA34434350891C1
2025-07-24 14:09:49.219 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.service.report_html_generator_service:generate_report_html:45 - 报告HTML页面生成并上传OSS成功: report_fd4e0395ca74_1753337388 -> https://oss.jingangai.cn/opinion-reports/report_fd4e0395ca74_1753337388.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755929389&Signature=duL9QhvK8x2yX9ZYZZOBdKUdn6k%3D
2025-07-24 14:09:49.219 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.service.push_report_service:push_report:69 - 报告页面已上传OSS: https://oss.jingangai.cn/opinion-reports/report_fd4e0395ca74_1753337388.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755929389&Signature=duL9QhvK8x2yX9ZYZZOBdKUdn6k%3D
2025-07-24 14:09:49.219 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.service.push_report_service:push_report:85 - 报告页面生成成功: https://oss.jingangai.cn/opinion-reports/report_fd4e0395ca74_1753337388.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755929389&Signature=duL9QhvK8x2yX9ZYZZOBdKUdn6k%3D
2025-07-24 14:09:49.220 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.service.push_report_service:_generate_mobile_friendly_url:611 - 生成移动端备用URL: http://localhost:9099/dev-api/public/report/view/report_fd4e0395ca74_1753337388
2025-07-24 14:09:49.488 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.service.push_report_service:_validate_report_url:579 - 报告URL验证成功: https://oss.jingangai.cn/opinion-reports/report_fd4e0395ca74_1753337388.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755929389&Signature=duL9QhvK8x2yX9ZYZZOBdKUdn6k%3D
2025-07-24 14:09:49.529 | 471e081a826f46bf8e0c4d18c65807b9 | WARNING  | module_opinion.service.push_report_service:push_report:166 - 未找到requirement_id 66 对应的任务，跳过OSS URL和情感统计数据保存
2025-07-24 14:09:51.052 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.service.push_report_service:_log_push_result:552 - 推送成功: {"push_id": "push_1753337388354", "target_url": "https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63", "success": true, "response_status": 200, "response_data": {"errcode": 0, "errmsg": "ok"}, "error_message": null, "push_time": "2025-07-24T14:09:48.354381", "user_id": null}
2025-07-24 14:09:51.053 | 471e081a826f46bf8e0c4d18c65807b9 | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1073 - 报告推送成功，推送ID: push_1753337388354
2025-07-24 14:11:06.592 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-24 14:11:06.592 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-24 14:29:33.681 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-24 14:29:33.681 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-24 14:29:36.034 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-24 14:29:36.035 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-24 14:29:36.039 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-24 14:29:36.853 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-24 14:29:37.448 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-24 14:29:37.449 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-24 14:37:03.514 | c3c12b14bd8748e3b51c5876cc04dcf7 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为d8527205-ca3c-48c6-ac00-ada8a890502f的会话获取图片验证码成功
2025-07-24 14:37:07.613 | 80441bd3ec7242e796a57e0e33c31d7a | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-24 14:37:07.970 | 994911299faa433d824a165dd1a56390 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-24 14:37:08.574 | 58c8896dd09f4eb39f3355566ff9506b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-24 14:37:09.233 | 3bbf68ff58f1409187916cff9945d3a3 | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-24 14:37:09.317 | 3bbf68ff58f1409187916cff9945d3a3 | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-24 14:37:09.318 | 3bbf68ff58f1409187916cff9945d3a3 | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-24 14:37:09.584 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-24 14:37:09.584 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-24 14:37:09.585 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-24 14:37:09.585 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-24 00:00:00 到 2025-07-24 23:59:59.999999 ===
2025-07-24 14:37:09.586 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-23 00:00:00 到 2025-07-23 23:59:59.999999 ===
2025-07-24 14:37:09.589 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-24 14:37:09.614 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-24 14:37:09.729 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-24 14:37:09.990 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-24 14:37:09.990 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 24, 14, 37, 9, 755192), 'package_name': '企业版', 'is_expired': True} ===
2025-07-24 14:37:10.014 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-24 14:37:10.014 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-24 14:37:10.017 | 5f8812d176b1497abb61bc90fa79e16f | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-24 14:37:10.037 | c0721f0ea6114c50a176addaa9a80395 | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-24 14:37:10.037 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-24 14:37:10.038 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 14:37:10.038 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 14:37:10.039 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-24 14:37:10.039 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 14:37:10.040 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 14:37:10.040 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-24 14:37:10.040 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 14:37:10.041 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-24 14:37:10.042 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 14:37:10.042 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-24 14:37:10.043 | c0721f0ea6114c50a176addaa9a80395 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x000002AB0654B410> ===
2025-07-24 14:37:10.058 | 5f8812d176b1497abb61bc90fa79e16f | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 24
2025-07-24 14:37:10.058 | 5f8812d176b1497abb61bc90fa79e16f | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 24
2025-07-24 14:37:10.059 | 5f8812d176b1497abb61bc90fa79e16f | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-24 14:37:13.359 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 24, 14, 37, 7), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-24 14:37:13.359 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 14:37:13.359 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-24 14:37:13.360 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 14:37:13.361 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-24 14:37:13.361 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-24 14:37:13.361 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-24 14:37:13.362 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-24 14:37:13.363 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 14:37:13.363 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 14:37:13.364 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-24 14:37:13.364 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-24 14:37:13.364 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-24 14:37:13.367 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-24 14:37:13.368 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-24 14:37:13.472 | 7602857c2542444f83cd3544d216d75e | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-24 14:37:13.472 | 7602857c2542444f83cd3544d216d75e | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-24 14:37:13.473 | 7602857c2542444f83cd3544d216d75e | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-24 14:37:13.473 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-24 14:37:13.473 | 7602857c2542444f83cd3544d216d75e | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-24 14:37:29.739 | 9da4edbac5934b00a32cf1a5e0288021 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-24 14:37:29.999 | 1f64d5a7786842c0b8bc48eddb2674ad | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-24 14:37:31.433 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 24, 14, 37, 7), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-24 14:37:31.433 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 14:37:31.433 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-24 14:37:31.433 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 14:37:31.433 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-24 14:37:31.433 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-24 14:37:31.433 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-24 14:37:31.434 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-24 14:37:31.434 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 14:37:31.434 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 14:37:31.434 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-24 14:37:31.434 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-24 14:37:31.435 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-24 14:37:31.435 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-24 14:37:31.436 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-24 14:37:31.511 | d786e49bd74646aa856f1e47a5468a86 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-24 14:37:31.512 | d786e49bd74646aa856f1e47a5468a86 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-24 14:37:31.512 | d786e49bd74646aa856f1e47a5468a86 | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-24 14:37:31.512 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-24 14:37:31.512 | d786e49bd74646aa856f1e47a5468a86 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-24 15:03:48.337 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-24 15:03:48.338 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-24 15:03:49.877 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-24 15:03:49.878 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-24 15:03:49.882 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-24 15:03:50.465 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-24 15:03:51.071 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-24 15:03:51.071 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-24 15:03:51.598 | cba8e6aa9eaa4908b533f9ec62414c0e | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为96ef9493-7ce5-4a74-8d18-1eae51de0b79的会话获取图片验证码成功
2025-07-24 15:05:36.465 | 2ec60cadc60249b49f4c6fbcae8d1df8 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-24 15:05:36.729 | 66f461fdc5e34ffc86559713f3dc32b3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-24 15:05:36.947 | bc71ae07f60f49c689bd83c9ee92f718 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-24 15:05:37.859 | 7691e6a3cec84dd39277883cb4841d1a | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-24 15:05:37.914 | 7691e6a3cec84dd39277883cb4841d1a | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-24 15:05:37.915 | 7691e6a3cec84dd39277883cb4841d1a | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-24 15:05:38.087 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-24 15:05:38.087 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-24 15:05:38.087 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-24 15:05:38.087 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-24 00:00:00 到 2025-07-24 23:59:59.999999 ===
2025-07-24 15:05:38.087 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-23 00:00:00 到 2025-07-23 23:59:59.999999 ===
2025-07-24 15:05:38.089 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-24 15:05:38.123 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-24 15:05:38.129 | e96550f4dddc4044923dabdb3e4ef3b3 | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-24 15:05:38.166 | e96550f4dddc4044923dabdb3e4ef3b3 | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 24
2025-07-24 15:05:38.167 | e96550f4dddc4044923dabdb3e4ef3b3 | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 24
2025-07-24 15:05:38.167 | e96550f4dddc4044923dabdb3e4ef3b3 | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-24 15:05:38.295 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-24 15:05:38.364 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-24 15:05:38.364 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 24, 15, 5, 38, 330564), 'package_name': '企业版', 'is_expired': True} ===
2025-07-24 15:05:38.404 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-24 15:05:38.405 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-24 15:05:38.439 | c926d4a87c114433903559eeeb727b1b | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-24 15:05:38.440 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-24 15:05:38.440 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 15:05:38.441 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 15:05:38.441 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-24 15:05:38.441 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 15:05:38.442 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 15:05:38.442 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-24 15:05:38.442 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-24 15:05:38.443 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-24 15:05:38.443 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-24 15:05:38.443 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-24 15:05:38.444 | c926d4a87c114433903559eeeb727b1b | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x00000168015D3CE0> ===
2025-07-24 15:05:40.926 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 24, 15, 5, 36), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-24 15:05:40.926 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 15:05:40.927 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-24 15:05:40.927 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 15:05:40.927 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-24 15:05:40.928 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-24 15:05:40.928 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-24 15:05:40.928 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-24 15:05:40.928 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-24 15:05:40.929 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-24 15:05:40.929 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-24 15:05:40.930 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-24 15:05:40.930 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-24 15:05:40.933 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-24 15:05:40.934 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-24 15:05:40.987 | 2a2ed7105d56450ab6917d7a047420da | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-24 15:05:40.988 | 2a2ed7105d56450ab6917d7a047420da | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-24 15:05:40.988 | 2a2ed7105d56450ab6917d7a047420da | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-24 15:05:40.988 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-24 15:05:40.988 | 2a2ed7105d56450ab6917d7a047420da | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-24 15:05:47.568 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1063 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 15:05:47.568 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.service.push_report_service:push_report:43 - 开始推送报告到: https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63
2025-07-24 15:05:47.605 | 5a179f48635444ae9b98883599b8ef2d | WARNING  | module_opinion.service.push_report_service:_get_actual_analysis_data:483 - 未找到需求ID 66 对应的任务
2025-07-24 15:05:47.605 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:80 - 生成HTML - report_data keys: ['requirementName', 'entityKeyword', 'specificRequirement', 'reportOssUrl', 'totalArticles', 'sentiment', 'dataSources', 'taskId', 'createTime', 'status', 'total_articles', 'data_sources']
2025-07-24 15:05:47.605 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:81 - 生成HTML - analysis_results keys: ['summary', 'reportUrl']
2025-07-24 15:05:47.605 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:82 - 生成HTML - selectedKeywords: []
2025-07-24 15:05:47.606 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:107 - 生成HTML - 提取到 0 篇文章
2025-07-24 15:05:47.606 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:111 - 生成HTML - 提取到 0 个关键词: []
2025-07-24 15:05:47.889 | 5a179f48635444ae9b98883599b8ef2d | INFO     | utils.oss_util:_get_client:47 - OSS客户端初始化成功
2025-07-24 15:05:48.059 | 5a179f48635444ae9b98883599b8ef2d | INFO     | utils.oss_util:_generate_signed_domain_url:204 - 生成可直接查看的OSS URL: opinion-reports/report_00036537afcf_1753340747.html
2025-07-24 15:05:48.060 | 5a179f48635444ae9b98883599b8ef2d | INFO     | utils.oss_util:_upload_html_standard:110 - HTML文件上传OSS成功: opinion-reports/report_00036537afcf_1753340747.html -> https://oss.jingangai.cn/opinion-reports/report_00036537afcf_1753340747.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755932748&Signature=IPb6nl7VPrhVg6qiY1rXz94TBC8%3D
2025-07-24 15:05:48.060 | 5a179f48635444ae9b98883599b8ef2d | INFO     | utils.oss_util:_upload_html_standard:111 - OSS上传结果 - 状态码: 200, 请求ID: 6881DB4B44C7E937341D5407
2025-07-24 15:05:48.060 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.service.report_html_generator_service:generate_report_html:45 - 报告HTML页面生成并上传OSS成功: report_00036537afcf_1753340747 -> https://oss.jingangai.cn/opinion-reports/report_00036537afcf_1753340747.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755932748&Signature=IPb6nl7VPrhVg6qiY1rXz94TBC8%3D
2025-07-24 15:05:48.060 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.service.push_report_service:push_report:76 - 报告页面已上传OSS: https://oss.jingangai.cn/opinion-reports/report_00036537afcf_1753340747.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755932748&Signature=IPb6nl7VPrhVg6qiY1rXz94TBC8%3D
2025-07-24 15:05:48.061 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.service.push_report_service:push_report:92 - 报告页面生成成功: https://oss.jingangai.cn/opinion-reports/report_00036537afcf_1753340747.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755932748&Signature=IPb6nl7VPrhVg6qiY1rXz94TBC8%3D
2025-07-24 15:05:48.061 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.service.push_report_service:_generate_mobile_friendly_url:712 - 生成移动端备用URL: http://localhost:9099/dev-api/public/report/view/report_00036537afcf_1753340747
2025-07-24 15:05:48.318 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.service.push_report_service:_validate_report_url:680 - 报告URL验证成功: https://oss.jingangai.cn/opinion-reports/report_00036537afcf_1753340747.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1755932748&Signature=IPb6nl7VPrhVg6qiY1rXz94TBC8%3D
2025-07-24 15:05:48.355 | 5a179f48635444ae9b98883599b8ef2d | WARNING  | module_opinion.service.push_report_service:push_report:173 - 未找到requirement_id 66 对应的任务，跳过OSS URL和情感统计数据保存
2025-07-24 15:05:49.029 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.service.push_report_service:_log_push_result:653 - 推送成功: {"push_id": "push_1753340747568", "target_url": "https://oapi.dingtalk.com/robot/send?access_token=620fdf10f4b844958510a0001d77b6a3c333320cf13dea0c78cfdd090ddfda63", "success": true, "response_status": 200, "response_data": {"errcode": 0, "errmsg": "ok"}, "error_message": null, "push_time": "2025-07-24T15:05:47.568976", "user_id": null}
2025-07-24 15:05:49.029 | 5a179f48635444ae9b98883599b8ef2d | INFO     | module_opinion.controller.opinion_analysis_controller:push_report:1073 - 报告推送成功，推送ID: push_1753340747568
2025-07-24 15:08:41.669 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-24 15:08:41.669 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-24 15:08:45.645 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-24 15:08:45.646 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-24 15:08:47.619 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-24 15:08:47.619 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-24 15:08:47.626 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-24 15:08:48.122 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-24 15:08:48.696 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-24 15:08:48.696 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-24 15:09:09.547 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-24 15:09:09.547 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-24 15:09:12.655 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-24 15:09:12.656 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-24 15:09:14.158 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-24 15:09:14.158 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-24 15:09:14.160 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-24 15:09:14.638 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-24 15:09:15.575 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-24 15:09:15.575 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-24 15:12:09.837 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-24 15:12:09.837 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
