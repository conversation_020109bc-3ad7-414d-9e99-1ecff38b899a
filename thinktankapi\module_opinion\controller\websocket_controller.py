import json
import asyncio
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.login_service import LoginService
from module_opinion.service.websocket_manager import websocket_manager
from module_opinion.service.analysis_progress_service import AnalysisProgressService
from utils.log_util import logger


# WebSocket路由
websocketController = APIRouter(prefix='/ws', tags=['WebSocket'])


@websocketController.websocket("/analysis-progress/{task_id}")
async def websocket_analysis_progress(
    websocket: WebSocket,
    task_id: str,
    token: str = Query(..., description="认证token"),
    query_db: AsyncSession = Depends(get_db)
):
    """
    分析进度WebSocket连接

    :param websocket: WebSocket连接
    :param task_id: 任务ID
    :param token: 认证token
    :param query_db: 数据库会话
    """
    connection_id = None

    try:
        # 验证token
        try:
            # 创建一个模拟的Request对象用于token验证
            from fastapi import Request
            from unittest.mock import MagicMock

            mock_request = MagicMock(spec=Request)
            current_user = await LoginService.get_current_user(mock_request, token, query_db)
            logger.info(f"WebSocket认证成功，用户ID: {current_user.user.user_id}")
        except Exception as e:
            logger.warning(f"WebSocket认证失败: {str(e)}")
            await websocket.close(code=4001, reason="Unauthorized")
            return

        # 建立WebSocket连接
        connection_id = await websocket_manager.connect(websocket, task_id)
        logger.info(f"分析进度WebSocket连接建立: {connection_id} -> 任务: {task_id}")
        
        # 发送当前进度状态
        try:
            progress = await AnalysisProgressService.get_analysis_progress(query_db, task_id)
            await websocket_manager.send_to_connection(connection_id, {
                "type": "initial_progress",
                "task_id": task_id,
                "data": progress.model_dump()
            })
        except Exception as e:
            logger.warning(f"获取初始进度失败: {task_id} - {str(e)}")
            await websocket_manager.send_to_connection(connection_id, {
                "type": "error",
                "message": f"获取初始进度失败: {str(e)}"
            })
        
        # 保持连接活跃，监听客户端消息
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理客户端消息
                await handle_client_message(connection_id, task_id, message, query_db)
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket客户端主动断开: {connection_id}")
                break
            except json.JSONDecodeError:
                logger.warning(f"收到无效JSON消息: {connection_id}")
                await websocket_manager.send_to_connection(connection_id, {
                    "type": "error",
                    "message": "无效的JSON格式"
                })
            except Exception as e:
                logger.error(f"处理WebSocket消息失败: {connection_id} - {str(e)}")
                await websocket_manager.send_to_connection(connection_id, {
                    "type": "error",
                    "message": f"处理消息失败: {str(e)}"
                })
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: {connection_id or 'unknown'}")
    except Exception as e:
        logger.error(f"WebSocket连接异常: {str(e)}")
    finally:
        # 清理连接
        if connection_id:
            await websocket_manager.disconnect(connection_id)


async def handle_client_message(
    connection_id: str,
    task_id: str,
    message: dict,
    query_db: AsyncSession
):
    """
    处理客户端消息
    
    :param connection_id: 连接ID
    :param task_id: 任务ID
    :param message: 客户端消息
    :param query_db: 数据库会话
    """
    try:
        message_type = message.get('type')
        
        if message_type == 'ping':
            # 心跳检测
            await websocket_manager.send_to_connection(connection_id, {
                "type": "pong",
                "timestamp": str(asyncio.get_event_loop().time())
            })
        
        elif message_type == 'get_progress':
            # 获取最新进度
            try:
                progress = await AnalysisProgressService.get_analysis_progress(query_db, task_id)
                await websocket_manager.send_to_connection(connection_id, {
                    "type": "progress_response",
                    "task_id": task_id,
                    "data": progress.model_dump()
                })
            except Exception as e:
                await websocket_manager.send_to_connection(connection_id, {
                    "type": "error",
                    "message": f"获取进度失败: {str(e)}"
                })
        
        elif message_type == 'cancel_task':
            # 取消任务
            try:
                success = await AnalysisProgressService.cancel_analysis_task(query_db, task_id)
                if success:
                    await websocket_manager.send_task_status_change(task_id, 'cancelled')
                    await websocket_manager.send_to_connection(connection_id, {
                        "type": "task_cancelled",
                        "task_id": task_id,
                        "message": "任务已取消"
                    })
                else:
                    await websocket_manager.send_to_connection(connection_id, {
                        "type": "error",
                        "message": "取消任务失败"
                    })
            except Exception as e:
                await websocket_manager.send_to_connection(connection_id, {
                    "type": "error",
                    "message": f"取消任务失败: {str(e)}"
                })
        
        else:
            # 未知消息类型
            await websocket_manager.send_to_connection(connection_id, {
                "type": "error",
                "message": f"未知消息类型: {message_type}"
            })
    
    except Exception as e:
        logger.error(f"处理客户端消息失败: {connection_id} - {str(e)}")
        await websocket_manager.send_to_connection(connection_id, {
            "type": "error",
            "message": f"处理消息失败: {str(e)}"
        })


@websocketController.websocket("/test")
async def websocket_test(websocket: WebSocket):
    """
    WebSocket测试连接
    """
    await websocket.accept()
    
    try:
        # 发送欢迎消息
        await websocket.send_text(json.dumps({
            "type": "welcome",
            "message": "WebSocket连接测试成功",
            "timestamp": str(asyncio.get_event_loop().time())
        }, ensure_ascii=False))
        
        # 保持连接并回显消息
        while True:
            data = await websocket.receive_text()
            await websocket.send_text(json.dumps({
                "type": "echo",
                "original_message": data,
                "timestamp": str(asyncio.get_event_loop().time())
            }, ensure_ascii=False))
    
    except WebSocketDisconnect:
        logger.info("WebSocket测试连接断开")
    except Exception as e:
        logger.error(f"WebSocket测试连接异常: {str(e)}")


# 获取WebSocket状态的HTTP接口
@websocketController.get("/status")
async def get_websocket_status(
    current_user = Depends(LoginService.get_current_user)
):
    """
    获取WebSocket连接状态
    """
    try:
        total_connections = websocket_manager.get_total_connections()
        active_tasks = list(websocket_manager.get_active_tasks())
        
        task_details = {}
        for task_id in active_tasks:
            task_details[task_id] = websocket_manager.get_task_connection_count(task_id)
        
        return {
            "total_connections": total_connections,
            "active_tasks_count": len(active_tasks),
            "active_tasks": active_tasks,
            "task_connection_details": task_details
        }
    
    except Exception as e:
        logger.error(f"获取WebSocket状态失败: {str(e)}")
        return {
            "error": f"获取状态失败: {str(e)}"
        }
