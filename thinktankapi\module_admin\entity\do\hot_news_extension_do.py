from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text, BigInteger, Float, ForeignKey
from sqlalchemy.orm import relationship
from config.database import Base


class HotNewsExtension(Base):
    """
    热点新闻扩展数据表
    """

    __tablename__ = 'hot_news_extension'
    __table_args__ = {'extend_existing': True}

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    news_id = Column(BigInteger, ForeignKey('news.id'), nullable=True, comment='新闻ID')
    heat_score = Column(Float, default=0.0, comment='热度分数')
    interaction_count = Column(Integer, default=0, comment='互动总数')
    share_count = Column(Integer, default=0, comment='分享数')
    comment_count = Column(Integer, default=0, comment='评论数')
    like_count = Column(Integer, default=0, comment='点赞数')
    view_count = Column(Integer, default=0, comment='浏览数')
    platform = Column(String(100), nullable=True, comment='平台名称')
    platform_type = Column(String(50), nullable=True, comment='平台类型：web, weibo, wechat, app, video等')
    region = Column(String(100), nullable=True, comment='地区')
    keywords = Column(Text, nullable=True, comment='关键词，JSON格式')
    trending_score = Column(Float, default=0.0, comment='趋势分数')
    influence_score = Column(Float, default=0.0, comment='影响力分数')
    engagement_rate = Column(Float, default=0.0, comment='参与率')
    spread_velocity = Column(Float, default=0.0, comment='传播速度')
    peak_time = Column(DateTime, nullable=True, comment='热度峰值时间')
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')

    # 关联关系
    # news = relationship("News", back_populates="hot_news_extensions")  # 暂时注释掉
