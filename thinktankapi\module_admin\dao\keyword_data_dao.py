from datetime import datetime
from sqlalchemy import delete, select, update, func, and_, or_, case, String
import re
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.keyword_data_do import KeywordData
from module_admin.entity.vo.keyword_data_vo import KeywordDataModel, KeywordDataPageQueryModel
from utils.page_util import PageUtil


class KeywordDataDao:
    """
    关键词数据管理模块数据库操作层
    """

    @classmethod
    def _build_filter_conditions(cls, keyword: str = None, start_date: str = None, end_date: str = None, 
                               platform_types: list = None, sentiment_types: list = None, 
                               info_attributes: list = None, web_site: str = None, type_field: str = None, base_conditions: list = None):
        """
        构建通用筛选条件
        
        :param keyword: 关键词筛选
        :param start_date: 开始日期
        :param end_date: 结束日期
        :param platform_types: 平台类型列表
        :param sentiment_types: 情感类型列表
        :param info_attributes: 信息属性列表
        :param web_site: 来源网站
        :param base_conditions: 基础条件列表
        :return: 筛选条件列表
        """
        conditions = base_conditions or []
        
        if keyword:
            conditions.append(KeywordData.keyword.like(f'%{keyword}%'))
        if start_date and end_date:
            conditions.append(KeywordData.createtime.between(start_date, end_date))
        if platform_types:
            conditions.append(KeywordData.type.in_(platform_types))
        if sentiment_types:
            conditions.append(KeywordData.sentiment.in_(sentiment_types))
        if info_attributes:
            attribute_conditions = []
            for attribute in info_attributes:
                attribute = attribute.strip()
                if attribute == 'official':
                    attribute_conditions.append(KeywordData.source.like('%官方%'))
                elif attribute == 'media':
                    attribute_conditions.append(KeywordData.source.like('%媒体%'))
                elif attribute == 'user_generated':
                    attribute_conditions.append(KeywordData.source.like('%用户%'))
                elif attribute == 'expert':
                    attribute_conditions.append(KeywordData.source.like('%专家%'))
            if attribute_conditions:
                conditions.append(or_(*attribute_conditions))
        if web_site:
            conditions.append(KeywordData.web.like(f'%{web_site}%'))
        if type_field:
            conditions.append(KeywordData.type.like(f'%{type_field}%'))
            
        return conditions

    @classmethod
    async def get_keyword_data_detail_by_id(cls, db: AsyncSession, keyword_data_id: int):
        """
        根据关键词数据ID获取详细信息

        :param db: orm对象
        :param keyword_data_id: 关键词数据ID
        :return: 关键词数据对象
        """
        keyword_data = (await db.execute(select(KeywordData).where(KeywordData.id == keyword_data_id))).scalars().first()
        return keyword_data

    @classmethod
    async def get_keyword_data_list(cls, db: AsyncSession, query_object: KeywordDataPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取关键词数据列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 关键词数据列表信息对象
        """
        query = select(KeywordData)
        
        # 构建查询条件
        conditions = []
        
        if query_object.title:
            conditions.append(KeywordData.title.like(f'%{query_object.title}%'))
        if query_object.keyword:
            conditions.append(KeywordData.keyword.like(f'%{query_object.keyword}%'))
        if query_object.type:
            conditions.append(KeywordData.type.like(f'%{query_object.type}%'))
        if query_object.web:
            conditions.append(KeywordData.web.like(f'%{query_object.web}%'))
        if query_object.begin_time:
            begin_time = datetime.strptime(query_object.begin_time, '%Y-%m-%d')
            # 使用日期比较，从当天00:00:00开始
            begin_time_str = begin_time.strftime('%Y-%m-%d 00:00:00')
            conditions.append(KeywordData.createtime >= begin_time_str)
        if query_object.end_time:
            end_time = datetime.strptime(query_object.end_time, '%Y-%m-%d')
            # 使用日期比较，到当天23:59:59结束
            end_time_str = end_time.strftime('%Y-%m-%d 23:59:59')
            conditions.append(KeywordData.createtime <= end_time_str)
        
        # 添加调试日志
        from utils.log_util import logger
        logger.info(f'DAO层时间筛选参数: begin_time={query_object.begin_time}, end_time={query_object.end_time}, time_filter={query_object.time_filter}')
        if query_object.begin_time:
            logger.info(f'开始时间字符串: {begin_time_str}')
        if query_object.end_time:
            logger.info(f'结束时间字符串: {end_time_str}')
        
        # 添加新的筛选条件
        if query_object.platform_types:
            # 处理平台类型参数，支持字符串和列表两种格式
            if isinstance(query_object.platform_types, str):
                platform_list = query_object.platform_types.split(',')
            else:
                platform_list = query_object.platform_types
            
            platform_conditions = []
            for platform in platform_list:
                platform = platform.strip()
                if platform == 'news':
                    platform_conditions.append(KeywordData.type.like('%新闻%'))
                elif platform == 'weibo':
                    platform_conditions.append(KeywordData.type.like('%微博%'))
                elif platform == 'wechat':
                    platform_conditions.append(KeywordData.type.like('%微信%'))
                elif platform == 'video':
                    platform_conditions.append(KeywordData.type.like('%视频%'))
                elif platform == 'app':
                    platform_conditions.append(KeywordData.type.like('%APP%'))
            if platform_conditions:
                conditions.append(or_(*platform_conditions))
        
        if query_object.sentiment_types:
            # 处理情感类型参数，支持字符串和列表两种格式
            if isinstance(query_object.sentiment_types, str):
                sentiment_list = query_object.sentiment_types.split(',')
            else:
                sentiment_list = query_object.sentiment_types
            
            sentiment_conditions = []
            for sentiment in sentiment_list:
                sentiment = sentiment.strip()
                if sentiment == 'positive':
                    sentiment_conditions.append(KeywordData.sentiment == 'positive')
                elif sentiment == 'neutral':
                    sentiment_conditions.append(KeywordData.sentiment == 'neutral')
                elif sentiment == 'negative':
                    sentiment_conditions.append(KeywordData.sentiment == 'negative')
            if sentiment_conditions:
                conditions.append(or_(*sentiment_conditions))
        
        if query_object.info_attributes:
            attribute_list = query_object.info_attributes.split(',')
            attribute_conditions = []
            for attribute in attribute_list:
                attribute = attribute.strip()
                if attribute == 'official':
                    attribute_conditions.append(KeywordData.source.like('%官方%'))
                elif attribute == 'media':
                    attribute_conditions.append(KeywordData.source.like('%媒体%'))
                elif attribute == 'user_generated':
                    attribute_conditions.append(KeywordData.source.like('%用户%'))
                elif attribute == 'expert':
                    attribute_conditions.append(KeywordData.source.like('%专家%'))
            if attribute_conditions:
                conditions.append(or_(*attribute_conditions))
        
        if query_object.web_site:
            conditions.append(KeywordData.web.like(f'%{query_object.web_site}%'))

        # 添加调试日志
        from utils.log_util import logger
        logger.info(f'总共构建了 {len(conditions)} 个查询条件')
        logger.info(f'查询参数: time_filter={query_object.time_filter}, platform_types={query_object.platform_types}, sentiment_types={query_object.sentiment_types}')
        
        if conditions:
            query = query.where(and_(*conditions))
            logger.info('已应用查询条件到SQL查询')
        else:
            logger.info('没有查询条件，将返回所有数据')

        # 按创建时间倒序排列
        query = query.order_by(KeywordData.createtime.desc())

        if is_page:
            # 分页查询
            return await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page=True)
        else:
            # 不分页查询
            keyword_data_list = (await db.execute(query)).scalars().all()
            return keyword_data_list

    @classmethod
    async def add_keyword_data_dao(cls, db: AsyncSession, keyword_data: KeywordDataModel):
        """
        新增关键词数据数据库操作

        :param db: orm对象
        :param keyword_data: 关键词数据对象
        :return:
        """
        db_keyword_data = KeywordData(**keyword_data.model_dump(exclude={'id'}))
        db.add(db_keyword_data)
        await db.flush()
        # 注释掉自动提交，让调用方控制事务
        # await db.commit()  # 提交事务，确保数据真正保存到数据库

        return db_keyword_data

    @classmethod
    async def edit_keyword_data_dao(cls, db: AsyncSession, keyword_data: dict):
        """
        编辑关键词数据数据库操作

        :param db: orm对象
        :param keyword_data: 需要更新的关键词数据字典
        :return:
        """
        await db.execute(update(KeywordData), [keyword_data])
        await db.commit()  # 提交事务，确保数据真正保存到数据库

    @classmethod
    async def delete_keyword_data_dao(cls, db: AsyncSession, keyword_data: KeywordDataModel):
        """
        删除关键词数据数据库操作

        :param db: orm对象
        :param keyword_data: 关键词数据对象
        :return:
        """
        await db.execute(delete(KeywordData).where(KeywordData.id.in_([keyword_data.id])))
        await db.commit()  # 提交事务，确保数据真正保存到数据库

    # @classmethod
    # async def get_type_statistics(cls, db: AsyncSession):
    #     """
    #     获取类型统计数据 - 已废弃，使用get_filter_statistics替代
    #
    #     :param db: orm对象
    #     :return: 类型统计数据
    #     """
    #     # 获取各类型的统计数据
    #     query = select(
    #         KeywordData.type,
    #         func.count(KeywordData.id).label('total_count'),
    #         func.sum(case((func.date(KeywordData.createtime) == func.curdate(), 1), else_=0)).label('today_count')
    #     ).group_by(KeywordData.type)
    #
    #     result = (await db.execute(query)).all()
    #     return result

    @classmethod
    async def get_filter_statistics(cls, db: AsyncSession, keyword: str = None, start_date: str = None, end_date: str = None, platform_types: list = None, sentiment_types: list = None, info_attributes: list = None, web_site: str = None, type_field: str = None):
        """
        获取筛选统计数据

        :param db: orm对象
        :param keyword: 关键词筛选
        :param start_date: 开始日期
        :param end_date: 结束日期
        :param platform_types: 平台类型列表
        :param sentiment_types: 情感类型列表
        :param info_attributes: 信息属性列表
        :param web_site: 来源网站
        :return: 筛选统计数据
        """
        # 获取平台分类（type字段）统计 - 不使用platform_types过滤，因为我们需要获取所有平台类型的统计
        type_conditions = cls._build_filter_conditions(
            keyword=keyword, start_date=start_date, end_date=end_date,
            platform_types=None, sentiment_types=sentiment_types,
            info_attributes=info_attributes, web_site=web_site, type_field=type_field,
            base_conditions=[KeywordData.type.isnot(None)]
        )
        type_query = select(
            KeywordData.type,
            func.count(KeywordData.id).label('total_count'),
            func.sum(case((func.date(KeywordData.createtime) == func.curdate(), 1), else_=0)).label('today_count')
        ).where(and_(*type_conditions)).group_by(KeywordData.type)

        # 获取来源网站（web字段）统计
        web_conditions = cls._build_filter_conditions(
            keyword=keyword, start_date=start_date, end_date=end_date,
            platform_types=platform_types, sentiment_types=sentiment_types,
            info_attributes=info_attributes, web_site=web_site, type_field=type_field,
            base_conditions=[KeywordData.web.isnot(None)]
        )
        web_query = select(
            KeywordData.web,
            func.count(KeywordData.id).label('total_count'),
            func.sum(case((func.date(KeywordData.createtime) == func.curdate(), 1), else_=0)).label('today_count')
        ).where(and_(*web_conditions)).group_by(KeywordData.web)

        # 获取关键词统计（取前10个）
        keyword_conditions = cls._build_filter_conditions(
            keyword=keyword, start_date=start_date, end_date=end_date,
            platform_types=platform_types, sentiment_types=sentiment_types,
            info_attributes=info_attributes, web_site=web_site, type_field=type_field,
            base_conditions=[KeywordData.keyword.isnot(None)]
        )
        keyword_query = select(
            KeywordData.keyword,
            func.count(KeywordData.id).label('total_count'),
            func.sum(case((func.date(KeywordData.createtime) == func.curdate(), 1), else_=0)).label('today_count')
        ).where(and_(*keyword_conditions)).group_by(KeywordData.keyword).order_by(func.count(KeywordData.id).desc()).limit(10)

        # 获取情感倾向统计
        sentiment_conditions = cls._build_filter_conditions(
            keyword=keyword, start_date=start_date, end_date=end_date,
            platform_types=platform_types, sentiment_types=None,  # 不使用sentiment_types过滤，因为我们需要获取所有情感类型的统计
            info_attributes=info_attributes, web_site=web_site, type_field=type_field,
            base_conditions=[KeywordData.sentiment.isnot(None)]
        )
        sentiment_query = select(
            KeywordData.sentiment,
            func.count(KeywordData.id).label('total_count'),
            func.sum(case((func.date(KeywordData.createtime) == func.curdate(), 1), else_=0)).label('today_count')
        ).where(and_(*sentiment_conditions)).group_by(KeywordData.sentiment)

        # 获取总计数据
        total_conditions = cls._build_filter_conditions(
            keyword=keyword, start_date=start_date, end_date=end_date,
            platform_types=platform_types, sentiment_types=sentiment_types,
            info_attributes=info_attributes, web_site=web_site, type_field=type_field
        )
        total_query = select(
            func.count(KeywordData.id).label('total_count'),
            func.sum(case((func.date(KeywordData.createtime) == func.curdate(), 1), else_=0)).label('today_count')
        )
        if total_conditions:
            total_query = total_query.where(and_(*total_conditions))

        # 执行查询
        type_result = (await db.execute(type_query)).all()
        web_result = (await db.execute(web_query)).all()
        keyword_result = (await db.execute(keyword_query)).all()
        sentiment_result = (await db.execute(sentiment_query)).all()
        total_result = (await db.execute(total_query)).first()

        return {
            'type_stats': type_result,
            'web_stats': web_result,
            'keyword_stats': keyword_result,
            'sentiment_stats': sentiment_result,
            'total_stats': total_result
        }
