2025-06-11 08:33:52.459 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 08:33:52.460 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 08:33:55.380 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 08:33:55.381 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 08:33:55.391 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 08:33:56.545 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 08:33:57.611 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 08:33:57.612 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 08:34:26.600 | 562de0003b2e4b228e0778f049903009 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为0bbbe909-8b7a-4d77-95c8-d0f4ab5bbffd的会话获取图片验证码成功
2025-06-11 08:34:38.783 | ebbfd469bfce4cfe809ccca0cd634ce8 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-11 08:34:39.451 | ec817a094480498780304220f9081523 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 08:34:40.962 | 27320a9cda2a4331933dd3033f31516b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 08:34:45.272 | c7ee18acb991465d872ccd0928d170c6 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 08:34:45.686 | c7ee18acb991465d872ccd0928d170c6 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 08:34:46.074 | c7ee18acb991465d872ccd0928d170c6 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 08:34:46.074 | c7ee18acb991465d872ccd0928d170c6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 08:34:46.637 | 09ac1f68c6a142098a150e599fe53a84 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 08:34:46.666 | 09ac1f68c6a142098a150e599fe53a84 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 08:34:47.049 | 09ac1f68c6a142098a150e599fe53a84 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 08:34:47.049 | 09ac1f68c6a142098a150e599fe53a84 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 08:34:58.495 | aff976a954044511ac32479c1a6a50de | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 08:34:58.991 | c2feb1cf2edd474999459cbb40f6d6be | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 08:34:59.555 | c0e65c2a257a4ef9840a9302816632b9 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 08:34:59.584 | c0e65c2a257a4ef9840a9302816632b9 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 08:34:59.724 | c0e65c2a257a4ef9840a9302816632b9 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 08:34:59.724 | c0e65c2a257a4ef9840a9302816632b9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 08:35:00.538 | 58b5c7ce6eac44a9a9976b9410aea095 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 08:35:00.567 | 58b5c7ce6eac44a9a9976b9410aea095 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 08:35:00.709 | 58b5c7ce6eac44a9a9976b9410aea095 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 08:35:00.709 | 58b5c7ce6eac44a9a9976b9410aea095 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 08:35:26.145 | 87b908adb2fa4452a0ab466e81cd8a73 | ERROR    | module_warning.service.warning_record_service:edit_warning_record_services:116 - 编辑预警记录失败: (asyncmy.errors.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`jgthinktanks`.`warning_record`, CONSTRAINT `warning_record_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `scheme` (`id`) ON DELETE CASCADE)')
[SQL: UPDATE warning_record SET scheme_id=%s, warning_type=%s, content=%s, status=%s, update_time=%s, update_by=%s, remark=%s WHERE warning_record.id = %s]
[parameters: (7, '负面', '方太品牌在某平台出现负面评论，用1', 0, datetime.datetime(2025, 6, 11, 8, 35, 26, 58518), 'admin', '自动检测到的负面舆情', 27)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-11 08:35:26.146 | 87b908adb2fa4452a0ab466e81cd8a73 | ERROR    | module_warning.controller.warning_record_controller:edit_warning_record:112 - 编辑预警记录失败: 
2025-06-11 08:36:46.115 | 2059a9a2a85244a6b1d000c09fd3d101 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 08:36:47.193 | 2009c6579eb5498ab0fad45a926aa191 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 08:36:53.515 | 6187274915fa41f8b89041f8cdc04bbe | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 08:36:53.642 | 6187274915fa41f8b89041f8cdc04bbe | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 08:36:54.255 | 6187274915fa41f8b89041f8cdc04bbe | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 08:36:54.256 | 6187274915fa41f8b89041f8cdc04bbe | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 08:36:54.921 | 9409f17b05284aa5b7c514e8e363e6c0 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 08:36:54.950 | 9409f17b05284aa5b7c514e8e363e6c0 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 08:36:55.323 | 9409f17b05284aa5b7c514e8e363e6c0 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 08:36:55.324 | 9409f17b05284aa5b7c514e8e363e6c0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:37:34.685 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 09:37:34.686 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 09:37:35.488 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 09:37:35.488 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 09:37:35.491 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 09:37:36.075 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 09:37:36.615 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 09:37:36.615 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 09:38:11.016 | 4999409a82f34808a39a6a1053787124 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为dc0d3a6e-526f-4c34-b279-81f669a66f9d的会话获取图片验证码成功
2025-06-11 09:39:01.181 | 3e2477ac4e0d45b7b89ab83c8275966a | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-11 09:39:01.389 | 340168eaab7a4666b03151df3fef3f7f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 09:39:01.878 | 285a316ddb614238a0c925b2a171944e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 09:39:04.892 | 0737c686c4f74690b52a2e5255c2b3d8 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:39:04.917 | 0737c686c4f74690b52a2e5255c2b3d8 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:39:05.039 | 0737c686c4f74690b52a2e5255c2b3d8 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:39:05.039 | 0737c686c4f74690b52a2e5255c2b3d8 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:39:05.245 | c15d1e0548de46ff9e286303369f2542 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:39:05.269 | c15d1e0548de46ff9e286303369f2542 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:39:05.390 | c15d1e0548de46ff9e286303369f2542 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:39:05.390 | c15d1e0548de46ff9e286303369f2542 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:39:21.451 | 551369e27f824886b45607e4c2ccacc2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 09:39:21.621 | de03ac6eb52d4cb9a6ad8b9e4102119b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 09:39:22.037 | 13ae84a754524a13b1c40719f7f8ee97 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:39:22.061 | 13ae84a754524a13b1c40719f7f8ee97 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:39:22.174 | 13ae84a754524a13b1c40719f7f8ee97 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:39:22.175 | 13ae84a754524a13b1c40719f7f8ee97 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:39:22.377 | 5952d33b6b9c4983b7352b48ea00e98d | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:39:22.401 | 5952d33b6b9c4983b7352b48ea00e98d | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:39:22.515 | 5952d33b6b9c4983b7352b48ea00e98d | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:39:22.515 | 5952d33b6b9c4983b7352b48ea00e98d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:47:33.889 | c5688d12a03f4b42b44d1ae0a4c2afd3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 09:47:34.059 | e6f813b04f994369bfd76c56fc44ab97 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 09:47:34.478 | 0cff1ff8da424156be23c7dc0f6710b2 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:47:34.501 | 0cff1ff8da424156be23c7dc0f6710b2 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:47:34.614 | 0cff1ff8da424156be23c7dc0f6710b2 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:47:34.614 | 0cff1ff8da424156be23c7dc0f6710b2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:47:34.817 | be640571064a48439e36cff7f79bada3 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:47:34.840 | be640571064a48439e36cff7f79bada3 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:47:34.953 | be640571064a48439e36cff7f79bada3 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:47:34.953 | be640571064a48439e36cff7f79bada3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:50:05.465 | 0598260bb652431a8996d1362ac0b4fe | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:50:05.489 | 0598260bb652431a8996d1362ac0b4fe | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:50:05.604 | 0598260bb652431a8996d1362ac0b4fe | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:50:05.604 | 0598260bb652431a8996d1362ac0b4fe | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:50:06.136 | 1b6b6bec4a0c42ab9e03b88f7aa4f6a6 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:50:06.160 | 1b6b6bec4a0c42ab9e03b88f7aa4f6a6 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:50:06.276 | 1b6b6bec4a0c42ab9e03b88f7aa4f6a6 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:50:06.277 | 1b6b6bec4a0c42ab9e03b88f7aa4f6a6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:50:16.544 | 956dc1a8f189415695a31f54ae9e1c56 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 09:50:16.724 | fbed33cf5bf14b5b91834a0c2a6ca5d1 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 09:50:28.681 | 1b022c3055984f3fb1817005197765b6 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:50:28.705 | 1b022c3055984f3fb1817005197765b6 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:50:28.827 | 1b022c3055984f3fb1817005197765b6 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:50:28.827 | 1b022c3055984f3fb1817005197765b6 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:50:29.353 | 3f187246edc849ca94af0877dcf79894 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:50:29.377 | 3f187246edc849ca94af0877dcf79894 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:50:29.494 | 3f187246edc849ca94af0877dcf79894 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:50:29.494 | 3f187246edc849ca94af0877dcf79894 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:51:07.233 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 09:51:07.234 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 09:51:08.021 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 09:51:08.022 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 09:51:08.024 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 09:51:08.426 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 09:51:08.983 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 09:51:08.983 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 09:51:32.422 | 4d0f868439374430b6c63b88703ed5e3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 09:51:32.603 | f25f6567cafc4deb9af30611d5dfa978 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 09:51:44.171 | 495cd939c4dc4059b50c9b48fc2d8fbf | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:51:44.196 | 495cd939c4dc4059b50c9b48fc2d8fbf | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:51:44.313 | 495cd939c4dc4059b50c9b48fc2d8fbf | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:51:44.314 | 495cd939c4dc4059b50c9b48fc2d8fbf | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:51:44.529 | 2dc364af400e4c97b6df563fb3520b87 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:51:44.554 | 2dc364af400e4c97b6df563fb3520b87 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:51:44.672 | 2dc364af400e4c97b6df563fb3520b87 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:51:44.673 | 2dc364af400e4c97b6df563fb3520b87 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:52:58.500 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 09:52:58.500 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 09:53:18.384 | 37ee45f4ec6c4f4eb5c0233e7598f233 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 09:53:18.563 | b1fd6e0b61b349b9964929b893a6c9ee | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 09:53:19.007 | 8a4c55028a4e4905a55b357e9e1b635a | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:53:19.032 | 8a4c55028a4e4905a55b357e9e1b635a | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:53:19.154 | 8a4c55028a4e4905a55b357e9e1b635a | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:53:19.155 | 8a4c55028a4e4905a55b357e9e1b635a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:53:19.370 | 871152cad3e14ad1b099967205fc09a3 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:53:19.398 | 871152cad3e14ad1b099967205fc09a3 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:53:19.742 | 871152cad3e14ad1b099967205fc09a3 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:53:19.742 | 871152cad3e14ad1b099967205fc09a3 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:56:04.577 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 09:56:04.577 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 09:56:05.466 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 09:56:05.467 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 09:56:05.469 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 09:56:05.916 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 09:56:06.436 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 09:56:06.436 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 09:58:38.825 | d9d6b6fa9baa4d6fa82a0b1ba1fc1a14 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 09:58:38.996 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 09:58:38.999 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 09:58:43.750 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 09:58:43.751 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 09:58:44.637 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 09:58:44.638 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 09:58:44.640 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 09:58:45.335 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 09:58:45.909 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 09:58:45.909 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 09:58:46.055 | a0daf783fed64915bbba64c65131365f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 09:58:46.712 | 3ab8ce55dd7b45bbaeb8feff5d497096 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:58:46.745 | 3ab8ce55dd7b45bbaeb8feff5d497096 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:58:46.889 | 3ab8ce55dd7b45bbaeb8feff5d497096 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:58:46.890 | 3ab8ce55dd7b45bbaeb8feff5d497096 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:58:47.149 | 3b04fb6a06654577b3936ea9a8f79f11 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 09:58:47.177 | 3b04fb6a06654577b3936ea9a8f79f11 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:58:47.313 | 3b04fb6a06654577b3936ea9a8f79f11 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 09:58:47.314 | 3b04fb6a06654577b3936ea9a8f79f11 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 09:58:49.467 | d78052a025cd45068c5be795852eb6ad | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:58:49.467 | d78052a025cd45068c5be795852eb6ad | INFO     | module_warning.controller.warning_record_controller:export_warning_record:64 - 导出预警记录成功
2025-06-11 09:59:11.431 | 682aa8e459df4e0d9368ad592f0df804 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 09:59:11.431 | 682aa8e459df4e0d9368ad592f0df804 | INFO     | module_warning.controller.warning_record_controller:export_warning_record:64 - 导出预警记录成功
2025-06-11 10:01:00.809 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 10:01:00.809 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 10:01:03.764 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:01:03.764 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:01:06.531 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:01:06.532 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:01:07.360 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:01:07.360 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:01:07.362 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:01:07.791 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:01:08.394 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:01:08.395 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 10:02:01.875 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 10:02:01.876 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 10:02:04.612 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:02:04.613 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:02:07.485 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:02:07.486 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:02:08.400 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:02:08.401 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:02:08.402 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:02:08.875 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:02:09.470 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:02:09.470 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 10:02:36.119 | f509b681010942daaa5f901630b8740f | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:02:36.144 | f509b681010942daaa5f901630b8740f | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-11 10:02:36.268 | f509b681010942daaa5f901630b8740f | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:02:36.268 | f509b681010942daaa5f901630b8740f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:02:36.374 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 10:02:36.375 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 10:02:39.915 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:02:39.916 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:02:40.714 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:02:40.714 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:02:40.716 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:02:41.126 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:02:41.675 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:02:41.675 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 10:02:41.827 | c43ef2a1ea874196a4f3dc7bd4c73d17 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:02:41.850 | c43ef2a1ea874196a4f3dc7bd4c73d17 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:02:41.931 | 064f2f8a01344d56a1d537ecb83a6769 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:02:41.967 | c43ef2a1ea874196a4f3dc7bd4c73d17 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:163 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:02:41.967 | c43ef2a1ea874196a4f3dc7bd4c73d17 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:02:42.120 | 770e1c7dfbf042bb9193e941742b02e9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:02:42.853 | 01d7a50cd10d4de0a31c199d52924614 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:02:42.876 | 01d7a50cd10d4de0a31c199d52924614 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:02:42.992 | 01d7a50cd10d4de0a31c199d52924614 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:163 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:02:42.992 | 01d7a50cd10d4de0a31c199d52924614 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:02:43.219 | 835a3bf75ae649d4bf70dc064ece1828 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:02:43.246 | 835a3bf75ae649d4bf70dc064ece1828 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:02:43.375 | 835a3bf75ae649d4bf70dc064ece1828 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:163 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:02:43.376 | 835a3bf75ae649d4bf70dc064ece1828 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:03:02.955 | 5e9b2b455bdf47de99a3f4e2c2bb575a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:03:03.144 | e7961f9393a84016902bbd71141d2a81 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:03:03.816 | 742f9b14365840169ceb3e040f7e7da4 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:03:03.840 | 742f9b14365840169ceb3e040f7e7da4 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:03:03.956 | 742f9b14365840169ceb3e040f7e7da4 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:163 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:03:03.957 | 742f9b14365840169ceb3e040f7e7da4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:03:04.185 | 6d255ab6044c44b897e3f42e2bd4e6af | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:03:04.212 | 6d255ab6044c44b897e3f42e2bd4e6af | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:03:04.342 | 6d255ab6044c44b897e3f42e2bd4e6af | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:163 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:03:04.342 | 6d255ab6044c44b897e3f42e2bd4e6af | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:03:10.694 | b3e623d9f1fc4df2a4e91b54696dc5b4 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:03:10.694 | b3e623d9f1fc4df2a4e91b54696dc5b4 | ERROR    | module_warning.controller.warning_record_controller:export_warning_record:69 - 导出预警记录失败: 'WarningRecord' object has no attribute 'get'
2025-06-11 10:03:25.750 | 73754fc621694bcc8cbddbd741126163 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:03:25.750 | 73754fc621694bcc8cbddbd741126163 | ERROR    | module_warning.controller.warning_record_controller:export_warning_record:69 - 导出预警记录失败: 'WarningRecord' object has no attribute 'get'
2025-06-11 10:07:01.205 | 257afc590faf42a2b37884a37b16f107 | ERROR    | module_warning.service.warning_record_service:edit_warning_record_services:117 - 编辑预警记录失败: (asyncmy.errors.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`jgthinktanks`.`warning_record`, CONSTRAINT `warning_record_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `scheme` (`id`) ON DELETE CASCADE)')
[SQL: UPDATE warning_record SET scheme_id=%s, warning_type=%s, content=%s, status=%s, update_time=%s, update_by=%s, remark=%s WHERE warning_record.id = %s]
[parameters: (7, '负面', '方太品牌在某平台出现负面评论，用户反映产品质量问题', 0, datetime.datetime(2025, 6, 11, 10, 7, 1, 126674), 'admin', '自动检测到的负面舆情', 27)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-11 10:07:01.205 | 257afc590faf42a2b37884a37b16f107 | ERROR    | module_warning.controller.warning_record_controller:edit_warning_record:137 - 编辑预警记录失败: 
2025-06-11 10:07:19.300 | e24cee790ea844698cdad8a25fce2e70 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:07:19.482 | 5a351f5466d647dd9360841d1dc032b0 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:07:19.948 | ed45f28245824177ac34130ba0e5a121 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:07:19.974 | ed45f28245824177ac34130ba0e5a121 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:07:20.111 | ed45f28245824177ac34130ba0e5a121 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:163 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:07:20.111 | ed45f28245824177ac34130ba0e5a121 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:07:20.323 | 70143244bc914581b0faeea99fecf397 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:07:20.346 | 70143244bc914581b0faeea99fecf397 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:07:20.696 | 70143244bc914581b0faeea99fecf397 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:163 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:07:20.696 | 70143244bc914581b0faeea99fecf397 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:07:26.298 | 43ba547c194e461a85288d1bc1ff921c | ERROR    | module_warning.service.warning_record_service:edit_warning_record_services:117 - 编辑预警记录失败: (asyncmy.errors.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`jgthinktanks`.`warning_record`, CONSTRAINT `warning_record_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `scheme` (`id`) ON DELETE CASCADE)')
[SQL: UPDATE warning_record SET scheme_id=%s, warning_type=%s, content=%s, status=%s, update_time=%s, update_by=%s, remark=%s WHERE warning_record.id = %s]
[parameters: (7, '负面', '方太品牌在某平台出现负面评论，用户反映产品质1', 0, datetime.datetime(2025, 6, 11, 10, 7, 26, 222657), 'admin', '自动检测到的负面舆情', 27)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-11 10:07:26.299 | 43ba547c194e461a85288d1bc1ff921c | ERROR    | module_warning.controller.warning_record_controller:edit_warning_record:137 - 编辑预警记录失败: 
2025-06-11 10:12:05.528 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 10:12:05.529 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 10:12:09.106 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:12:09.107 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:12:11.887 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:12:11.887 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:12:12.272 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 10:12:12.273 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 10:12:12.980 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:12:12.980 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:12:12.982 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:12:13.551 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:12:14.299 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:12:14.299 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 10:12:15.935 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:12:15.935 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:12:16.967 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:12:16.967 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:12:16.969 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:12:17.379 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:12:18.011 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:12:18.011 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 10:18:00.998 | 5efd57bbbd914985806575d5c67e9c1c | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:18:01.026 | 5efd57bbbd914985806575d5c67e9c1c | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:18:01.151 | 5efd57bbbd914985806575d5c67e9c1c | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:18:01.151 | 5efd57bbbd914985806575d5c67e9c1c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:18:01.370 | 94bcba9615384a3f9bb08d62e7dc1411 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:18:01.394 | 94bcba9615384a3f9bb08d62e7dc1411 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:18:01.739 | 94bcba9615384a3f9bb08d62e7dc1411 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:18:01.740 | 94bcba9615384a3f9bb08d62e7dc1411 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:18:07.625 | 8c9d09bca15542f5bc9a425099fd1842 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:18:08.063 | 419598fa74aa433e869c0f8deed5f3d7 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:18:08.634 | 789c5dde06e2471297a8d41566a0e95f | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:18:08.659 | 789c5dde06e2471297a8d41566a0e95f | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:18:08.778 | 789c5dde06e2471297a8d41566a0e95f | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:18:08.779 | 789c5dde06e2471297a8d41566a0e95f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:18:09.020 | 4c1a9ad7367544528fb4e4593c179c73 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:18:09.045 | 4c1a9ad7367544528fb4e4593c179c73 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:18:09.162 | 4c1a9ad7367544528fb4e4593c179c73 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:18:09.162 | 4c1a9ad7367544528fb4e4593c179c73 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:18:12.639 | 4b324619fa524d22a9b2facacc967362 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:100 - 开始编辑预警记录，输入数据: {'id': 27, 'scheme_id': None, 'warning_type': None, 'content': '方太品牌在某平台出现负面评论，用户反映产1', 'keywords': None, 'status': 0, 'create_time': None, 'update_time': datetime.datetime(2025, 6, 11, 10, 18, 12, 639506), 'create_by': '', 'update_by': 'admin', 'remark': '自动检测到的负面舆情'}
2025-06-11 10:18:12.664 | 4b324619fa524d22a9b2facacc967362 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:111 - 找到现有记录: ID=27, scheme_id=11
2025-06-11 10:18:12.665 | 4b324619fa524d22a9b2facacc967362 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:118 - 准备更新的数据: {'id': 27, 'content': '方太品牌在某平台出现负面评论，用户反映产1', 'status': 0, 'update_time': datetime.datetime(2025, 6, 11, 10, 18, 12, 665066), 'update_by': 'admin', 'remark': '自动检测到的负面舆情'}
2025-06-11 10:18:12.665 | 4b324619fa524d22a9b2facacc967362 | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:111 - DAO层更新数据: record_id=27, update_data={'content': '方太品牌在某平台出现负面评论，用户反映产1', 'status': 0, 'update_time': datetime.datetime(2025, 6, 11, 10, 18, 12, 665066), 'update_by': 'admin', 'remark': '自动检测到的负面舆情'}
2025-06-11 10:18:12.921 | 4b324619fa524d22a9b2facacc967362 | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:119 - 数据库更新结果: 影响行数=1
2025-06-11 10:18:12.964 | 4b324619fa524d22a9b2facacc967362 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:122 - 编辑预警记录成功，记录ID: 27
2025-06-11 10:18:12.965 | 4b324619fa524d22a9b2facacc967362 | INFO     | module_warning.controller.warning_record_controller:edit_warning_record:134 - 更新成功
2025-06-11 10:18:13.383 | 3777b553a4fb4e3f93b2739705196c40 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:18:13.412 | 3777b553a4fb4e3f93b2739705196c40 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:18:13.533 | 3777b553a4fb4e3f93b2739705196c40 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:18:13.534 | 3777b553a4fb4e3f93b2739705196c40 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:19:52.774 | dc35d2822d0c4c1589a0a3cfffc91de4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:19:52.979 | 999f0c6667584966868541287319540a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:19:54.054 | 7a8682d6d99e40c2910edc5265f12464 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:19:54.079 | 7a8682d6d99e40c2910edc5265f12464 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:19:54.202 | 7a8682d6d99e40c2910edc5265f12464 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:19:54.203 | 7a8682d6d99e40c2910edc5265f12464 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:19:54.457 | a898bfeda68a4a02973ae6170c13893d | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:19:54.482 | a898bfeda68a4a02973ae6170c13893d | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:19:54.824 | a898bfeda68a4a02973ae6170c13893d | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:19:54.825 | a898bfeda68a4a02973ae6170c13893d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:22:09.869 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 10:22:09.870 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 10:22:12.318 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:22:12.318 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:22:13.337 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:22:13.337 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:22:13.339 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:22:13.743 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:22:13.909 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 10:22:13.910 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 10:22:14.267 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:22:14.267 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 10:22:16.602 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:22:16.603 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:22:17.473 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:22:17.473 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:22:17.475 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:22:17.926 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:22:18.444 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:22:18.445 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 10:22:28.342 | 3f4bb7dacfba416abb4cff4470ff02d3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:22:28.541 | 372b9be06bee4d3cbbb47a1544857504 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:22:29.018 | e86672c92f794979b2676b48533cb78b | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:22:29.046 | e86672c92f794979b2676b48533cb78b | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:22:29.181 | e86672c92f794979b2676b48533cb78b | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:22:29.182 | e86672c92f794979b2676b48533cb78b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:22:29.416 | baf321acd5da4e43ae5e98f285772bca | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:22:29.443 | baf321acd5da4e43ae5e98f285772bca | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:22:29.572 | baf321acd5da4e43ae5e98f285772bca | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:22:29.573 | baf321acd5da4e43ae5e98f285772bca | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:25:58.325 | 051a03845049425db600d66c15502e7c | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:25:58.351 | 051a03845049425db600d66c15502e7c | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:25:58.474 | 051a03845049425db600d66c15502e7c | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:25:58.474 | 051a03845049425db600d66c15502e7c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:25:58.700 | 7cdc4be41e1f4b76897a92169bf1f735 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:25:58.726 | 7cdc4be41e1f4b76897a92169bf1f735 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:25:58.849 | 7cdc4be41e1f4b76897a92169bf1f735 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:25:58.850 | 7cdc4be41e1f4b76897a92169bf1f735 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:26:02.374 | ef6644c95c0d4ccea9f2c61672c134cc | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:26:02.567 | 054898a266b94e98b2ec0545b14120ef | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:26:03.025 | 55f561ee22f149829bd7033272591538 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:26:03.051 | 55f561ee22f149829bd7033272591538 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:26:03.179 | 55f561ee22f149829bd7033272591538 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:26:03.180 | 55f561ee22f149829bd7033272591538 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:26:03.408 | dbdb515157524730a378d8c931be8bb5 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:26:03.435 | dbdb515157524730a378d8c931be8bb5 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:26:03.561 | dbdb515157524730a378d8c931be8bb5 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:26:03.562 | dbdb515157524730a378d8c931be8bb5 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:28:32.060 | d308b0115001406c90331c2ab4ea8f4b | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:28:32.086 | d308b0115001406c90331c2ab4ea8f4b | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:28:32.212 | d308b0115001406c90331c2ab4ea8f4b | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:28:32.212 | d308b0115001406c90331c2ab4ea8f4b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:28:32.747 | d5e6ce96e9c84676a39ec6a908aae3dd | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:28:32.773 | d5e6ce96e9c84676a39ec6a908aae3dd | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:28:32.901 | d5e6ce96e9c84676a39ec6a908aae3dd | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:28:32.901 | d5e6ce96e9c84676a39ec6a908aae3dd | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:28:39.182 | 1af5fca99cf3481d84f33914a3123dd1 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:28:39.373 | 4be660cdffcd4004ba966b199ac53165 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:28:40.109 | 7929d4ae78d842ce96db2e3409eda86d | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:28:40.136 | 7929d4ae78d842ce96db2e3409eda86d | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:28:40.265 | 7929d4ae78d842ce96db2e3409eda86d | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:28:40.266 | 7929d4ae78d842ce96db2e3409eda86d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:28:40.495 | 1003bb3a7ab8409e88191643c88c5344 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:28:40.521 | 1003bb3a7ab8409e88191643c88c5344 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:28:40.646 | 1003bb3a7ab8409e88191643c88c5344 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:28:40.647 | 1003bb3a7ab8409e88191643c88c5344 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:29:08.816 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 10:29:08.817 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 10:31:20.197 | 592bf9d4418949eca895eeb84d8fb6fe | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:31:20.222 | 592bf9d4418949eca895eeb84d8fb6fe | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:31:20.345 | 592bf9d4418949eca895eeb84d8fb6fe | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:31:20.345 | 592bf9d4418949eca895eeb84d8fb6fe | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:31:20.565 | a06f0e97f17a41a0b5ef32dd432cbc52 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:31:20.590 | a06f0e97f17a41a0b5ef32dd432cbc52 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:31:20.713 | a06f0e97f17a41a0b5ef32dd432cbc52 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:31:20.713 | a06f0e97f17a41a0b5ef32dd432cbc52 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:31:27.012 | 6342b154a6bc4551803d9d3f8f3ab8cf | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:31:27.204 | c77a41cf0366493883fc929e302a02e8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:31:27.665 | 986e59a03fb64c4aac81bec7a2bd7a19 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:31:27.691 | 986e59a03fb64c4aac81bec7a2bd7a19 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:31:27.819 | 986e59a03fb64c4aac81bec7a2bd7a19 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:31:27.819 | 986e59a03fb64c4aac81bec7a2bd7a19 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:31:28.048 | d430edfd88e647d08c22bf24f547b945 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:31:28.074 | d430edfd88e647d08c22bf24f547b945 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:31:28.201 | d430edfd88e647d08c22bf24f547b945 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:31:28.201 | d430edfd88e647d08c22bf24f547b945 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:32:33.231 | 69bf259f43c24394a94881beada6f4e8 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:32:33.256 | 69bf259f43c24394a94881beada6f4e8 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:32:33.379 | 69bf259f43c24394a94881beada6f4e8 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:32:33.379 | 69bf259f43c24394a94881beada6f4e8 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:32:33.909 | 970c8a090f6844d4afc8504cd69633ed | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:32:33.935 | 970c8a090f6844d4afc8504cd69633ed | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:32:34.060 | 970c8a090f6844d4afc8504cd69633ed | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:32:34.060 | 970c8a090f6844d4afc8504cd69633ed | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:32:59.966 | 87755a83559f46f183bd9868bdb0838c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:33:00.157 | d5efe32b57384a8ab1a6f5128b017509 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:33:00.904 | e4551d41d65146ef8b7aa43627c69615 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:33:00.930 | e4551d41d65146ef8b7aa43627c69615 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:33:01.058 | e4551d41d65146ef8b7aa43627c69615 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:33:01.058 | e4551d41d65146ef8b7aa43627c69615 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:33:01.289 | 20a4497a74f749f3a4083c695bf628a2 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:33:01.315 | 20a4497a74f749f3a4083c695bf628a2 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:33:01.442 | 20a4497a74f749f3a4083c695bf628a2 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:33:01.443 | 20a4497a74f749f3a4083c695bf628a2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:35:43.483 | 7ce90b72cd194be783ba0e9403cac3af | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:35:43.509 | 7ce90b72cd194be783ba0e9403cac3af | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:35:43.635 | 7ce90b72cd194be783ba0e9403cac3af | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:35:43.635 | 7ce90b72cd194be783ba0e9403cac3af | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:35:44.181 | ed598d800c9341cb831c3f115e1ce7dc | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:35:44.207 | ed598d800c9341cb831c3f115e1ce7dc | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:35:44.575 | ed598d800c9341cb831c3f115e1ce7dc | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:35:44.576 | ed598d800c9341cb831c3f115e1ce7dc | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:35:47.371 | cbfa9751982c4e078a1f00b5f9ebe773 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:35:47.556 | 41cbd8667fe7486185d89256b96bc443 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:35:48.317 | 7e945ce7013248a68565c61207078737 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:35:48.342 | 7e945ce7013248a68565c61207078737 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:35:48.466 | 7e945ce7013248a68565c61207078737 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:35:48.466 | 7e945ce7013248a68565c61207078737 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:35:48.693 | af2f94bd367246c2862dfe2710504731 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:35:48.720 | af2f94bd367246c2862dfe2710504731 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:35:48.844 | af2f94bd367246c2862dfe2710504731 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:35:48.844 | af2f94bd367246c2862dfe2710504731 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:35:51.465 | ea217b77db1e4cdcbafa1f411ac1b865 | INFO     | module_warning.service.warning_record_service:delete_warning_record_services:154 - 删除预警记录成功，记录数量: 1
2025-06-11 10:35:51.465 | ea217b77db1e4cdcbafa1f411ac1b865 | INFO     | module_warning.controller.warning_record_controller:delete_warning_record:155 - 删除成功
2025-06-11 10:35:51.842 | b14b28042bcd4b47aae746674c56aac9 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:35:51.867 | b14b28042bcd4b47aae746674c56aac9 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:35:51.992 | b14b28042bcd4b47aae746674c56aac9 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:35:51.992 | b14b28042bcd4b47aae746674c56aac9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:35:57.000 | e1110583344f4543871a736b2bd6fd3a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:35:57.187 | c5d757d1c1e245adb53ac354053c67b9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:35:57.971 | 703178e7ee184fa7aa6d21d64196497e | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:35:57.998 | 703178e7ee184fa7aa6d21d64196497e | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:35:58.123 | 703178e7ee184fa7aa6d21d64196497e | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:35:58.124 | 703178e7ee184fa7aa6d21d64196497e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:35:58.361 | 350705078d2c4ba5b99459ee00f1cdf2 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:35:58.387 | 350705078d2c4ba5b99459ee00f1cdf2 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:35:58.523 | 350705078d2c4ba5b99459ee00f1cdf2 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:35:58.523 | 350705078d2c4ba5b99459ee00f1cdf2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:36:03.687 | 80b5ec6370ea4be99f20b4c23877cc2d | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:36:03.713 | 80b5ec6370ea4be99f20b4c23877cc2d | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:36:03.839 | 80b5ec6370ea4be99f20b4c23877cc2d | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:36:03.839 | 80b5ec6370ea4be99f20b4c23877cc2d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:36:14.438 | 7c0fabb9cf9b4483b46b2ea3b0526588 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:36:14.464 | 7c0fabb9cf9b4483b46b2ea3b0526588 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:36:14.591 | 7c0fabb9cf9b4483b46b2ea3b0526588 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:36:14.592 | 7c0fabb9cf9b4483b46b2ea3b0526588 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:36:19.718 | 4a70ceaec0fb41828a24873e0379896f | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:36:19.745 | 4a70ceaec0fb41828a24873e0379896f | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:36:19.867 | 4a70ceaec0fb41828a24873e0379896f | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:36:19.868 | 4a70ceaec0fb41828a24873e0379896f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:36:22.091 | bdcf4b3ce09045d8bb07b10ee81c0048 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:36:22.123 | bdcf4b3ce09045d8bb07b10ee81c0048 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:36:22.254 | bdcf4b3ce09045d8bb07b10ee81c0048 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:36:22.254 | bdcf4b3ce09045d8bb07b10ee81c0048 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:36:33.421 | f420ca201be043d697104945eb3a0c69 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:36:33.605 | 19366e1466a04b30a9907ef19911b0c6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:36:34.330 | 53e77f0739cc4ae58c0fb816f4db7ba9 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:36:34.356 | 53e77f0739cc4ae58c0fb816f4db7ba9 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:36:34.487 | 53e77f0739cc4ae58c0fb816f4db7ba9 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:36:34.487 | 53e77f0739cc4ae58c0fb816f4db7ba9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:36:34.714 | 4f83411cae5d48caba0c3f2c45bcb71e | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:36:34.740 | 4f83411cae5d48caba0c3f2c45bcb71e | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:36:35.094 | 4f83411cae5d48caba0c3f2c45bcb71e | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:36:35.095 | 4f83411cae5d48caba0c3f2c45bcb71e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:36:54.377 | eeda01a51d2c4d83a4f1898ea07b17c4 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:36:54.402 | eeda01a51d2c4d83a4f1898ea07b17c4 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:36:54.526 | eeda01a51d2c4d83a4f1898ea07b17c4 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:36:54.527 | eeda01a51d2c4d83a4f1898ea07b17c4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:37:33.409 | a51cb44fb3634dc2918fb9fe8a6dbf09 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:37:33.434 | a51cb44fb3634dc2918fb9fe8a6dbf09 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:37:33.557 | a51cb44fb3634dc2918fb9fe8a6dbf09 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:37:33.558 | a51cb44fb3634dc2918fb9fe8a6dbf09 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:37:44.828 | 16ec0421e5e54e799e2d824ac41b7827 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:37:44.856 | 16ec0421e5e54e799e2d824ac41b7827 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:37:45.215 | 16ec0421e5e54e799e2d824ac41b7827 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:37:45.215 | 16ec0421e5e54e799e2d824ac41b7827 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:37:45.859 | 0c9e3e740f654988a864c1d0b5d901cb | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:37:45.886 | 0c9e3e740f654988a864c1d0b5d901cb | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:37:46.009 | 0c9e3e740f654988a864c1d0b5d901cb | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:37:46.009 | 0c9e3e740f654988a864c1d0b5d901cb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:37:52.134 | fd73b44ef6e3443fa43ba56bbb8fe8cc | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 保存预警设置失败: "WarningSettingsConfigModel" object has no field "create_by"
2025-06-11 10:43:10.885 | 8b7a4fa4114047078619f463b4709ffb | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 保存预警设置失败: "WarningSettingsConfigModel" object has no field "create_by"
2025-06-11 10:43:11.201 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 10:43:11.202 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 10:43:15.971 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:43:15.972 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:43:16.762 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:43:16.762 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:43:16.765 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:43:17.294 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:43:17.847 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:43:17.847 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 10:43:17.977 | d169250f2bf645e9b13c5df69ac68188 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:43:18.158 | 7649662e5ed34f8abde21c445481f80e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:43:18.987 | 7725de64683e4c538c4a873da226397f | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:43:19.012 | 7725de64683e4c538c4a873da226397f | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:43:19.134 | 7725de64683e4c538c4a873da226397f | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:43:19.134 | 7725de64683e4c538c4a873da226397f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:43:19.345 | 4b0d763748db4291b08565b7a8a31a46 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:43:19.369 | 4b0d763748db4291b08565b7a8a31a46 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:43:19.486 | 4b0d763748db4291b08565b7a8a31a46 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:43:19.487 | 4b0d763748db4291b08565b7a8a31a46 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:43:23.700 | 14599b963b8d44a29dc77ac3c456d39a | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:63 - 保存预警设置失败: 
2025-06-11 10:46:24.064 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:46:24.064 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:46:24.861 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:46:24.862 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:46:24.863 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:46:25.269 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:46:25.790 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:46:25.791 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 10:48:13.123 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 10:48:13.125 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 10:48:16.230 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:48:16.230 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:48:17.148 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:48:17.148 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:48:17.150 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:48:17.614 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:48:18.099 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:48:18.100 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 10:58:07.596 | 6ed84a048b8a40999bdbba6d6707c308 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:63 - 保存预警设置失败: 
2025-06-11 10:58:10.069 | c4c4219e83904954b3830b3e61a25a8b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 10:58:10.277 | 469bb128e4dc43c1b36a5a594a7e8c5e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 10:58:11.046 | 99df6b0f53ea4f5fbc2d20da6d9abc98 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:58:11.075 | 99df6b0f53ea4f5fbc2d20da6d9abc98 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:58:11.215 | 99df6b0f53ea4f5fbc2d20da6d9abc98 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:58:11.215 | 99df6b0f53ea4f5fbc2d20da6d9abc98 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:58:11.457 | 70baf504b6f9467d8af9750067db566b | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 10:58:11.485 | 70baf504b6f9467d8af9750067db566b | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 10:58:11.620 | 70baf504b6f9467d8af9750067db566b | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 10:58:11.620 | 70baf504b6f9467d8af9750067db566b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 10:58:19.422 | 56677331966e400180ef91227f1cbbda | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:63 - 保存预警设置失败: 
2025-06-11 10:59:53.076 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 10:59:53.077 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 10:59:53.894 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 10:59:53.894 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 10:59:53.895 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 10:59:54.712 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 10:59:55.220 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 10:59:55.220 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:00:35.663 | da6507ed1f194047a246b7801efaf7de | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:00:35.818 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 11:00:35.818 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 11:00:38.364 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:00:38.365 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:00:39.146 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:00:39.147 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:00:39.148 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:00:39.542 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:00:40.050 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:00:40.050 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:00:40.170 | 0896edb54a384110bd7375cb8320e01d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:00:40.656 | b7e7f7487dbb4766a148748e3b52a155 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:00:40.680 | b7e7f7487dbb4766a148748e3b52a155 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:00:40.795 | b7e7f7487dbb4766a148748e3b52a155 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:00:40.795 | b7e7f7487dbb4766a148748e3b52a155 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:00:40.995 | 19515a948b26436fab9e4c0cb762b4cd | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:00:41.018 | 19515a948b26436fab9e4c0cb762b4cd | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:00:41.131 | 19515a948b26436fab9e4c0cb762b4cd | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:00:41.131 | 19515a948b26436fab9e4c0cb762b4cd | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:00:43.916 | f166f1c19b6f4e8d928719167a307a49 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:00:43.917 | f166f1c19b6f4e8d928719167a307a49 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:00:43.917 | f166f1c19b6f4e8d928719167a307a49 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:00:43.943 | f166f1c19b6f4e8d928719167a307a49 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:00:43.985 | f166f1c19b6f4e8d928719167a307a49 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:00:43.986 | f166f1c19b6f4e8d928719167a307a49 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:63 - 保存预警设置失败: 
2025-06-11 11:01:22.611 | d8e17a0d8d5246b78d64101661f4f12f | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:01:22.611 | d8e17a0d8d5246b78d64101661f4f12f | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:01:22.612 | d8e17a0d8d5246b78d64101661f4f12f | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:01:22.635 | d8e17a0d8d5246b78d64101661f4f12f | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:01:22.678 | d8e17a0d8d5246b78d64101661f4f12f | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:01:22.678 | d8e17a0d8d5246b78d64101661f4f12f | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:63 - 保存预警设置失败: 
2025-06-11 11:01:39.247 | 8a648ee99ea74b798d0aa47924b142a0 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:01:39.248 | 8a648ee99ea74b798d0aa47924b142a0 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:01:39.248 | 8a648ee99ea74b798d0aa47924b142a0 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:01:39.270 | 8a648ee99ea74b798d0aa47924b142a0 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:01:39.313 | 8a648ee99ea74b798d0aa47924b142a0 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:01:39.313 | 8a648ee99ea74b798d0aa47924b142a0 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:63 - 保存预警设置失败: 
2025-06-11 11:02:55.302 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:02:55.302 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:02:56.096 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:02:56.097 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:02:56.098 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:02:56.512 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:02:57.017 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:02:57.018 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:05:36.539 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 11:05:36.540 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 11:05:39.016 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:05:39.017 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:05:39.891 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:05:39.891 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:05:39.893 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:05:40.336 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:05:40.851 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:05:40.852 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:07:57.544 | a2de0718db064926879bb66345e150d1 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:07:57.732 | 3f23d2ba85654933be05887377e3fc8d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:07:58.448 | ff37e3ccb96d4aa582ecbc5a8cd0a990 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:07:58.475 | ff37e3ccb96d4aa582ecbc5a8cd0a990 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:07:58.604 | ff37e3ccb96d4aa582ecbc5a8cd0a990 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:07:58.604 | ff37e3ccb96d4aa582ecbc5a8cd0a990 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:07:58.829 | 95bb9b432b5043ad83703f484c81a073 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:07:58.854 | 95bb9b432b5043ad83703f484c81a073 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:07:58.980 | 95bb9b432b5043ad83703f484c81a073 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:07:58.980 | 95bb9b432b5043ad83703f484c81a073 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:08:02.706 | bfb8c65b341d4e8aae6f133ba4ba8d25 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:55 - 控制器层：收到保存预警设置请求，方案ID: None
2025-06-11 11:08:02.706 | bfb8c65b341d4e8aae6f133ba4ba8d25 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:56 - 控制器层：用户名: admin
2025-06-11 11:08:02.706 | bfb8c65b341d4e8aae6f133ba4ba8d25 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:57 - 控制器层：设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:08:02.706 | bfb8c65b341d4e8aae6f133ba4ba8d25 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:60 - 控制器层：开始调用服务层
2025-06-11 11:08:02.707 | bfb8c65b341d4e8aae6f133ba4ba8d25 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:08:02.707 | bfb8c65b341d4e8aae6f133ba4ba8d25 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:08:02.707 | bfb8c65b341d4e8aae6f133ba4ba8d25 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:08:02.732 | bfb8c65b341d4e8aae6f133ba4ba8d25 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:08:02.780 | bfb8c65b341d4e8aae6f133ba4ba8d25 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:08:02.780 | bfb8c65b341d4e8aae6f133ba4ba8d25 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:69 - 控制器层：捕获异常，类型: ServiceException
2025-06-11 11:08:02.781 | bfb8c65b341d4e8aae6f133ba4ba8d25 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:70 - 控制器层：异常详情: 
2025-06-11 11:08:02.782 | bfb8c65b341d4e8aae6f133ba4ba8d25 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:72 - 控制器层：异常堆栈: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_warning\controller\warning_settings_controller.py", line 62, in save_warning_settings
    save_result = await WarningSettingsService.save_warning_settings_services(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        query_db, settings_config, current_user.user.user_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\thinktank\thinktankapi\module_warning\service\warning_settings_service.py", line 76, in save_warning_settings_services
    raise ServiceException(message='预警方案不存在')
exceptions.exception.ServiceException

2025-06-11 11:09:25.342 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 11:09:25.343 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 11:09:25.412 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:09:25.413 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:09:26.284 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:09:26.284 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:09:26.286 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:09:26.739 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:09:27.323 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:09:27.323 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:09:28.088 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:09:28.089 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:09:29.155 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:09:29.155 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:09:29.157 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:09:29.622 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:09:30.124 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:09:30.125 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:10:06.536 | ac18eca0edf243a8a6aa1ae36299c42c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:10:06.711 | df67f694074b42f4be83422d5d91b9a0 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:10:07.141 | df8746f638e34f01b1dfe4f3627eb595 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:10:07.168 | df8746f638e34f01b1dfe4f3627eb595 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:10:07.290 | df8746f638e34f01b1dfe4f3627eb595 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:10:07.290 | df8746f638e34f01b1dfe4f3627eb595 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:10:07.500 | 46923a356c5b40db82cec3004936e73b | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:10:07.524 | 46923a356c5b40db82cec3004936e73b | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:10:07.642 | 46923a356c5b40db82cec3004936e73b | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:10:07.642 | 46923a356c5b40db82cec3004936e73b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:10:11.848 | 318f10808a59493e86a36222bc6b3c70 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:60 - 控制器层：收到保存预警设置请求，方案ID: None
2025-06-11 11:10:11.848 | 318f10808a59493e86a36222bc6b3c70 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:61 - 控制器层：用户名: admin
2025-06-11 11:10:11.849 | 318f10808a59493e86a36222bc6b3c70 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 控制器层：设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:10:11.849 | 318f10808a59493e86a36222bc6b3c70 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:65 - 控制器层：开始调用服务层
2025-06-11 11:10:11.849 | 318f10808a59493e86a36222bc6b3c70 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:10:11.850 | 318f10808a59493e86a36222bc6b3c70 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:10:11.850 | 318f10808a59493e86a36222bc6b3c70 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:10:11.875 | 318f10808a59493e86a36222bc6b3c70 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:10:11.920 | 318f10808a59493e86a36222bc6b3c70 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:10:11.920 | 318f10808a59493e86a36222bc6b3c70 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:74 - 控制器层：捕获异常，类型: ServiceException
2025-06-11 11:10:11.921 | 318f10808a59493e86a36222bc6b3c70 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:75 - 控制器层：异常详情: 
2025-06-11 11:10:11.923 | 318f10808a59493e86a36222bc6b3c70 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:77 - 控制器层：异常堆栈: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_warning\controller\warning_settings_controller.py", line 67, in save_warning_settings
    save_result = await WarningSettingsService.save_warning_settings_services(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        query_db, settings_config, current_user.user.user_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\thinktank\thinktankapi\module_warning\service\warning_settings_service.py", line 76, in save_warning_settings_services
    raise ServiceException(message='预警方案不存在')
exceptions.exception.ServiceException

2025-06-11 11:12:13.877 | ca98e6b3a59b4fe7bb5af6d61ad51ec9 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:12:14.058 | 6391504e75294662906e593b5dc5dd29 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:12:40.151 | 1c7be7d6bc05487ea4440a5a26e646b2 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:12:40.175 | 1c7be7d6bc05487ea4440a5a26e646b2 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:12:40.295 | 1c7be7d6bc05487ea4440a5a26e646b2 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:12:40.296 | 1c7be7d6bc05487ea4440a5a26e646b2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:12:40.827 | 40732ddc0566443281cd12659b03ad9c | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:12:40.852 | 40732ddc0566443281cd12659b03ad9c | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:12:40.971 | 40732ddc0566443281cd12659b03ad9c | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:12:40.972 | 40732ddc0566443281cd12659b03ad9c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:12:42.892 | e0dda23ca8a84593b9ea3d16be47f5f6 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:60 - 控制器层：收到保存预警设置请求，方案ID: None
2025-06-11 11:12:42.892 | e0dda23ca8a84593b9ea3d16be47f5f6 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:61 - 控制器层：用户名: admin
2025-06-11 11:12:42.893 | e0dda23ca8a84593b9ea3d16be47f5f6 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 控制器层：设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:12:42.893 | e0dda23ca8a84593b9ea3d16be47f5f6 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:65 - 控制器层：开始调用服务层
2025-06-11 11:12:42.893 | e0dda23ca8a84593b9ea3d16be47f5f6 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:12:42.894 | e0dda23ca8a84593b9ea3d16be47f5f6 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:12:42.894 | e0dda23ca8a84593b9ea3d16be47f5f6 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:12:42.918 | e0dda23ca8a84593b9ea3d16be47f5f6 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:12:42.963 | e0dda23ca8a84593b9ea3d16be47f5f6 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:12:42.963 | e0dda23ca8a84593b9ea3d16be47f5f6 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:74 - 控制器层：捕获异常，类型: ServiceException
2025-06-11 11:12:42.963 | e0dda23ca8a84593b9ea3d16be47f5f6 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:75 - 控制器层：异常详情: 
2025-06-11 11:12:42.964 | e0dda23ca8a84593b9ea3d16be47f5f6 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:77 - 控制器层：异常堆栈: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_warning\controller\warning_settings_controller.py", line 67, in save_warning_settings
    save_result = await WarningSettingsService.save_warning_settings_services(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        query_db, settings_config, current_user.user.user_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\thinktank\thinktankapi\module_warning\service\warning_settings_service.py", line 76, in save_warning_settings_services
    raise ServiceException(message='预警方案不存在')
exceptions.exception.ServiceException

2025-06-11 11:14:31.499 | e7113c0566984a6ea221645e9f625b49 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:14:31.674 | ae3f9dd7df0944f1b8fbc8a4cc808cdc | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:14:32.092 | b613ebc1471242439c26ca4364f04598 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:14:32.116 | b613ebc1471242439c26ca4364f04598 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:14:32.233 | b613ebc1471242439c26ca4364f04598 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:14:32.233 | b613ebc1471242439c26ca4364f04598 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:14:32.442 | 53fcb2fff9914695a00f3edff493480f | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:14:32.466 | 53fcb2fff9914695a00f3edff493480f | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:14:32.582 | 53fcb2fff9914695a00f3edff493480f | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:14:32.582 | 53fcb2fff9914695a00f3edff493480f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:14:35.751 | 83ddce698a464ca6a4a1a14f3ddbc3f0 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:60 - 控制器层：收到保存预警设置请求，方案ID: None
2025-06-11 11:14:35.752 | 83ddce698a464ca6a4a1a14f3ddbc3f0 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:61 - 控制器层：用户名: admin
2025-06-11 11:14:35.752 | 83ddce698a464ca6a4a1a14f3ddbc3f0 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 控制器层：设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:14:35.752 | 83ddce698a464ca6a4a1a14f3ddbc3f0 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:65 - 控制器层：开始调用服务层
2025-06-11 11:14:35.753 | 83ddce698a464ca6a4a1a14f3ddbc3f0 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:14:35.753 | 83ddce698a464ca6a4a1a14f3ddbc3f0 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:14:35.753 | 83ddce698a464ca6a4a1a14f3ddbc3f0 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:14:35.776 | 83ddce698a464ca6a4a1a14f3ddbc3f0 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:14:35.821 | 83ddce698a464ca6a4a1a14f3ddbc3f0 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:14:35.821 | 83ddce698a464ca6a4a1a14f3ddbc3f0 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:74 - 控制器层：捕获异常，类型: ServiceException
2025-06-11 11:14:35.822 | 83ddce698a464ca6a4a1a14f3ddbc3f0 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:75 - 控制器层：异常详情: 
2025-06-11 11:14:35.823 | 83ddce698a464ca6a4a1a14f3ddbc3f0 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:77 - 控制器层：异常堆栈: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_warning\controller\warning_settings_controller.py", line 67, in save_warning_settings
    save_result = await WarningSettingsService.save_warning_settings_services(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        query_db, settings_config, current_user.user.user_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\thinktank\thinktankapi\module_warning\service\warning_settings_service.py", line 76, in save_warning_settings_services
    raise ServiceException(message='预警方案不存在')
exceptions.exception.ServiceException

2025-06-11 11:14:42.524 | ff4b47845d7244a782534a54d8690c79 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:60 - 控制器层：收到保存预警设置请求，方案ID: None
2025-06-11 11:14:42.525 | ff4b47845d7244a782534a54d8690c79 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:61 - 控制器层：用户名: admin
2025-06-11 11:14:42.525 | ff4b47845d7244a782534a54d8690c79 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 控制器层：设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:14:42.526 | ff4b47845d7244a782534a54d8690c79 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:65 - 控制器层：开始调用服务层
2025-06-11 11:14:42.526 | ff4b47845d7244a782534a54d8690c79 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:14:42.526 | ff4b47845d7244a782534a54d8690c79 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:14:42.527 | ff4b47845d7244a782534a54d8690c79 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:14:42.551 | ff4b47845d7244a782534a54d8690c79 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:14:42.596 | ff4b47845d7244a782534a54d8690c79 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:14:42.596 | ff4b47845d7244a782534a54d8690c79 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:74 - 控制器层：捕获异常，类型: ServiceException
2025-06-11 11:14:42.597 | ff4b47845d7244a782534a54d8690c79 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:75 - 控制器层：异常详情: 
2025-06-11 11:14:42.599 | ff4b47845d7244a782534a54d8690c79 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:77 - 控制器层：异常堆栈: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_warning\controller\warning_settings_controller.py", line 67, in save_warning_settings
    save_result = await WarningSettingsService.save_warning_settings_services(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        query_db, settings_config, current_user.user.user_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\thinktank\thinktankapi\module_warning\service\warning_settings_service.py", line 76, in save_warning_settings_services
    raise ServiceException(message='预警方案不存在')
exceptions.exception.ServiceException

2025-06-11 11:14:47.383 | c85d1aeb302245c08db2d5e37eec9af2 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:60 - 控制器层：收到保存预警设置请求，方案ID: None
2025-06-11 11:14:47.384 | c85d1aeb302245c08db2d5e37eec9af2 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:61 - 控制器层：用户名: admin
2025-06-11 11:14:47.384 | c85d1aeb302245c08db2d5e37eec9af2 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 控制器层：设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:14:47.384 | c85d1aeb302245c08db2d5e37eec9af2 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:65 - 控制器层：开始调用服务层
2025-06-11 11:14:47.385 | c85d1aeb302245c08db2d5e37eec9af2 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:14:47.385 | c85d1aeb302245c08db2d5e37eec9af2 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:14:47.385 | c85d1aeb302245c08db2d5e37eec9af2 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:14:47.410 | c85d1aeb302245c08db2d5e37eec9af2 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:14:47.455 | c85d1aeb302245c08db2d5e37eec9af2 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:14:47.456 | c85d1aeb302245c08db2d5e37eec9af2 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:74 - 控制器层：捕获异常，类型: ServiceException
2025-06-11 11:14:47.456 | c85d1aeb302245c08db2d5e37eec9af2 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:75 - 控制器层：异常详情: 
2025-06-11 11:14:47.457 | c85d1aeb302245c08db2d5e37eec9af2 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:77 - 控制器层：异常堆栈: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_warning\controller\warning_settings_controller.py", line 67, in save_warning_settings
    save_result = await WarningSettingsService.save_warning_settings_services(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        query_db, settings_config, current_user.user.user_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\thinktank\thinktankapi\module_warning\service\warning_settings_service.py", line 76, in save_warning_settings_services
    raise ServiceException(message='预警方案不存在')
exceptions.exception.ServiceException

2025-06-11 11:14:48.908 | b52101b0b707455cba996e4664b964c1 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:60 - 控制器层：收到保存预警设置请求，方案ID: None
2025-06-11 11:14:48.908 | b52101b0b707455cba996e4664b964c1 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:61 - 控制器层：用户名: admin
2025-06-11 11:14:48.908 | b52101b0b707455cba996e4664b964c1 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 控制器层：设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:14:48.909 | b52101b0b707455cba996e4664b964c1 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:65 - 控制器层：开始调用服务层
2025-06-11 11:14:48.909 | b52101b0b707455cba996e4664b964c1 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:14:48.910 | b52101b0b707455cba996e4664b964c1 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:14:48.910 | b52101b0b707455cba996e4664b964c1 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:14:48.934 | b52101b0b707455cba996e4664b964c1 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:14:48.980 | b52101b0b707455cba996e4664b964c1 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:14:48.981 | b52101b0b707455cba996e4664b964c1 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:74 - 控制器层：捕获异常，类型: ServiceException
2025-06-11 11:14:48.981 | b52101b0b707455cba996e4664b964c1 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:75 - 控制器层：异常详情: 
2025-06-11 11:14:48.983 | b52101b0b707455cba996e4664b964c1 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:77 - 控制器层：异常堆栈: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_warning\controller\warning_settings_controller.py", line 67, in save_warning_settings
    save_result = await WarningSettingsService.save_warning_settings_services(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        query_db, settings_config, current_user.user.user_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\thinktank\thinktankapi\module_warning\service\warning_settings_service.py", line 76, in save_warning_settings_services
    raise ServiceException(message='预警方案不存在')
exceptions.exception.ServiceException

2025-06-11 11:14:52.938 | 1dd9f69d303d4d01a4d558afecec3b87 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:60 - 控制器层：收到保存预警设置请求，方案ID: None
2025-06-11 11:14:52.938 | 1dd9f69d303d4d01a4d558afecec3b87 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:61 - 控制器层：用户名: admin
2025-06-11 11:14:52.938 | 1dd9f69d303d4d01a4d558afecec3b87 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 控制器层：设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:14:52.938 | 1dd9f69d303d4d01a4d558afecec3b87 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:65 - 控制器层：开始调用服务层
2025-06-11 11:14:52.938 | 1dd9f69d303d4d01a4d558afecec3b87 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:14:52.939 | 1dd9f69d303d4d01a4d558afecec3b87 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:14:52.939 | 1dd9f69d303d4d01a4d558afecec3b87 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:14:52.963 | 1dd9f69d303d4d01a4d558afecec3b87 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:14:53.009 | 1dd9f69d303d4d01a4d558afecec3b87 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:14:53.009 | 1dd9f69d303d4d01a4d558afecec3b87 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:74 - 控制器层：捕获异常，类型: ServiceException
2025-06-11 11:14:53.010 | 1dd9f69d303d4d01a4d558afecec3b87 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:75 - 控制器层：异常详情: 
2025-06-11 11:14:53.011 | 1dd9f69d303d4d01a4d558afecec3b87 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:77 - 控制器层：异常堆栈: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_warning\controller\warning_settings_controller.py", line 67, in save_warning_settings
    save_result = await WarningSettingsService.save_warning_settings_services(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        query_db, settings_config, current_user.user.user_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\thinktank\thinktankapi\module_warning\service\warning_settings_service.py", line 76, in save_warning_settings_services
    raise ServiceException(message='预警方案不存在')
exceptions.exception.ServiceException

2025-06-11 11:15:06.140 | 59bd5a7f2419436e94fca60ca3f830c1 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:15:06.315 | 5cf110fe3f954c789c4ea911f98c59a0 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:15:06.724 | 1941895dfa3346d887ffd397dd1ff961 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:15:06.748 | 1941895dfa3346d887ffd397dd1ff961 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:15:06.866 | 1941895dfa3346d887ffd397dd1ff961 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:15:06.866 | 1941895dfa3346d887ffd397dd1ff961 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:15:07.381 | e47dda12f40b4ea1a411e07b99179f9f | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:15:07.406 | e47dda12f40b4ea1a411e07b99179f9f | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:15:07.523 | e47dda12f40b4ea1a411e07b99179f9f | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:15:07.523 | e47dda12f40b4ea1a411e07b99179f9f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:15:14.625 | f25c9c6937964cf38b721b99bb934a02 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:60 - 控制器层：收到保存预警设置请求，方案ID: None
2025-06-11 11:15:14.626 | f25c9c6937964cf38b721b99bb934a02 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:61 - 控制器层：用户名: admin
2025-06-11 11:15:14.626 | f25c9c6937964cf38b721b99bb934a02 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:62 - 控制器层：设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:15:14.626 | f25c9c6937964cf38b721b99bb934a02 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:65 - 控制器层：开始调用服务层
2025-06-11 11:15:14.626 | f25c9c6937964cf38b721b99bb934a02 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:15:14.626 | f25c9c6937964cf38b721b99bb934a02 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:15:14.626 | f25c9c6937964cf38b721b99bb934a02 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:15:14.649 | f25c9c6937964cf38b721b99bb934a02 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:15:14.694 | f25c9c6937964cf38b721b99bb934a02 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:15:14.694 | f25c9c6937964cf38b721b99bb934a02 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:74 - 控制器层：捕获异常，类型: ServiceException
2025-06-11 11:15:14.694 | f25c9c6937964cf38b721b99bb934a02 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:75 - 控制器层：异常详情: 
2025-06-11 11:15:14.695 | f25c9c6937964cf38b721b99bb934a02 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:77 - 控制器层：异常堆栈: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_warning\controller\warning_settings_controller.py", line 67, in save_warning_settings
    save_result = await WarningSettingsService.save_warning_settings_services(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        query_db, settings_config, current_user.user.user_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\thinktank\thinktankapi\module_warning\service\warning_settings_service.py", line 76, in save_warning_settings_services
    raise ServiceException(message='预警方案不存在')
exceptions.exception.ServiceException

2025-06-11 11:16:45.134 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:16:45.135 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:16:45.966 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:16:45.967 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:16:45.968 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:16:46.395 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:16:46.903 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:16:46.903 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:19:26.055 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 11:19:26.056 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 11:19:28.823 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:19:28.824 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:19:29.577 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:19:29.577 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:19:29.579 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:19:29.979 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:19:30.526 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:19:30.527 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:20:17.322 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:20:17.323 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:20:18.129 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:20:18.130 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:20:18.131 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:20:18.548 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:20:19.059 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:20:19.059 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:23:30.658 | 687803a5f8474bf79821ccf0871d699c | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:78 - 控制器层：收到保存预警设置请求，方案ID: None
2025-06-11 11:23:30.659 | 687803a5f8474bf79821ccf0871d699c | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:79 - 控制器层：用户名: admin
2025-06-11 11:23:30.660 | 687803a5f8474bf79821ccf0871d699c | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:80 - 控制器层：设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:23:30.660 | 687803a5f8474bf79821ccf0871d699c | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:83 - 控制器层：开始调用服务层
2025-06-11 11:23:30.661 | 687803a5f8474bf79821ccf0871d699c | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:23:30.661 | 687803a5f8474bf79821ccf0871d699c | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:23:30.662 | 687803a5f8474bf79821ccf0871d699c | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:23:30.686 | 687803a5f8474bf79821ccf0871d699c | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:23:30.728 | 687803a5f8474bf79821ccf0871d699c | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:23:30.728 | 687803a5f8474bf79821ccf0871d699c | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:92 - 控制器层：捕获异常，类型: ServiceException
2025-06-11 11:23:30.729 | 687803a5f8474bf79821ccf0871d699c | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:93 - 控制器层：异常详情: 
2025-06-11 11:23:30.732 | 687803a5f8474bf79821ccf0871d699c | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:95 - 控制器层：异常堆栈: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_warning\controller\warning_settings_controller.py", line 85, in save_warning_settings
    save_result = await WarningSettingsService.save_warning_settings_services(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        query_db, settings_config, current_user.user.user_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\thinktank\thinktankapi\module_warning\service\warning_settings_service.py", line 76, in save_warning_settings_services
    raise ServiceException(message='预警方案不存在')
exceptions.exception.ServiceException

2025-06-11 11:23:30.800 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 11:23:30.800 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 11:23:33.750 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:23:33.750 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:23:34.564 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:23:34.564 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:23:34.566 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:23:34.991 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:23:35.544 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:23:35.545 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:23:35.676 | 03318d7482ae4f76906d8f22d562f3f9 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:23:35.857 | 2e29ff81db864bed8013309b3514ed52 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:23:36.603 | cdf45f2415bd44b489f27dec88dd7b1c | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:23:36.627 | cdf45f2415bd44b489f27dec88dd7b1c | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:23:36.748 | cdf45f2415bd44b489f27dec88dd7b1c | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:23:36.748 | cdf45f2415bd44b489f27dec88dd7b1c | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:23:36.960 | 7e834aeebe5f4f77a5e85871372358d9 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:23:36.984 | 7e834aeebe5f4f77a5e85871372358d9 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:23:37.102 | 7e834aeebe5f4f77a5e85871372358d9 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:23:37.102 | 7e834aeebe5f4f77a5e85871372358d9 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:23:39.084 | d75a063c6be14bd9a7c92898d5791f69 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:78 - 控制器层：收到保存预警设置请求，方案ID: None
2025-06-11 11:23:39.084 | d75a063c6be14bd9a7c92898d5791f69 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:79 - 控制器层：用户名: admin
2025-06-11 11:23:39.084 | d75a063c6be14bd9a7c92898d5791f69 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:80 - 控制器层：设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:23:39.085 | d75a063c6be14bd9a7c92898d5791f69 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:83 - 控制器层：开始调用服务层
2025-06-11 11:23:39.085 | d75a063c6be14bd9a7c92898d5791f69 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:23:39.086 | d75a063c6be14bd9a7c92898d5791f69 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:23:39.086 | d75a063c6be14bd9a7c92898d5791f69 | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:23:39.112 | d75a063c6be14bd9a7c92898d5791f69 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:23:39.156 | d75a063c6be14bd9a7c92898d5791f69 | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:23:39.157 | d75a063c6be14bd9a7c92898d5791f69 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:92 - 控制器层：捕获异常，类型: ServiceException
2025-06-11 11:23:39.157 | d75a063c6be14bd9a7c92898d5791f69 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:93 - 控制器层：异常详情: 
2025-06-11 11:23:39.159 | d75a063c6be14bd9a7c92898d5791f69 | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:95 - 控制器层：异常堆栈: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_warning\controller\warning_settings_controller.py", line 85, in save_warning_settings
    save_result = await WarningSettingsService.save_warning_settings_services(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        query_db, settings_config, current_user.user.user_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\thinktank\thinktankapi\module_warning\service\warning_settings_service.py", line 76, in save_warning_settings_services
    raise ServiceException(message='预警方案不存在')
exceptions.exception.ServiceException

2025-06-11 11:23:44.520 | fe6fa44f3ac24213aaee1221f767b5df | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:78 - 控制器层：收到保存预警设置请求，方案ID: None
2025-06-11 11:23:44.520 | fe6fa44f3ac24213aaee1221f767b5df | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:79 - 控制器层：用户名: admin
2025-06-11 11:23:44.520 | fe6fa44f3ac24213aaee1221f767b5df | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:80 - 控制器层：设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:23:44.521 | fe6fa44f3ac24213aaee1221f767b5df | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:83 - 控制器层：开始调用服务层
2025-06-11 11:23:44.521 | fe6fa44f3ac24213aaee1221f767b5df | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:68 - 服务层开始保存预警设置，方案ID: None, 用户: admin
2025-06-11 11:23:44.521 | fe6fa44f3ac24213aaee1221f767b5df | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:69 - 接收到的设置数据: {'scheme_id': None, 'platform_types': ['all'], 'content_property': 'all', 'info_type': 'noncomment', 'match_objects': None, 'match_method': 'exact', 'publish_regions': None, 'ip_areas': None, 'media_categories': None, 'article_categories': None, 'allow_words': '', 'reject_words': '', 'auto_warning_settings': None}
2025-06-11 11:23:44.522 | fe6fa44f3ac24213aaee1221f767b5df | INFO     | module_warning.service.warning_settings_service:save_warning_settings_services:72 - 检查方案是否存在，方案ID: None
2025-06-11 11:23:44.546 | fe6fa44f3ac24213aaee1221f767b5df | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:75 - 预警方案不存在，方案ID: None
2025-06-11 11:23:44.591 | fe6fa44f3ac24213aaee1221f767b5df | ERROR    | module_warning.service.warning_settings_service:save_warning_settings_services:96 - 服务异常: 
2025-06-11 11:23:44.591 | fe6fa44f3ac24213aaee1221f767b5df | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:92 - 控制器层：捕获异常，类型: ServiceException
2025-06-11 11:23:44.591 | fe6fa44f3ac24213aaee1221f767b5df | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:93 - 控制器层：异常详情: 
2025-06-11 11:23:44.593 | fe6fa44f3ac24213aaee1221f767b5df | ERROR    | module_warning.controller.warning_settings_controller:save_warning_settings:95 - 控制器层：异常堆栈: Traceback (most recent call last):
  File "D:\thinktank\thinktankapi\module_warning\controller\warning_settings_controller.py", line 85, in save_warning_settings
    save_result = await WarningSettingsService.save_warning_settings_services(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        query_db, settings_config, current_user.user.user_name
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\thinktank\thinktankapi\module_warning\service\warning_settings_service.py", line 76, in save_warning_settings_services
    raise ServiceException(message='预警方案不存在')
exceptions.exception.ServiceException

2025-06-11 11:24:07.209 | bae907efaeae4f489b3e4fe55ba5d17a | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:100 - 开始编辑预警记录，输入数据: {'id': 29, 'scheme_id': None, 'warning_type': None, 'content': '厨电行业市场分析报告发布，整体趋势平稳', 'keywords': None, 'status': 0, 'create_time': None, 'update_time': datetime.datetime(2025, 6, 11, 11, 24, 7, 209509), 'create_by': '', 'update_by': 'admin', 'remark': '行业动态监控'}
2025-06-11 11:24:07.234 | bae907efaeae4f489b3e4fe55ba5d17a | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:111 - 找到现有记录: ID=29, scheme_id=12
2025-06-11 11:24:07.234 | bae907efaeae4f489b3e4fe55ba5d17a | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:118 - 准备更新的数据: {'id': 29, 'content': '厨电行业市场分析报告发布，整体趋势平稳', 'status': 0, 'update_time': datetime.datetime(2025, 6, 11, 11, 24, 7, 234364), 'update_by': 'admin', 'remark': '行业动态监控'}
2025-06-11 11:24:07.234 | bae907efaeae4f489b3e4fe55ba5d17a | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:111 - DAO层更新数据: record_id=29, update_data={'content': '厨电行业市场分析报告发布，整体趋势平稳', 'status': 0, 'update_time': datetime.datetime(2025, 6, 11, 11, 24, 7, 234364), 'update_by': 'admin', 'remark': '行业动态监控'}
2025-06-11 11:24:07.259 | bae907efaeae4f489b3e4fe55ba5d17a | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:119 - 数据库更新结果: 影响行数=1
2025-06-11 11:24:07.309 | bae907efaeae4f489b3e4fe55ba5d17a | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:122 - 编辑预警记录成功，记录ID: 29
2025-06-11 11:24:07.310 | bae907efaeae4f489b3e4fe55ba5d17a | INFO     | module_warning.controller.warning_record_controller:edit_warning_record:134 - 更新成功
2025-06-11 11:24:08.399 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 11:24:08.399 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 11:24:11.488 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:24:11.489 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:24:12.470 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:24:12.471 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:24:12.472 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:24:12.972 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:24:13.534 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:24:13.535 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:24:13.720 | f53f1e2f36d748068f359f9713e481a4 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:24:13.720 | f53f1e2f36d748068f359f9713e481a4 | ERROR    | module_warning.controller.warning_record_controller:export_warning_record:69 - 导出预警记录失败: 'WarningRecord' object has no attribute 'get'
2025-06-11 11:24:13.774 | 30c963abe7a54ad0987e3a842aa03be3 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:100 - 开始编辑预警记录，输入数据: {'id': 29, 'scheme_id': None, 'warning_type': None, 'content': '厨电行业市场分析报告发布，整体趋势平稳', 'keywords': None, 'status': 0, 'create_time': None, 'update_time': datetime.datetime(2025, 6, 11, 11, 24, 13, 774802), 'create_by': '', 'update_by': 'admin', 'remark': '行业动态监控'}
2025-06-11 11:24:13.798 | 30c963abe7a54ad0987e3a842aa03be3 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:111 - 找到现有记录: ID=29, scheme_id=12
2025-06-11 11:24:13.799 | 30c963abe7a54ad0987e3a842aa03be3 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:118 - 准备更新的数据: {'id': 29, 'content': '厨电行业市场分析报告发布，整体趋势平稳', 'status': 0, 'update_time': datetime.datetime(2025, 6, 11, 11, 24, 13, 799042), 'update_by': 'admin', 'remark': '行业动态监控'}
2025-06-11 11:24:13.799 | 30c963abe7a54ad0987e3a842aa03be3 | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:111 - DAO层更新数据: record_id=29, update_data={'content': '厨电行业市场分析报告发布，整体趋势平稳', 'status': 0, 'update_time': datetime.datetime(2025, 6, 11, 11, 24, 13, 799042), 'update_by': 'admin', 'remark': '行业动态监控'}
2025-06-11 11:24:13.823 | 30c963abe7a54ad0987e3a842aa03be3 | INFO     | module_warning.dao.warning_record_dao:edit_warning_record_dao:119 - 数据库更新结果: 影响行数=1
2025-06-11 11:24:13.828 | a8e4440f7a6144b7b88eff0ac83aecb2 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:24:13.854 | a8e4440f7a6144b7b88eff0ac83aecb2 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:24:13.869 | 30c963abe7a54ad0987e3a842aa03be3 | INFO     | module_warning.service.warning_record_service:edit_warning_record_services:122 - 编辑预警记录成功，记录ID: 29
2025-06-11 11:24:13.869 | 30c963abe7a54ad0987e3a842aa03be3 | INFO     | module_warning.controller.warning_record_controller:edit_warning_record:134 - 更新成功
2025-06-11 11:24:13.987 | a8e4440f7a6144b7b88eff0ac83aecb2 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:24:13.987 | a8e4440f7a6144b7b88eff0ac83aecb2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:24:14.570 | c01099ec0ea84fa5bf11dd8880d71c30 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:24:14.598 | c01099ec0ea84fa5bf11dd8880d71c30 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:24:14.740 | c01099ec0ea84fa5bf11dd8880d71c30 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:24:14.740 | c01099ec0ea84fa5bf11dd8880d71c30 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:25:14.527 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 11:25:14.527 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 11:25:17.243 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:25:17.243 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:25:18.031 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:25:18.031 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:25:18.032 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:25:18.434 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:25:18.970 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:25:18.971 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:27:56.629 | d1899faf22e0488091eb7bf8aa7c703f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:27:56.820 | 8bf1a3cd19114e7b8f138a865d8928de | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:27:57.533 | c5cf588032754bc48c9e24d25f3f1e0e | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:27:57.562 | c5cf588032754bc48c9e24d25f3f1e0e | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:27:57.707 | c5cf588032754bc48c9e24d25f3f1e0e | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:27:57.707 | c5cf588032754bc48c9e24d25f3f1e0e | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:27:57.990 | 80b647e684dc41489ea6bd7f70f2eb76 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:27:58.018 | 80b647e684dc41489ea6bd7f70f2eb76 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:27:58.128 | b354112f2dbb4915ab3ae1ec308bd1d5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:27:58.149 | 80b647e684dc41489ea6bd7f70f2eb76 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:27:58.149 | 80b647e684dc41489ea6bd7f70f2eb76 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:27:58.343 | b7e189006f6749d4b8bcd0ae0f044908 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:27:59.241 | 268e074f797f4b58bcd21d6c54765bd0 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:27:59.265 | 268e074f797f4b58bcd21d6c54765bd0 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:27:59.384 | 268e074f797f4b58bcd21d6c54765bd0 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:27:59.385 | 268e074f797f4b58bcd21d6c54765bd0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:27:59.944 | 1c4a5b32803c46b8a5a7977684f1737d | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:27:59.971 | 1c4a5b32803c46b8a5a7977684f1737d | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:28:00.101 | 1c4a5b32803c46b8a5a7977684f1737d | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:28:00.101 | 1c4a5b32803c46b8a5a7977684f1737d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:28:10.769 | 59f2adeae099466299e3faa9328c1a6b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:28:10.969 | 09fb6673f5e5426e8c93ef03865709e2 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:28:11.617 | baaa24b5d0034dfb9bf78efc6f6b2093 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:28:11.644 | baaa24b5d0034dfb9bf78efc6f6b2093 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:28:11.776 | baaa24b5d0034dfb9bf78efc6f6b2093 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:28:11.777 | baaa24b5d0034dfb9bf78efc6f6b2093 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:28:12.360 | 3d05aa6b697d4b0991e4df64d9829fab | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:28:12.390 | 3d05aa6b697d4b0991e4df64d9829fab | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:28:12.422 | 116be59f5b4042329690c2ae4bfc3684 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:28:12.534 | 3d05aa6b697d4b0991e4df64d9829fab | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:28:12.534 | 3d05aa6b697d4b0991e4df64d9829fab | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:28:12.675 | 6d7129c0f10c48a38d94fbd88fd7b635 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:28:13.446 | ebae6dd622884723bbe5cc450589447f | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:28:13.470 | ebae6dd622884723bbe5cc450589447f | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:28:13.590 | ebae6dd622884723bbe5cc450589447f | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:28:13.591 | ebae6dd622884723bbe5cc450589447f | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:28:13.858 | 44fd4b10239a4db7927896dd18d8fef1 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:28:13.887 | 44fd4b10239a4db7927896dd18d8fef1 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:28:14.030 | 44fd4b10239a4db7927896dd18d8fef1 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:28:14.031 | 44fd4b10239a4db7927896dd18d8fef1 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:29:09.405 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 11:29:09.406 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 11:29:13.735 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:29:13.736 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:29:14.614 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:29:14.614 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:29:14.616 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:29:15.067 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:29:15.661 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:29:15.661 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:30:14.590 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 11:30:14.592 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 11:30:16.951 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:30:16.952 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:30:19.519 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:30:19.519 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:30:20.446 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:30:20.447 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:30:20.452 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:30:20.931 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:30:21.473 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:30:21.473 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:31:15.288 | 0a74d15f1fe74151b49717186da824bd | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:31:15.441 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 11:31:15.442 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 11:31:18.761 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:31:18.761 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:31:19.567 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:31:19.567 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:31:19.569 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:31:19.987 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:31:20.523 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:31:20.523 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-11 11:31:20.649 | 6663372b6b4c427190d0261aa66ea2db | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:31:20.766 | 469e218a4df9418d866836a92e8efa69 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:31:20.944 | aee711bbc324408a8d1dce0320d759df | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:31:21.539 | 8e9e58c33e05436bbfe89f9218d59aa2 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:31:21.563 | 8e9e58c33e05436bbfe89f9218d59aa2 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:31:21.682 | 8e9e58c33e05436bbfe89f9218d59aa2 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:31:21.682 | 8e9e58c33e05436bbfe89f9218d59aa2 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:31:22.063 | 7c6b35faae6e4b1fa52c1a057a874050 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:31:22.087 | 7c6b35faae6e4b1fa52c1a057a874050 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:31:22.203 | 7c6b35faae6e4b1fa52c1a057a874050 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:31:22.204 | 7c6b35faae6e4b1fa52c1a057a874050 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:31:22.233 | 1566190ea3d149a2b4e5371e88a9659d | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:31:22.256 | 1566190ea3d149a2b4e5371e88a9659d | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:31:22.372 | 1566190ea3d149a2b4e5371e88a9659d | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:31:22.372 | 1566190ea3d149a2b4e5371e88a9659d | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:31:22.740 | 8f9c6385cb6a47548ed8f1d39205345a | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:31:22.764 | 8f9c6385cb6a47548ed8f1d39205345a | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:31:22.879 | 8f9c6385cb6a47548ed8f1d39205345a | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:31:22.880 | 8f9c6385cb6a47548ed8f1d39205345a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:31:33.613 | c1b41457aa1c4fb7be184ea95f224bbd | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:31:33.789 | 07f23762e60d47fd87999c844d0a58e0 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:31:33.947 | 0bd84bdd7dce43a4903f5a88cdfeeca7 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:31:34.179 | 4dd499cfcce447cd806b46c730e98a67 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:31:34.477 | 6e0d361765004bc6b3e950f455f89e0b | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:31:34.500 | 6e0d361765004bc6b3e950f455f89e0b | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:31:34.616 | 6e0d361765004bc6b3e950f455f89e0b | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:31:34.616 | 6e0d361765004bc6b3e950f455f89e0b | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:31:34.871 | b2cd79a53f8f430cb5e8e4448fdc1eb4 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:31:34.895 | b2cd79a53f8f430cb5e8e4448fdc1eb4 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:31:34.960 | 2e34b1c5bdfc4f828a4bb952f7f48aac | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:31:34.984 | 2e34b1c5bdfc4f828a4bb952f7f48aac | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:31:35.008 | b2cd79a53f8f430cb5e8e4448fdc1eb4 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:31:35.008 | b2cd79a53f8f430cb5e8e4448fdc1eb4 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:31:35.098 | 2e34b1c5bdfc4f828a4bb952f7f48aac | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:31:35.098 | 2e34b1c5bdfc4f828a4bb952f7f48aac | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:31:35.341 | bb8191743b2042b4a4506bff728df6cb | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:31:35.364 | bb8191743b2042b4a4506bff728df6cb | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:31:35.480 | bb8191743b2042b4a4506bff728df6cb | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:31:35.480 | bb8191743b2042b4a4506bff728df6cb | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:31:52.513 | 7746f14bc3a34b458049721a6afb6997 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:31:52.686 | 29201c8a467a421ca925bf610d245f03 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:31:53.772 | 9c1ed108652a49148d70864848ca2a5a | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:31:53.796 | 9c1ed108652a49148d70864848ca2a5a | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:31:53.911 | 9c1ed108652a49148d70864848ca2a5a | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:31:53.911 | 9c1ed108652a49148d70864848ca2a5a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:31:53.986 | 9def639bd0aa4078b630e707447f7317 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:31:54.173 | 136ff2f94fa544a4bfcdb09b84bbc2d0 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:31:54.177 | ae3b5854e9c54905a0bbe42fe5133b37 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:31:54.196 | 136ff2f94fa544a4bfcdb09b84bbc2d0 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:31:54.311 | 136ff2f94fa544a4bfcdb09b84bbc2d0 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:31:54.311 | 136ff2f94fa544a4bfcdb09b84bbc2d0 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:31:55.006 | ef5ebbf433ce432b9504fb337cbe91cf | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:31:55.030 | ef5ebbf433ce432b9504fb337cbe91cf | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:31:55.145 | ef5ebbf433ce432b9504fb337cbe91cf | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:31:55.145 | ef5ebbf433ce432b9504fb337cbe91cf | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:31:55.679 | 2a6cc5a0d0264194b00f6038c948dd2a | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:31:55.702 | 2a6cc5a0d0264194b00f6038c948dd2a | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:31:55.816 | 2a6cc5a0d0264194b00f6038c948dd2a | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:31:55.817 | 2a6cc5a0d0264194b00f6038c948dd2a | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:33:22.308 | 80a68820738146ad8e17927629c9467f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-11 11:33:22.482 | 6e65000fd0914355a03d2d416537918d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-11 11:33:22.891 | 66c7d08db61d44b2a72f3a28edd59675 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:33:22.916 | 66c7d08db61d44b2a72f3a28edd59675 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:33:23.034 | 66c7d08db61d44b2a72f3a28edd59675 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:33:23.035 | 66c7d08db61d44b2a72f3a28edd59675 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:33:23.247 | 1fa53d5e3582492e9a75a93afb886386 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-11 11:33:23.271 | 1fa53d5e3582492e9a75a93afb886386 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:37 - 获取预警记录列表成功
2025-06-11 11:33:23.389 | 1fa53d5e3582492e9a75a93afb886386 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:176 - 获取预警统计数据成功，方案ID: None
2025-06-11 11:33:23.390 | 1fa53d5e3582492e9a75a93afb886386 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-11 11:35:17.787 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-11 11:35:17.788 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-11 11:35:20.140 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-11 11:35:20.141 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-11 11:35:21.062 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-11 11:35:21.062 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-11 11:35:21.065 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-11 11:35:21.537 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-11 11:35:22.150 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-11 11:35:22.150 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
