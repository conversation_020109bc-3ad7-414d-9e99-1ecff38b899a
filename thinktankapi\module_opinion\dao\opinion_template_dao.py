from datetime import datetime
from sqlalchemy import and_, or_, desc, asc, func, select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from module_opinion.entity.do.opinion_template_do import OpinionTemplate
from module_opinion.entity.vo.opinion_template_vo import OpinionTemplatePageQueryModel, OpinionTemplateModel
from utils.page_util import PageUtil


class OpinionTemplateDao:
    """
    舆情分析模板数据访问层
    """

    @classmethod
    async def get_opinion_template_list(
        cls, db: AsyncSession, query_object: OpinionTemplatePageQueryModel, is_page: bool = False
    ):
        """
        获取舆情分析模板列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 舆情分析模板列表信息对象
        """
        query = select(OpinionTemplate).where(OpinionTemplate.is_deleted == 0)
        
        # 构建查询条件
        if query_object.template_name:
            query = query.where(OpinionTemplate.template_name.like(f'%{query_object.template_name}%'))
        if query_object.template_category:
            query = query.where(OpinionTemplate.template_category.like(f'%{query_object.template_category}%'))
        if query_object.priority:
            query = query.where(OpinionTemplate.priority == query_object.priority)
        if query_object.is_system_template is not None:
            query = query.where(OpinionTemplate.is_system_template == query_object.is_system_template)
        if query_object.is_active is not None:
            query = query.where(OpinionTemplate.is_active == query_object.is_active)
        if query_object.create_time_start:
            query = query.where(OpinionTemplate.create_time >= query_object.create_time_start)
        if query_object.create_time_end:
            query = query.where(OpinionTemplate.create_time <= query_object.create_time_end)

        # 排序：系统模板优先，然后按sort_order排序，最后按使用次数降序
        query = query.order_by(
            desc(OpinionTemplate.is_system_template),
            asc(OpinionTemplate.sort_order),
            desc(OpinionTemplate.usage_count),
            desc(OpinionTemplate.create_time)
        )

        if is_page:
            return await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size)
        else:
            result = await db.execute(query)
            return result.scalars().all()

    @classmethod
    async def get_opinion_template_detail(cls, db: AsyncSession, template_id: int):
        """
        获取舆情分析模板详细信息

        :param db: orm对象
        :param template_id: 模板ID
        :return: 舆情分析模板详细信息对象
        """
        query = select(OpinionTemplate).where(
            and_(OpinionTemplate.id == template_id, OpinionTemplate.is_deleted == 0)
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @classmethod
    async def add_opinion_template_dao(cls, db: AsyncSession, template_object: OpinionTemplateModel):
        """
        新增舆情分析模板数据库操作

        :param db: orm对象
        :param template_object: 舆情分析模板对象
        :return: 新增校验结果
        """
        db_template = OpinionTemplate(**template_object.model_dump())
        db.add(db_template)
        await db.flush()
        return db_template

    @classmethod
    async def edit_opinion_template_dao(cls, db: AsyncSession, template_object: OpinionTemplateModel):
        """
        编辑舆情分析模板数据库操作

        :param db: orm对象
        :param template_object: 舆情分析模板对象
        :return: 编辑校验结果
        """
        query = update(OpinionTemplate).where(OpinionTemplate.id == template_object.id)
        values = template_object.model_dump(exclude_unset=True, exclude={'id'})
        values['update_time'] = datetime.now()
        query = query.values(**values)
        await db.execute(query)

    @classmethod
    async def delete_opinion_template_dao(cls, db: AsyncSession, template_ids: List[int]):
        """
        删除舆情分析模板数据库操作

        :param db: orm对象
        :param template_ids: 舆情分析模板ID列表
        :return: 删除校验结果
        """
        query = update(OpinionTemplate).where(OpinionTemplate.id.in_(template_ids))
        query = query.values(is_deleted=1, update_time=datetime.now())
        await db.execute(query)

    @classmethod
    async def get_template_categories(cls, db: AsyncSession):
        """
        获取模板分类列表

        :param db: orm对象
        :return: 模板分类列表
        """
        query = select(
            OpinionTemplate.template_category,
            func.count(OpinionTemplate.id).label('template_count')
        ).where(
            and_(OpinionTemplate.is_deleted == 0, OpinionTemplate.is_active == 1)
        ).group_by(OpinionTemplate.template_category).order_by(OpinionTemplate.template_category)
        
        result = await db.execute(query)
        return result.all()

    @classmethod
    async def update_template_usage_count(cls, db: AsyncSession, template_id: int, increment: int = 1):
        """
        更新模板使用次数

        :param db: orm对象
        :param template_id: 模板ID
        :param increment: 增加的使用次数
        :return: 更新结果
        """
        query = update(OpinionTemplate).where(
            and_(OpinionTemplate.id == template_id, OpinionTemplate.is_deleted == 0)
        ).values(
            usage_count=OpinionTemplate.usage_count + increment,
            update_time=datetime.now()
        )
        await db.execute(query)

    @classmethod
    async def get_active_templates_for_selection(cls, db: AsyncSession):
        """
        获取用于选择的激活模板列表（简化信息）

        :param db: orm对象
        :return: 模板选择列表
        """
        query = select(
            OpinionTemplate.id,
            OpinionTemplate.template_name,
            OpinionTemplate.template_category,
            OpinionTemplate.entity_keyword,
            OpinionTemplate.specific_requirement,
            OpinionTemplate.priority,
            OpinionTemplate.usage_count
        ).where(
            and_(OpinionTemplate.is_deleted == 0, OpinionTemplate.is_active == 1)
        ).order_by(
            desc(OpinionTemplate.is_system_template),
            asc(OpinionTemplate.sort_order),
            desc(OpinionTemplate.usage_count)
        )
        
        result = await db.execute(query)
        return result.all()
