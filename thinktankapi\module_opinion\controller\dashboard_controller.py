from typing import Optional
from datetime import datetime
from fastapi import APIRouter, Depends, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_opinion.service.dashboard_service import DashboardService
from module_opinion.entity.vo.dashboard_vo import RecentAnalysisRecordsResponseModel, DashboardStatisticsModel
from utils.response_util import ResponseUtil
from utils.log_util import logger


# Dashboard API路由 - 添加认证保护
dashboardController = APIRouter(prefix='/api/dashboard', dependencies=[Depends(LoginService.get_current_user)])


@dashboardController.get('/recent-analysis')
async def get_recent_analysis_records(
    request: Request,
    limit: int = Query(default=3, description='返回记录数量，默认3条', ge=1, le=10),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取最新分析记录

    :param request: 请求对象
    :param limit: 返回记录数量限制，默认3条，最大10条
    :param query_db: 数据库会话
    :param current_user: 当前登录用户信息
    :return: 最新分析记录响应
    """
    try:
        # 从认证用户中获取用户ID
        user_id = current_user.user.user_id

        # 获取最新分析记录，传递用户ID参数
        recent_records = await DashboardService.get_recent_analysis_records_services(
            query_db, limit, user_id
        )

        logger.info(f'获取最新分析记录成功，用户ID: {user_id}，返回{len(recent_records.records)}条记录')

        return ResponseUtil.success(model_content=recent_records)

    except Exception as e:
        logger.error(f'获取最新分析记录失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取最新分析记录失败: {str(e)}')


@dashboardController.get('/statistics')
async def get_dashboard_statistics(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取Dashboard统计数据

    :param request: 请求对象
    :param query_db: 数据库会话
    :param current_user: 当前登录用户信息
    :return: Dashboard统计数据响应
    """
    try:
        # 从认证用户中获取用户ID
        user_id = current_user.user.user_id
        logger.info(f'=== Dashboard统计API被调用，用户ID: {user_id} ===')

        # 获取Dashboard统计数据
        statistics = await DashboardService.get_dashboard_statistics_services(query_db, user_id)

        logger.info(f'=== 从Service获取的统计数据: {statistics} ===')
        logger.info(f'=== 统计数据类型: {type(statistics)} ===')

        # 转换为字典格式
        statistics_dict = statistics.model_dump()
        logger.info(f'=== 转换后的字典数据: {statistics_dict} ===')

        logger.info('=== Dashboard统计数据获取成功 ===')

        response_data = ResponseUtil.success(data=statistics_dict)
        logger.info(f'=== 最终响应数据: {response_data} ===')

        return response_data

    except Exception as e:
        logger.error(f'=== Dashboard统计数据获取失败: {str(e)} ===')
        logger.error(f'=== 错误详情: {e} ===', exc_info=True)
        return ResponseUtil.error(msg=f'获取Dashboard统计数据失败: {str(e)}')


@dashboardController.get('/opinion-trend')
async def get_opinion_trend_data(
    request: Request,
    days: int = Query(default=7, description='天数，默认7天', ge=1, le=30),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取舆情分析趋势数据

    :param request: 请求对象
    :param days: 天数，默认7天，最大30天
    :param query_db: 数据库会话
    :param current_user: 当前登录用户信息
    :return: 舆情趋势数据响应
    """
    try:
        # 从认证用户中获取用户ID（如果需要按用户过滤）
        user_id = current_user.user.user_id

        # 获取舆情趋势数据
        trend_data = await DashboardService.get_opinion_trend_data_services(
            query_db, days
        )

        logger.info(f'获取{days}天舆情趋势数据成功，用户ID: {user_id}')

        return ResponseUtil.success(model_content=trend_data)

    except Exception as e:
        logger.error(f'获取舆情趋势数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取舆情趋势数据失败: {str(e)}')


# 内部接口，需要认证
internalDashboardController = APIRouter(
    prefix='/system/dashboard', 
    dependencies=[Depends(LoginService.get_current_user)]
)


@internalDashboardController.get('/recent-analysis')
async def get_internal_recent_analysis_records(
    request: Request,
    limit: int = Query(default=3, description='返回记录数量，默认3条', ge=1, le=10),
    user_id: Optional[int] = Query(default=None, description='用户ID，用于过滤特定用户的分析记录'),
    query_db: AsyncSession = Depends(get_db),
    current_user = Depends(LoginService.get_current_user),
):
    """
    获取最新分析记录（内部接口，需要认证）

    :param request: 请求对象
    :param limit: 返回记录数量限制，默认3条，最大10条
    :param user_id: 用户ID，用于过滤特定用户的分析记录
    :param query_db: 数据库会话
    :param current_user: 当前登录用户
    :return: 最新分析记录响应
    """
    try:
        # 如果没有提供user_id，使用当前登录用户的ID
        filter_user_id = user_id if user_id is not None else current_user.user.user_id

        # 获取最新分析记录，传递用户ID参数
        recent_records = await DashboardService.get_recent_analysis_records_services(
            query_db, limit, filter_user_id
        )

        logger.info(f'获取最新分析记录成功（内部接口），用户ID: {filter_user_id}，返回{len(recent_records.records)}条记录')

        return ResponseUtil.success(model_content=recent_records)

    except Exception as e:
        logger.error(f'获取最新分析记录失败（内部接口）: {str(e)}')
        return ResponseUtil.error(msg=f'获取最新分析记录失败: {str(e)}')


@internalDashboardController.get('/statistics')
async def get_internal_dashboard_statistics(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取Dashboard统计数据（内部接口，需要认证）

    :param request: 请求对象
    :param query_db: 数据库会话
    :return: Dashboard统计数据响应
    """
    try:
        # 获取Dashboard统计数据
        statistics = await DashboardService.get_dashboard_statistics_services(query_db)

        logger.info('获取Dashboard统计数据成功（内部接口）')

        return ResponseUtil.success(data=statistics.model_dump())

    except Exception as e:
        logger.error(f'获取Dashboard统计数据失败（内部接口）: {str(e)}')
        return ResponseUtil.error(msg=f'获取Dashboard统计数据失败: {str(e)}')


@dashboardController.get('/user-info')
async def get_user_dashboard_info(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取用户Dashboard信息（用户信息和会员信息）

    :param request: 请求对象
    :param query_db: 数据库会话
    :param current_user: 当前登录用户信息
    :return: 用户Dashboard信息响应
    """
    try:
        # 从认证用户中获取用户ID
        user_id = current_user.user.user_id

        # 导入BillService来获取用户会员信息
        from module_bill.service.bill_service import BillService

        # 获取用户会员信息
        membership_info = await BillService.get_user_membership_info(query_db, user_id)

        # 组装响应数据
        dashboard_data = {
            "user_info": {
                "user_id": membership_info.user_info.user_id,
                "user_name": membership_info.user_info.user_name,
                "nick_name": membership_info.user_info.nick_name,
                "email": membership_info.user_info.email,
                "avatar": membership_info.user_info.avatar,
                "vip_level": membership_info.user_info.vip_level,
                "account_balance": membership_info.user_info.account_balance,
                "total_spent": membership_info.user_info.total_spent,
                "last_payment_time": membership_info.user_info.last_payment_time
            },
            "membership_info": {
                "membership_status": membership_info.membership_status,
                "expire_time": membership_info.expire_time.isoformat() if membership_info.expire_time else None,
                "current_package": membership_info.current_package
            }
        }

        logger.info(f'获取用户Dashboard信息成功，用户ID: {user_id}')

        return ResponseUtil.success(data=dashboard_data)

    except Exception as e:
        logger.error(f'获取用户Dashboard信息失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取用户Dashboard信息失败: {str(e)}')


@dashboardController.get('/current-month-analysis-count')
async def get_current_month_analysis_count(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取用户当前月份的分析记录数量（备用统计）

    :param request: 请求对象
    :param query_db: 数据库会话
    :param current_user: 当前登录用户信息
    :return: 当前月份分析记录数量响应
    """
    try:
        # 从认证用户中获取用户ID
        user_id = current_user.user.user_id
        logger.info(f'=== 获取当前月份分析记录数API被调用，用户ID: {user_id} ===')

        # 获取当前月份的分析记录数量
        monthly_count = await DashboardService.get_current_month_analysis_count_service(query_db, user_id)

        logger.info(f'=== 用户{user_id}当前月份分析记录数: {monthly_count} ===')

        response_data = {
            'monthly_count': monthly_count,
            'user_id': user_id,
            'query_month': f"{datetime.now().year}-{datetime.now().month:02d}"
        }

        logger.info('=== 当前月份分析记录数获取成功 ===')
        return ResponseUtil.success(data=response_data)

    except Exception as e:
        logger.error(f'获取当前月份分析记录数失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取当前月份分析记录数失败: {str(e)}')
