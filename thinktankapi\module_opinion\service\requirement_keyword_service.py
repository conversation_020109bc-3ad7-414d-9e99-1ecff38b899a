from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from exceptions.exception import ServiceException
from module_opinion.dao.requirement_keyword_dao import RequirementKeywordDao
from module_opinion.dao.opinion_requirement_dao import OpinionRequirementDao
from module_opinion.entity.vo.requirement_keyword_vo import (
    RequirementKeywordModel,
    RequirementKeywordPageQueryModel,
    GenerateKeywordsModel,
    SelectKeywordsModel,
    GeneratedKeywordModel,
    GenerateKeywordsResponseModel,
    KeywordCategoryModel
)
from module_admin.entity.vo.common_vo import CrudResponseModel
from utils.common_util import CamelCaseUtil


class RequirementKeywordService:
    """
    需求关键词管理模块服务层
    """

    @classmethod
    async def get_requirement_keyword_list_services(
        cls, query_db: AsyncSession, query_object: RequirementKeywordPageQueryModel, is_page: bool = False
    ):
        """
        获取需求关键词列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 需求关键词列表信息对象
        """
        keyword_list_result = await RequirementKeywordDao.get_requirement_keyword_list(query_db, query_object, is_page)
        
        if is_page:
            return keyword_list_result
        else:
            return CamelCaseUtil.transform_result(keyword_list_result)

    @classmethod
    async def generate_keywords_services(cls, query_db: AsyncSession, generate_request: GenerateKeywordsModel, current_user_id: int):
        """
        生成关联词service

        :param query_db: orm对象
        :param generate_request: 生成关键词请求对象
        :param current_user_id: 当前用户ID
        :return: 生成的关键词列表
        """
        # 检查需求是否存在
        requirement = await OpinionRequirementDao.get_opinion_requirement_by_id(query_db, generate_request.requirement_id)
        if not requirement:
            raise ServiceException(message='舆情需求不存在')

        # 清空之前生成的关键词
        await RequirementKeywordDao.clear_requirement_keywords(query_db, generate_request.requirement_id)

        # 模拟生成关联词（实际项目中可以接入AI生成服务）
        generated_keywords = cls._generate_related_keywords(
            generate_request.entity_keyword, 
            generate_request.specific_requirement,
            generate_request.max_count
        )

        # 保存生成的关键词到数据库
        keyword_models = []
        for i, keyword_text in enumerate(generated_keywords):
            keyword_model = RequirementKeywordModel(
                requirement_id=generate_request.requirement_id,
                keyword=keyword_text,
                keyword_type='generated',
                category_id=generate_request.category_id,
                is_selected=1 if i < requirement.max_keywords_limit else 0,  # 默认选中前几个
                weight=len(generated_keywords) - i,  # 权重递减
                status=1,
                create_by=str(current_user_id)
            )
            keyword_models.append(keyword_model)

        try:
            await RequirementKeywordDao.batch_add_requirement_keywords_dao(query_db, keyword_models)
            
            # 更新需求的关键词数量
            selected_count = min(len(generated_keywords), requirement.max_keywords_limit)
            await OpinionRequirementDao.update_keywords_count(query_db, generate_request.requirement_id, selected_count)
            
            await query_db.commit()

            # 构建响应
            response_keywords = []
            for keyword_model in keyword_models:
                response_keywords.append(GeneratedKeywordModel(
                    keyword=keyword_model.keyword,
                    weight=keyword_model.weight,
                    category_id=keyword_model.category_id,
                    is_selected=keyword_model.is_selected == 1
                ))

            return GenerateKeywordsResponseModel(
                requirement_id=generate_request.requirement_id,
                generated_keywords=response_keywords,
                total_count=len(generated_keywords),
                max_selectable=requirement.max_keywords_limit
            )
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    def _generate_related_keywords(cls, entity_keyword: str, specific_requirement: str, max_count: int = 20) -> List[str]:
        """
        生成关联词的内部方法（模拟实现）

        :param entity_keyword: 实体关键词
        :param specific_requirement: 具体需求
        :param max_count: 最大生成数量
        :return: 生成的关键词列表
        """
        # 这里是模拟实现，实际项目中可以接入AI生成服务
        base_keywords = [
            f'{entity_keyword} 售后服务',
            f'{entity_keyword} 三包义务',
            f'{entity_keyword} 客服态度',
            f'{entity_keyword} 质量',
            f'{entity_keyword} 故障',
            f'{entity_keyword} 投诉处理',
            f'{entity_keyword} 对解',
            f'{entity_keyword} 投诉公示',
            f'{entity_keyword} 消费者不满',
            f'{entity_keyword} 不满',
            f'{entity_keyword} 投诉平台',
            f'{entity_keyword} 虚假宣传',
            f'{entity_keyword} 以次充好',
            f'{entity_keyword} 维修',
            f'{entity_keyword} 退换货',
            f'{entity_keyword} 保修',
            f'{entity_keyword} 服务态度',
            f'{entity_keyword} 产品缺陷',
            f'{entity_keyword} 用户体验',
            f'{entity_keyword} 品牌形象'
        ]
        
        return base_keywords[:max_count]

    @classmethod
    async def select_keywords_services(cls, query_db: AsyncSession, select_request: SelectKeywordsModel, current_user_id: int):
        """
        选择关键词service

        :param query_db: orm对象
        :param select_request: 选择关键词请求对象
        :param current_user_id: 当前用户ID
        :return: 选择结果
        """
        # 检查需求是否存在
        requirement = await OpinionRequirementDao.get_opinion_requirement_by_id(query_db, select_request.requirement_id)
        if not requirement:
            raise ServiceException(message='舆情需求不存在')

        # 检查选择的关键词数量是否超过限制
        if len(select_request.keyword_ids) > requirement.max_keywords_limit:
            raise ServiceException(message=f'最多只能选择{requirement.max_keywords_limit}个关键词')

        try:
            # 批量更新关键词选择状态
            await RequirementKeywordDao.batch_update_keywords_selection(
                query_db, select_request.requirement_id, select_request.keyword_ids
            )
            
            # 更新需求的关键词数量
            await OpinionRequirementDao.update_keywords_count(query_db, select_request.requirement_id, len(select_request.keyword_ids))
            
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='关键词选择成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def get_selected_keywords_services(cls, query_db: AsyncSession, requirement_id: int):
        """
        获取已选择的关键词service

        :param query_db: orm对象
        :param requirement_id: 需求ID
        :return: 已选择的关键词列表
        """
        selected_keywords = await RequirementKeywordDao.get_selected_keywords_by_requirement(query_db, requirement_id)
        return CamelCaseUtil.transform_result(selected_keywords)

    @classmethod
    async def get_keyword_categories_services(cls, query_db: AsyncSession):
        """
        获取关键词分类列表service

        :param query_db: orm对象
        :return: 分类列表
        """
        categories = await RequirementKeywordDao.get_keyword_categories(query_db)
        
        result = []
        for category in categories:
            result.append(KeywordCategoryModel(
                id=category.id,
                category_name=category.category_name,
                category_code=category.category_code,
                parent_id=0,  # 数据库中没有此字段，设为默认值
                category_level=1,  # 数据库中没有此字段，设为默认值
                sort_order=category.sort_order,
                description=category.description,
                status=category.is_active  # 使用is_active字段
            ))
        
        return result

    @classmethod
    async def get_keywords_count_services(cls, query_db: AsyncSession, requirement_id: int, is_selected: Optional[int] = None):
        """
        获取关键词数量service

        :param query_db: orm对象
        :param requirement_id: 需求ID
        :param is_selected: 是否选中
        :return: 关键词数量
        """
        return await RequirementKeywordDao.get_keywords_count_by_requirement(query_db, requirement_id, is_selected)
