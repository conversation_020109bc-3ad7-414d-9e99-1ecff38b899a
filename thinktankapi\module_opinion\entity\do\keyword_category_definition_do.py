from datetime import datetime
from sqlalchemy import BigInteger, Column, DateTime, Integer, String, Text
from config.database import Base


class KeywordCategoryDefinition(Base):
    """
    关键词分类定义表
    """

    __tablename__ = 'keyword_category_definition'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    category_name = Column(String(100), nullable=False, comment='分类名称')
    category_code = Column(String(50), nullable=False, comment='分类编码')
    description = Column(Text, nullable=True, comment='分类描述')
    match_rules = Column(Text, nullable=True, comment='匹配规则（JSON格式）')
    sort_order = Column(Integer, default=0, comment='排序顺序')
    is_active = Column(Integer, default=1, comment='是否激活：0-否，1-是')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
