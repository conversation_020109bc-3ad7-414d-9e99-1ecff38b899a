from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, BigInteger, Float, Date
from config.database import Base


class PlatformAnalysisSummary(Base):
    """
    平台分析汇总表
    """

    __tablename__ = 'platform_analysis_summary'
    __table_args__ = {'extend_existing': True}

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    platform = Column(String(100), nullable=False, comment='平台名称')
    platform_type = Column(String(50), nullable=True, comment='平台类型：web, weibo, wechat, app, video等')
    date = Column(Date, nullable=False, comment='统计日期')
    total_count = Column(Integer, default=0, comment='总数量')
    positive_count = Column(Integer, default=0, comment='正面数量')
    negative_count = Column(Integer, default=0, comment='负面数量')
    neutral_count = Column(Integer, default=0, comment='中性数量')
    positive_rate = Column(Float, default=0.0, comment='正面比例')
    negative_rate = Column(Float, default=0.0, comment='负面比例')
    neutral_rate = Column(Float, default=0.0, comment='中性比例')
    avg_heat_score = Column(Float, default=0.0, comment='平均热度分数')
    max_heat_score = Column(Float, default=0.0, comment='最高热度分数')
    total_views = Column(BigInteger, default=0, comment='总浏览数')
    total_shares = Column(BigInteger, default=0, comment='总分享数')
    total_comments = Column(BigInteger, default=0, comment='总评论数')
    total_likes = Column(BigInteger, default=0, comment='总点赞数')
    engagement_rate = Column(Float, default=0.0, comment='参与率')
    influence_score = Column(Float, default=0.0, comment='影响力分数')
    trending_topics = Column(Integer, default=0, comment='趋势话题数')
    hot_news_count = Column(Integer, default=0, comment='热点新闻数')
    media_count = Column(Integer, default=0, comment='媒体数量')
    unique_authors = Column(Integer, default=0, comment='独特作者数')
    peak_hour = Column(Integer, nullable=True, comment='峰值小时')
    region_distribution = Column(String(1000), nullable=True, comment='地区分布，JSON格式')
    top_keywords = Column(String(1000), nullable=True, comment='热门关键词，JSON格式')
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')
