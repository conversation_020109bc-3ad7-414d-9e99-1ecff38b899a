class LoginException(Exception):
    """
    自定义登录异常LoginException
    """

    def __init__(self, data: str = None, message: str = None):
        self.data = data
        self.message = message
        super().__init__(message)

    def __str__(self):
        return self.message or "LoginException occurred"


class AuthException(Exception):
    """
    自定义令牌异常AuthException
    """

    def __init__(self, data: str = None, message: str = None):
        self.data = data
        self.message = message
        super().__init__(message)

    def __str__(self):
        return self.message or "AuthException occurred"


class PermissionException(Exception):
    """
    自定义权限异常PermissionException
    """

    def __init__(self, data: str = None, message: str = None):
        self.data = data
        self.message = message
        super().__init__(message)

    def __str__(self):
        return self.message or "PermissionException occurred"


class ServiceException(Exception):
    """
    自定义服务异常ServiceException
    """

    def __init__(self, data: str = None, message: str = None):
        self.data = data
        self.message = message
        super().__init__(message)  # 确保Exception的message被正确设置

    def __str__(self):
        return self.message or "ServiceException occurred"


class ServiceWarning(Exception):
    """
    自定义服务警告ServiceWarning
    """

    def __init__(self, data: str = None, message: str = None):
        self.data = data
        self.message = message
        super().__init__(message)

    def __str__(self):
        return self.message or "ServiceWarning occurred"


class ModelValidatorException(Exception):
    """
    自定义模型校验异常ModelValidatorException
    """

    def __init__(self, data: str = None, message: str = None):
        self.data = data
        self.message = message
        super().__init__(message)

    def __str__(self):
        return self.message or "ModelValidatorException occurred"
