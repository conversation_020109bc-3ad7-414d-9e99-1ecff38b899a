"""
情感分析配置文件
"""

class SentimentConfig:
    """情感分析配置类"""
    
    # 是否启用AI情感分析
    ENABLE_AI_SENTIMENT = True
    
    # AI情感分析失败时是否降级到本地分析
    ENABLE_FALLBACK_LOCAL = True
    
    # 情感分析缓存过期时间（秒）
    SENTIMENT_CACHE_EXPIRE = 3600  # 1小时
    
    # 批量分析的最大文本数量
    MAX_BATCH_SIZE = 10
    
    # 单次分析的最大文本长度
    MAX_TEXT_LENGTH = 1000
    
    # 扩展的正面情感关键词
    POSITIVE_KEYWORDS = [
        '好', '优秀', '成功', '增长', '提升', '改善', '积极', '正面', '赞', '支持',
        '喜欢', '满意', '高兴', '开心', '兴奋', '赞扬', '称赞', '推荐', '优质',
        '出色', '卓越', '完美', '棒', '赞同', '认可', '肯定', '表扬', '鼓励',
        '欣赏', '感谢', '赞美', '夸奖', '佩服', '敬佩', '信任', '可靠', '稳定',
        '创新', '突破', '领先', '先进', '高效', '便民', '实用', '有效', '有益'
    ]
    
    # 扩展的负面情感关键词
    NEGATIVE_KEYWORDS = [
        '坏', '失败', '下降', '问题', '困难', '危机', '负面', '批评', '反对', '担心',
        '讨厌', '不满', '愤怒', '生气', '失望', '沮丧', '糟糕', '差', '恶劣',
        '谴责', '抗议', '质疑', '怀疑', '不信任', '拒绝', '否定', '抱怨', '投诉',
        '指责', '责备', '埋怨', '不安', '焦虑', '恐慌', '害怕', '担忧', '忧虑',
        '混乱', '错误', '故障', '缺陷', '漏洞', '风险', '威胁', '损失', '浪费'
    ]
    
    # 中性关键词（用于平衡判断）
    NEUTRAL_KEYWORDS = [
        '说', '表示', '认为', '指出', '提到', '显示', '数据', '报告', '调查',
        '研究', '分析', '统计', '发现', '观察', '记录', '描述', '介绍', '说明'
    ]
    
    # AI情感分析的prompt模板
    AI_SENTIMENT_PROMPT_TEMPLATE = """
请对以下文本进行情感分析，只返回一个词：positive（积极）、negative（消极）或neutral（中性）。

分析要求：
1. 考虑整体语境和语义
2. 理解反讽、双重否定等复杂表达
3. 综合评估整体情感倾向
4. 重点关注主要观点而非细节描述

文本内容：
{text}

请只返回：positive、negative 或 neutral
"""
    
    # 情感分析结果映射
    SENTIMENT_MAPPING = {
        'positive': '积极',
        'negative': '消极', 
        'neutral': '中性'
    }
    
    # 情感分析置信度阈值
    CONFIDENCE_THRESHOLD = 0.6

    # AI文章提取的prompt模板
    AI_ARTICLE_EXTRACTION_PROMPT = """
你是一个专业的新闻文章提取助手。请严格按照以下要求从内容中提取结构化的文章信息。

**重要要求**：
1. 必须返回有效的JSON数组格式
2. 不要添加任何解释文字或markdown标记
3. 确保JSON格式正确，可以被程序解析

**提取要求**：
1. 识别并提取所有独立的文章或新闻条目
2. 为每篇文章生成简洁准确的标题（不超过50字）
3. 提取文章的核心内容（200-500字）
4. 识别文章来源
5. **URL要求**：必须提供真实可访问的新闻文章URL链接
   - 不要使用示例URL、占位符URL或虚构的链接
   - 确保每个URL都指向真实存在的网页内容
   - 优先提供权威媒体和官方信息源的链接
   - 如果确实无法获取真实URL，则设为空字符串
6. 设置发布时间为当前时间格式：YYYY-MM-DD HH:MM:SS

**严格的JSON格式**：
[
  {{
    "title": "文章标题",
    "content": "文章主要内容",
    "source": "来源网站或平台",
    "url": "真实可访问的完整URL地址",
    "publish_time": "2024-01-01 12:00:00"
  }}
]

**原始内容**：
{content}

**输出要求**：只返回JSON数组，开头必须是"["，结尾必须是"]"，不要包含任何其他文字。
"""

    @classmethod
    def get_ai_prompt(cls, text: str) -> str:
        """获取AI情感分析的prompt"""
        return cls.AI_SENTIMENT_PROMPT_TEMPLATE.format(text=text[:cls.MAX_TEXT_LENGTH])

    @classmethod
    def get_article_extraction_prompt(cls, content: str) -> str:
        """获取AI文章提取的prompt"""
        return cls.AI_ARTICLE_EXTRACTION_PROMPT.format(content=content[:5000])
    
    @classmethod
    def is_ai_enabled(cls) -> bool:
        """检查是否启用AI情感分析"""
        return cls.ENABLE_AI_SENTIMENT
    
    @classmethod
    def is_fallback_enabled(cls) -> bool:
        """检查是否启用降级策略"""
        return cls.ENABLE_FALLBACK_LOCAL
