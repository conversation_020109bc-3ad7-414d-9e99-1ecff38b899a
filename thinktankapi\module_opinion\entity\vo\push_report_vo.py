from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List
from datetime import datetime


class PushReportRequestModel(BaseModel):
    """
    推送报告请求模型
    """
    target_url: str = Field(..., description='推送目标URL')
    report_data: Dict[str, Any] = Field(..., description='报告数据')
    analysis_results: Dict[str, Any] = Field(..., description='分析结果')
    requirement_id: Optional[int] = Field(None, description='需求ID')
    push_type: Optional[str] = Field('immediate', description='推送类型：immediate-立即推送，scheduled-计划推送')
    
    @validator('target_url')
    def validate_target_url(cls, v):
        """验证目标URL格式（宽松验证）"""
        if not v or not v.strip():
            raise ValueError('推送目标URL不能为空')

        url = v.strip()

        # 宽松的URL格式验证
        # 检查是否包含基本的URL结构
        if '://' not in url:
            raise ValueError('推送目标URL格式不正确，缺少协议部分（如 http:// 或 https://）')

        # 检查URL长度
        if len(url) < 10:
            raise ValueError('推送目标URL格式不正确，URL过短')

        # 检查是否包含空格
        if ' ' in url:
            raise ValueError('推送目标URL不能包含空格')

        return url
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class PushReportResponseModel(BaseModel):
    """
    推送报告响应模型
    """
    success: bool = Field(..., description='推送是否成功')
    message: str = Field(..., description='推送结果消息')
    push_id: Optional[str] = Field(None, description='推送记录ID')
    target_url: str = Field(..., description='推送目标URL')
    push_time: datetime = Field(..., description='推送时间')
    response_status: Optional[int] = Field(None, description='目标服务器响应状态码')
    response_data: Optional[Dict[str, Any]] = Field(None, description='目标服务器响应数据')
    error_details: Optional[str] = Field(None, description='错误详情')
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class DingTalkMessageModel(BaseModel):
    """
    钉钉机器人消息模型
    """
    msgtype: str = Field(..., description='消息类型')
    markdown: Optional[Dict[str, str]] = Field(None, description='markdown消息内容')
    text: Optional[Dict[str, str]] = Field(None, description='文本消息内容')
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class PushLogModel(BaseModel):
    """
    推送日志模型
    """
    id: Optional[int] = Field(None, description='日志ID')
    requirement_id: Optional[int] = Field(None, description='需求ID')
    target_url: str = Field(..., description='推送目标URL')
    push_type: str = Field(..., description='推送类型')
    push_status: str = Field(..., description='推送状态：success-成功，failed-失败')
    request_data: Dict[str, Any] = Field(..., description='请求数据')
    response_status: Optional[int] = Field(None, description='响应状态码')
    response_data: Optional[Dict[str, Any]] = Field(None, description='响应数据')
    error_message: Optional[str] = Field(None, description='错误信息')
    push_time: datetime = Field(..., description='推送时间')
    created_by: Optional[int] = Field(None, description='创建者ID')
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class PushHistoryQueryModel(BaseModel):
    """
    推送历史查询模型
    """
    requirement_id: Optional[int] = Field(None, description='需求ID')
    target_url: Optional[str] = Field(None, description='目标URL')
    push_status: Optional[str] = Field(None, description='推送状态')
    start_time: Optional[datetime] = Field(None, description='开始时间')
    end_time: Optional[datetime] = Field(None, description='结束时间')
    page_num: int = Field(1, description='页码')
    page_size: int = Field(10, description='每页数量')
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
