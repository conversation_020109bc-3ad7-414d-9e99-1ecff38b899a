import request from '@/utils/request'

// ==================== 事件分析管理 ====================

// 获取事件分析列表
export function getEventAnalysisList(query) {
  return request({
    url: '/event-analysis/list',
    method: 'get',
    params: query
  })
}

// 获取事件分析详情
export function getEventAnalysisDetail(eventId) {
  return request({
    url: '/event-analysis/detail/' + eventId,
    method: 'get'
  })
}

// 获取事件分析统计数据
export function getEventAnalysisStatistics(query) {
  return request({
    url: '/event-analysis/statistics',
    method: 'get',
    params: query
  })
}

// 获取事件趋势分析
export function getEventTrendAnalysis(eventId, query) {
  return request({
    url: '/event-analysis/trend/' + eventId,
    method: 'get',
    params: query
  })
}

// 获取事件传播路径
export function getEventSpreadPath(eventId) {
  return request({
    url: '/event-analysis/spread-path/' + eventId,
    method: 'get'
  })
}

// 获取事件影响力分析
export function getEventInfluenceAnalysis(eventId) {
  return request({
    url: '/event-analysis/influence/' + eventId,
    method: 'get'
  })
}

// 获取事件关键词分析
export function getEventKeywordAnalysis(eventId) {
  return request({
    url: '/event-analysis/keywords/' + eventId,
    method: 'get'
  })
}

// 获取事件情感分析
export function getEventSentimentAnalysis(eventId, query) {
  return request({
    url: '/event-analysis/sentiment/' + eventId,
    method: 'get',
    params: query
  })
}

// 获取事件媒体分布
export function getEventMediaDistribution(eventId) {
  return request({
    url: '/event-analysis/media-distribution/' + eventId,
    method: 'get'
  })
}

// 获取事件时间线
export function getEventTimeline(eventId) {
  return request({
    url: '/event-analysis/timeline/' + eventId,
    method: 'get'
  })
}

// 导出事件分析报告
export function exportEventAnalysisReport(eventId, format) {
  return request({
    url: '/event-analysis/export/' + eventId,
    method: 'get',
    params: { format: format },
    responseType: 'blob'
  })
}

// 创建事件分析任务
export function createEventAnalysisTask(data) {
  return request({
    url: '/event-analysis/task',
    method: 'post',
    data: data
  })
}

// 获取事件分析任务状态
export function getEventAnalysisTaskStatus(taskId) {
  return request({
    url: '/event-analysis/task/status/' + taskId,
    method: 'get'
  })
}
