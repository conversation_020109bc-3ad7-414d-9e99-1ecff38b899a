from datetime import datetime, timedelta
from sqlalchemy import select, func, and_, or_, case, desc, text
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.keyword_data_do import KeywordData
from module_admin.entity.do.scheme_do import SchemeDO, SchemeSummaryStatisticDO
from module_admin.entity.do.emotion_definition_do import EmotionDefinition
from module_admin.entity.do.news_do import News
from module_admin.entity.do.hot_news_extension_do import HotNewsExtension
from module_admin.entity.do.event_do import Event
from module_admin.entity.do.event_news_do import EventNews
from module_admin.entity.do.platform_analysis_summary_do import PlatformAnalysisSummary
from utils.log_util import logger


class SpreadAnalysisDao:
    """
    传播分析数据访问层
    """

    @classmethod
    async def get_scheme_summary_statistics(cls, db: AsyncSession, scheme_id: int):
        """
        获取方案汇总统计数据
        """
        try:
            query = select(SchemeSummaryStatisticDO).where(SchemeSummaryStatisticDO.scheme_id == scheme_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取方案汇总统计数据失败: {e}")
            return None

    @classmethod
    async def get_keyword_data_statistics(cls, db: AsyncSession, time_range: str = "today", 
                                        date_start: str = None, date_end: str = None,
                                        platform_types: list = None, sentiment_types: list = None):
        """
        获取关键词数据统计
        """
        try:
            # 构建基础查询
            query = select(KeywordData)
            conditions = []

            # 时间筛选
            if time_range == "today":
                today = datetime.now().date()
                conditions.append(func.date(KeywordData.createtime) == today)
            elif time_range == "yesterday":
                yesterday = (datetime.now() - timedelta(days=1)).date()
                conditions.append(func.date(KeywordData.createtime) == yesterday)
            elif time_range == "7d":
                seven_days_ago = (datetime.now() - timedelta(days=7)).date()
                conditions.append(func.date(KeywordData.createtime) >= seven_days_ago)
            elif time_range == "30d":
                thirty_days_ago = (datetime.now() - timedelta(days=30)).date()
                conditions.append(func.date(KeywordData.createtime) >= thirty_days_ago)
            elif time_range == "custom" and date_start and date_end:
                conditions.append(func.date(KeywordData.createtime) >= date_start)
                conditions.append(func.date(KeywordData.createtime) <= date_end)

            # 平台类型筛选
            if platform_types and 'all_platform' not in platform_types:
                conditions.append(KeywordData.type.in_(platform_types))

            # 情感类型筛选
            if sentiment_types and 'all_sentiment' not in sentiment_types:
                conditions.append(KeywordData.sentiment.in_(sentiment_types))

            if conditions:
                query = query.where(and_(*conditions))

            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"获取关键词数据统计失败: {e}")
            return []

    @classmethod
    async def get_platform_distribution_statistics(cls, db: AsyncSession, time_range: str = "today"):
        """
        获取平台分布统计
        """
        try:
            # 构建时间条件
            conditions = []
            if time_range == "today":
                today = datetime.now().date()
                conditions.append(func.date(KeywordData.createtime) == today)
            elif time_range == "7d":
                seven_days_ago = (datetime.now() - timedelta(days=7)).date()
                conditions.append(func.date(KeywordData.createtime) >= seven_days_ago)

            # 查询平台分布
            query = select(
                KeywordData.type.label('platform'),
                func.count(KeywordData.id).label('count')
            ).group_by(KeywordData.type)

            if conditions:
                query = query.where(and_(*conditions))

            result = await db.execute(query)
            return result.fetchall()
        except Exception as e:
            logger.error(f"获取平台分布统计失败: {e}")
            return []

    @classmethod
    async def get_sentiment_distribution_statistics(cls, db: AsyncSession, time_range: str = "today"):
        """
        获取情感分布统计
        """
        try:
            # 构建时间条件
            conditions = []
            if time_range == "today":
                today = datetime.now().date()
                conditions.append(func.date(KeywordData.createtime) == today)
            elif time_range == "7d":
                seven_days_ago = (datetime.now() - timedelta(days=7)).date()
                conditions.append(func.date(KeywordData.createtime) >= seven_days_ago)

            # 查询情感分布
            query = select(
                KeywordData.sentiment.label('sentiment'),
                func.count(KeywordData.id).label('count')
            ).group_by(KeywordData.sentiment)

            if conditions:
                query = query.where(and_(*conditions))

            result = await db.execute(query)
            return result.fetchall()
        except Exception as e:
            logger.error(f"获取情感分布统计失败: {e}")
            return []

    @classmethod
    async def get_keyword_cloud_data(cls, db: AsyncSession, time_range: str = "today", limit: int = 20):
        """
        获取关键词云数据
        """
        try:
            # 构建时间条件
            conditions = []
            if time_range == "today":
                today = datetime.now().date()
                conditions.append(func.date(KeywordData.createtime) == today)
            elif time_range == "7d":
                seven_days_ago = (datetime.now() - timedelta(days=7)).date()
                conditions.append(func.date(KeywordData.createtime) >= seven_days_ago)

            # 查询关键词统计
            query = select(
                KeywordData.keyword.label('keyword'),
                func.count(KeywordData.id).label('count')
            ).where(
                and_(
                    KeywordData.keyword.isnot(None),
                    KeywordData.keyword != '',
                    *conditions
                )
            ).group_by(KeywordData.keyword).order_by(desc('count')).limit(limit)

            result = await db.execute(query)
            return result.fetchall()
        except Exception as e:
            logger.error(f"获取关键词云数据失败: {e}")
            return []

    @classmethod
    async def get_trend_data(cls, db: AsyncSession, days: int = 7):
        """
        获取趋势数据
        """
        try:
            # 计算日期范围
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days-1)

            # 查询每日统计数据
            query = select(
                func.date(KeywordData.createtime).label('date'),
                func.count(KeywordData.id).label('count'),
                func.sum(case((KeywordData.sentiment == 'positive', 1), else_=0)).label('positive'),
                func.sum(case((KeywordData.sentiment == 'negative', 1), else_=0)).label('negative'),
                func.sum(case((KeywordData.sentiment == 'neutral', 1), else_=0)).label('neutral')
            ).where(
                and_(
                    func.date(KeywordData.createtime) >= start_date,
                    func.date(KeywordData.createtime) <= end_date
                )
            ).group_by(func.date(KeywordData.createtime)).order_by('date')

            result = await db.execute(query)
            return result.fetchall()
        except Exception as e:
            logger.error(f"获取趋势数据失败: {e}")
            return []

    @classmethod
    async def get_emotion_statistics(cls, db: AsyncSession):
        """
        获取情感统计数据
        """
        try:
            # 查询总体情感统计
            query = select(
                func.count(KeywordData.id).label('total_count'),
                func.sum(case((KeywordData.sentiment == 'positive', 1), else_=0)).label('positive_count'),
                func.sum(case((KeywordData.sentiment == 'negative', 1), else_=0)).label('negative_count'),
                func.sum(case((KeywordData.sentiment == 'neutral', 1), else_=0)).label('neutral_count')
            )

            result = await db.execute(query)
            return result.fetchone()
        except Exception as e:
            logger.error(f"获取情感统计数据失败: {e}")
            return None
