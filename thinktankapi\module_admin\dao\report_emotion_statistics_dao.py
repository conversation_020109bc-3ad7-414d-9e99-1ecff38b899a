from datetime import datetime, timedelta
from sqlalchemy import and_, or_, desc, asc, func, select, text
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any, Tuple
from module_admin.entity.do.report_emotion_statistics_do import ReportEmotionStatisticsDO
from module_admin.entity.vo.report_emotion_statistics_vo import (
    ReportEmotionStatisticsPageQueryModel,
    CreateReportEmotionStatisticsModel,
    UpdateReportEmotionStatisticsModel
)
from utils.page_util import PageUtil
from utils.log_util import logger


class ReportEmotionStatisticsDao:
    """
    报告情感统计数据访问层
    """

    @classmethod
    async def get_report_emotion_statistics_list(
        cls,
        query_db: AsyncSession,
        query_object: ReportEmotionStatisticsPageQueryModel,
        is_page: bool = False
    ) -> List[ReportEmotionStatisticsDO]:
        """
        获取报告情感统计列表
        
        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否分页
        :return: 报告情感统计列表
        """
        query = select(ReportEmotionStatisticsDO)
        
        # 构建查询条件
        conditions = []
        
        if query_object.report_id:
            conditions.append(ReportEmotionStatisticsDO.report_id.like(f'%{query_object.report_id}%'))
        
        if query_object.scheme_id:
            conditions.append(ReportEmotionStatisticsDO.scheme_id == query_object.scheme_id)
        
        if query_object.report_type:
            conditions.append(ReportEmotionStatisticsDO.report_type == query_object.report_type)
        
        if query_object.data_source:
            conditions.append(ReportEmotionStatisticsDO.data_source.like(f'%{query_object.data_source}%'))
        
        if query_object.status:
            conditions.append(ReportEmotionStatisticsDO.status == query_object.status)
        
        if query_object.start_time:
            conditions.append(ReportEmotionStatisticsDO.create_time >= query_object.start_time)
        
        if query_object.end_time:
            conditions.append(ReportEmotionStatisticsDO.create_time <= query_object.end_time)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 排序
        query = query.order_by(desc(ReportEmotionStatisticsDO.create_time))
        
        # 分页
        if is_page:
            page_obj = PageUtil(query_object.page_num, query_object.page_size)
            query = query.limit(page_obj.page_size).offset(page_obj.start)
        
        result = await query_db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_report_emotion_statistics_detail(
        cls,
        query_db: AsyncSession,
        report_id: str
    ) -> Optional[ReportEmotionStatisticsDO]:
        """
        获取报告情感统计详情
        
        :param query_db: orm对象
        :param report_id: 报告ID
        :return: 报告情感统计详情
        """
        query = select(ReportEmotionStatisticsDO).where(
            ReportEmotionStatisticsDO.report_id == report_id
        )
        result = await query_db.execute(query)
        return result.scalar_one_or_none()

    @classmethod
    async def add_report_emotion_statistics(
        cls,
        query_db: AsyncSession,
        statistics_data: CreateReportEmotionStatisticsModel,
        user_id: Optional[int] = None
    ) -> ReportEmotionStatisticsDO:
        """
        新增报告情感统计
        
        :param query_db: orm对象
        :param statistics_data: 统计数据
        :param user_id: 用户ID
        :return: 新增的统计记录
        """
        new_statistics = ReportEmotionStatisticsDO(
            report_id=statistics_data.report_id,
            scheme_id=statistics_data.scheme_id,
            positive_count=statistics_data.positive_count,
            neutral_count=statistics_data.neutral_count,
            negative_count=statistics_data.negative_count,
            statistics_start_time=statistics_data.statistics_start_time,
            statistics_end_time=statistics_data.statistics_end_time,
            data_source=statistics_data.data_source,
            analysis_keywords=statistics_data.analysis_keywords,
            report_type=statistics_data.report_type,
            remark=statistics_data.remark,
            create_by=str(user_id) if user_id else 'system',
            update_by=str(user_id) if user_id else 'system'
        )
        
        # 自动计算总数和占比
        new_statistics.update_statistics(
            statistics_data.positive_count,
            statistics_data.neutral_count,
            statistics_data.negative_count
        )
        
        query_db.add(new_statistics)
        await query_db.flush()
        await query_db.refresh(new_statistics)
        return new_statistics

    @classmethod
    async def edit_report_emotion_statistics(
        cls,
        query_db: AsyncSession,
        statistics_data: UpdateReportEmotionStatisticsModel,
        report_id: str,
        user_id: Optional[int] = None
    ) -> int:
        """
        编辑报告情感统计
        
        :param query_db: orm对象
        :param statistics_data: 统计数据
        :param report_id: 报告ID
        :param user_id: 用户ID
        :return: 影响行数
        """
        # 获取现有记录
        existing_statistics = await cls.get_report_emotion_statistics_detail(query_db, report_id)
        if not existing_statistics:
            return 0
        
        # 更新字段
        update_data = {}
        
        if statistics_data.positive_count is not None:
            update_data['positive_count'] = statistics_data.positive_count
        
        if statistics_data.neutral_count is not None:
            update_data['neutral_count'] = statistics_data.neutral_count
        
        if statistics_data.negative_count is not None:
            update_data['negative_count'] = statistics_data.negative_count
        
        if statistics_data.statistics_start_time is not None:
            update_data['statistics_start_time'] = statistics_data.statistics_start_time
        
        if statistics_data.statistics_end_time is not None:
            update_data['statistics_end_time'] = statistics_data.statistics_end_time
        
        if statistics_data.data_source is not None:
            update_data['data_source'] = statistics_data.data_source
        
        if statistics_data.analysis_keywords is not None:
            update_data['analysis_keywords'] = statistics_data.analysis_keywords
        
        if statistics_data.report_type is not None:
            update_data['report_type'] = statistics_data.report_type
        
        if statistics_data.remark is not None:
            update_data['remark'] = statistics_data.remark
        
        # 更新记录
        for key, value in update_data.items():
            setattr(existing_statistics, key, value)
        
        # 如果情感数量有更新，重新计算占比
        if any(key in update_data for key in ['positive_count', 'neutral_count', 'negative_count']):
            existing_statistics.update_statistics(
                existing_statistics.positive_count,
                existing_statistics.neutral_count,
                existing_statistics.negative_count
            )
        
        existing_statistics.update_by = str(user_id) if user_id else 'system'
        existing_statistics.update_time = datetime.now()
        
        await query_db.flush()
        return 1

    @classmethod
    async def delete_report_emotion_statistics(
        cls,
        query_db: AsyncSession,
        report_ids: List[str]
    ) -> int:
        """
        删除报告情感统计
        
        :param query_db: orm对象
        :param report_ids: 报告ID列表
        :return: 删除的记录数
        """
        query = select(ReportEmotionStatisticsDO).where(
            ReportEmotionStatisticsDO.report_id.in_(report_ids)
        )
        result = await query_db.execute(query)
        statistics_to_delete = result.scalars().all()
        
        for statistics in statistics_to_delete:
            await query_db.delete(statistics)
        
        await query_db.flush()
        return len(statistics_to_delete)

    @classmethod
    async def get_report_emotion_statistics_count(
        cls,
        query_db: AsyncSession,
        query_object: ReportEmotionStatisticsPageQueryModel
    ) -> int:
        """
        获取报告情感统计总数
        
        :param query_db: orm对象
        :param query_object: 查询参数对象
        :return: 总数
        """
        query = select(func.count(ReportEmotionStatisticsDO.id))
        
        # 构建查询条件（与列表查询相同）
        conditions = []
        
        if query_object.report_id:
            conditions.append(ReportEmotionStatisticsDO.report_id.like(f'%{query_object.report_id}%'))
        
        if query_object.scheme_id:
            conditions.append(ReportEmotionStatisticsDO.scheme_id == query_object.scheme_id)
        
        if query_object.report_type:
            conditions.append(ReportEmotionStatisticsDO.report_type == query_object.report_type)
        
        if query_object.data_source:
            conditions.append(ReportEmotionStatisticsDO.data_source.like(f'%{query_object.data_source}%'))
        
        if query_object.status:
            conditions.append(ReportEmotionStatisticsDO.status == query_object.status)
        
        if query_object.start_time:
            conditions.append(ReportEmotionStatisticsDO.create_time >= query_object.start_time)
        
        if query_object.end_time:
            conditions.append(ReportEmotionStatisticsDO.create_time <= query_object.end_time)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await query_db.execute(query)
        return result.scalar()

    @classmethod
    async def check_report_id_exists(
        cls,
        query_db: AsyncSession,
        report_id: str,
        exclude_id: Optional[int] = None
    ) -> bool:
        """
        检查报告ID是否存在
        
        :param query_db: orm对象
        :param report_id: 报告ID
        :param exclude_id: 排除的记录ID
        :return: 是否存在
        """
        query = select(func.count(ReportEmotionStatisticsDO.id)).where(
            ReportEmotionStatisticsDO.report_id == report_id
        )
        
        if exclude_id:
            query = query.where(ReportEmotionStatisticsDO.id != exclude_id)
        
        result = await query_db.execute(query)
        count = result.scalar()
        return count > 0

    @classmethod
    async def get_emotion_statistics_by_scheme(
        cls,
        query_db: AsyncSession,
        scheme_id: int,
        days: int = 30
    ) -> List[ReportEmotionStatisticsDO]:
        """
        根据方案ID获取情感统计数据

        :param query_db: orm对象
        :param scheme_id: 方案ID
        :param days: 天数
        :return: 情感统计列表
        """
        start_date = datetime.now() - timedelta(days=days)

        query = select(ReportEmotionStatisticsDO).where(
            and_(
                ReportEmotionStatisticsDO.scheme_id == scheme_id,
                ReportEmotionStatisticsDO.create_time >= start_date,
                ReportEmotionStatisticsDO.status == '0'
            )
        ).order_by(desc(ReportEmotionStatisticsDO.create_time))

        result = await query_db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_emotion_trend_data(
        cls,
        query_db: AsyncSession,
        scheme_id: Optional[int] = None,
        days: int = 7
    ) -> List[Dict[str, Any]]:
        """
        获取情感趋势数据

        :param query_db: orm对象
        :param scheme_id: 方案ID（可选）
        :param days: 天数
        :return: 趋势数据列表
        """
        start_date = datetime.now() - timedelta(days=days)

        # 构建查询
        query = select(
            func.date(ReportEmotionStatisticsDO.create_time).label('date'),
            func.sum(ReportEmotionStatisticsDO.positive_count).label('positive_count'),
            func.sum(ReportEmotionStatisticsDO.neutral_count).label('neutral_count'),
            func.sum(ReportEmotionStatisticsDO.negative_count).label('negative_count'),
            func.sum(ReportEmotionStatisticsDO.total_count).label('total_count')
        ).where(
            and_(
                ReportEmotionStatisticsDO.create_time >= start_date,
                ReportEmotionStatisticsDO.status == '0'
            )
        )

        if scheme_id:
            query = query.where(ReportEmotionStatisticsDO.scheme_id == scheme_id)

        query = query.group_by(func.date(ReportEmotionStatisticsDO.create_time)).order_by('date')

        result = await query_db.execute(query)
        rows = result.fetchall()

        return [
            {
                'date': row.date.strftime('%Y-%m-%d'),
                'positive_count': int(row.positive_count or 0),
                'neutral_count': int(row.neutral_count or 0),
                'negative_count': int(row.negative_count or 0),
                'total_count': int(row.total_count or 0)
            }
            for row in rows
        ]

    @classmethod
    async def get_emotion_summary_by_type(
        cls,
        query_db: AsyncSession,
        report_type: str = 'normal',
        days: int = 30
    ) -> Dict[str, Any]:
        """
        根据报告类型获取情感统计摘要

        :param query_db: orm对象
        :param report_type: 报告类型
        :param days: 天数
        :return: 统计摘要
        """
        start_date = datetime.now() - timedelta(days=days)

        query = select(
            func.sum(ReportEmotionStatisticsDO.positive_count).label('total_positive'),
            func.sum(ReportEmotionStatisticsDO.neutral_count).label('total_neutral'),
            func.sum(ReportEmotionStatisticsDO.negative_count).label('total_negative'),
            func.sum(ReportEmotionStatisticsDO.total_count).label('total_count'),
            func.count(ReportEmotionStatisticsDO.id).label('report_count')
        ).where(
            and_(
                ReportEmotionStatisticsDO.report_type == report_type,
                ReportEmotionStatisticsDO.create_time >= start_date,
                ReportEmotionStatisticsDO.status == '0'
            )
        )

        result = await query_db.execute(query)
        row = result.fetchone()

        if not row or not row.total_count:
            return {
                'total_count': 0,
                'positive_count': 0,
                'neutral_count': 0,
                'negative_count': 0,
                'positive_percentage': 0.0,
                'neutral_percentage': 0.0,
                'negative_percentage': 0.0,
                'report_count': 0
            }

        total_positive = int(row.total_positive or 0)
        total_neutral = int(row.total_neutral or 0)
        total_negative = int(row.total_negative or 0)
        total_count = int(row.total_count or 0)

        return {
            'total_count': total_count,
            'positive_count': total_positive,
            'neutral_count': total_neutral,
            'negative_count': total_negative,
            'positive_percentage': round((total_positive / total_count) * 100, 2) if total_count > 0 else 0.0,
            'neutral_percentage': round((total_neutral / total_count) * 100, 2) if total_count > 0 else 0.0,
            'negative_percentage': round((total_negative / total_count) * 100, 2) if total_count > 0 else 0.0,
            'report_count': int(row.report_count or 0)
        }
