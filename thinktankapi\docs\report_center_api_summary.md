# 报告中心API接口总结

## 已创建的API接口

### 方案管理接口
1. `GET /report/scheme/list` - 获取方案列表（分页）
2. `GET /report/scheme/{scheme_id}` - 获取方案详情
3. `GET /report/scheme-type/list` - 获取方案类型列表
4. `GET /report/menu/categories` - 获取侧边栏菜单分类数据
5. `POST /report/scheme` - 新增方案
6. `PUT /report/scheme` - 编辑方案
7. `DELETE /report/scheme` - 删除方案

### 报告模板管理接口
1. `GET /report/template/list` - 获取模板列表（分页）
2. `GET /report/template/{template_id}` - 获取模板详情
3. `GET /report/template/type/{template_type}` - 根据类型获取模板列表
4. `POST /report/template` - 新增模板
5. `PUT /report/template` - 编辑模板
6. `DELETE /report/template` - 删除模板

## 数据库表结构

### 已使用的表
- `scheme` - 方案表
- `scheme_type` - 方案类型表
- `scheme_config` - 方案配置表
- `scheme_summary_statistic` - 方案统计表
- `report_template` - 报告模板表（新创建）

### 关键字段映射
- 方案名称: `scheme.name`
- 创建时间: `scheme.create_time`
- 配置类型: `scheme_type.name`
- 状态: `scheme.status`
- 报告状态: `scheme.refresh_status`

## 前端集成要点

### 报告列表数据获取
```javascript
// 替换现有的 fetchReportList 方法
async fetchReportList() {
  const response = await this.$http.get('/report/scheme/list', {
    params: {
      pageNum: this.currentPage,
      pageSize: this.pageSize,
      searchKeyword: this.searchKeyword
    }
  });
  this.reportList = response.data.data.records;
  this.total = response.data.data.total;
}
```

### 侧边栏菜单数据获取
```javascript
// 替换现有的菜单数据
async fetchMenuCategories() {
  const response = await this.$http.get('/report/menu/categories');
  this.menuCategories = response.data.data;
}
```

### 模板列表数据获取
```javascript
// 替换现有的模板数据
async fetchTemplateList() {
  const response = await this.$http.get('/report/template/list', {
    params: {
      templateType: this.templateType,
      pageNum: this.currentPage,
      pageSize: this.pageSize
    }
  });
  this.templateList = response.data.data.records;
}
```

## 关键词设置数据存储

基于您选择的关键词设置代码，关键词数据存储在以下位置：

### 关键词设置接口
1. `GET /report/scheme/{scheme_id}/keywords` - 获取方案关键词设置
2. `PUT /report/scheme/{scheme_id}/keywords` - 更新方案关键词设置

### 数据库表字段
- `warning_settings.allow_words` - 存储允许词（allowWords）
- `warning_settings.reject_words` - 存储拒绝词（rejectWords）
- 通过 `warning_settings.scheme_id` 关联到具体的方案

### 前端数据绑定
```javascript
// 关键词设置数据结构
keywordSettings: {
  allowWords: '', // 对应 warning_settings.allow_words
  rejectWords: '' // 对应 warning_settings.reject_words
}
```

### API接口支持
关键词设置通过专门的接口进行管理：
- 获取关键词设置：`GET /report/scheme/{scheme_id}/keywords`
- 更新关键词设置：`PUT /report/scheme/{scheme_id}/keywords`

### 前端集成示例
```javascript
// 获取关键词设置
async getKeywordSettings(schemeId) {
  const response = await this.$http.get(`/report/scheme/${schemeId}/keywords`);
  this.keywordSettings = response.data.data;
}

// 保存关键词设置
async saveKeywordSettings(schemeId) {
  const response = await this.$http.put(`/report/scheme/${schemeId}/keywords`, {
    allowWords: this.keywordSettings.allowWords,
    rejectWords: this.keywordSettings.rejectWords
  });
  if (response.data.code === 200) {
    this.$message.success('关键词设置保存成功');
  }
}
```
