from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from typing import Any, Optional


class PageQueryModel(BaseModel):
    """
    分页查询基础模型
    """
    model_config = ConfigDict()  # 移除别名生成器，直接使用下划线命名

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')

    @classmethod
    def as_query(cls, page_num: int = 1, page_size: int = 10, **kwargs):
        """
        作为查询参数的工厂方法
        """
        return cls(page_num=page_num, page_size=page_size, **kwargs)


class CrudResponseModel(BaseModel):
    """
    操作响应模型
    """

    is_success: bool = Field(description='操作是否成功')
    message: str = Field(description='响应信息')
    result: Optional[Any] = Field(default=None, description='响应结果')


class UploadResponseModel(BaseModel):
    """
    上传响应模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    file_name: Optional[str] = Field(default=None, description='新文件映射路径')
    new_file_name: Optional[str] = Field(default=None, description='新文件名称')
    original_filename: Optional[str] = Field(default=None, description='原文件名称')
    url: Optional[str] = Field(default=None, description='新文件url')
