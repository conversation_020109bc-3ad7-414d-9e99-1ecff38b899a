-- 确保 opinion_task 表有 push_url 字段（如果不存在则添加）
-- 这个脚本是幂等的，可以安全地重复执行

-- 检查并添加 push_url 字段（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'opinion_task' 
        AND column_name = 'push_url'
    ) THEN
        ALTER TABLE opinion_task 
        ADD COLUMN push_url VARCHAR(1000) NULL COMMENT '推送URL';
        
        RAISE NOTICE 'Added push_url column to opinion_task table';
    ELSE
        RAISE NOTICE 'push_url column already exists in opinion_task table';
    END IF;
END $$;

-- 检查并添加 push_config 字段（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'opinion_task' 
        AND column_name = 'push_config'
    ) THEN
        ALTER TABLE opinion_task 
        ADD COLUMN push_config TEXT NULL COMMENT '推送配置（JSON格式）';
        
        RAISE NOTICE 'Added push_config column to opinion_task table';
    ELSE
        RAISE NOTICE 'push_config column already exists in opinion_task table';
    END IF;
END $$;

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_opinion_task_push_url ON opinion_task(push_url);
CREATE INDEX IF NOT EXISTS idx_opinion_task_requirement_push ON opinion_task(requirement_id, push_url);

-- 添加注释说明
COMMENT ON COLUMN opinion_task.push_url IS '推送URL - 报告推送的目标地址';
COMMENT ON COLUMN opinion_task.push_config IS '推送配置 - JSON格式的推送相关配置信息';
