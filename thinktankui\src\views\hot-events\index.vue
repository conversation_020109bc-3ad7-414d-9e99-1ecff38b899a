<template>
  <div class="hot-events-container">
    <!-- 顶部标签页 -->
    <div class="tabs-header">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="热点地图" name="map"></el-tab-pane>
        <el-tab-pane label="三方热点" name="third-party"></el-tab-pane>
        <el-tab-pane label="七日热点" name="seven-days"></el-tab-pane>
        <el-tab-pane label="平台" name="platform"></el-tab-pane>
        <el-tab-pane label="主题" name="theme"></el-tab-pane>
      </el-tabs>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧地图区域 -->
      <div class="map-section">
        <div class="map-container">
          <div class="china-map">
            <div class="map-placeholder">
              <i class="el-icon-location"></i>
              <p>中国热点地图</p>
              <p class="map-note">地图功能开发中...</p>
            </div>
          </div>
          <!-- 地图图例 -->
          <div class="map-legend">
            <div class="legend-item">
              <span class="legend-color" style="background: #8B0000;"></span>
              <span>≥ 500000</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" style="background: #CD5C5C;"></span>
              <span>100000 - 500000</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" style="background: #F08080;"></span>
              <span>50000 - 100000</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" style="background: #FFA07A;"></span>
              <span>10000 - 50000</span>
            </div>
            <div class="legend-item">
              <span class="legend-color" style="background: #FFE4E1;"></span>
              <span>0 - 10000</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧热点列表 -->
      <div class="hotlist-section">
        <div class="hotlist-header">
          <h3>昨日热点事件TOP10</h3>
        </div>
        <div class="hotlist-content">
          <div v-if="loading" class="loading-container">
            <i class="el-icon-loading"></i>
            <span>加载中...</span>
          </div>
          <div
            v-else
            v-for="(item, index) in hotEventsList"
            :key="index"
            class="hot-item"
            :class="{ 'top-three': index < 3 }"
          >
            <div class="rank-number">{{ index + 1 }}</div>
            <div class="event-content">
              <div class="event-title">{{ item.title }}</div>
              <div class="event-heat">{{ item.heat }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部详情区域 -->
    <div class="details-section">
      <div class="detail-card">
        <div class="card-header">
          <h4>全国热点</h4>
        </div>
        <div class="card-content" v-if="detailsData && detailsData.nationalHot">
          <div class="event-item">
            <div class="event-icon">{{ detailsData.nationalHot.icon || '热' }}</div>
            <div class="event-info">
              <div class="event-title">{{ detailsData.nationalHot.title }}</div>
              <div class="event-time">{{ detailsData.nationalHot.time }}</div>
            </div>
          </div>
          <div class="event-description">
            {{ detailsData.nationalHot.description }}
          </div>
        </div>
        <div class="card-content" v-else>
          <div class="event-item">
            <div class="event-icon">热</div>
            <div class="event-info">
              <div class="event-title">商务部回应欧盟对华人造板反倾销立案调查：中方对此表示强烈不满和坚决反对</div>
              <div class="event-time">2023-04-29</div>
            </div>
          </div>
          <div class="event-description">
            针对欧盟对华人造板反倾销立案调查，中方表示强烈不满和坚决反对。商务部表示，中国人造板产业发展健康，出口产品质量优良，价格公平合理。
          </div>
        </div>
      </div>

      <div class="detail-card">
        <div class="card-header">
          <h4>北京市热点</h4>
          <el-button type="text" size="small">查看详情</el-button>
        </div>
        <div class="card-content" v-if="detailsData && detailsData.regionalHot">
          <div class="event-item">
            <div class="event-icon">{{ detailsData.regionalHot.icon || '京' }}</div>
            <div class="event-info">
              <div class="event-title">{{ detailsData.regionalHot.title }}</div>
              <div class="event-time">{{ detailsData.regionalHot.time }}</div>
            </div>
          </div>
          <div class="event-description">
            {{ detailsData.regionalHot.description }}
          </div>
        </div>
        <div class="card-content" v-else>
          <div class="event-item">
            <div class="event-icon">京</div>
            <div class="event-info">
              <div class="event-title">北京海淀法院："老赖"被限，法院强制执行，追回欠款</div>
              <div class="event-time">2023-04-29</div>
            </div>
          </div>
          <div class="event-description">
            北京海淀法院近日成功执行一起债务纠纷案件，通过限制被执行人消费等强制措施，成功追回欠款，维护了当事人合法权益。
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSimpleHotEventsList, getSimpleHotEventsMap, getSimpleHotEventsDetails, getHotEventsByCategory } from '@/api/hot-events'

export default {
  name: 'HotEvents',
  data() {
    return {
      activeTab: 'map',
      hotEventsList: [],
      mapData: null,
      detailsData: null,
      loading: false
    }
  },
  mounted() {
    this.loadHotEventsList()
    this.loadMapData()
    this.loadDetailsData()
  },
  methods: {
    async loadHotEventsList() {
      try {
        this.loading = true
        const response = await getSimpleHotEventsList()
        if (response.code === 200 && response.data) {
          this.hotEventsList = response.data
        }
      } catch (error) {
        console.error('加载热点事件列表失败:', error)
        // 保持使用默认的硬编码数据
        this.hotEventsList = [
          { title: '我军在台海进行实战化演练，展现维护国家主权决心', heat: '20196' },
          { title: '马云现身，何时归来？', heat: '19640' },
          { title: '全国疫情（2023年4月29日）', heat: '12840' },
          { title: '俄军打击乌克兰目标，乌方称遭受重大损失', heat: '11480' },
          { title: '今天一定要重点关注的', heat: '11100' },
          { title: '到底是什么原因呢！', heat: '9950' },
          { title: '一条微博引发上千万网友热议', heat: '9740' },
          { title: '新，俄乌冲突最新进展：乌克兰称击退俄军一次攻击', heat: '9410' },
          { title: '让我们共同关注这个事件，事关每个人', heat: '8550' },
          { title: '国际军事专家分析当前局势，称局势仍在发展', heat: '7980' }
        ]
      } finally {
        this.loading = false
      }
    },
    async loadMapData() {
      try {
        const response = await getSimpleHotEventsMap()
        if (response.code === 200 && response.data) {
          this.mapData = response.data
        }
      } catch (error) {
        console.error('加载地图数据失败:', error)
      }
    },
    async loadDetailsData() {
      try {
        // 使用默认事件ID 1 来获取详情数据
        const response = await getSimpleHotEventsDetails(1)
        if (response.code === 200 && response.data) {
          this.detailsData = response.data
        }
      } catch (error) {
        console.error('加载详情数据失败:', error)
      }
    },
    async handleTabClick(tab) {
      console.log('切换标签页:', tab.name)

      // 根据不同标签页加载不同数据
      if (tab.name === 'third-party') {
        await this.loadCategoryData('third-party')
      } else if (tab.name === 'seven-days') {
        await this.loadCategoryData('seven-days')
      } else if (tab.name === 'platform') {
        await this.loadCategoryData('platform')
      } else if (tab.name === 'theme') {
        await this.loadCategoryData('theme')
      }
    },
    async loadCategoryData(category) {
      try {
        this.loading = true
        // 修复参数传递方式，category作为路径参数，查询参数作为第二个参数
        const response = await getHotEventsByCategory(category, {})
        if (response.code === 200 && response.data) {
          // 更新对应分类的数据
          console.log(`${category}数据:`, response.data)
        }
      } catch (error) {
        console.error(`加载${category}数据失败:`, error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.hot-events-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.tabs-header {
  background: white;
  border-radius: 8px;
  padding: 0 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.main-content {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.map-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.map-container {
  position: relative;
  height: 400px;
}

.china-map {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ddd;
}

.map-placeholder {
  text-align: center;
  color: #666;
  font-size: 16px;
  line-height: 1.5;
}

.map-placeholder i {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 10px;
  display: block;
}

.map-placeholder p {
  margin: 5px 0;
}

.map-note {
  font-size: 14px;
  color: #999;
}

.map-legend {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(255,255,255,0.9);
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-color {
  width: 16px;
  height: 12px;
  margin-right: 8px;
  border-radius: 2px;
}

.hotlist-section {
  width: 350px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.hotlist-header h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.hot-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.hot-item:last-child {
  border-bottom: none;
}

.rank-number {
  width: 24px;
  height: 24px;
  background: #f0f0f0;
  color: #666;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
  flex-shrink: 0;
}

.top-three .rank-number {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  color: white;
}

.event-content {
  flex: 1;
}

.event-title {
  font-size: 13px;
  color: #333;
  line-height: 1.4;
  margin-bottom: 4px;
}

.event-heat {
  font-size: 12px;
  color: #ff6b6b;
  font-weight: bold;
}

.details-section {
  display: flex;
  gap: 20px;
}

.detail-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.card-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.event-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}

.event-icon {
  width: 32px;
  height: 32px;
  background: #ff6b6b;
  color: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
  flex-shrink: 0;
}

.event-info {
  flex: 1;
}

.event-info .event-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
}

.event-time {
  font-size: 12px;
  color: #999;
}

.event-description {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-top: 10px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #666;
  font-size: 14px;
}

.loading-container i {
  margin-right: 8px;
  font-size: 16px;
}
</style>
