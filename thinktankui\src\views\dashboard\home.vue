<template>
  <div class="home-dashboard">
    <!-- 用户信息卡片 -->
    <div class="user-info-card" v-loading="user_loading">
      <div class="user-info-left">
        <div class="user-avatar">
          <span>{{ getAvatarText() }}</span>
        </div>
        <div class="user-details">
          <h3>{{ user_info.nick_name || user_info.user_name || 'demo_user' }}</h3>
          <p>{{ user_info.email || '<EMAIL>' }}</p>
        </div>
      </div>
      <div class="user-info-right">
        <span class="member-tag">{{ membership_info.membership_status || '专业版会员' }}</span>
        <span class="expire-date" v-if="membership_info.expire_time">
          到期时间：{{ formatDate(membership_info.expire_time) }}
        </span>
        <span class="expire-date" v-else>
          到期时间：2024-08-15
        </span>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-cards" v-loading="stats_loading">
      <div class="stat-card">
        <div class="stat-icon blue">
          <i class="el-icon-data-analysis"></i>
        </div>
        <div class="stat-content">
          <div class="stat-title">套餐总次数</div>
          <div class="stat-value">{{ formatPackageLimit(user_stats.package_limit)  }}</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon green">
          <i class="el-icon-check"></i>
        </div>
        <div class="stat-content">
          <div class="stat-title">今日分析</div>
          <div class="stat-value">{{ user_stats.today_analysis || 0 }}</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon orange">
          <i class="el-icon-time"></i>
        </div>
        <div class="stat-content">
          <div class="stat-title">已用次数</div>
          <div class="stat-value">{{ user_stats.total_analysis || 0 }}</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon red">
          <i class="el-icon-edit"></i>
        </div>
        <div class="stat-content">
          <div class="stat-title">剩余次数</div>
          <div class="stat-value">{{ formatRemainingCount(user_stats.remaining_count) }}</div>
        </div>
      </div>
    </div>

    <!-- 使用情况 -->
    <div class="usage-section">
      <div class="usage-header">
        <h3>使用情况</h3>
        <span class="usage-text">分析次数使用情况</span>
        <span class="usage-ratio">{{ user_stats.total_analysis || 0 }}/{{ formatPackageLimit(user_stats.package_limit) }}</span>
      </div>
      <div class="usage-progress">
        <el-progress
          :percentage="getUsagePercentage()"
          :show-text="false"
          :stroke-width="8"
          color="#1890ff">
        </el-progress>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="quick-actions-header">
        <h3>快速操作</h3>
        <div class="remaining-info" v-if="!isUnlimitedPackage">
          <span class="remaining-text">剩余次数：</span>
          <span class="remaining-count" :class="{ 'warning': isLowRemaining, 'danger': isNoRemaining }">
            {{ formatRemainingCount(user_stats.remaining_count) }}
          </span>
        </div>
      </div>
      <div class="action-buttons">
        <!-- 开始分析按钮 -->
        <el-tooltip
          :content="isNoRemaining ? '套餐次数已用完，请升级套餐后继续使用' : ''"
          :disabled="!isNoRemaining"
          placement="top">
          <el-button
            type="primary"
            size="large"
            :disabled="isNoRemaining"
            :class="{ 'disabled-button': isNoRemaining }"
            @click="startAnalysis">
            开始分析
          </el-button>
        </el-tooltip>

        <!-- 查看报告按钮 -->
        <el-button type="default" size="large" @click="viewReports">查看报告</el-button>

        <!-- 升级套餐按钮 -->
        <el-button
          :type="isLowRemaining || isNoRemaining ? 'warning' : 'default'"
          size="large"
          :class="{ 'highlight-upgrade': isLowRemaining || isNoRemaining }"
          @click="upgradePackage">
          <i class="el-icon-top-right" v-if="isLowRemaining || isNoRemaining"></i>
          升级套餐
        </el-button>

        <!-- 下载数据按钮 -->
        <el-tooltip
          :content="isNoRemaining ? '套餐次数已用完，请升级套餐后继续使用' : ''"
          :disabled="!isNoRemaining"
          placement="top">
          <el-button
            type="default"
            size="large"
            :disabled="isNoRemaining"
            :class="{ 'disabled-button': isNoRemaining }"
            @click="downloadData">
            下载数据
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <h3>最近活动</h3>
      <div class="activity-list">
        <div
          v-for="record in recent_analysis_records"
          :key="record.id"
          class="activity-item"
          @click="viewAnalysisDetail(record)"
        >
          <div class="activity-icon">
            <i class="el-icon-data-analysis"></i>
          </div>
          <div class="activity-content">
            <div class="activity-title">完成舆情分析：{{ record.requirement_name || '品牌监测调研报告' }}</div>
            <div class="activity-time">{{ formatTime(record.create_time) }}</div>
          </div>
        </div>
        <!-- 默认显示三条活动记录 -->
        <template v-if="recent_analysis_records.length === 0">
          <div class="activity-item">
            <div class="activity-icon">
              <i class="el-icon-data-analysis"></i>
            </div>
            <div class="activity-content">
              <div class="activity-title">完成舆情分析：品牌监测调研报告</div>
              <div class="activity-time">2小时前</div>
            </div>
          </div>
          <div class="activity-item">
            <div class="activity-icon">
              <i class="el-icon-data-analysis"></i>
            </div>
            <div class="activity-content">
              <div class="activity-title">完成舆情分析：竞品分析报告</div>
              <div class="activity-time">5小时前</div>
            </div>
          </div>
          <div class="activity-item">
            <div class="activity-icon">
              <i class="el-icon-data-analysis"></i>
            </div>
            <div class="activity-content">
              <div class="activity-title">完成舆情分析：市场趋势分析</div>
              <div class="activity-time">1天前</div>
            </div>
          </div>
        </template>
      </div>
    </div>




  </div>
</template>

<script>
import { getRecentAnalysisRecords, getUserDashboardInfo, getDashboardStatistics, getCurrentMonthAnalysisCount } from '@/api/dashboard'

export default {
  name: 'HomeDashboard',
  data() {
    return {
      user_info: {
        user_name: '',
        nick_name: '',
        email: ''
      },
      membership_info: {
        membership_status: '',
        expire_time: ''
      },
      user_stats: {
        total_analysis: 0,
        today_analysis: 0,
        remaining_count: 0,
        today_remaining: 0,
        package_limit: 0,
        package_name: '基础版',
        usage_percentage: 0.0
      },
      recent_analysis_records: [],
      loading: false,
      user_loading: false,
      stats_loading: false
    }
  },
  computed: {
    // 判断是否为无限制套餐
    isUnlimitedPackage() {
      return this.user_stats.package_limit === -1
    },

    // 判断是否没有剩余次数
    isNoRemaining() {
      if (this.isUnlimitedPackage) return false
      return this.user_stats.remaining_count <= 0
    },

    // 判断是否剩余次数较少（少于总数的20%或少于5次）
    isLowRemaining() {
      if (this.isUnlimitedPackage) return false
      const remaining = this.user_stats.remaining_count
      const limit = this.user_stats.package_limit

      if (remaining <= 0) return false // 已经没有了，不算低
      if (remaining <= 5) return true // 少于5次算低
      if (limit > 0 && (remaining / limit) <= 0.2) return true // 少于20%算低

      return false
    }
  },
  mounted() {
    this.loadUserInfo()
    this.loadAnalysisData()
    this.loadUserStats().then(() => {
      // 数据加载完成后检查套餐状态
      this.$nextTick(() => {
        this.checkPackageStatus()
      })
    })
  },
  methods: {
    getAvatarText() {
      const name = this.user_info.nick_name || this.user_info.user_name || this.$store.getters.name || 'D'
      return name.charAt(0).toUpperCase()
    },

    getUsagePercentage() {
      const packageLimit = this.user_stats.package_limit || 0
      const used = this.user_stats.total_analysis || 0

      // 无限制套餐时，显示0%
      if (packageLimit < 0) return 0
      // 套餐限制为0时，显示0%
      if (packageLimit === 0) return 0

      // 计算使用百分比，最大100%
      return Math.min(100, Math.round((used / packageLimit) * 100))
    },

    formatPackageLimit(limit) {
      // 格式化套餐限制显示
      if (limit === null || limit === undefined) {
        return '未知'
      }
      if (limit < 0) return '无限制'
      return limit.toString()
    },

    formatRemainingCount(remaining) {
      // 格式化剩余次数显示
      if (remaining === null || remaining === undefined) return '未知'
      if (remaining < 0) return '无限制'
      return remaining.toString()
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    },

    async loadUserInfo() {
      this.user_loading = true
      try {
        // 从 store 获取用户信息
        const currentUserId = this.$store.getters.id || this.$store.state.user.id

        if (!currentUserId) {
          console.warn('无法获取当前用户ID，使用默认数据')
          this.user_info = {
            user_name: this.$store.getters.name || 'demo_user',
            nick_name: '',
            email: '<EMAIL>'
          }
          this.membership_info = {
            membership_status: '专业版会员',
            expire_time: '2024-08-15'
          }
          return
        }

        // 调用API获取用户信息和会员信息
        const response = await getUserDashboardInfo(currentUserId)
        if (response.code === 200) {
          this.user_info = response.data.user_info || {
            user_name: this.$store.getters.name || 'demo_user',
            nick_name: '',
            email: '<EMAIL>'
          }
          this.membership_info = response.data.membership_info || {
            membership_status: '专业版会员',
            expire_time: '2024-08-15'
          }
        } else {
          // 使用默认数据
          this.user_info = {
            user_name: this.$store.getters.name || 'demo_user',
            nick_name: '',
            email: '<EMAIL>'
          }
          this.membership_info = {
            membership_status: '专业版会员',
            expire_time: '2024-08-15'
          }
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
        // 使用默认数据
        this.user_info = {
          user_name: this.$store.getters.name || 'demo_user',
          nick_name: '',
          email: '<EMAIL>'
        }
        this.membership_info = {
          membership_status: '专业版会员',
          expire_time: '2024-08-15'
        }
      } finally {
        this.user_loading = false
      }
    },

    async loadUserStats() {
      try {
        this.stats_loading = true
        const currentUserId = this.$store.getters.id || this.$store.state.user.id

        if (!currentUserId) {
          console.warn('无法获取当前用户ID，使用默认统计数据')
          return
        }

        // 调用统计API获取用户数据
        const response = await getDashboardStatistics(currentUserId)

        if (response.code === 200) {
          // 更新用户统计数据
          this.user_stats = {
            total_analysis: response.data.total_analysis || 0,
            today_analysis: response.data.today_analysis || 0,
            remaining_count: response.data.remaining_count || 0,
            today_remaining: response.data.today_remaining || 0,
            package_limit: response.data.package_limit || 0,
            package_name: response.data.package_name || '基础版',
            usage_percentage: response.data.usage_percentage || 0
          }

          console.log('API响应数据:', response.data)
          console.log('用户统计数据加载成功:', this.user_stats)
          console.log('package_limit值:', this.user_stats.package_limit)

          // 检查是否需要使用备用计算逻辑
          await this.checkAndApplyFallbackAnalysisCount(currentUserId)
        } else {
          console.warn('获取用户统计数据失败，使用默认数据')
          // 确保即使API失败也有默认值
          this.user_stats = {
            total_analysis: 0,
            today_analysis: 0,
            remaining_count: 0,
            today_remaining: 0,
            package_limit: 0,
            package_name: '基础版',
            usage_percentage: 0.0
          }
        }
      } catch (error) {
        console.error('获取用户统计数据失败:', error)
        this.$message.error('获取统计数据失败')
        // 异常情况下也设置默认值
        this.user_stats = {
          total_analysis: 0,
          today_analysis: 0,
          remaining_count: 0,
          today_remaining: 0,
          package_limit: 0,
          package_name: '基础版',
          usage_percentage: 0.0
        }
      } finally {
        this.stats_loading = false
      }
    },

    async loadAnalysisData() {
      try {
        this.loading = true
        // 加载最近的分析记录
        await this.loadRecentAnalysisRecords()
      } catch (error) {
        console.error('加载分析数据失败:', error)
        this.$message.error('加载分析数据失败')
      } finally {
        this.loading = false
      }
    },

    // 快速操作方法
    startAnalysis() {
      // 检查套餐次数限制
      if (this.isNoRemaining) {
        this.$message.warning('套餐次数已用完，请升级套餐后继续使用')
        return
      }

      // 如果次数较少，给出提醒
      if (this.isLowRemaining) {
        this.$confirm(
          `您的套餐剩余次数较少（${this.user_stats.remaining_count}次），是否继续分析？`,
          '次数提醒',
          {
            confirmButtonText: '继续分析',
            cancelButtonText: '升级套餐',
            type: 'warning'
          }
        ).then(() => {
          this.$router.push('/opinion-analysis/opinion-analysis')
        }).catch(() => {
          this.upgradePackage()
        })
      } else {
        this.$router.push('/opinion-analysis/opinion-analysis')
      }
    },

    viewReports() {
      this.$router.push('/analyze-logs/analyze-logs')
    },

    upgradePackage() {
      // 跳转到套餐升级页面
      this.$router.push('/set-meal/set-meal')
    },

    downloadData() {
      // 检查套餐次数限制
      if (this.isNoRemaining) {
        this.$message.warning('套餐次数已用完，请升级套餐后继续使用')
        return
      }

      this.$message.info('下载数据功能开发中')
    },



    async loadRecentAnalysisRecords() {
      try {
        // 获取当前用户ID
        const currentUserId = this.$store.getters.id || this.$store.state.user.id

        if (!currentUserId) {
          console.warn('无法获取当前用户ID，跳过加载分析记录')
          this.recent_analysis_records = []
          return
        }

        // 调用真实的API获取最新分析记录，传递用户ID参数
        const response = await getRecentAnalysisRecords(3, currentUserId)
        if (response.code === 200 && response.records) {
          this.recent_analysis_records = response.records.map(record => ({
            id: record.id,
            requirement_name: record.requirement_name || '未命名需求',
            entity_keyword: record.entity_keyword || '无关键词',
            status: record.status || 'unknown',
            create_time: record.create_time ? new Date(record.create_time) : new Date(),
            positive_count: record.positive_count || 0,
            neutral_count: record.neutral_count || 0,
            negative_count: record.negative_count || 0,
            task_name: record.task_name || '未命名任务',
            progress_percentage: record.progress_percentage || 0,
            report_oss_url: record.report_oss_url || null
          }))
        } else {
          // 如果API调用失败，显示空数据（不显示其他用户的模拟数据）
          this.recent_analysis_records = []
          console.warn('获取分析记录失败，显示空数据')
        }
      } catch (error) {
        console.error('加载分析记录失败:', error)
        // 发生错误时显示空数据，确保数据安全
        this.recent_analysis_records = []
      }
    },

    getStatusText(status) {
      const statusMap = {
        'completed': '已完成',
        'running': '执行中',
        'processing': '分析中',
        'pending': '待处理',
        'failed': '失败',
        'cancelled': '已取消'
      }
      return statusMap[status] || '未知'
    },

    formatTime(time) {
      if (!time) return ''
      const now = new Date()
      const diff = now - new Date(time)
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

      if (hours > 0) {
        return `${hours}小时前`
      } else if (minutes > 0) {
        return `${minutes}分钟前`
      } else {
        return '刚刚'
      }
    },

    viewAllAnalysis() {
      this.$router.push('/analyze-logs/analyze-logs')
    },

    viewAnalysisDetail(record) {
      // 检查是否有报告OSS URL
      if (record.report_oss_url) {
        // 如果有OSS URL，直接在新窗口打开报告页面
        window.open(record.report_oss_url, '_blank')
      } else {
        // 如果没有OSS URL，显示暂无报告提示
        this.$message.info('该分析记录暂无报告')
      }
    },

    // 刷新用户统计数据
    async refreshUserStats() {
      try {
        await this.loadUserStats()
        console.log('用户统计数据已刷新')
      } catch (error) {
        console.error('刷新用户统计数据失败:', error)
      }
    },

    // 检查套餐状态并给出相应提示
    checkPackageStatus() {
      if (this.isNoRemaining) {
        this.$notify({
          title: '套餐提醒',
          message: '您的套餐次数已用完，请升级套餐以继续使用',
          type: 'warning',
          duration: 5000
        })
      } else if (this.isLowRemaining) {
        this.$notify({
          title: '套餐提醒',
          message: `您的套餐剩余次数较少（${this.user_stats.remaining_count}次），建议及时升级`,
          type: 'info',
          duration: 4000
        })
      }
    },

    // 检查并应用备用分析次数计算逻辑
    async checkAndApplyFallbackAnalysisCount(userId) {
      try {
        // 检查是否需要使用备用计算逻辑
        // 当 total_analysis 为空、未定义、或为0且用户有套餐时，触发备用查询
        const needsFallback = (
          this.user_stats.total_analysis === null ||
          this.user_stats.total_analysis === undefined ||
          (this.user_stats.total_analysis === 0 && this.user_stats.package_limit > 0)
        )

        if (needsFallback) {
          console.log('检测到需要使用备用计算逻辑，开始查询当前月份的分析记录数')

          // 调用备用API获取当前月份的分析记录数
          const fallbackResponse = await getCurrentMonthAnalysisCount(userId)

          if (fallbackResponse.code === 200) {
            const monthlyCount = fallbackResponse.data.monthly_count || 0
            console.log(`备用查询成功，当前月份分析记录数: ${monthlyCount}`)

            // 更新已用次数
            this.user_stats.total_analysis = monthlyCount

            // 重新计算剩余次数和使用百分比
            if (this.user_stats.package_limit > 0 && this.user_stats.package_limit !== -1) {
              this.user_stats.remaining_count = Math.max(0, this.user_stats.package_limit - monthlyCount)
              this.user_stats.usage_percentage = Math.round((monthlyCount / this.user_stats.package_limit) * 100)
            }

            console.log('备用计算完成，更新后的统计数据:', this.user_stats)
            this.$message.success('已使用备用统计方式更新分析次数')
          } else {
            console.warn('备用查询失败:', fallbackResponse.msg)
          }
        } else {
          console.log('无需使用备用计算逻辑，当前统计数据有效')
        }
      } catch (error) {
        console.error('备用计算逻辑执行失败:', error)
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.home-dashboard {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);
}

/* 用户信息卡片样式 */
.user-info-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.user-info-left {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #36cfc9;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.user-avatar span {
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.user-details h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #303133;
}

.user-details p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.user-info-right {
  text-align: right;
}

.member-tag {
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  display: block;
  margin-bottom: 8px;
}

.expire-date {
  font-size: 14px;
  color: #666;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;

  i {
    font-size: 28px;
    color: white;
  }

  &.blue {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  }

  &.green {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  }

  &.orange {
    background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
  }

  &.red {
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  }
}

.stat-content {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #333;
}

/* 使用情况 */
.usage-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.usage-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
    font-weight: 600;
    margin-right: 20px;
  }

  .usage-text {
    font-size: 14px;
    color: #666;
    flex: 1;
  }

  .usage-ratio {
    font-size: 14px;
    color: #333;
    font-weight: 500;
  }
}

.usage-progress {
  margin-top: 8px;
}

/* 快速操作 */
.quick-actions {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-actions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
    font-weight: 600;
  }
}

.remaining-info {
  display: flex;
  align-items: center;
  font-size: 14px;

  .remaining-text {
    color: #666;
    margin-right: 4px;
  }

  .remaining-count {
    font-weight: 600;
    color: #333;

    &.warning {
      color: #faad14;
    }

    &.danger {
      color: #ff4d4f;
    }
  }
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;

  .el-button {
    height: 40px;
    transition: all 0.3s ease;

    &.disabled-button {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &.highlight-upgrade {
      animation: pulse 2s infinite;
      box-shadow: 0 0 0 0 rgba(250, 173, 20, 0.7);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
      }
    }
  }
}

/* 升级套餐按钮脉冲动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(250, 173, 20, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(250, 173, 20, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(250, 173, 20, 0);
  }
}

/* 最近活动 */
.recent-activity {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #333;
    font-weight: 600;
  }
}

.activity-list {
  .activity-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f8f9fa;
    }
  }
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;

  i {
    font-size: 18px;
    color: white;
  }
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-buttons {
    grid-template-columns: repeat(2, 1fr);
  }

  .quick-actions-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .home-dashboard {
    padding: 15px;
  }

  .user-info-section {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }

  .user-avatar {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .user-status {
    text-align: left;
    margin-top: 15px;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }

  .quick-actions-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .remaining-info {
      font-size: 13px;
    }
  }
}


</style>
