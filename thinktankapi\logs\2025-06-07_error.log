2025-06-07 11:08:57.790 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-07 11:08:57.790 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-07 11:08:58.713 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-07 11:08:58.714 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-07 11:08:58.720 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-07 11:08:59.369 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-07 11:08:59.986 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-07 11:08:59.986 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-07 11:09:24.261 | ede7d3cda3924cc4aa66b0ba9dbcda49 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为0da963bf-7a2f-437d-b8ed-ea8c02be93e7的会话获取图片验证码成功
2025-06-07 11:09:34.835 | 83f1d7d316a944849ce247c5763214e2 | WARNING  | module_admin.service.login_service:authenticate_user:124 - 密码错误
2025-06-07 11:09:34.835 | 83f1d7d316a944849ce247c5763214e2 | WARNING  | module_admin.annotation.log_annotation:wrapper:123 - 密码错误
2025-06-07 11:09:34.996 | 50e27d45107b4dbdbe266f07b65b171b | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为b6499962-c920-49bb-b517-ce5029f841ac的会话获取图片验证码成功
2025-06-07 11:09:42.250 | 6d605e3d98724c6aa4eb2a9173e8d79b | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-07 11:09:42.774 | 3dd92d8c7e474ff5b1194e8d73c6c250 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:09:43.036 | af831bf2d0ec4a469191a8527657a8c9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:10:07.651 | 3799a0e5644a4a25bf166cd15a8c5eed | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:10:07.841 | 40a353cb7a7a468792856fd13e3abe67 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:10:10.302 | f235601dfb7e44a2a293bbe7db2a9714 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:10:10.491 | a244385219f34dbd91a92b707b4b663e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:10:11.575 | 0765c8aa5a224f4782081baadc499861 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:10:11.764 | ed7bd630ec90414581d5c3974f3383db | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:13:51.981 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-07 11:13:51.981 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-07 11:13:58.241 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-07 11:13:58.242 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-07 11:14:00.787 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-07 11:14:00.788 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-07 11:14:00.791 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-07 11:14:01.484 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-07 11:14:02.077 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-07 11:14:02.078 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-07 11:18:52.431 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-07 11:18:52.432 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-07 11:18:56.266 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-07 11:18:56.267 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-07 11:18:57.630 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-07 11:18:57.630 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-07 11:18:57.631 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-07 11:18:58.208 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-07 11:18:58.932 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-07 11:18:58.932 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-07 11:22:12.416 | 3186035ac47c45d38c9715def6417269 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:22:12.524 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-07 11:22:12.524 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-07 11:22:15.961 |  | INFO     | server:lifespan:37 - RuoYi-FastAPI开始启动
2025-06-07 11:22:15.962 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-07 11:22:18.582 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-07 11:22:18.582 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-07 11:22:18.585 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-07 11:22:19.379 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-07 11:22:20.059 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-07 11:22:20.060 |  | INFO     | server:lifespan:44 - RuoYi-FastAPI启动成功
2025-06-07 11:22:20.298 | dec4429f236a4ceeb4a6d9fec8e14013 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:23:39.047 | 49fa1d7bfd544dd1a9e581e5f85ec9c5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:23:39.171 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-07 11:23:39.172 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-07 11:23:42.635 |  | INFO     | server:lifespan:37 - RuoYi-FastAPI开始启动
2025-06-07 11:23:42.636 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-07 11:23:43.974 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-07 11:23:43.975 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-07 11:23:43.977 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-07 11:23:44.777 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-07 11:23:45.580 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-07 11:23:45.580 |  | INFO     | server:lifespan:44 - RuoYi-FastAPI启动成功
2025-06-07 11:23:45.763 | 1edabb1012d542fc9001685e4e7e8d47 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:23:47.955 | 770739ed76de428aac22ca489e25ccd4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:23:48.250 | c2f38b03abd94d83a92d460ef20c87f7 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:26:10.844 | 572033e61e494c189ca65d28a9ea2e62 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:26:10.992 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-07 11:26:10.993 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-07 11:26:14.310 |  | INFO     | server:lifespan:38 - RuoYi-FastAPI开始启动
2025-06-07 11:26:14.311 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-07 11:26:15.752 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-07 11:26:15.752 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-07 11:26:15.755 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-07 11:26:16.453 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-07 11:26:17.035 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-07 11:26:17.036 |  | INFO     | server:lifespan:45 - RuoYi-FastAPI启动成功
2025-06-07 11:26:17.247 | 4309020422124e62ba67ad1521c2cb12 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:26:25.395 | efc6ba42b7154512b64909d07bc7c0be | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:26:25.677 | 5ca38af7e7d346eda03a20e076218f5e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:26:31.042 | 719c6c7edb894e0799d0994eb9d8f9e6 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:26:31.341 | 3b691a8d88794b2f8cfac95303522ae2 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:28:54.959 | 32808e869dd742d19fd63ff1354fad0a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:28:55.248 | 3620285f848b4c0085c1ce5211591818 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:29:05.129 | f600c650c4204660933f0ef612cd787b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:29:05.438 | e4b012e31e584881b26d36667bdcdcc1 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:29:12.827 | 94d587b02db9458d906d12ef6f8206a8 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:29:13.114 | be87260a25c14ae3b7dd81ebbf61255b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:37:15.801 | 3e102bbd826548a192a6606caea6c505 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-07 11:37:15.801 | 3e102bbd826548a192a6606caea6c505 | INFO     | module_warning.controller.warning_scheme_controller:get_active_warning_schemes:60 - 获取启用预警方案列表成功
2025-06-07 11:37:19.518 | 732bef9073ed4f8c9906fda58ab742ec | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:37:19.803 | 49a38ff6eb674c4189c2b4221fba5780 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:37:20.722 | ebf2cc59f6344446a76682ec381c30f0 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-07 11:37:20.722 | ebf2cc59f6344446a76682ec381c30f0 | INFO     | module_warning.controller.warning_scheme_controller:get_active_warning_schemes:60 - 获取启用预警方案列表成功
2025-06-07 11:39:39.092 | 767104ab40d94e438d553beee6944c35 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:39:39.382 | f2573126cdc04c65a89ca358044bfd8d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:39:40.012 | ff0b7603861c49cca48d6824f4002962 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-07 11:39:40.013 | ff0b7603861c49cca48d6824f4002962 | INFO     | module_warning.controller.warning_scheme_controller:get_active_warning_schemes:60 - 获取启用预警方案列表成功
2025-06-07 11:39:40.684 | abc1a5e85b4744588aff271a956adc56 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-07 11:39:40.726 | abc1a5e85b4744588aff271a956adc56 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-07 11:39:40.937 | abc1a5e85b4744588aff271a956adc56 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-07 11:39:40.938 | abc1a5e85b4744588aff271a956adc56 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-07 11:40:25.030 | 3f54442fa0ec41a09be8c2b8aafb6df3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-07 11:40:25.324 | 187a315af72e481c9fdb7ef064c7c012 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-07 11:40:26.245 | dd32503739f145fc87241e9b9f554589 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-07 11:40:26.246 | dd32503739f145fc87241e9b9f554589 | INFO     | module_warning.controller.warning_scheme_controller:get_active_warning_schemes:60 - 获取启用预警方案列表成功
2025-06-07 11:40:26.597 | e61ff7c2f8984ee0ae86656890df4db8 | INFO     | module_warning.service.warning_scheme_service:get_active_warning_schemes_services:53 - 获取启用预警方案列表成功
2025-06-07 11:40:26.637 | e61ff7c2f8984ee0ae86656890df4db8 | INFO     | module_warning.service.warning_record_service:get_warning_record_list_services:36 - 获取预警记录列表成功
2025-06-07 11:40:26.841 | e61ff7c2f8984ee0ae86656890df4db8 | INFO     | module_warning.service.warning_record_service:get_warning_statistics_services:162 - 获取预警统计数据成功，方案ID: None
2025-06-07 11:40:26.842 | e61ff7c2f8984ee0ae86656890df4db8 | INFO     | module_warning.controller.warning_frontend_controller:get_warning_frontend_data:75 - 获取预警中心前端数据成功，方案ID: None
2025-06-07 11:53:01.891 | d1cafceae9284ff2963aa8a6d2a856b8 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-07 11:53:02.974 | 34ff199085b045d09d3e8510e8fd7c79 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-07 11:53:03.077 | d1792333cec9454fb61726fa4d066469 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-06-07 11:53:05.005 | 8dba08b775814f6ebe545119f3a27ffe | INFO     | module_admin.controller.menu_controller:query_detail_system_menu:112 - 获取menu_id为1的信息成功
2025-06-07 11:53:05.152 | 4cc46040518a40efb71534499fef80a1 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
