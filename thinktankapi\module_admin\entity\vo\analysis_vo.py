from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel


class EmotionAnalysisSummaryModel(BaseModel):
    """
    情感分析汇总模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='汇总ID')
    scheme_id: int = Field(description='方案ID')
    emotion_id: int = Field(description='情感ID')
    count: Optional[int] = Field(default=0, description='数量')
    date_range_start: Optional[datetime] = Field(default=None, description='日期范围开始')
    date_range_end: Optional[datetime] = Field(default=None, description='日期范围结束')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default=None, description='创建者')
    update_by: Optional[str] = Field(default=None, description='更新者')
    remark: Optional[str] = Field(default=None, description='备注')


class PlatformAnalysisSummaryModel(BaseModel):
    """
    平台分析汇总模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='汇总ID')
    scheme_id: int = Field(description='方案ID')
    platform_id: int = Field(description='平台ID')
    count: Optional[int] = Field(default=0, description='数量')
    date_range_start: Optional[datetime] = Field(default=None, description='日期范围开始')
    date_range_end: Optional[datetime] = Field(default=None, description='日期范围结束')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default=None, description='创建者')
    update_by: Optional[str] = Field(default=None, description='更新者')
    remark: Optional[str] = Field(default=None, description='备注')


class AnalysisPageQueryModel(BaseModel):
    """
    分析数据分页查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    page_num: int = Field(default=1, description='页码')
    page_size: int = Field(default=10, description='每页数量')
    scheme_id: Optional[int] = Field(default=None, description='方案ID')
    start_time: Optional[datetime] = Field(default=None, description='开始时间')
    end_time: Optional[datetime] = Field(default=None, description='结束时间')

    @classmethod
    def as_query(cls,
                 page_num: int = 1,
                 page_size: int = 10,
                 scheme_id: Optional[int] = None,
                 start_time: Optional[datetime] = None,
                 end_time: Optional[datetime] = None):
        return cls(
            page_num=page_num,
            page_size=page_size,
            scheme_id=scheme_id,
            start_time=start_time,
            end_time=end_time
        )


class EmotionStatisticsModel(BaseModel):
    """
    情感统计模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    emotion_name: str = Field(description='情感名称')
    emotion_id: int = Field(description='情感ID')
    count: int = Field(description='数量')
    percentage: float = Field(description='百分比')


class PlatformStatisticsModel(BaseModel):
    """
    平台统计模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    platform_name: str = Field(description='平台名称')
    platform_id: int = Field(description='平台ID')
    count: int = Field(description='数量')
    percentage: float = Field(description='百分比')


class TrendAnalysisModel(BaseModel):
    """
    趋势分析模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    date: str = Field(description='日期')
    count: int = Field(description='数量')
    emotion_positive: int = Field(default=0, description='正面情感数量')
    emotion_negative: int = Field(default=0, description='负面情感数量')
    emotion_neutral: int = Field(default=0, description='中性情感数量')


class AnalysisOverviewModel(BaseModel):
    """
    分析概览模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    scheme_id: int = Field(description='方案ID')
    total_count: int = Field(default=0, description='总数量')
    today_count: int = Field(default=0, description='今日数量')
    emotion_statistics: List[EmotionStatisticsModel] = Field(default=[], description='情感统计')
    platform_statistics: List[PlatformStatisticsModel] = Field(default=[], description='平台统计')
    trend_data: List[TrendAnalysisModel] = Field(default=[], description='趋势数据')
    hot_keywords: List[str] = Field(default=[], description='热门关键词')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
