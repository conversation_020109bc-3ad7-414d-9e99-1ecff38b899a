from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from typing import List, Optional
from module_admin.annotation.pydantic_annotation import as_query


class EmotionDefinitionModel(BaseModel):
    """
    情感定义表对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='情感类型ID')
    emotion_name: Optional[str] = Field(default=None, description='情感类型名称')
    emotion_code: Optional[str] = Field(default=None, description='情感类型编码')
    emotion_color: Optional[str] = Field(default=None, description='情感类型颜色')
    description: Optional[str] = Field(default=None, description='情感类型描述')
    sort_order: Optional[int] = Field(default=None, description='排序顺序')
    status: Optional[str] = Field(default=None, description='状态（0正常 1停用）')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default=None, description='创建者')
    update_by: Optional[str] = Field(default=None, description='更新者')
    remark: Optional[str] = Field(default=None, description='备注')


class EmotionAnalysisSummaryModel(BaseModel):
    """
    情感分析汇总表对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='主键ID')
    scheme_id: Optional[int] = Field(default=None, description='关联的方案ID')
    emotion_id: Optional[int] = Field(default=None, description='情感类型ID')
    count: Optional[int] = Field(default=None, description='该情感类型的舆情数量')
    date_range_start: Optional[datetime] = Field(default=None, description='统计区间开始时间')
    date_range_end: Optional[datetime] = Field(default=None, description='统计区间结束时间')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default=None, description='创建者')
    update_by: Optional[str] = Field(default=None, description='更新者')
    remark: Optional[str] = Field(default=None, description='备注')
    emotion_definition: Optional[EmotionDefinitionModel] = Field(default=None, description='情感定义信息')


class EmotionAnalysisQueryModel(BaseModel):
    """
    情感分析查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    scheme_id: Optional[int] = Field(default=None, description='方案ID')
    date_range_start: Optional[str] = Field(default=None, description='开始时间')
    date_range_end: Optional[str] = Field(default=None, description='结束时间')


@as_query
class EmotionAnalysisPageQueryModel(EmotionAnalysisQueryModel):
    """
    情感分析分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class EmotionStatisticsModel(BaseModel):
    """
    情感统计数据模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    emotion_name: str = Field(description='情感类型名称')
    emotion_code: str = Field(description='情感类型编码')
    emotion_color: str = Field(description='情感类型颜色')
    count: int = Field(description='数量')
    percentage: float = Field(description='百分比')


class EmotionTrendDataModel(BaseModel):
    """
    情感趋势数据模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    date: str = Field(description='日期')
    emotion_data: List[EmotionStatisticsModel] = Field(description='情感数据')


class OpinionOverviewDataModel(BaseModel):
    """
    舆情总览数据模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    # 情感统计数据
    emotion_statistics: List[EmotionStatisticsModel] = Field(description='情感统计数据')
    
    # 趋势图数据
    trend_dates: List[str] = Field(description='趋势图日期数据')
    trend_series: List[dict] = Field(description='趋势图系列数据')
    
    # 总计数据
    total_count: int = Field(description='总数量')
    
    # 数据来源标识
    is_mock_data: bool = Field(default=False, description='是否为模拟数据')
