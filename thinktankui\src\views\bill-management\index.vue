<template>
  <div class="app-container">
    <!-- 用户信息卡片 -->
    <div class="user-info-card" v-loading="loading">
      <div class="user-info-left">
        <div class="user-avatar">
          <span>{{ getAvatarText() }}</span>
        </div>
        <div class="user-details">
          <h3>{{ user_info.nick_name || user_info.user_name || '用户' }}</h3>
          <p v-if="user_info.email" class="user-email">{{ user_info.email }}</p>
          <p v-else class="no-email">未设置邮箱</p>
        </div>
      </div>
      <div class="user-info-right">
        <span v-if="getMembershipStatusText()" class="member-tag">{{ getMembershipStatusText() }}</span>
        <span class="expire-date" v-if="membership_info.expire_time">
          到期时间：{{ formatDate(membership_info.expire_time) }}
        </span>
        <span class="expire-date" v-else-if="shouldShowExpireTime()">
          永久有效
        </span>
      </div>
    </div>

    <!-- 查询条件 -->
    <div class="query-form">
      <el-form :inline="true" :model="query_form" class="demo-form-inline">
        <el-form-item label="支付状态">
          <el-select v-model="query_form.payment_status" placeholder="请选择支付状态" clearable style="width: 150px;">
            <el-option label="未支付" value="unpaid"></el-option>
            <el-option label="已支付" value="paid"></el-option>
            <el-option label="支付失败" value="failed"></el-option>
            <el-option label="已退款" value="refunded"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式">
          <el-select v-model="query_form.payment_method" placeholder="请选择支付方式" clearable style="width: 150px;">
            <el-option label="支付宝" value="alipay"></el-option>
            <el-option label="微信支付" value="wechat"></el-option>
            <el-option label="信用卡支付" value="stripe"></el-option>
            <el-option label="银行转账" value="bank_transfer"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="bill_data" style="width: 100%" v-loading="table_loading" stripe border>
        <el-table-column prop="order_no" label="订单号" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="package_name" label="套餐名称" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="final_price" label="金额" min-width="100" align="right">
          <template slot-scope="scope">
            <span style="color: #f56c6c; font-weight: bold;">{{ formatAmount(scope.row.final_price) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="payment_status" label="支付状态" min-width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getPaymentStatusType(scope.row.payment_status)" size="small">
              {{ getPaymentStatusText(scope.row.payment_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="payment_method" label="支付方式" min-width="100" align="center">
          <template slot-scope="scope">
            {{ getPaymentMethodText(scope.row.payment_method) }}
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" min-width="160" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleViewDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="query_form.page_num"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="query_form.page_size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getBillDashboardData,
  getUserOrdersPage,
  formatAmount,
  formatDate,
  formatDateTime,
  getPaymentStatusDisplay,
  getPaymentMethodDisplay
} from '@/api/bill'
import { mapGetters } from 'vuex'

export default {
  name: 'BillManagement',
  computed: {
    ...mapGetters(['id'])
  },
  data() {
    return {
      loading: false,
      table_loading: false,
      user_info: {},
      membership_info: {},
      bill_data: [],
      total: 0,
      query_form: {
        user_id: null, // 将在created中从vuex获取
        payment_status: '',
        payment_method: '',
        start_date: '',
        end_date: '',
        page_num: 1,
        page_size: 10
      }
    }
  },
  created() {
    // 从vuex获取当前用户ID
    this.query_form.user_id = this.id || this.$store.getters.id
    if (!this.query_form.user_id) {
      this.$message.error('请先登录')
      this.$router.push('/login')
      return
    }
    this.loadDashboardData()
    this.loadBillData()
  },
  methods: {
    // 加载仪表板数据
    async loadDashboardData() {
      this.loading = true
      try {
        const response = await getBillDashboardData(this.query_form.user_id)
        if (response.code === 200) {
          this.user_info = response.data.user_info || {}
          this.membership_info = response.data.membership_info || {}
        } else {
          this.$message.error(response.msg || '加载用户信息失败')
        }
      } catch (error) {
        console.error('加载仪表板数据失败:', error)
        this.$message.error('加载用户信息失败')
      } finally {
        this.loading = false
      }
    },

    // 加载账单数据
    async loadBillData() {
      this.table_loading = true
      try {
        const response = await getUserOrdersPage(this.query_form)
        if (response.code === 200) {
          this.bill_data = response.data.rows || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || '加载账单数据失败')
        }
      } catch (error) {
        console.error('加载账单数据失败:', error)
        this.$message.error('加载账单数据失败')
      } finally {
        this.table_loading = false
      }
    },

    // 查询
    handleQuery() {
      this.query_form.page_num = 1
      this.loadBillData()
    },

    // 重置查询
    resetQuery() {
      this.query_form = {
        ...this.query_form,
        payment_status: '',
        payment_method: '',
        start_date: '',
        end_date: '',
        page_num: 1
      }
      this.loadBillData()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.query_form.page_size = val
      this.query_form.page_num = 1
      this.loadBillData()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.query_form.page_num = val
      this.loadBillData()
    },

    // 查看详情
    handleViewDetail(row) {
      this.$message.info(`查看订单 ${row.order_no} 的详情`)
      // 这里可以打开详情对话框或跳转到详情页面
    },

    // 获取头像文本
    getAvatarText() {
      const name = this.user_info.nick_name || this.user_info.user_name || 'D'
      return name.charAt(0).toUpperCase()
    },

    // 格式化金额
    formatAmount(amount) {
      return formatAmount(amount)
    },

    // 格式化日期
    formatDate(dateString) {
      return formatDate(dateString)
    },

    // 获取会员状态显示文本
    getMembershipStatusText() {
      const status = this.membership_info.membership_status
      if (!status) {
        return ''  // 没有数据时返回空字符串
      }
      // 统一术语显示
      if (status === '基础版') {
        return '基础版会员'
      }
      if (status === '专业版') {
        return '专业版会员'
      }
      if (status === '企业版') {
        return '企业版会员'
      }
      return status
    },

    // 判断是否应该显示到期时间
    shouldShowExpireTime() {
      const status = this.membership_info.membership_status
      // 如果是普通用户或没有状态，不显示到期时间
      if (!status || status === '普通用户') {
        return false
      }
      return true
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      return formatDateTime(dateString)
    },

    // 获取支付状态类型
    getPaymentStatusType(status) {
      const display = getPaymentStatusDisplay(status)
      return display.type
    },

    // 获取支付状态文本
    getPaymentStatusText(status) {
      const display = getPaymentStatusDisplay(status)
      return display.text
    },

    // 获取支付方式文本
    getPaymentMethodText(method) {
      return getPaymentMethodDisplay(method)
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

/* 统一的用户信息卡片样式 */
.user-info-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.user-info-left {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #36cfc9;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.user-avatar span {
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.user-details h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #303133;
}

.user-details p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.user-details .user-email {
  color: #909399;
}

.user-details .no-email {
  color: #c0c4cc;
  font-style: italic;
}

.user-info-right {
  text-align: right;
}

.member-tag {
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  display: block;
  margin-bottom: 8px;
}

.expire-date {
  font-size: 14px;
  color: #666;
}

.query-form {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.demo-form-inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 0;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

.el-table td {
  padding: 12px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .user-summary {
    align-self: flex-end;
  }

  .query-form {
    padding: 15px;
  }

  .table-container {
    padding: 15px;
    overflow-x: auto;
  }

  .demo-form-inline .el-form-item {
    margin-right: 10px;
    margin-bottom: 10px;
  }
}

/* 表格内容样式 */
.el-tag {
  border-radius: 12px;
  padding: 0 8px;
  font-size: 12px;
}

.el-button--mini {
  padding: 5px 10px;
  font-size: 12px;
}

.el-button--text {
  color: #409eff;
}

.el-button--text:hover {
  color: #66b1ff;
}
</style>