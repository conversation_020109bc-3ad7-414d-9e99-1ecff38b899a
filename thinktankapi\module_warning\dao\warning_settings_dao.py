from datetime import datetime
from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_warning.entity.do.warning_settings_do import WarningSettings
from module_warning.entity.vo.warning_vo import WarningSettingsModel, WarningSettingsConfigModel
import json


class WarningSettingsDao:
    """
    预警设置管理模块数据库操作层
    """

    @classmethod
    async def get_warning_settings_by_id(cls, db: AsyncSession, setting_id: int):
        """
        根据预警设置id获取预警设置详细信息

        :param db: orm对象
        :param setting_id: 预警设置id
        :return: 预警设置信息对象
        """
        settings_info = (await db.execute(select(WarningSettings).where(WarningSettings.id == setting_id))).scalars().first()

        return settings_info

    @classmethod
    async def get_warning_settings_by_scheme_id(cls, db: AsyncSession, scheme_id: int):
        """
        根据预警方案id获取预警设置信息

        :param db: orm对象
        :param scheme_id: 预警方案id
        :return: 预警设置信息对象
        """
        settings_info = (await db.execute(select(WarningSettings).where(WarningSettings.scheme_id == scheme_id))).scalars().first()

        return settings_info

    @classmethod
    async def save_warning_settings(cls, db: AsyncSession, settings: WarningSettingsConfigModel):
        """
        保存预警设置（新增或更新）

        :param db: orm对象
        :param settings: 预警设置配置对象
        :return: 保存的设置对象
        """
        # 检查是否已存在该方案的设置
        existing_settings = await cls.get_warning_settings_by_scheme_id(db, settings.scheme_id)
        
        # 准备数据
        settings_data = {
            'scheme_id': settings.scheme_id,
            'platform_types': json.dumps(settings.platform_types) if settings.platform_types else None,
            'content_property': settings.content_property,
            'info_type': settings.info_type,
            'match_objects': json.dumps(settings.match_objects) if settings.match_objects else None,
            'match_method': settings.match_method,
            'publish_regions': json.dumps(settings.publish_regions) if settings.publish_regions else None,
            'ip_areas': json.dumps(settings.ip_areas) if settings.ip_areas else None,
            'media_categories': json.dumps(settings.media_categories) if settings.media_categories else None,
            'article_categories': json.dumps(settings.article_categories) if settings.article_categories else None,
            'update_time': datetime.now()
        }

        if existing_settings:
            # 更新现有设置
            settings_data['id'] = existing_settings.id
            await db.execute(update(WarningSettings), [settings_data])
            return existing_settings
        else:
            # 新增设置
            settings_data['create_time'] = datetime.now()
            db_settings = WarningSettings(**settings_data)
            db.add(db_settings)
            await db.flush()
            return db_settings

    @classmethod
    async def update_warning_settings(cls, db: AsyncSession, settings: dict):
        """
        更新预警设置数据库操作

        :param db: orm对象
        :param settings: 需要更新的预警设置字典
        :return:
        """
        settings_id = settings.get('id')  # 获取ID用于WHERE条件
        if not settings_id:
            raise ValueError("设置ID不能为空")

        # 创建更新数据的副本，排除ID字段
        update_data = {k: v for k, v in settings.items() if k != 'id'}

        await db.execute(
            update(WarningSettings)
            .where(WarningSettings.id == settings_id)
            .values(**update_data)
        )

    @classmethod
    async def delete_warning_settings_dao(cls, db: AsyncSession, setting_ids: list):
        """
        删除预警设置数据库操作

        :param db: orm对象
        :param setting_ids: 预警设置ID列表
        :return:
        """
        await db.execute(delete(WarningSettings).where(WarningSettings.id.in_(setting_ids)))

    @classmethod
    async def delete_warning_settings_by_scheme_id(cls, db: AsyncSession, scheme_id: int):
        """
        根据方案ID删除预警设置

        :param db: orm对象
        :param scheme_id: 预警方案ID
        :return:
        """
        await db.execute(delete(WarningSettings).where(WarningSettings.scheme_id == scheme_id))

    @classmethod
    def convert_to_config_model(cls, settings: WarningSettings) -> WarningSettingsConfigModel:
        """
        将数据库实体转换为配置模型

        :param settings: 数据库设置实体
        :return: 配置模型
        """
        from utils.log_util import logger

        if not settings:
            return None

        def safe_json_parse(value, field_name="unknown"):
            """安全的JSON解析，支持字符串和已解析的对象"""
            if not value:
                return []

            # 如果已经是list或dict类型，直接返回
            if isinstance(value, (list, dict)):
                logger.debug(f'字段 {field_name} 已经是 {type(value).__name__} 类型，直接使用')
                return value

            # 如果是字符串类型，尝试JSON解析
            if isinstance(value, str):
                try:
                    result = json.loads(value)
                    logger.debug(f'字段 {field_name} JSON解析成功')
                    return result
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f'字段 {field_name} JSON解析失败: {e}，返回空列表')
                    return []

            # 其他类型，记录警告并返回空列表
            logger.warning(f'字段 {field_name} 类型未知: {type(value)}，返回空列表')
            return []

        try:
            return WarningSettingsConfigModel(
                scheme_id=settings.scheme_id,
                platform_types=safe_json_parse(settings.platform_types, "platform_types"),
                content_property=settings.content_property,
                info_type=settings.info_type,
                match_objects=safe_json_parse(settings.match_objects, "match_objects"),
                match_method=settings.match_method,
                publish_regions=safe_json_parse(settings.publish_regions, "publish_regions"),
                ip_areas=safe_json_parse(settings.ip_areas, "ip_areas"),
                media_categories=safe_json_parse(settings.media_categories, "media_categories"),
                article_categories=safe_json_parse(settings.article_categories, "article_categories"),
                allow_words='',  # 这些字段可能需要从其他地方获取
                reject_words='',
                auto_warning_settings={}
            )
        except Exception as e:
            logger.error(f'转换预警设置配置模型失败: {e}')
            # 返回默认配置以避免完全失败
            return WarningSettingsConfigModel(
                scheme_id=settings.scheme_id,
                platform_types=['all'],
                content_property='all',
                info_type='noncomment',
                match_objects=[],
                match_method='exact',
                publish_regions=[],
                ip_areas=[],
                media_categories=[],
                article_categories=[],
                allow_words='',
                reject_words='',
                auto_warning_settings={}
            )

    @classmethod
    async def get_default_settings_config(cls) -> WarningSettingsConfigModel:
        """
        获取默认的预警设置配置

        :return: 默认配置模型
        """
        return WarningSettingsConfigModel(
            platform_types=['all'],
            content_property='all',
            info_type='noncomment',
            match_objects=[],
            match_method='exact',
            publish_regions=[],
            ip_areas=[],
            media_categories=[],
            article_categories=[],
            allow_words='',
            reject_words='',
            auto_warning_settings={
                'time_range': {'start_hour': '06', 'start_minute': '00', 'end_hour': '18', 'end_minute': '00'},
                'platforms': {'weibo': True, 'wechat': True, 'website': True, 'douyin': True, 'redbook': True},
                'warning_type': 'negative',
                'process_method': 'all',
                'priority': 'normal',
                'handle_method': 'auto',
                'notify_methods': {'sms': True, 'email': False, 'wechat_notify': True}
            }
        )

    @classmethod
    async def validate_settings_config(cls, settings: WarningSettingsConfigModel) -> bool:
        """
        验证预警设置配置的有效性

        :param settings: 预警设置配置
        :return: 是否有效
        """
        # 基本验证
        if not settings.scheme_id:
            return False

        # 验证平台类型
        valid_platforms = ['all', 'webpage', 'wechat', 'weibo', 'toutiao', 'app', 'video', 'forum', 'newspaper', 'qa']
        if settings.platform_types:
            for platform in settings.platform_types:
                if platform not in valid_platforms:
                    return False

        # 验证内容属性
        if settings.content_property and settings.content_property not in ['all', 'yes', 'no']:
            return False

        # 验证信息类型
        if settings.info_type and settings.info_type not in ['all', 'noncomment', 'comment']:
            return False

        # 验证匹配方式
        if settings.match_method and settings.match_method not in ['exact', 'fuzzy']:
            return False

        return True
