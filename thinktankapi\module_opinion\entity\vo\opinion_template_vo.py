from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from module_admin.entity.vo.common_vo import PageQueryModel
from module_admin.annotation.pydantic_annotation import as_query


class OpinionTemplateModel(BaseModel):
    """
    舆情分析模板模型
    """
    id: Optional[int] = Field(default=None, description='主键ID')
    template_name: str = Field(..., description='模板名称')
    template_category: str = Field(..., description='模板分类')
    entity_keyword: str = Field(..., description='实体关键词')
    specific_requirement: str = Field(..., description='具体需求描述')
    template_description: Optional[str] = Field(default=None, description='模板描述')
    priority: Optional[str] = Field(default='medium', description='优先级：high, medium, low')
    max_keywords_limit: Optional[int] = Field(default=5, description='最大关键词选择数量')
    template_tags: Optional[str] = Field(default=None, description='模板标签，多个标签用逗号分隔')
    usage_count: Optional[int] = Field(default=0, description='使用次数统计')
    is_system_template: Optional[int] = Field(default=0, description='是否系统模板：0-用户自定义，1-系统预设')
    is_active: Optional[int] = Field(default=1, description='是否激活：0-否，1-是')
    is_deleted: Optional[int] = Field(default=0, description='是否删除：0-否，1-是')
    sort_order: Optional[int] = Field(default=0, description='排序顺序')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default='', description='创建者')
    update_by: Optional[str] = Field(default='', description='更新者')
    remark: Optional[str] = Field(default='', description='备注信息')


@as_query
class OpinionTemplatePageQueryModel(PageQueryModel):
    """
    舆情分析模板分页查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    template_name: Optional[str] = Field(default=None, description='模板名称')
    template_category: Optional[str] = Field(default=None, description='模板分类')
    priority: Optional[str] = Field(default=None, description='优先级')
    is_system_template: Optional[int] = Field(default=None, description='是否系统模板')
    is_active: Optional[int] = Field(default=None, description='是否激活')
    create_time_start: Optional[str] = Field(default=None, description='创建时间开始')
    create_time_end: Optional[str] = Field(default=None, description='创建时间结束')


class CreateOpinionTemplateModel(BaseModel):
    """
    创建舆情分析模板模型
    """
    template_name: str = Field(..., description='模板名称')
    template_category: str = Field(..., description='模板分类')
    entity_keyword: str = Field(..., description='实体关键词')
    specific_requirement: str = Field(..., description='具体需求描述')
    template_description: Optional[str] = Field(default='', description='模板描述')
    priority: Optional[str] = Field(default='medium', description='优先级：high, medium, low')
    max_keywords_limit: Optional[int] = Field(default=5, description='最大关键词选择数量')
    template_tags: Optional[str] = Field(default='', description='模板标签，多个标签用逗号分隔')
    sort_order: Optional[int] = Field(default=0, description='排序顺序')
    remark: Optional[str] = Field(default='', description='备注信息')


class UpdateOpinionTemplateModel(BaseModel):
    """
    更新舆情分析模板模型
    """
    id: int = Field(..., description='主键ID')
    template_name: Optional[str] = Field(default=None, description='模板名称')
    template_category: Optional[str] = Field(default=None, description='模板分类')
    entity_keyword: Optional[str] = Field(default=None, description='实体关键词')
    specific_requirement: Optional[str] = Field(default=None, description='具体需求描述')
    template_description: Optional[str] = Field(default=None, description='模板描述')
    priority: Optional[str] = Field(default=None, description='优先级')
    max_keywords_limit: Optional[int] = Field(default=None, description='最大关键词选择数量')
    template_tags: Optional[str] = Field(default=None, description='模板标签')
    is_active: Optional[int] = Field(default=None, description='是否激活')
    sort_order: Optional[int] = Field(default=None, description='排序顺序')
    remark: Optional[str] = Field(default=None, description='备注信息')


class DeleteOpinionTemplateModel(BaseModel):
    """
    删除舆情分析模板模型
    """
    ids: List[int] = Field(..., description='需要删除的ID列表')


class OpinionTemplateListModel(BaseModel):
    """
    舆情分析模板列表模型（用于下拉选择）
    """
    id: int = Field(..., description='主键ID')
    template_name: str = Field(..., description='模板名称')
    template_category: str = Field(..., description='模板分类')
    entity_keyword: str = Field(..., description='实体关键词')
    specific_requirement: str = Field(..., description='具体需求描述')
    priority: str = Field(..., description='优先级')
    usage_count: int = Field(..., description='使用次数')


class UpdateTemplateUsageModel(BaseModel):
    """
    更新模板使用次数模型
    """
    template_id: int = Field(..., description='模板ID')
    increment: Optional[int] = Field(default=1, description='增加的使用次数，默认为1')


class OpinionTemplateCategoryModel(BaseModel):
    """
    舆情分析模板分类模型
    """
    category_name: str = Field(..., description='分类名称')
    template_count: int = Field(..., description='该分类下的模板数量')
