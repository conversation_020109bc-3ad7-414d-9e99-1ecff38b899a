import random
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from module_admin.dao.emotion_analysis_dao import EmotionAnalysisDao
from module_admin.entity.vo.emotion_analysis_vo import (
    EmotionAnalysisQueryModel,
    EmotionStatisticsModel,
    OpinionOverviewDataModel
)
from utils.common_util import CamelCaseUtil


class EmotionAnalysisService:
    """
    情感分析管理模块服务层
    """

    @classmethod
    async def get_opinion_overview_data(
        cls, query_db: AsyncSession, scheme_id: int = 1
    ) -> OpinionOverviewDataModel:
        """
        获取舆情总览数据

        :param query_db: orm对象
        :param scheme_id: 方案ID，默认为1
        :return: 舆情总览数据
        """
        # 检查数据库是否有数据
        has_data = await EmotionAnalysisDao.check_data_exists(query_db, scheme_id)
        
        if has_data:
            # 使用真实数据
            return await cls._get_real_data(query_db, scheme_id)
        else:
            # 使用模拟数据
            return await cls._get_mock_data()

    @classmethod
    async def _get_real_data(cls, query_db: AsyncSession, scheme_id: int) -> OpinionOverviewDataModel:
        """
        获取真实数据

        :param query_db: orm对象
        :param scheme_id: 方案ID
        :return: 舆情总览数据
        """
        # 获取情感统计数据
        emotion_stats = await EmotionAnalysisDao.get_emotion_statistics_by_scheme(query_db, scheme_id)
        
        # 计算总数和百分比
        total_count = sum(stat['total_count'] for stat in emotion_stats)
        emotion_statistics = []
        
        for stat in emotion_stats:
            count = stat['total_count'] or 0
            percentage = (count / total_count * 100) if total_count > 0 else 0
            emotion_statistics.append(EmotionStatisticsModel(
                emotionName=stat['emotion_name'],
                emotionCode=stat['emotion_code'],
                emotionColor=stat['emotion_color'],
                count=count,
                percentage=round(percentage, 1)
            ))

        # 获取趋势数据
        trend_data = await EmotionAnalysisDao.get_emotion_trend_data(query_db, scheme_id)
        trend_dates, trend_series = cls._process_trend_data(trend_data)

        return OpinionOverviewDataModel(
            emotionStatistics=emotion_statistics,
            trendDates=trend_dates,
            trendSeries=trend_series,
            totalCount=total_count,
            isMockData=False
        )

    @classmethod
    async def _get_mock_data(cls) -> OpinionOverviewDataModel:
        """
        获取模拟数据

        :return: 舆情总览数据
        """
        # 模拟情感统计数据
        emotion_statistics = [
            EmotionStatisticsModel(
                emotionName="积极",
                emotionCode="positive",
                emotionColor="#52c41a",
                count=1550,
                percentage=35.2
            ),
            EmotionStatisticsModel(
                emotionName="消极",
                emotionCode="negative",
                emotionColor="#ff4d4f",
                count=1825,
                percentage=41.4
            ),
            EmotionStatisticsModel(
                emotionName="中性",
                emotionCode="neutral",
                emotionColor="#faad14",
                count=1007,
                percentage=22.9
            ),
            EmotionStatisticsModel(
                emotionName="其他",
                emotionCode="other",
                emotionColor="#722ed1",
                count=359,
                percentage=8.1
            )
        ]

        # 生成30天的趋势数据
        trend_dates = []
        today = datetime.now()
        for i in range(29, -1, -1):
            date = today - timedelta(days=i)
            trend_dates.append(date.strftime('%m/%d'))

        # 生成趋势系列数据
        trend_series = []
        for emotion in emotion_statistics:
            data = []
            base_value = emotion.count / 30  # 平均值作为基准
            for _ in range(30):
                # 在基准值附近随机波动
                value = int(base_value * (0.7 + random.random() * 0.6))
                data.append(value)
            
            trend_series.append({
                'name': emotion.emotion_name,
                'type': 'line',
                'data': data,
                'itemStyle': {'color': emotion.emotion_color},
                'lineStyle': {'color': emotion.emotion_color}
            })

        total_count = sum(stat.count for stat in emotion_statistics)

        return OpinionOverviewDataModel(
            emotionStatistics=emotion_statistics,
            trendDates=trend_dates,
            trendSeries=trend_series,
            totalCount=total_count,
            isMockData=True
        )

    @classmethod
    def _process_trend_data(cls, trend_data: List[dict]) -> tuple:
        """
        处理趋势数据

        :param trend_data: 原始趋势数据
        :return: (日期列表, 系列数据)
        """
        # 按日期分组数据
        date_groups = {}
        emotions = {}
        
        for item in trend_data:
            date_str = item['date_range_start'].strftime('%m/%d') if item['date_range_start'] else ''
            emotion_code = item['emotion_code']
            
            if date_str not in date_groups:
                date_groups[date_str] = {}
            date_groups[date_str][emotion_code] = item['count']
            
            if emotion_code not in emotions:
                emotions[emotion_code] = {
                    'name': item['emotion_name'],
                    'color': item['emotion_color']
                }

        # 生成日期列表
        trend_dates = sorted(date_groups.keys())
        
        # 生成系列数据
        trend_series = []
        for emotion_code, emotion_info in emotions.items():
            data = []
            for date in trend_dates:
                count = date_groups[date].get(emotion_code, 0)
                data.append(count)
            
            trend_series.append({
                'name': emotion_info['name'],
                'type': 'line',
                'data': data,
                'itemStyle': {'color': emotion_info['color']},
                'lineStyle': {'color': emotion_info['color']}
            })

        return trend_dates, trend_series
