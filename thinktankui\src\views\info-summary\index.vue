<template>
  <div class="app-container">
    <!-- 操作按钮区域 -->
    <div class="action-buttons">
      <el-button type="primary" icon="el-icon-message" @click="showSendAlertDialog">发送预警</el-button>
      <el-button type="primary" icon="el-icon-plus" @click="showCreatePlanDialog">新建方案</el-button>
      <el-button type="primary" icon="el-icon-folder-add" @click="showAddToAlertMaterialDialog">加入至报告素材</el-button>
      <el-button type="primary" icon="el-icon-share" @click="showInfoGraphDialog">信息图谱</el-button>
      <el-button type="primary" icon="el-icon-document-checked" @click="showOriginalProofreadingDialog">原稿校对</el-button>
    </div>

    <div class="info-summary-container">
      <!-- 左侧导航栏 -->
      <div class="left-sidebar">
        <div class="sidebar-header">
          <span class="sidebar-title">方太</span>
          <i class="el-icon-arrow-right"></i>
        </div>
        <div class="sidebar-menu">
          <el-menu
            :default-active="activeMenu"
            class="el-menu-vertical"
          >
            <el-menu-item
              v-for="item in menuItems"
              :key="item.index"
              :index="item.index"
              @click="handleMenuClick(item)"
              :class="{ 'is-active': searchKeyword === item.index }"
            >
              <i :class="item.icon"></i>
              <span slot="title">{{ item.name }}({{ item.count }})</span>
            </el-menu-item>
          </el-menu>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <!-- 标题和操作区域 -->
        <div class="content-header">
          <div class="entity-title">
            <span class="entity-name">方太</span>
            <i class="el-icon-arrow-right"></i>
          </div>
          <div class="view-actions">
            <el-button-group>
              <el-button size="small" type="primary" icon="el-icon-s-grid"></el-button>
              <el-button size="small" icon="el-icon-menu"></el-button>
              <el-button size="small" icon="el-icon-s-unfold"></el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 标签页 -->
        <div class="tabs-container">
          <div class="filter-tabs">
            <el-radio-group v-model="activeTab" size="small">
              <el-radio-button label="custom">自定义</el-radio-button>
            </el-radio-group>
          </div>

          <!-- 自定义时间选择器 -->
          <div class="custom-date-container">
            <el-date-picker
              v-model="customDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              style="margin-left: 10px;"
              @change="handleCustomDateChange"
            >
            </el-date-picker>

            <!-- 快速时间选择按钮 -->
            <div class="quick-date-buttons">
              <el-button size="mini" @click="setQuickDate(7)">最近7天</el-button>
              <el-button size="mini" @click="setQuickDate(30)">最近30天</el-button>
              <el-button size="mini" @click="setQuickDate(90)">最近3个月</el-button>
              <el-button size="mini" @click="clearCustomDate">清除</el-button>
            </div>
          </div>
        </div>

        <!-- 操作栏 - 信息类型筛选 -->
        <div class="filter-section">
          <div class="filter-row">
            <span class="filter-label">情感倾向:</span>
            <el-checkbox-group v-model="sentimentTypes" size="small">
              <el-checkbox
                v-for="option in sentimentOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }} ({{ option.todayCount }}/{{ option.count }})
              </el-checkbox>
            </el-checkbox-group>
          </div>

          <div class="filter-actions">
            <el-button size="small" type="primary" @click="handleFilter">筛选</el-button>
            <el-button size="small" @click="handleResetFilter">重置</el-button>
          </div>
        </div>
        <div class="action-bar">
          <div class="left-actions">
            <div class="select-all-container">
              <el-checkbox
                v-model="isAllSelected"
                :indeterminate="isIndeterminate"
                @change="handleSelectAll"
                class="select-all-checkbox"
              >
                全选
              </el-checkbox>
              <span v-if="selectedCount > 0" class="selected-count">
                (已选 {{ selectedCount }}/{{ paginatedList.length }})
              </span>
            </div>
            <el-dropdown
              size="small"
              split-button
              type="primary"
              @click="handleExport('excel')"
              @command="handleExport"
              :disabled="selectedCount === 0"
              :loading="exportLoading"
            >
              <span>
                <i class="el-icon-download"></i>
                导出 {{ selectedCount > 0 ? `(${selectedCount})` : '' }}
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="excel">
                  <i class="el-icon-document"></i> Excel格式
                </el-dropdown-item>
                <el-dropdown-item command="html">
                  <i class="el-icon-monitor"></i> HTML报表
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="right-actions">
            <!-- 增强的搜索框 -->
            <div class="enhanced-search-container">
              <el-autocomplete
                v-model="searchKeyword"
                :fetch-suggestions="querySearchAsync"
                placeholder="搜索关键词"
                prefix-icon="el-icon-search"
                size="small"
                clearable
                class="search-input"
                @input="handleSearchInput"
                @clear="handleSearchClear"
                @select="handleSearchSelect"
                @focus="handleSearchFocus"
                @blur="handleSearchBlur"
                :trigger-on-focus="false"
                :debounce="300"
                popper-class="search-suggestions-popper"
              >
                <template slot-scope="{ item }">
                  <div class="search-suggestion-item">
                    <i :class="item.icon" class="suggestion-icon"></i>
                    <span class="suggestion-text">{{ item.value }}</span>
                    <span v-if="item.type" class="suggestion-type">{{ item.type }}</span>
                  </div>
                </template>
                <el-button slot="append" icon="el-icon-search" @click="handleSearchKeyword"></el-button>
              </el-autocomplete>

              <!-- 搜索选项下拉 -->
              <el-dropdown trigger="click" @command="handleSearchOptionCommand" class="search-options-dropdown">
                <el-button size="small" type="text" class="search-options-btn">
                  <i class="el-icon-setting"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="title">仅搜索标题</el-dropdown-item>
                  <el-dropdown-item command="content">仅搜索内容</el-dropdown-item>
                  <el-dropdown-item command="source">仅搜索来源</el-dropdown-item>
                  <el-dropdown-item command="all" divided>搜索全部</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>

            <el-dropdown size="small" split-button type="primary" @command="handleCommand">
              排序
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="time_desc">时间降序</el-dropdown-item>
                <el-dropdown-item command="time_asc">时间升序</el-dropdown-item>
                <el-dropdown-item command="relevance">相关性</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>

        <!-- 信息列表 -->
        <div class="info-list">
          <!-- 有数据时显示列表 -->
          <div v-if="paginatedList && paginatedList.length > 0">
            <div v-for="(item, index) in paginatedList" :key="index" class="info-item">
              <el-checkbox v-model="item.selected" class="item-checkbox"></el-checkbox>
              <div class="info-content">
                <div class="info-header" @click="toggleItemExpand(item, index)" style="cursor: pointer;">
                  <div class="info-title" v-html="item.title"></div>
                  <div class="info-actions" @click.stop>
                    <el-button type="text" :icon="item.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" size="mini"></el-button>
                    <el-button type="text" icon="el-icon-star-off"></el-button>
                    <el-button type="text" icon="el-icon-share"></el-button>
                    <el-button type="text" icon="el-icon-more"></el-button>
                  </div>
                </div>

                <!-- 展开时显示的内容 -->
                <div v-if="item.expanded" class="info-expanded-content">
                  <div class="info-summary" v-html="item.content"></div>
                  <div class="info-images" v-if="item.images && item.images.length > 0">
                    <img v-for="(img, imgIndex) in item.images" :key="imgIndex" :src="img" class="info-image" />
                  </div>
                </div>

                <div class="info-footer">
                  <span class="info-source">{{ item.source }}</span>
                  <span class="info-time">{{ item.time }}</span>
                  <span class="info-platform" v-if="item.platform_name">
                    <i class="el-icon-monitor"></i> {{ item.platform_name }}
                  </span>
                  <span class="info-sentiment" :class="'sentiment-' + item.sentiment">
                    <el-button
                      :type="item.sentiment === 'positive' ? 'success' : item.sentiment === 'negative' ? 'danger' : 'info'"
                      size="mini"
                      @click="handleSentimentClick(item)"
                    >
                      <i :class="getSentimentIcon(item.sentiment)"></i>
                      {{ getSentimentText(item.sentiment) }}
                    </el-button>
                  </span>
                  <span class="info-views">
                    <i class="el-icon-view"></i> {{ item.views }}
                  </span>
                  <span class="info-comments">
                    <i class="el-icon-chat-line-square"></i> {{ item.comments }}
                  </span>
                  <span class="info-index">{{ (currentPage - 1) * pageSize + index + 1 }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 无数据时显示空状态 -->
          <div v-else class="empty-state">
            <div class="empty-content">
              <i class="el-icon-document empty-icon"></i>
              <p class="empty-text">暂无数据</p>
              <p class="empty-description">当前筛选条件下没有找到相关信息</p>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalItems"
            background
          ></el-pagination>
        </div>
      </div>
    </div>

    <!-- 新建方案对话框 -->
    <el-dialog
      title="新建方案"
      :visible.sync="createPlanDialogVisible"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      custom-class="create-plan-dialog"
    >
      <el-tabs v-model="planActiveTab">
        <el-tab-pane label="监测方式" name="standard">
          <el-form :model="planForm" label-width="70px" size="small">
            <el-form-item label="方案名称">
              <el-input v-model="planForm.name" placeholder="请输入方案名称"></el-input>
            </el-form-item>

            <el-form-item label="作用范围">
              <el-select v-model="planForm.scope" placeholder="请选择" style="width: 100%">
                <el-option label="全部" value="all"></el-option>
                <el-option label="选项1" value="option1"></el-option>
                <el-option label="选项2" value="option2"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="监测对象">
              <div class="monitor-object-select">
                <el-input v-model="planForm.monitorObject" placeholder="请输入监测对象"></el-input>
                <el-dropdown trigger="click" @command="handleMonitorObjectCommand">
                  <span class="el-dropdown-link">
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="option1">选项1</el-dropdown-item>
                    <el-dropdown-item command="option2">选项2</el-dropdown-item>
                    <el-dropdown-item command="option3">选项3</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </el-form-item>

            <el-form-item label="地域">
              <div class="location-select">
                <el-input v-model="planForm.location" placeholder="请选择地域" readonly></el-input>
                <el-button type="text" class="location-btn" @click="showLocationMap">
                  <i class="el-icon-location"></i>
                </el-button>
              </div>
            </el-form-item>

            <el-form-item label="主题">
              <div class="theme-row">
                <el-tag
                  v-for="(tag, index) in planForm.themes"
                  :key="index"
                  closable
                  @close="handleRemoveTheme(tag)"
                >
                  {{ tag }}
                </el-tag>
                <el-input
                  class="theme-input"
                  v-if="themeInputVisible"
                  v-model="themeInputValue"
                  ref="themeInput"
                  size="small"
                  @keyup.enter.native="handleAddTheme"
                  @blur="handleAddTheme"
                >
                </el-input>
                <el-button v-else class="theme-button" size="small" @click="showThemeInput">+ 添加主题</el-button>
              </div>
            </el-form-item>

            <el-form-item label="行业分类">
              <div class="industry-row">
                <el-tag
                  v-if="planForm.industry"
                  closable
                  @close="planForm.industry = ''"
                >
                  {{ planForm.industry }}
                </el-tag>
                <el-button v-if="!planForm.industry" class="industry-button" size="small" @click="showIndustrySelect">+ 添加行业分类</el-button>
              </div>
            </el-form-item>

            <el-form-item label="时间段">
              <el-date-picker
                v-model="planForm.timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>

            <el-form-item label="渠道">
              <el-checkbox-group v-model="planForm.channels">
                <div class="channels-row">
                  <el-checkbox label="news">新闻</el-checkbox>
                  <el-checkbox label="weibo">微博</el-checkbox>
                  <el-checkbox label="wechat">微信</el-checkbox>
                </div>
                <div class="channels-row">
                  <el-checkbox label="video">视频</el-checkbox>
                  <el-checkbox label="app">APP</el-checkbox>
                  <el-checkbox label="forum">论坛</el-checkbox>
                </div>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="高级方式" name="advanced">
          <el-form :model="advancedPlanForm" label-width="70px" size="small">
            <!-- 高级模式的表单内容 -->
            <el-form-item label="方案名称">
              <el-input v-model="advancedPlanForm.name" placeholder="请输入方案名称"></el-input>
            </el-form-item>

            <!-- 其他高级选项 -->
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="createPlanDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="savePlan">保存</el-button>
      </div>
    </el-dialog>

    <!-- 行业分类弹窗 -->
    <el-dialog
      title="行业分类"
      :visible.sync="industryDialogVisible"
      width="40%"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      append-to-body
      custom-class="industry-dialog"
    >
      <div class="industry-dialog-content">
        <div class="industry-tree-container">
          <el-tree
            :data="industryTreeData"
            :props="industryTreeProps"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleIndustryNodeClick"
          />
        </div>
        <div class="industry-selected-container">
          <div class="industry-selected-title">
            {{ selectedIndustry ? selectedIndustry.label : '请选择行业分类' }}
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="industryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmIndustrySelect">确定</el-button>
      </div>
    </el-dialog>

    <!-- 发送预警弹窗 -->
    <el-dialog
      title="发送预警"
      :visible.sync="sendAlertDialogVisible"
      width="40%"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      append-to-body
      custom-class="send-alert-dialog"
    >
      <div class="send-alert-content">
        <el-form :model="alertForm" label-width="80px" size="small">
          <el-form-item label="预警标题">
            <el-input v-model="alertForm.title" placeholder="请输入预警标题"></el-input>
          </el-form-item>

          <el-form-item label="接收人">
            <div class="receiver-list">
              <div class="receiver-item" v-for="(receiver, index) in receivers" :key="index">
                <div class="receiver-type">{{ receiver.type }}</div>
                <el-select
                  v-model="alertForm.selectedReceivers[index]"
                  :placeholder="'请选择' + receiver.type"
                  class="receiver-select"
                >
                  <el-option
                    v-for="person in receiver.persons"
                    :key="person"
                    :label="person"
                    :value="person"
                  ></el-option>
                </el-select>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelSendAlert">取消</el-button>
        <el-button type="primary" @click="confirmSendAlert">确定</el-button>
      </div>
    </el-dialog>

    <!-- 正面情感信息弹窗 -->
    <el-dialog
      title="情感属性纠错"
      :visible.sync="positiveSentimentDialogVisible"
      width="50%"
      append-to-body
      custom-class="positive-sentiment-dialog"
    >
      <el-radio-group v-model="selectedSentiment" size="small">
        <el-radio-button label="positive">正面</el-radio-button>
        <el-radio-button label="neutral">中性</el-radio-button>
        <el-radio-button label="negative">负面</el-radio-button>
      </el-radio-group>
      <span slot="footer" class="dialog-footer">
        <el-button @click="positiveSentimentDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePositiveDialogConfirm">确定</el-button>
      </span>
    </el-dialog>

    <!-- 加入至报告素材对话框 -->
    <el-dialog
      title="加入至报告素材"
      :visible.sync="addToAlertMaterialDialogVisible"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      custom-class="add-to-alert-material-dialog"
    >
      <div class="add-to-alert-material-content">
        <el-form label-width="80px" size="small">
          <el-form-item label="选择素材库">
            <el-select v-model="selectedMaterialLibrary" placeholder="请选择素材库" style="width: 100%">
              <!-- 这里需要根据实际数据填充选项 -->
              <el-option label="素材库1" value="library1"></el-option>
              <el-option label="素材库2" value="library2"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAddToAlertMaterial">取消</el-button>
        <el-button type="primary" @click="confirmAddToAlertMaterial">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import * as XLSX from 'xlsx'

export default {
  name: 'InfoSummary',
  computed: {
    // 根据当前页码和每页显示数量计算当前页的数据
    paginatedList() {
      // 直接返回从后端获取的分页数据
      // 后端已经处理了分页逻辑，前端不需要再次分页
      return this.infoList || [];
    },

    // 计算选中项数量
    selectedCount() {
      return this.paginatedList.filter(item => item.selected).length;
    },

    // 计算是否全选状态
    isAllSelected: {
      get() {
        if (this.paginatedList.length === 0) return false;
        return this.paginatedList.every(item => item.selected);
      },
      set(value) {
        // 这个setter是为了支持v-model，实际逻辑在handleSelectAll中处理
      }
    },

    // 计算是否为不确定状态（部分选中）
    isIndeterminate() {
      const selectedCount = this.selectedCount;
      return selectedCount > 0 && selectedCount < this.paginatedList.length;
    }
  },
  watch: {
    // 监听时间标签页变化（现在只有自定义选项）
    activeTab(newTab, oldTab) {
      // 现在只有custom选项，不需要清除逻辑
      this.currentPage = 1; // 重置到第一页
      this.loadKeywordData();
      // 重新加载筛选统计数据
      this.loadFilterStatistics();
    },

    // 监听自定义时间范围变化
    customDateRange: {
      handler(newRange, oldRange) {
        // 只有在 activeTab 为 'custom' 且有有效日期范围时才重新加载数据
        if (this.activeTab === 'custom' && newRange && newRange.length === 2) {
          this.currentPage = 1;
          this.loadKeywordData();
          this.loadFilterStatistics();
        }
      },
      deep: true // 深度监听数组变化
    }
  },
  data() {
    return {
      // 页面基础数据
      originalTopNav: undefined, // 存储原始的topNav状态
      searchKeyword: '',
      searchScope: 'all', // 搜索范围：all, title, content, source
      searchSuggestions: [], // 搜索建议列表
      hotKeywords: [], // 热门关键词
      isSearchFocused: false, // 搜索框是否聚焦
      positiveSentimentDialogVisible: false, // 控制正面情感弹窗的显示
      selectedSentiment: '', // 用于情感属性纠错弹窗的选中值
      editingItem: null, // 当前编辑的情感条目
      activeMenu: '2', // 默认选中品牌
      activeTab: 'custom', // 默认选中自定义
      customDateRange: null, // 自定义时间范围

      // 导航栏菜单数据
      menuItems: [
        { index: '1', name: '总览', icon: 'el-icon-s-home', count: 0, web: 'all' }
      ],
      currentWebSite: 'all', // 当前选中的网站

      // 临时筛选条件（用户选择但未应用的条件）
      sentimentTypes: [], // 临时选中的情感类型

      // 已应用的筛选条件（实际用于API请求的条件）
      appliedSentimentTypes: [], // 已应用的情感类型

      // 分页相关数据
      currentPage: 1,
      pageSize: 10,
      totalItems: 120, // 假设总共有120条数据

      // 筛选统计数据
      filterStatistics: null,
      sentimentOptions: [],
      sourceOptions: [],
      keywordOptions: [],

      // 新建方案对话框相关数据
      createPlanDialogVisible: false,
      planActiveTab: 'standard',
      themeInputVisible: false,
      themeInputValue: '',
      planForm: {
        name: '',
        scope: 'all',
        monitorObject: '',
        location: '',
        themes: [],
        industry: '',
        timeRange: '',
        channels: ['news', 'weibo', 'wechat']
      },
      advancedPlanForm: {
        name: ''
      },

      // 行业分类弹窗相关数据
      industryDialogVisible: false,
      selectedIndustry: null,
      industryTreeProps: {
        label: 'label',
        children: 'children'
      },

      // 发送预警弹窗相关数据
      sendAlertDialogVisible: false,
      addToAlertMaterialDialogVisible: false, // 新增：控制加入报告素材弹窗的显示
      selectedMaterialLibrary: '', // 新增：选中的素材库
      alertForm: {
        title: '',
        selectedReceivers: ['', '', '', '', '', '']
      },
      receivers: [
        {
          type: '总监',
          persons: ['王总监', '李总监', '张总监']
        },
        {
          type: '经理',
          persons: ['王经理', '李经理', '张经理']
        },
        {
          type: '主管',
          persons: ['王主管', '李主管', '张主管']
        },
        {
          type: '员工',
          persons: ['王员工', '李员工', '张员工']
        },
        {
          type: '外部人员',
          persons: ['外部人员1', '外部人员2', '外部人员3']
        },
        {
          type: '其他',
          persons: ['其他人员1', '其他人员2', '其他人员3']
        }
      ],
      industryTreeData: [
        {
          id: 1,
          label: '制造',
          children: []
        },
        {
          id: 2,
          label: '公共',
          children: []
        },
        {
          id: 3,
          label: '教育',
          children: []
        },
        {
          id: 4,
          label: '工业设备',
          children: []
        },
        {
          id: 5,
          label: '环保设备',
          children: []
        },
        {
          id: 6,
          label: '金融',
          children: []
        },
        {
          id: 7,
          label: '商业',
          children: []
        },
        {
          id: 8,
          label: '民用与商用',
          children: []
        },
        {
          id: 9,
          label: '政府部门',
          children: []
        }
      ],

      // 信息列表数据
      infoList: [],

      // 导出相关状态
      exportLoading: false
    };
  },
  mounted() {
    // 隐藏顶部导航栏
    this.originalTopNav = this.$store.state.settings.topNav
    this.$store.dispatch('settings/changeSetting', {
      key: 'topNav',
      value: false
    })
    // 加载菜单项数据
    this.loadMenuItems()
    // 加载筛选统计数据
    this.loadFilterStatistics()
    // 加载关键词数据
    this.loadKeywordData()
    // 加载热门关键词
    this.loadHotKeywords()
  },
  beforeDestroy() {
    // 恢复顶部导航栏设置
    if (this.originalTopNav !== undefined) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'topNav',
        value: this.originalTopNav
      })
    }
  },
  methods: {
    // 加载关键词数据
    async loadKeywordData() {
      console.log('开始加载关键词数据...');
      console.log('当前页码:', this.currentPage, '每页数量:', this.pageSize);
      console.log('当前标签页:', this.activeTab);
      console.log('搜索关键词:', this.searchKeyword);

      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          timeFilter: this.activeTab !== 'custom' ? this.activeTab : undefined,
          keyword: this.searchKeyword.trim() || '' // 添加搜索关键词参数
        }

        // 添加筛选条件到请求参数中（包括空数组，让后端知道要清除筛选）
        console.log('准备发送的筛选条件:', {
          appliedSentimentTypes: this.appliedSentimentTypes
        });

        if (this.appliedSentimentTypes.length > 0) {
          params.sentimentTypes = this.appliedSentimentTypes.join(',');
        }

        // 根据当前选中菜单项的 fieldType 决定发送哪个参数
        const currentMenuItem = this.menuItems.find(item => item.index === this.activeMenu);
        if (this.currentWebSite && this.currentWebSite !== 'all') {
          if (currentMenuItem && currentMenuItem.fieldType === 'type') {
            // 发送 typeField 参数（平台类型筛选）
            params.typeField = this.currentWebSite;
          } else {
            // 发送 webSite 参数（网站筛选）
            params.webSite = this.currentWebSite;
          }
        }

        // 如果是自定义时间范围，添加时间参数
        if (this.activeTab === 'custom' && this.customDateRange && this.customDateRange.length === 2) {
          params.beginTime = this.formatDate(this.customDateRange[0]);
          params.endTime = this.formatDate(this.customDateRange[1]);
        }

        console.log('发送到后端的参数:', params);
        console.log('当前webSite筛选值:', this.currentWebSite);

        const response = await request({
          url: '/public/keywordData/list',
          method: 'get',
          params: params
        })

        if (response.code === 200) {
          console.log('API响应数据:', response.data);
          console.log('获取到的数据行数:', response.data.rows ? response.data.rows.length : 0);

          // 将API返回的数据转换为前端需要的格式
          this.infoList = response.data.rows || []
          this.totalItems = response.data.total || 0

          console.log('更新后的infoList长度:', this.infoList.length);
          console.log('infoList数据示例:', this.infoList.slice(0, 2));

          // 更新菜单项的数据统计（如果后端返回了统计数据）
          if (response.data.menuCounts) {
            this.updateMenuCounts(response.data.menuCounts);
          }
        } else {
          this.$message.error('获取数据失败：' + response.msg)
        }
      } catch (error) {
        console.error('加载关键词数据失败:', error)
        this.$message.error('加载数据失败，请稍后重试')
      }
    },

    // 加载筛选统计数据
    async loadFilterStatistics() {
      try {
        // 构建筛选参数
        const params = {}

        // 添加关键词筛选
        if (this.searchKeyword && this.searchKeyword.trim()) {
          params.keyword = this.searchKeyword.trim()
        }

        // 添加时间范围筛选（现在只有自定义时间）
        if (this.activeTab === 'custom' && this.customDateRange && this.customDateRange.length === 2) {
          params.startDate = this.formatDate(this.customDateRange[0])
          params.endDate = this.formatDate(this.customDateRange[1])
        }

        // 根据当前选中菜单项的 fieldType 决定发送哪个参数
        const currentMenuItem = this.menuItems.find(item => item.index === this.activeMenu);
        if (this.currentWebSite && this.currentWebSite !== 'all') {
          if (currentMenuItem && currentMenuItem.fieldType === 'type') {
            // 发送 typeField 参数（平台类型筛选）
            params.typeField = this.currentWebSite;
          } else {
            // 发送 webSite 参数（网站筛选）
            params.webSite = this.currentWebSite;
          }
        }

        // 添加情感类型筛选（使用已应用的筛选条件）
        if (this.appliedSentimentTypes && this.appliedSentimentTypes.length > 0) {
          params.sentimentTypes = this.appliedSentimentTypes.join(',')
        }

        console.log('发送筛选统计参数:', params)

        const response = await request({
          url: '/public/keywordData/filter-statistics',
          method: 'get',
          params: params
        })

        console.log('筛选统计API响应:', response)

        if (response.code === 200) {
          console.log('API返回的原始数据:', response.data)
          this.filterStatistics = response.data
          console.log('设置filterStatistics后的值:', this.filterStatistics)
          this.updateFilterOptions()
        } else {
          console.error('获取筛选统计数据失败：', response.msg)
          console.error('完整响应:', response)
          // 如果获取失败，使用默认数据
          this.useDefaultFilterOptions()
        }
      } catch (error) {
        console.error('加载筛选统计数据失败:', error)
        console.error('错误详情:', error.stack)
        // 如果获取失败，使用默认数据
        this.useDefaultFilterOptions()
      }
    },

    // 更新筛选选项
    updateFilterOptions() {
      console.log('开始更新筛选选项，filterStatistics:', this.filterStatistics)

      if (!this.filterStatistics) {
        console.error('filterStatistics 为空，使用默认选项')
        this.useDefaultFilterOptions()
        return
      }

      try {
        // 更新情感倾向选项
        if (this.filterStatistics.sentimentTypes && Array.isArray(this.filterStatistics.sentimentTypes)) {
          this.sentimentOptions = this.filterStatistics.sentimentTypes.map(item => ({
            label: item.label,
            value: item.value,
            count: item.totalCount,
            todayCount: item.todayCount
          }))
          console.log('sentimentOptions 更新成功:', this.sentimentOptions)
        } else {
          console.error('sentimentTypes 数据格式不正确:', this.filterStatistics.sentimentTypes)
          this.sentimentOptions = []
        }

        // 更新来源网站选项
        if (this.filterStatistics.sourceWebsites && Array.isArray(this.filterStatistics.sourceWebsites)) {
          this.sourceOptions = this.filterStatistics.sourceWebsites.map(item => ({
            label: item.label,
            value: item.value,
            count: item.totalCount,
            todayCount: item.todayCount
          }))
          console.log('sourceOptions 更新成功:', this.sourceOptions)
        } else {
          console.error('sourceWebsites 数据格式不正确:', this.filterStatistics.sourceWebsites)
          this.sourceOptions = []
        }

        // 更新关键词选项
        if (this.filterStatistics.keywords && Array.isArray(this.filterStatistics.keywords)) {
          this.keywordOptions = this.filterStatistics.keywords.map(item => ({
            label: item.label,
            value: item.value,
            count: item.totalCount,
            todayCount: item.todayCount
          }))
          console.log('keywordOptions 更新成功:', this.keywordOptions)
        } else {
          console.error('keywords 数据格式不正确:', this.filterStatistics.keywords)
          this.keywordOptions = []
        }

        // 强制触发Vue响应式更新
        this.$forceUpdate()
        console.log('所有筛选选项更新完成')
      } catch (error) {
        console.error('更新筛选选项时发生错误:', error)
        this.useDefaultFilterOptions()
      }
    },

    // 使用默认筛选选项
    useDefaultFilterOptions() {
      this.sentimentOptions = [
        { label: '正面', value: 'positive', count: 217, todayCount: 46 },
        { label: '中性', value: 'neutral', count: 8, todayCount: 7 },
        { label: '负面', value: 'negative', count: 57, todayCount: 3 },
        { label: '其他', value: 'other', count: 3, todayCount: 1 }
      ]

      this.sourceOptions = [
        { label: '新浪财经', value: 'sina_finance', count: 89, todayCount: 12 },
        { label: '科技日报', value: 'tech_daily', count: 67, todayCount: 8 },
        { label: '消费者报告', value: 'consumer_report', count: 45, todayCount: 6 },
        { label: '企业官网', value: 'company_official', count: 123, todayCount: 15 },
        { label: '家电网', value: 'appliance_net', count: 78, todayCount: 9 }
      ]

      this.keywordOptions = [
        { label: '卡萨帝', value: 'casarte', count: 156, todayCount: 23 },
        { label: '华帝', value: 'vatti', count: 134, todayCount: 18 },
        { label: '方太', value: 'fotile', count: 98, todayCount: 15 },
        { label: '智能家电', value: 'smart_appliance', count: 87, todayCount: 12 },
        { label: '厨电', value: 'kitchen_appliance', count: 76, todayCount: 10 }
      ]
    },

    // 菜单点击事件
    handleMenuClick(menuItem) {
      console.log('选择菜单:', menuItem);
      console.log('菜单web字段值:', menuItem.web);
      console.log('菜单type字段值:', menuItem.type);

      // 更新当前选中的菜单
      this.activeMenu = menuItem.index;
      // 取消web字段绑定，不再设置currentWebSite
      this.currentWebSite = menuItem.web;

      console.log('当前currentWebSite保持为:', this.currentWebSite);

      // 重置到第一页
      this.currentPage = 1;

      // 加载数据（不传递web参数）
      this.loadKeywordDataByWebSite();

      // 显示筛选成功消息
      this.$message.success(`已切换到${menuItem.name}数据`);
    },

    // 根据网站加载数据
    async loadKeywordDataByWebSite(webSite) {
      // 调用通用的数据加载方法，网站参数已在loadKeywordData中处理
      await this.loadKeywordData();
      // 重新加载筛选统计数据
      await this.loadFilterStatistics();
    },

    // 加载菜单项数据
    async loadMenuItems() {
      try {
        const response = await request({
          url: '/public/keywordData/web-statistics',
          method: 'get'
        });

        if (response.code === 200 && response.data && response.data.websites) {
          // 清空现有菜单项（保留总览）
          this.menuItems = [
            { index: '1', name: '总览', icon: 'el-icon-s-home', count: response.data.totalCount || 0, web: 'all', type: 'overview' }
          ];

          // 添加从数据库获取的网站菜单项
          response.data.websites.forEach((item, index) => {
            // 根据后端数据库实际存储的关键词进行映射
            // 后端使用模糊匹配 web.like('%{webSite}%')，数据库中存储的是中文关键词
            const webMapping = {
              '新浪新闻': '新浪',
              '腾讯新闻': '腾讯',
              '网易新闻': '网易',
              '搜狐新闻': '搜狐',
              '凤凰网': '凤凰',
              '央视网': '央视',
              '方太': '方太',
              '华帝': '华帝',
              '老板': '老板',
              '西门子': '西门子',
              '卡萨帝': '卡萨帝',
              '东方财富': '东方财富',
              '每日经济新闻': '每日经济新闻'
            };

            // 判断菜单项类型并设置 fieldType
            const menuName = item.name;
            let fieldType = 'web'; // 默认为网站类型

            // 根据菜单名称判断是平台类型还是网站类型
            // 排除具体的网站名称，只对纯平台类型进行判断
            const platformTypes = ['新闻', '微博', '微信', '视频', 'APP', '论坛', '博客'];
            const specificWebsites = ['每日经济新闻', '新浪新闻', '腾讯新闻', '网易新闻', '搜狐新闻', '凤凰网', '央视网'];

            if (platformTypes.some(platform => menuName.includes(platform)) &&
                !specificWebsites.includes(menuName)) {
              fieldType = 'type';
            }

            this.menuItems.push({
              index: String(index + 2),
              name: item.name,
              icon: 'el-icon-monitor', // 设置默认图标
              count: item.count,
              web: webMapping[item.name] || item.name, // 使用映射的关键词，如果没有映射则使用原名称
              type: webMapping[item.name] || item.name, // 添加 type 字段，与 web 字段保持一致
              fieldType: fieldType // 添加字段类型标识
            });
          });
        } else {
          console.error('获取网站菜单数据失败：', response.msg);
          // 如果获取失败，使用默认菜单
          this.setDefaultMenuItems();
        }
      } catch (error) {
        console.error('加载菜单项失败:', error);
        // 如果获取失败，使用默认菜单
        this.setDefaultMenuItems();
      }
    },

    // 设置默认菜单项
    setDefaultMenuItems() {
      this.menuItems = [
        { index: '1', name: '总11览', icon: 'el-icon-s-home', count: 0, web: 'all', type: 'overview', fieldType: 'web' },
        { index: '2', name: '东方财11富', icon: 'el-icon-monitor', count: 289, web: '东方财富', type: '东方财富', fieldType: 'web' },
        { index: '3', name: '每日经济新11闻', icon: 'el-icon-monitor', count: 213, web: '每日经济新闻', type: '每日经济新闻', fieldType: 'web' }
      ];
    },

    // 更新菜单项的数据统计
    updateMenuCounts(menuCounts) {
      if (!menuCounts) return;

      this.menuItems.forEach(item => {
        if (menuCounts[item.type] !== undefined) {
          item.count = menuCounts[item.type];
        }
      });
    },

     // 加载菜单统计数据（从数据库web字段获取）
     async loadMenuStatistics() {
       try {
         const response = await request({
           url: '/public/keywordData/web-statistics',
           method: 'get'
         });

         if (response.code === 200 && response.data && response.data.websites) {
          // 构建菜单项数据
          this.menuItems = [
            { index: '1', name: '总览', icon: 'el-icon-s-home', count: response.data.totalCount || 0, web: 'all', type: 'overview' }
          ];

          // 添加网站菜单项
          response.data.websites.forEach((item, index) => {
            // 根据后端数据库实际存储的关键词进行映射
            // 后端使用模糊匹配 web.like('%{webSite}%')，数据库中存储的是中文关键词
            const webMapping = {
              '新浪新闻': '新浪',
              '腾讯新闻': '腾讯',
              '网易新闻': '网易',
              '搜狐新闻': '搜狐',
              '凤凰网': '凤凰',
              '央视网': '央视',
              '方太': '方太',
              '华帝': '华帝',
              '老板': '老板',
              '西门子': '西门子',
              '卡萨帝': '卡萨帝',
              '东方财富': '东方财富',
              '每日经济新闻': '每日经济新闻'
            };

            this.menuItems.push({
              index: String(index + 2),
              name: item.name,
              icon: 'el-icon-monitor',
              count: item.count,
              web: webMapping[item.name] || item.name, // 使用映射的关键词，如果没有映射则使用原名称
              type: webMapping[item.name] || item.name // 添加 type 字段，与 web 字段保持一致
            });
          });

          // 设置默认选中第一个菜单项
          if (this.menuItems.length > 0) {
            this.activeMenu = this.menuItems[0].index;
            this.currentMenuType = this.menuItems[0].type;
          }
        }
       } catch (error) {
         console.error('加载菜单统计数据失败:', error);
         // 如果获取统计数据失败，使用默认值
         this.setDefaultMenuCounts();
       }
     },

     // 设置默认菜单统计数据
     setDefaultMenuCounts() {
       const defaultCounts = {
         overview: 0,
         '东方财富': 289,
         '每日经济新闻': 213,
         '新浪': 0,
         '腾讯': 0,
         '网易': 0,
         '搜狐': 0,
         '凤凰': 0,
         '央视': 0,
         '方太': 0,
         '华帝': 0,
         '老板': 0,
         '西门子': 0,
         '卡萨帝': 0
       };
       this.updateMenuCounts(defaultCounts);
     },

     // 获取情感图标
    getSentimentIcon(sentiment) {
      const icons = {
        positive: 'el-icon-sunny',
        neutral: 'el-icon-partly-cloudy',
        negative: 'el-icon-cloudy'
      };
      return icons[sentiment] || 'el-icon-question';
    },

    // 获取情感文本
    getSentimentText(sentiment) {
      const texts = {
        positive: '正面',
        neutral: '中性',
        negative: '负面'
      };
      return texts[sentiment] || '未知';
    },

    // 下拉菜单命令处理
    handleCommand(command) {
      console.log('执行命令:', command);
    },

    // 处理搜索输入
    handleSearchInput() {
      // 如果搜索框为空，自动重新加载数据
      if (!this.searchKeyword.trim()) {
        this.handleSearchClear();
      }
    },

    // 处理搜索关键词
    handleSearchKeyword() {
      if (this.searchKeyword.trim()) {
        // 保存搜索历史
        this.saveSearchHistory(this.searchKeyword.trim());

        // 在当前页面进行搜索，重置到第一页并重新加载数据
        this.currentPage = 1;
        this.loadKeywordData();
        // 重新加载筛选统计数据
        this.loadFilterStatistics();

        this.$message.success(`正在搜索: ${this.searchKeyword.trim()}`);
      } else {
        this.$message.warning('请输入搜索关键词');
      }
    },

    // 处理搜索清空
    handleSearchClear() {
      this.searchKeyword = '';
      this.currentPage = 1;
      this.loadKeywordData();
      // 重新加载筛选统计数据
      this.loadFilterStatistics();
      this.$message.info('已清空搜索条件');
    },

    // 保存搜索历史
    saveSearchHistory(keyword) {
      try {
        let searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');

        // 移除重复项
        searchHistory = searchHistory.filter(item => item !== keyword);

        // 添加到开头
        searchHistory.unshift(keyword);

        // 限制历史记录数量
        if (searchHistory.length > 10) {
          searchHistory = searchHistory.slice(0, 10);
        }

        localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
      } catch (error) {
        console.error('保存搜索历史失败:', error);
      }
    },

    // 获取搜索历史
    getSearchHistory() {
      try {
        return JSON.parse(localStorage.getItem('searchHistory') || '[]');
      } catch (error) {
        console.error('获取搜索历史失败:', error);
        return [];
      }
    },

    // 加载热门关键词
    async loadHotKeywords() {
      try {
        // 这里可以调用API获取热门关键词，暂时使用模拟数据
        this.hotKeywords = [
          { value: '方太', type: '品牌', icon: 'el-icon-star-on' },
          { value: '智能厨电', type: '产品', icon: 'el-icon-goods' },
          { value: '油烟机', type: '产品', icon: 'el-icon-goods' },
          { value: '燃气灶', type: '产品', icon: 'el-icon-goods' },
          { value: '消毒柜', type: '产品', icon: 'el-icon-goods' },
          { value: '蒸烤箱', type: '产品', icon: 'el-icon-goods' },
          { value: '洗碗机', type: '产品', icon: 'el-icon-goods' },
          { value: '集成灶', type: '产品', icon: 'el-icon-goods' }
        ];
      } catch (error) {
        console.error('加载热门关键词失败:', error);
      }
    },

    // 异步查询搜索建议
    querySearchAsync(queryString, callback) {
      const suggestions = this.createSearchSuggestions(queryString);
      // 模拟异步请求延迟
      setTimeout(() => {
        callback(suggestions);
      }, 100);
    },

    // 创建搜索建议
    createSearchSuggestions(queryString) {
      const suggestions = [];

      if (!queryString || queryString.trim() === '') {
        // 如果没有输入，显示搜索历史和热门关键词
        const searchHistory = this.getSearchHistory();

        // 添加搜索历史
        searchHistory.slice(0, 5).forEach(item => {
          suggestions.push({
            value: item,
            type: '历史',
            icon: 'el-icon-time'
          });
        });

        // 添加热门关键词
        this.hotKeywords.slice(0, 5).forEach(item => {
          suggestions.push(item);
        });

        return suggestions;
      }

      // 根据输入内容过滤建议
      const query = queryString.toLowerCase();

      // 从搜索历史中匹配
      const searchHistory = this.getSearchHistory();
      searchHistory.forEach(item => {
        if (item.toLowerCase().includes(query)) {
          suggestions.push({
            value: item,
            type: '历史',
            icon: 'el-icon-time'
          });
        }
      });

      // 从热门关键词中匹配
      this.hotKeywords.forEach(item => {
        if (item.value.toLowerCase().includes(query)) {
          suggestions.push(item);
        }
      });

      // 从关键词选项中匹配
      this.keywordOptions.forEach(item => {
        if (item.label.toLowerCase().includes(query)) {
          suggestions.push({
            value: item.label,
            type: '关键词',
            icon: 'el-icon-search'
          });
        }
      });

      // 去重并限制数量
      const uniqueSuggestions = suggestions.filter((item, index, self) =>
        index === self.findIndex(t => t.value === item.value)
      );

      return uniqueSuggestions.slice(0, 8);
    },

    // 处理搜索建议选择
    handleSearchSelect(item) {
      this.searchKeyword = item.value;
      this.handleSearchKeyword();
    },

    // 处理搜索框聚焦
    handleSearchFocus() {
      this.isSearchFocused = true;
    },

    // 处理搜索框失焦
    handleSearchBlur() {
      // 延迟失焦，避免点击建议项时立即隐藏
      setTimeout(() => {
        this.isSearchFocused = false;
      }, 200);
    },

    // 处理搜索选项命令
    handleSearchOptionCommand(command) {
      this.searchScope = command;
      const scopeText = {
        'all': '全部',
        'title': '标题',
        'content': '内容',
        'source': '来源'
      };
      this.$message.success(`搜索范围已设置为：${scopeText[command]}`);
    },

    // 分页相关方法
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // 切换每页显示数量时，重置为第一页
      console.log('每页显示数量:', size);
      this.loadKeywordData(); // 重新加载数据
    },

    handleCurrentChange(page) {
      this.currentPage = page;
      console.log('当前页码:', page);
      this.loadKeywordData(); // 重新加载数据
    },

    // 新建方案相关方法
    showCreatePlanDialog() {
      this.createPlanDialogVisible = true;
    },

    // 主题标签相关方法
    handleRemoveTheme(tag) {
      this.planForm.themes.splice(this.planForm.themes.indexOf(tag), 1);
    },

    showThemeInput() {
      this.themeInputVisible = true;
      this.$nextTick(_ => {
        this.$refs.themeInput.$refs.input.focus();
      });
    },

    handleAddTheme() {
      let inputValue = this.themeInputValue;
      if (inputValue) {
        if (!this.planForm.themes.includes(inputValue)) {
          this.planForm.themes.push(inputValue);
        }
      }
      this.themeInputVisible = false;
      this.themeInputValue = '';
    },

    // 监测对象下拉菜单命令处理
    handleMonitorObjectCommand(command) {
      this.planForm.monitorObject = command;
    },

    // 显示地域选择地图
    showLocationMap() {
      // 这里可以实现地图选择功能
      this.$message.info('显示地域选择地图');
    },

    // 显示行业分类选择
    showIndustrySelect() {
      this.industryDialogVisible = true;
    },

    // 处理行业分类树节点点击
    handleIndustryNodeClick(data) {
      this.selectedIndustry = data;
    },

    // 确认行业分类选择
    confirmIndustrySelect() {
      if (this.selectedIndustry) {
        this.planForm.industry = this.selectedIndustry.label;
      }
      this.industryDialogVisible = false;
    },

    // 保存方案
    savePlan() {
      // 根据当前激活的标签页选择不同的表单数据
      const formData = this.planActiveTab === 'standard' ? this.planForm : this.advancedPlanForm;

      console.log('保存方案:', formData);
      // 这里可以添加表单验证和提交到后端的逻辑

      // 关闭对话框
      this.createPlanDialogVisible = false;

      // 重置表单
      if (this.planActiveTab === 'standard') {
        this.planForm = {
          name: '',
          scope: 'all',
          monitorObject: '',
          location: '',
          themes: [],
          industry: '',
          timeRange: '',
          channels: ['news', 'weibo', 'wechat']
        };
      } else {
        this.advancedPlanForm = {
          name: ''
        };
      }
    },

    // 显示发送预警弹窗
    showSendAlertDialog() {
      this.sendAlertDialogVisible = true;
    },

    // 取消发送预警
    cancelSendAlert() {
      this.sendAlertDialogVisible = false;
      // 重置表单
      this.alertForm = {
        title: '',
        selectedReceivers: ['', '', '', '', '', '']
      };
    },
    handleSentimentClick(item) {
      this.editingItem = item;
      this.selectedSentiment = item.sentiment;
      this.positiveSentimentDialogVisible = true;
    },
    handlePositiveDialogConfirm() {
      if (this.editingItem) {
        this.editingItem.sentiment = this.selectedSentiment;
        // 在实际应用中，这里可能需要调用API将更改保存到后端
        // 例如: this.updateSentimentApi(this.editingItem.id, this.selectedSentiment);
      }
      this.positiveSentimentDialogVisible = false;
    },

    // 确认发送预警
    confirmSendAlert() {
      // 在这里处理发送预警的逻辑
      console.log('发送预警:', this.alertForm);
      this.sendAlertDialogVisible = false;
      // 清空表单
      this.alertForm = {
        title: '',
        selectedReceivers: ['', '', '', '', '', '']
      };
    },

    // 加入至报告素材对话框相关方法
    showAddToAlertMaterialDialog() {
      this.addToAlertMaterialDialogVisible = true;
    },
    cancelAddToAlertMaterial() {
      this.addToAlertMaterialDialogVisible = false;
      this.selectedMaterialLibrary = ''; // 清空选中值
    },
    confirmAddToAlertMaterial() {
      // 这里添加确认逻辑，例如提交选中的素材库
      console.log('Selected Material Library:', this.selectedMaterialLibrary);
      this.addToAlertMaterialDialogVisible = false;
      this.selectedMaterialLibrary = ''; // 清空选中值
    },

    // 显示原稿校对对话框
    showOriginalProofreadingDialog() {
      this.$message.info('原稿校对功能开发中...');
    },

    // 显示信息图谱对话框
    showInfoGraphDialog() {
      this.$message.info('信息图谱功能开发中...');
    },

    // 处理筛选按钮点击
    handleFilter() {
      console.log('开始应用筛选条件...');
      console.log('当前临时筛选条件:', {
        sentimentTypes: this.sentimentTypes
      });

      // 将临时筛选条件应用到已应用的筛选条件
      this.appliedSentimentTypes = [...this.sentimentTypes];

      console.log('已应用的筛选条件:', {
        appliedSentimentTypes: this.appliedSentimentTypes
      });

      // 重置到第一页
      this.currentPage = 1;

      // 重新加载数据
      this.loadKeywordData();
      // 重新加载筛选统计数据
      this.loadFilterStatistics();

      // 根据当前选择的时间类型显示不同的消息
      if (this.activeTab === 'custom' && this.customDateRange) {
        this.$message.success(`筛选条件已应用（时间范围：${this.formatDate(this.customDateRange[0])} 至 ${this.formatDate(this.customDateRange[1])}）`);
      } else {
        this.$message.success('筛选条件已应用');
      }
    },

    // 处理重置按钮点击
    handleResetFilter() {
      console.log('开始重置筛选条件...');

      // 重置临时筛选条件到默认值（清空所有筛选）
      this.sentimentTypes = [];

      // 将重置后的条件应用到已应用的筛选条件
      this.appliedSentimentTypes = [...this.sentimentTypes];

      // 重置自定义时间范围
      this.customDateRange = null;

      // 保持自定义时间标签
      this.activeTab = 'custom';

      // 重置到第一页
      this.currentPage = 1;

      console.log('重置后的筛选条件:', {
        sentimentTypes: this.sentimentTypes,
        appliedSentimentTypes: this.appliedSentimentTypes
      });

      // 重新加载数据
      this.loadKeywordData();
      // 重新加载筛选统计数据
      this.loadFilterStatistics();

      this.$message.success('所有筛选条件已重置');
    },

    // 处理自定义日期变化
    handleCustomDateChange(dateRange) {
      if (dateRange && dateRange.length === 2) {
        // 自动应用筛选条件
        this.currentPage = 1;
        this.loadKeywordData();
        // 重新加载筛选统计数据
        this.loadFilterStatistics();
        this.$message.success(`已选择时间范围：${this.formatDate(dateRange[0])} 至 ${this.formatDate(dateRange[1])}`);
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 设置快速日期范围
    setQuickDate(days) {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - days + 1);

      this.customDateRange = [startDate, endDate];
      this.handleCustomDateChange(this.customDateRange);
    },

    // 清除自定义日期
    clearCustomDate() {
      this.customDateRange = null;
      // 保持自定义标签
      this.activeTab = 'custom';
      this.currentPage = 1;
      this.loadKeywordData();
      this.$message.success('已清除自定义时间范围');
    },

    // 切换信息项的展开/收起状态
    toggleItemExpand(item, index) {
      // 使用Vue.set确保响应式更新
      this.$set(item, 'expanded', !item.expanded);
    },

    // 处理全选/取消全选
    handleSelectAll(value) {
      // value 参数来自复选框的 change 事件
      const shouldSelectAll = value;

      this.paginatedList.forEach(item => {
        this.$set(item, 'selected', shouldSelectAll);
      });

      const message = shouldSelectAll ?
        `已选中当前页面所有 ${this.paginatedList.length} 项` :
        '已取消选中当前页面所有项';
      this.$message.success(message);
    },

    // 获取选中的数据项
    getSelectedItems() {
      return this.paginatedList.filter(item => item.selected);
    },

    // 格式化导出数据
    formatExportData(items) {
      return items.map((item, index) => ({
        '序号': index + 1,
        '标题': this.stripHtml(item.title || ''),
        '内容': this.stripHtml(item.content || ''),
        '来源': item.source || '',
        '时间': item.time || '',
        '平台': item.platform_name || '',
        '情感倾向': this.getSentimentText(item.sentiment),
        '浏览量': item.views || 0,
        '评论数': item.comments || 0
      }));
    },

    // 去除HTML标签
    stripHtml(html) {
      if (!html) return '';
      const div = document.createElement('div');
      div.innerHTML = html;
      return div.textContent || div.innerText || '';
    },

    // 处理导出功能
    async handleExport(format = 'excel') {
      const selectedItems = this.getSelectedItems();

      if (selectedItems.length === 0) {
        this.$message.warning('请先选择要导出的数据');
        return;
      }

      this.exportLoading = true;

      try {
        if (format === 'excel') {
          await this.exportToExcel(selectedItems);
        } else if (format === 'html') {
          await this.exportToHtml(selectedItems);
        }
      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败，请稍后重试');
      } finally {
        this.exportLoading = false;
      }
    },

    // Excel导出
    async exportToExcel(selectedItems) {
      // 格式化导出数据
      const exportData = this.formatExportData(selectedItems);

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [
        { wch: 8 },   // 序号
        { wch: 50 },  // 标题
        { wch: 80 },  // 内容
        { wch: 20 },  // 来源
        { wch: 20 },  // 时间
        { wch: 15 },  // 平台
        { wch: 12 },  // 情感倾向
        { wch: 12 },  // 浏览量
        { wch: 12 }   // 评论数
      ];
      ws['!cols'] = colWidths;

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '信息汇总');

      // 生成文件名
      const now = new Date();
      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
      const filename = `信息汇总_${dateStr}_${timeStr}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, filename);

      this.$message.success(`成功导出 ${selectedItems.length} 条数据到 ${filename}`);
    },

    // HTML报表导出
    async exportToHtml(selectedItems) {
      // 直接生成HTML报表内容
      const reportHtml = this.generateReportHtml(selectedItems);

      // 创建并下载HTML文件
      this.downloadHtmlFile(reportHtml, selectedItems.length);
    },

    // 生成报表HTML内容
    generateReportHtml(selectedItems) {
      const dateRange = this.getDateRangeText();
      const reportDate = new Date().toLocaleString('zh-CN');

      // 统计数据
      const totalCount = selectedItems.length;
      const positiveCount = selectedItems.filter(item => item.sentiment === 'positive').length;
      const neutralCount = selectedItems.filter(item => item.sentiment === 'neutral').length;
      const negativeCount = selectedItems.filter(item => item.sentiment === 'negative').length;

      // 来源统计
      const sourceMap = {};
      selectedItems.forEach(item => {
        const source = item.source || '未知来源';
        sourceMap[source] = (sourceMap[source] || 0) + 1;
      });

      const sourceData = Object.entries(sourceMap).map(([name, value]) => ({ name, value }));

      return this.generateCompleteHtml(this.generateReportBody({
        reportDate,
        dateRange,
        totalCount,
        positiveCount,
        neutralCount,
        negativeCount,
        sourceData,
        selectedItems
      }));
    },

    // 生成报表主体内容
    generateReportBody(data) {
      const { reportDate, dateRange, totalCount, positiveCount, neutralCount, negativeCount, sourceData, selectedItems } = data;

      return `
        <div class="report-container">
          <!-- 报表头部 -->
          <div class="report-header">
            <div class="report-title">
              <h1>信息汇总分析报表</h1>
              <div class="report-meta">
                <span class="report-date">生成时间：${reportDate}</span>
                ${dateRange ? `<span class="report-range">数据范围：${dateRange}</span>` : ''}
              </div>
            </div>
          </div>

          <!-- 统计概览 -->
          <div class="report-section">
            <h2 class="section-title">数据概览</h2>
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-number">${totalCount}</div>
                <div class="stat-label">总信息数</div>
              </div>
              <div class="stat-card positive">
                <div class="stat-number">${positiveCount}</div>
                <div class="stat-label">正面信息</div>
              </div>
              <div class="stat-card neutral">
                <div class="stat-number">${neutralCount}</div>
                <div class="stat-label">中性信息</div>
              </div>
              <div class="stat-card negative">
                <div class="stat-number">${negativeCount}</div>
                <div class="stat-label">负面信息</div>
              </div>
            </div>
          </div>

          <!-- 图表分析 -->
          <div class="report-section">
            <h2 class="section-title">数据分析</h2>
            <div class="charts-grid">
              <div class="chart-container">
                <h3 class="chart-title">情感倾向分布</h3>
                <div id="sentimentChart" class="chart"></div>
              </div>
              <div class="chart-container">
                <h3 class="chart-title">来源分布</h3>
                <div id="sourceChart" class="chart"></div>
              </div>
            </div>
          </div>

          <!-- 详细数据列表 -->
          <div class="report-section">
            <h2 class="section-title">详细信息列表</h2>
            <div class="data-table">
              <table class="report-table">
                <thead>
                  <tr>
                    <th>序号</th>
                    <th>标题</th>
                    <th>来源</th>
                    <th>时间</th>
                    <th>情感倾向</th>
                    <th>浏览量</th>
                    <th>评论数</th>
                  </tr>
                </thead>
                <tbody>
                  ${selectedItems.map((item, index) => `
                    <tr>
                      <td>${index + 1}</td>
                      <td class="title-cell">${this.stripHtml(item.title || '')}</td>
                      <td>${item.source || ''}</td>
                      <td>${item.time || ''}</td>
                      <td>
                        <span class="sentiment-tag sentiment-${item.sentiment}">
                          ${this.getSentimentText(item.sentiment)}
                        </span>
                      </td>
                      <td>${item.views || 0}</td>
                      <td>${item.comments || 0}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
          </div>

          <!-- 报表尾部 -->
          <div class="report-footer">
            <div class="footer-info">
              <p>本报表由智库系统自动生成</p>
              <p>生成时间：${reportDate}</p>
            </div>
          </div>
        </div>

        <script>
          // 初始化图表
          setTimeout(() => {
            if (window.echarts) {
              // 情感倾向饼图
              const sentimentChart = echarts.init(document.getElementById('sentimentChart'));
              const sentimentOption = {
                tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
                legend: { orient: 'vertical', left: 10, data: ['正面', '中性', '负面'] },
                series: [{
                  name: '情感倾向',
                  type: 'pie',
                  radius: ['40%', '70%'],
                  center: ['60%', '50%'],
                  data: [
                    { name: '正面', value: ${positiveCount}, itemStyle: { color: '#67C23A' } },
                    { name: '中性', value: ${neutralCount}, itemStyle: { color: '#E6A23C' } },
                    { name: '负面', value: ${negativeCount}, itemStyle: { color: '#F56C6C' } }
                  ]
                }]
              };
              sentimentChart.setOption(sentimentOption);

              // 来源分布柱状图
              const sourceChart = echarts.init(document.getElementById('sourceChart'));
              const sourceOption = {
                tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
                grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                xAxis: { type: 'value', boundaryGap: [0, 0.01] },
                yAxis: { type: 'category', data: [${sourceData.map(item => `'${item.name}'`).join(',')}] },
                series: [{
                  name: '数量',
                  type: 'bar',
                  data: [${sourceData.map(item => item.value).join(',')}],
                  itemStyle: { color: '#409EFF' }
                }]
              };
              sourceChart.setOption(sourceOption);
            }
          }, 500);
        <\/script>
      `;
    },

    // 生成完整的HTML文档
    generateCompleteHtml(bodyContent) {
      return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息汇总分析报表</title>
    <style>
        body { margin: 0; padding: 20px; font-family: 'Microsoft YaHei', Arial, sans-serif; background: #f5f5f5; }
        .report-container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 12px rgba(0,0,0,0.1); }
        .report-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #409EFF; }
        .report-title h1 { margin: 0; color: #409EFF; font-size: 28px; font-weight: bold; }
        .report-meta { margin-top: 10px; font-size: 14px; color: #666; }
        .report-meta span { margin-right: 20px; }
        .section-title { font-size: 20px; color: #409EFF; margin-bottom: 20px; padding-left: 10px; border-left: 4px solid #409EFF; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #409EFF; }
        .stat-card.positive { border-left-color: #67C23A; background: #f0f9ff; }
        .stat-card.neutral { border-left-color: #E6A23C; background: #fefce8; }
        .stat-card.negative { border-left-color: #F56C6C; background: #fef2f2; }
        .stat-number { font-size: 32px; font-weight: bold; color: #409EFF; margin-bottom: 5px; }
        .stat-label { font-size: 14px; color: #666; }
        .charts-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; }
        .chart-container { background: #f8f9fa; padding: 20px; border-radius: 8px; }
        .chart-title { font-size: 16px; color: #333; margin-bottom: 15px; text-align: center; }
        .chart { height: 300px; width: 100%; }
        .report-table { width: 100%; border-collapse: collapse; font-size: 14px; }
        .report-table th, .report-table td { padding: 12px 8px; text-align: left; border-bottom: 1px solid #eee; }
        .report-table th { background: #f8f9fa; font-weight: bold; color: #333; border-bottom: 2px solid #409EFF; }
        .report-table tr:hover { background: #f5f5f5; }
        .title-cell { max-width: 300px; word-break: break-word; }
        .sentiment-tag { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .sentiment-tag.sentiment-positive { background: #f0f9ff; color: #67C23A; border: 1px solid #67C23A; }
        .sentiment-tag.sentiment-neutral { background: #fefce8; color: #E6A23C; border: 1px solid #E6A23C; }
        .sentiment-tag.sentiment-negative { background: #fef2f2; color: #F56C6C; border: 1px solid #F56C6C; }
        .report-footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666; font-size: 12px; }
        @media print { .report-container { padding: 0; box-shadow: none; } .charts-grid { grid-template-columns: 1fr; } }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.5.1/dist/echarts.min.js"><\/script>
</head>
<body>
    ${bodyContent}
    <script>
        // 重新初始化图表
        setTimeout(() => {
            const sentimentChart = document.querySelector('[data-chart="sentiment"]');
            const sourceChart = document.querySelector('[data-chart="source"]');

            if (sentimentChart && window.echarts) {
                // 这里需要根据实际数据重新渲染图表
                console.log('图表容器已准备就绪');
            }
        }, 100);
    <\/script>
</body>
</html>`;
    },

    // 下载HTML文件
    downloadHtmlFile(htmlContent, itemCount) {
      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
      const url = URL.createObjectURL(blob);

      const now = new Date();
      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
      const filename = `信息汇总报表_${dateStr}_${timeStr}.html`;

      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      this.$message.success(`成功导出 ${itemCount} 条数据的HTML报表`);

      // 同时在新窗口预览
      this.previewHtmlReport(htmlContent);
    },

    // 预览HTML报表
    previewHtmlReport(htmlContent) {
      const previewWindow = window.open('', '_blank');
      previewWindow.document.write(htmlContent);
      previewWindow.document.close();
    },

    // 获取日期范围文本
    getDateRangeText() {
      if (this.activeTab === 'custom' && this.customDateRange && this.customDateRange.length === 2) {
        const startDate = this.formatDate(this.customDateRange[0]);
        const endDate = this.formatDate(this.customDateRange[1]);
        return `${startDate} 至 ${endDate}`;
      }
      return '全部数据';
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background-color: #f0f2f5;
}

// 操作按钮区域样式
.action-buttons {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 10;
  display: flex;
  gap: 10px;
}

.info-summary-container {
  display: flex;
  height: 100%;
  padding-top: 60px; // 为新建方案按钮留出空间
}

// 左侧导航栏样式
.left-sidebar {
  width: 180px;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-header {
  padding: 15px;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-menu {
  flex: 1;
}

.el-menu-vertical {
  border-right: none;
}

// 右侧内容区域样式
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow: hidden;
  margin: 0 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.content-header {
  padding: 15px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.entity-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.entity-name {
  margin-right: 5px;
}

// 标签页样式
.tabs-container {
  padding: 10px 15px;
  border-bottom: 1px solid #e8e8e8;
}

.filter-tabs {
  display: flex;
  align-items: center;
}

.custom-date-container {
  margin-top: 10px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;

  .el-date-editor {
    width: 280px;
  }

  .quick-date-buttons {
    display: flex;
    gap: 5px;
    margin-left: 10px;

    .el-button--mini {
      padding: 4px 8px;
      font-size: 12px;
    }
  }
}

// 筛选区域样式
.filter-section {
  padding: 15px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #e8e8e8;
}

.filter-row {
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 80px;
  color: #606266;
  line-height: 28px;
}

.filter-actions {
  margin-top: 10px;
  padding-left: 80px;
}

// 操作栏样式
.action-bar {
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.left-actions, .right-actions {
  display: flex;
  align-items: center;
}

// 全选容器样式
.select-all-container {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

.select-all-checkbox {
  margin-right: 5px;
}

.selected-count {
  color: #909399;
  font-size: 12px;
  margin-left: 5px;
}

// 增强搜索容器样式
.enhanced-search-container {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.search-input {
  width: 250px;
  margin-right: 5px;
}

.search-options-dropdown {
  margin-left: 5px;
}

.search-options-btn {
  padding: 7px 8px;
  color: #909399;

  &:hover {
    color: #409eff;
  }
}

// 信息列表样式
.info-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.info-item {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: flex-start;
}

// 无数据状态样式
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 300px;
}

.empty-content {
  text-align: center;
  color: #909399;
}

.empty-icon {
  font-size: 64px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #606266;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.item-checkbox {
  margin-right: 10px;
  margin-top: 3px;
}

.info-content {
  flex: 1;
}

.info-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.info-summary {
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}

.info-footer {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 13px;
}

.info-source, .info-time, .info-platform, .info-sentiment, .info-views, .info-comments {
  margin-right: 15px;
}

.info-platform {
  display: flex;
  align-items: center;
}

.info-platform i {
  margin-right: 4px;
}

.info-index {
  margin-left: auto;
}

.sentiment-positive {
  color: #67c23a;
}

.sentiment-neutral {
  color: #909399;
}

.sentiment-negative {
  color: #f56c6c;
}

.info-images {
  display: flex;
  margin-top: 10px;
}

.info-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  margin-right: 10px;
  border-radius: 4px;
}

// 展开内容样式
.info-expanded-content {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    overflow: hidden;
  }
  to {
    opacity: 1;
    max-height: 500px;
    overflow: visible;
  }
}

.info-header {
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
    border-radius: 4px;
  }
}

// 高亮样式
:deep(.highlight) {
  color: #409eff;
  font-weight: bold;
}

// 新建方案对话框样式
.create-plan-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .el-tabs__header {
    margin-bottom: 20px;
  }

  .el-tabs__nav {
    width: 100%;
  }

  .el-tabs__item {
    flex: 1;
    text-align: center;
    height: 40px;
    line-height: 40px;
  }

  .el-form-item {
    margin-bottom: 15px;
  }
}

.monitor-object-select {
  display: flex;
  align-items: center;
  position: relative;

  .el-dropdown {
    position: absolute;
    right: 10px;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}

.location-select {
  display: flex;
  align-items: center;
}

.location-btn {
  margin-left: 10px;
}

.industry-select-btn {
  width: 100%;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 8px 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #c0c4cc;
  }
}

.theme-row, .industry-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.theme-input, .industry-input {
  width: 100px;
  margin-left: 10px;
  vertical-align: bottom;
}

.theme-button, .industry-button {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.el-tag {
  margin-right: 10px;
  margin-bottom: 5px;
}

.channels-row {
  display: flex;
  margin-bottom: 10px;

  .el-checkbox {
    margin-right: 20px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// 分页容器样式
.pagination-container {
  padding: 15px;
  text-align: right;
  background-color: #fff;
  border-top: 1px solid #e8e8e8;
}

// 行业分类弹窗样式
.industry-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .industry-dialog-content {
    display: flex;
    height: 400px;
  }

  .industry-tree-container {
    flex: 1;
    padding: 15px;
    border-right: 1px solid #e8e8e8;
    overflow-y: auto;
  }

  .industry-selected-container {
    width: 200px;
    padding: 15px;
    display: flex;
    flex-direction: column;
  }

  .industry-selected-title {
    font-weight: bold;
    margin-bottom: 10px;
  }
}

// 发送预警弹窗样式
.send-alert-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .send-alert-content {
    padding: 20px;
  }

  .receiver-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .receiver-item {
    display: flex;
    align-items: center;
  }

  .receiver-type {
    width: 80px;
    margin-right: 10px;
  }

  .receiver-select {
    width: 100%;
  }
}

// 正面情感弹窗样式
.positive-sentiment-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }
  .el-dialog__body {
    padding: 20px;
  }
}
</style>
.app-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background-color: #f0f2f5;
}

// 操作按钮区域样式
.action-buttons {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 10;
  display: flex;
  gap: 10px;
}

.info-summary-container {
  display: flex;
  height: 100%;
  padding-top: 60px; // 为新建方案按钮留出空间
}

// 左侧导航栏样式
.left-sidebar {
  width: 180px;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-header {
  padding: 15px;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-menu {
  flex: 1;
}

.el-menu-vertical {
  border-right: none;
}

// 右侧内容区域样式
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow: hidden;
  margin: 0 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.content-header {
  padding: 15px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.entity-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.entity-name {
  margin-right: 5px;
}

// 标签页样式
.tabs-container {
  padding: 10px 15px;
  border-bottom: 1px solid #e8e8e8;
}

.filter-tabs {
  display: flex;
  align-items: center;
}

// 筛选区域样式
.filter-section {
  padding: 15px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #e8e8e8;
}

.filter-row {
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 80px;
  color: #606266;
  line-height: 28px;
}

.filter-actions {
  margin-top: 10px;
  padding-left: 80px;
}

// 操作栏样式
.action-bar {
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.left-actions, .right-actions {
  display: flex;
  align-items: center;
}

.search-input {
  width: 200px;
  margin-right: 10px;
}

// 信息列表样式
.info-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.info-item {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: flex-start;
}

.item-checkbox {
  margin-right: 10px;
  margin-top: 3px;
}

.info-content {
  flex: 1;
}

.info-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.info-summary {
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}

.info-footer {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 13px;
}

.info-source, .info-time, .info-sentiment, .info-views, .info-comments {
  margin-right: 15px;
}

.info-index {
  margin-left: auto;
}

.sentiment-positive {
  color: #67c23a;
}

.sentiment-neutral {
  color: #909399;
}

.sentiment-negative {
  color: #f56c6c;
}

.info-images {
  display: flex;
  margin-top: 10px;
}

.info-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  margin-right: 10px;
  border-radius: 4px;
}

// 高亮样式
:deep(.highlight) {
  color: #409eff;
  font-weight: bold;
}

// 新建方案对话框样式
.create-plan-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .el-tabs__header {
    margin-bottom: 20px;
  }

  .el-tabs__nav {
    width: 100%;
  }

  .el-tabs__item {
    flex: 1;
    text-align: center;
    height: 40px;
    line-height: 40px;
  }

  .el-form-item {
    margin-bottom: 15px;
  }
}

.monitor-object-select {
  display: flex;
  align-items: center;
  position: relative;

  .el-dropdown {
    position: absolute;
    right: 10px;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}

.location-select {
  display: flex;
  align-items: center;
}

.location-btn {
  margin-left: 10px;
}

.industry-select-btn {
  width: 100%;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 8px 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #c0c4cc;
  }
}

.theme-row, .industry-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.theme-input, .industry-input {
  width: 100px;
  margin-left: 10px;
  vertical-align: bottom;
}

.theme-button, .industry-button {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.el-tag {
  margin-right: 10px;
  margin-bottom: 5px;
}

.channels-row {
  display: flex;
  margin-bottom: 10px;

  .el-checkbox {
    margin-right: 20px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// 分页容器样式
.pagination-container {
  padding: 15px;
  text-align: right;
  background-color: #fff;
  border-top: 1px solid #e8e8e8;
}

// 行业分类弹窗样式
.industry-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .industry-dialog-content {
    display: flex;
    height: 400px;
  }

  .industry-tree-container {
    flex: 1;
    padding: 15px;
    border-right: 1px solid #e8e8e8;
    overflow-y: auto;
  }

  .industry-selected-container {
    width: 200px;
    padding: 15px;
    display: flex;
    flex-direction: column;
  }

  .industry-selected-title {
    font-weight: bold;
    margin-bottom: 10px;
  }
}

// 发送预警弹窗样式
.send-alert-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .send-alert-content {
    padding: 20px;
  }

  .receiver-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .receiver-item {
    display: flex;
    align-items: center;
  }

  .receiver-type {
    width: 80px;
    margin-right: 10px;
  }

  .receiver-select {
    width: 100%;
  }
}

// 正面情感弹窗样式
.positive-sentiment-dialog {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e8e8e8;
  }
  .el-dialog__body {
    padding: 20px;
  }
}

// 搜索建议样式
:deep(.search-suggestions-popper) {
  .el-autocomplete-suggestion__list {
    max-height: 300px;
    overflow-y: auto;
  }

  .el-autocomplete-suggestion__wrap {
    max-height: none;
  }
}

.search-suggestion-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;

  .suggestion-icon {
    margin-right: 8px;
    color: #909399;
    font-size: 14px;
  }

  .suggestion-text {
    flex: 1;
    color: #303133;
    font-size: 14px;
  }

  .suggestion-type {
    color: #909399;
    font-size: 12px;
    background-color: #f5f7fa;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 8px;
  }

  &:hover {
    background-color: #f5f7fa;

    .suggestion-icon {
      color: #409eff;
    }
  }
}
</style>
