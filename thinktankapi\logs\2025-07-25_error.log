2025-07-25 13:17:32.792 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-25 13:17:32.792 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-25 13:17:34.446 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-25 13:17:34.446 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-25 13:17:34.460 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-25 13:17:35.109 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-25 13:17:35.781 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-25 13:17:35.782 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-25 13:17:57.103 | ef86060188314ca49b0d4e2913597e37 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为3192345f-1d45-41d8-a5a3-0fe1fabe69ab的会话获取图片验证码成功
2025-07-25 13:17:59.249 | 2eefa2d4d0614bfb9cb4a057268b8c51 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-25 13:17:59.495 | d5937b4565c445e8bf0b75b5ace5bd4d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-25 13:18:00.000 | 76a46bdbeb8646aaaa2cf0f0f5bceec9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-25 13:18:00.528 | adc16c69ffc84885b6d628063d95e984 | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-25 13:18:00.593 | adc16c69ffc84885b6d628063d95e984 | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-25 13:18:00.593 | adc16c69ffc84885b6d628063d95e984 | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-25 13:18:00.823 | 1a12129fb90b4411ba704f34cfef36bf | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-25 13:18:00.853 | 1a12129fb90b4411ba704f34cfef36bf | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 24
2025-07-25 13:18:00.854 | 1a12129fb90b4411ba704f34cfef36bf | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 24
2025-07-25 13:18:00.854 | 1a12129fb90b4411ba704f34cfef36bf | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-25 13:18:01.007 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-25 13:18:01.007 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-25 13:18:01.008 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-25 13:18:01.008 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-25 00:00:00 到 2025-07-25 23:59:59.999999 ===
2025-07-25 13:18:01.008 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-24 00:00:00 到 2025-07-24 23:59:59.999999 ===
2025-07-25 13:18:01.010 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-25 13:18:01.046 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-25 13:18:01.231 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-25 13:18:01.494 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-25 13:18:01.495 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 25, 13, 18, 1, 459283), 'package_name': '企业版', 'is_expired': True} ===
2025-07-25 13:18:01.530 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-25 13:18:01.531 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-25 13:18:01.565 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-25 13:18:01.566 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-25 13:18:01.566 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 13:18:01.566 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 13:18:01.567 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-25 13:18:01.567 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 13:18:01.568 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-25 13:18:01.568 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-25 13:18:01.568 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-25 13:18:01.569 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-25 13:18:01.569 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 13:18:01.570 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-25 13:18:01.570 | 8a82c99f2d6e43d6812c5259159b2e1d | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x000001B3085768B0> ===
2025-07-25 13:18:03.307 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 25, 13, 17, 59), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-25 13:18:03.308 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-25 13:18:03.308 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-25 13:18:03.308 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-25 13:18:03.309 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-25 13:18:03.309 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-25 13:18:03.310 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-25 13:18:03.310 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-25 13:18:03.311 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-25 13:18:03.311 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-25 13:18:03.311 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-25 13:18:03.312 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-25 13:18:03.312 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-25 13:18:03.315 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-25 13:18:03.316 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-25 13:18:03.373 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-25 13:18:03.373 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-25 13:18:03.374 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-25 13:18:03.374 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-25 13:18:03.374 | 91ca3a3a30a2482094f2a3070d4a2948 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-25 13:27:07.923 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 25, 13, 17, 59), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-25 13:27:07.924 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-25 13:27:07.924 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-25 13:27:07.924 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-25 13:27:07.924 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-25 13:27:07.925 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-25 13:27:07.925 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-25 13:27:07.925 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-25 13:27:07.925 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-25 13:27:07.926 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-25 13:27:07.926 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-25 13:27:07.926 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-25 13:27:07.927 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-25 13:27:07.928 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-25 13:27:07.929 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-25 13:27:08.002 | ce3ceaa1356247e0823407effa641ce1 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-25 13:27:08.003 | ce3ceaa1356247e0823407effa641ce1 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-25 13:27:08.003 | ce3ceaa1356247e0823407effa641ce1 | INFO     | utils.page_util:paginate:91 - 🔍 total: 34
2025-07-25 13:27:08.003 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 34, 当前页记录数: 10
2025-07-25 13:27:08.003 | ce3ceaa1356247e0823407effa641ce1 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共34条记录
2025-07-25 14:41:11.705 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-25 14:41:11.706 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-25 14:41:14.276 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-25 14:41:14.276 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-25 14:41:14.278 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-25 14:41:14.963 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-25 14:41:15.837 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-25 14:41:15.837 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-25 14:41:21.696 | d46ad287f2b0412192b0925364810405 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为3e8ab231-32ce-4e24-869d-f9d5f40bc721的会话获取图片验证码成功
2025-07-25 14:41:27.210 | 1de68893dc4e4419980294cd55c8d47e | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-25 14:41:27.560 | 1d8bc7275aae4bf2920333978ee94caf | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-25 14:41:28.110 | c0d0bf1975d94c078dbe74f348fdc2f5 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-25 14:41:28.728 | 138e0ff990b246a1a05402bc9da85b40 | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-25 14:41:28.765 | 138e0ff990b246a1a05402bc9da85b40 | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 24
2025-07-25 14:41:28.766 | 138e0ff990b246a1a05402bc9da85b40 | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 24
2025-07-25 14:41:28.766 | 138e0ff990b246a1a05402bc9da85b40 | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-25 14:41:29.149 | 49548f2d5e244f0e8833abadbb937772 | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-25 14:41:29.158 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-25 14:41:29.159 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-25 14:41:29.159 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-25 14:41:29.160 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-25 00:00:00 到 2025-07-25 23:59:59.999999 ===
2025-07-25 14:41:29.160 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-24 00:00:00 到 2025-07-24 23:59:59.999999 ===
2025-07-25 14:41:29.161 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-25 14:41:29.197 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-25 14:41:29.201 | 49548f2d5e244f0e8833abadbb937772 | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-25 14:41:29.202 | 49548f2d5e244f0e8833abadbb937772 | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-25 14:41:29.372 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-25 14:41:29.653 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-25 14:41:29.654 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 25, 14, 41, 29, 614719), 'package_name': '企业版', 'is_expired': True} ===
2025-07-25 14:41:29.685 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-25 14:41:29.686 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-25 14:41:29.716 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-25 14:41:29.717 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-25 14:41:29.717 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 14:41:29.718 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 14:41:29.718 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-25 14:41:29.719 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 14:41:29.719 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-25 14:41:29.719 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-25 14:41:29.720 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-25 14:41:29.720 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-25 14:41:29.720 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 14:41:29.721 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-25 14:41:29.721 | e9a19307ef4a4f2bae18831c147659b7 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x000001BBEB0ABCE0> ===
2025-07-25 14:41:31.190 | 1efe25f2449b442c861cb62f36ca0edb | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:123 - 获取关键词分类列表成功
2025-07-25 14:41:31.955 | 8e169566c7b3466081d4d183b17161b7 | INFO     | module_opinion.controller.opinion_template_controller:get_templates_for_selection:47 - 获取模板选择列表成功
2025-07-25 14:41:33.744 | df7460cac6b94d8aa81d89de0eb6882f | INFO     | module_opinion.controller.opinion_template_controller:update_template_usage:155 - 更新模板使用次数成功: 2
2025-07-25 14:41:35.413 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-25 14:41:35.413 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-25 14:41:35.414 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-25 14:41:35.414 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-25 00:00:00 到 2025-07-25 23:59:59.999999 ===
2025-07-25 14:41:35.414 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-24 00:00:00 到 2025-07-24 23:59:59.999999 ===
2025-07-25 14:41:35.415 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-25 14:41:35.450 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-25 14:41:35.628 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-25 14:41:35.811 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 65 ===
2025-07-25 14:41:35.811 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 25, 14, 41, 35, 758966), 'package_name': '企业版', 'is_expired': True} ===
2025-07-25 14:41:35.855 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-25 14:41:35.856 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-25 14:41:35.907 | 4357c54a430f41b292670c96d1898584 | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-25 14:41:35.907 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-25 14:41:35.908 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 14:41:35.908 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 14:41:35.909 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-25 14:41:35.909 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 14:41:35.909 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-25 14:41:35.910 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-25 14:41:35.910 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=65 today_analysis=0 remaining_count=35 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=65.0 ===
2025-07-25 14:41:35.910 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-25 14:41:35.911 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 65, 'today_analysis': 0, 'remaining_count': 35, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 65.0} ===
2025-07-25 14:41:35.911 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-25 14:41:35.912 | 4357c54a430f41b292670c96d1898584 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x000001BBEC42A6C0> ===
2025-07-25 14:41:36.633 | 3dc3817db6ac44209851de9c6d205c63 | INFO     | module_opinion.controller.opinion_analysis_controller:create_public_requirement:145 - 创建舆情需求成功
2025-07-25 14:41:36.976 | d1daa8f6fd66413fbab0d59bc2d14b41 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:55 - 用户 admin 请求生成关联词，需求内容长度: 44
2025-07-25 14:41:36.977 | d1daa8f6fd66413fbab0d59bc2d14b41 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:67 - 开始生成关联词，需求内容长度: 44, 最大数量: 20, 用户ID: 1
2025-07-25 14:41:57.879 | d1daa8f6fd66413fbab0d59bc2d14b41 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-25 14:41:57.882 | d1daa8f6fd66413fbab0d59bc2d14b41 | INFO     | module_opinion.service.related_keywords_service:_generate_keywords_by_ai:119 - AI返回的原始内容: ["用户评价","产品功能","用户满意度","产品性能","改进建议","产品设计","用户体验","用户反馈","评论分析","好评率","差评收集","功能缺陷","性能优化","设计缺陷","售后评价","质量投诉","使用体验","社交媒体评价","产品体验","建议收集"]...
2025-07-25 14:41:57.883 | d1daa8f6fd66413fbab0d59bc2d14b41 | INFO     | module_opinion.service.related_keywords_service:_parse_ai_keywords_response:161 - 成功解析AI生成的关联词: 20 个
2025-07-25 14:41:57.887 | d1daa8f6fd66413fbab0d59bc2d14b41 | INFO     | module_opinion.service.related_keywords_service:_deduct_package_usage_for_keywords:254 - 开始为用户1的关键词生成功能扣减套餐次数
2025-07-25 14:41:58.151 | d1daa8f6fd66413fbab0d59bc2d14b41 | INFO     | module_opinion.service.related_keywords_service:_deduct_package_usage_for_keywords:274 - 成功为用户1创建关键词生成记录，ID: 266
2025-07-25 14:41:58.151 | d1daa8f6fd66413fbab0d59bc2d14b41 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:89 - 关联词生成成功，共生成 20 个关联词
2025-07-25 14:41:58.151 | d1daa8f6fd66413fbab0d59bc2d14b41 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:75 - 关联词生成成功，共生成 20 个关联词
2025-07-25 14:42:00.224 | ef260005ce6a4c0ba13ab093739e2cc7 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-25 14:42:00.225 | ef260005ce6a4c0ba13ab093739e2cc7 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 2
2025-07-25 14:42:00.226 | ef260005ce6a4c0ba13ab093739e2cc7 | INFO     | utils.page_util:paginate:91 - 🔍 total: 2
2025-07-25 14:42:00.226 | ef260005ce6a4c0ba13ab093739e2cc7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_data_source_list:1119 - 获取需求数据源列表成功（公开接口）
2025-07-25 14:42:00.419 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-25 14:42:00.420 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-25 14:42:00.420 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-25 14:42:00.420 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-25 00:00:00 到 2025-07-25 23:59:59.999999 ===
2025-07-25 14:42:00.420 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-24 00:00:00 到 2025-07-24 23:59:59.999999 ===
2025-07-25 14:42:00.422 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-25 14:42:00.450 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-25 14:42:00.601 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-25 14:42:00.661 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 67 ===
2025-07-25 14:42:00.662 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 25, 14, 42, 0, 632997), 'package_name': '企业版', 'is_expired': True} ===
2025-07-25 14:42:00.690 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 2 ===
2025-07-25 14:42:00.690 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-25 14:42:00.722 | 727dc186a77f49768af7a264b4205c34 | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-25 14:42:00.723 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-25 14:42:00.723 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 67, 'today_analysis': 2, 'remaining_count': 33, 'today_remaining': 1, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 67.0} ===
2025-07-25 14:42:00.724 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 67, 'today_analysis': 2, 'remaining_count': 33, 'today_remaining': 1, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 67.0} ===
2025-07-25 14:42:00.724 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-25 14:42:00.725 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 67, 'today_analysis': 2, 'remaining_count': 33, 'today_remaining': 1, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 67.0} ===
2025-07-25 14:42:00.726 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=67 today_analysis=2 remaining_count=33 today_remaining=1 package_name='VIP1' package_limit=100 usage_percentage=67.0 ===
2025-07-25 14:42:00.726 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-25 14:42:00.727 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=11 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=24 running_count=0 failed_count=0 total_analysis=67 today_analysis=2 remaining_count=33 today_remaining=1 package_name='VIP1' package_limit=100 usage_percentage=67.0 ===
2025-07-25 14:42:00.728 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-25 14:42:00.728 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 11, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 24, 'running_count': 0, 'failed_count': 0, 'total_analysis': 67, 'today_analysis': 2, 'remaining_count': 33, 'today_remaining': 1, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 67.0} ===
2025-07-25 14:42:00.729 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-25 14:42:00.730 | 727dc186a77f49768af7a264b4205c34 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x000001BBEC4A32B0> ===
2025-07-25 14:42:01.098 | 56b333ccbdd64eb88f7d0852b7a0ca85 | INFO     | module_opinion.dao.analysis_progress_dao:create_analysis_task:52 - 创建分析任务成功，任务ID: analysis_7ed1aef6ef4644af
2025-07-25 14:42:01.203 | 56b333ccbdd64eb88f7d0852b7a0ca85 | DEBUG    | module_opinion.dao.analysis_progress_dao:add_progress_log:169 - 添加进度日志成功: analysis_7ed1aef6ef4644af - 分析任务已创建
2025-07-25 14:42:01.203 | 56b333ccbdd64eb88f7d0852b7a0ca85 | INFO     | module_opinion.service.analysis_progress_service:create_analysis_task:60 - 创建分析任务成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:01.204 | 56b333ccbdd64eb88f7d0852b7a0ca85 | INFO     | module_opinion.controller.opinion_analysis_controller:create_analysis_task:1355 - 创建分析任务成功，任务ID: analysis_7ed1aef6ef4644af
2025-07-25 14:42:01.800 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:998 - 开始分析，需求ID: 265
2025-07-25 14:42:01.801 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:1004 - 执行联网搜索...
2025-07-25 14:42:01.801 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 方太
2025-07-25 14:42:01.801 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:perform_online_search:40 - 第一阶段：开始模型能力验证
2025-07-25 14:42:01.801 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:88 - 开始验证AI模型的URL提供能力
2025-07-25 14:42:01.868 | 7c8f667d676d4ae1952186a584b9ff73 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:03.701 | bef51aa7e5b54229b1b1f85ba0527e00 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:05.829 | b4709c0a39d4465da86817cd9d228014 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:07.974 | e01f758c50a84ca1a069a6be9e68ca8b | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:10.086 | 39bf518782664f3abe5a4291eef7df32 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:12.236 | 043f65516a784324b45bf345c0d1336f | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:14.378 | 9e0f71811357413182f73538c0be1d2c | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:16.519 | 923c7d81081d44b5b29b8ad513251b41 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:17.472 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-25 14:42:17.473 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:135 - AI能力测试回复: 

https://www.chinanews.com.cn/gn/2024/09-29/10235598.shtml...
2025-07-25 14:42:17.475 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:146 - AI返回了URL: ['https://www.chinanews.com.cn/gn/2024/09-29/10235598.shtml']
2025-07-25 14:42:17.475 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:108 - 模型能力验证完成: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://www.chinanews.com.cn/gn/2024/09-29/10235598.shtml'], 'url_count': 1}
2025-07-25 14:42:17.476 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:perform_online_search:42 - 模型能力验证结果: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://www.chinanews.com.cn/gn/2024/09-29/10235598.shtml'], 'url_count': 1}
2025-07-25 14:42:17.476 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:perform_online_search:45 - 第二阶段：开始正式搜索
2025-07-25 14:42:17.476 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_build_adaptive_search_query:232 - 使用高能力搜索模式，强调URL真实性
2025-07-25 14:42:18.607 | 5afab3591c96436198b9827ebcd06a6c | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:20.733 | 7368d200490548ca995dddf15eff8345 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:22.814 | df01cb74e4944cc0aa5ea29caa234290 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:25.007 | b54d8f93571d4e7982725b29c40559a8 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:27.260 | 58d6008bd2574713be06b25e21d9b751 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:29.492 | 441d0168e9f94d8a97e2d603fbe92d3f | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:31.580 | 1dac724cc95e4249bfead34e08522978 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:33.729 | eff457e29a6747db8225b7da18be819f | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:35.846 | 7b8e613356bf4569a2fd535d902864ea | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:38.015 | 2a091a1641ba476d9dea27ed38df317f | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:40.123 | bf21799159ba4d379a5947e42aac6f52 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:42.276 | eb20b315c4b84fe9a596d5dafa9fefc3 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:44.350 | eeb03f71cfdf48b2a71bd6cf39c5cd6f | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:46.488 | f478c765faf9446ca0aa2fdd6def5bfb | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:48.578 | 696c7146545d4adfaf7910460a36859e | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:50.708 | 0cc954692b1c475da5a32fb9c72b06bc | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:52.820 | 6997aee6b6b34b3eb84adf5a10076e98 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:54.937 | e913dfe4408f47a0a5a755764e4843cb | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:57.019 | 7930dd163080448bb136c6a6da3434d6 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:42:59.192 | 4ca92b7903c2402f882d6a91c8971f7c | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:01.323 | e37b6af98cdc43cb93fc196dc3a3a011 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:03.552 | 0ea7fa5f9e3c4e42a000010ef7b460b4 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:05.678 | 5909623429ff461185dd17d24e12f3e7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:07.838 | 297918f13a414ba4bc7c5c3a73475930 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:08.641 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-25 14:43:09.926 | 32da2fba46794b8b84a47bf6a45e5109 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:12.129 | 6edbdfbd9487433985c4ca88f305aeb4 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:14.269 | 609b5df939f74767b49efc6997598682 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:16.410 | 93d28b7f50ba49368c700d50fcbbd69a | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:18.505 | 954d5948a00a4740a66ed59dadce9a30 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:20.655 | 02008647426948769b3a9c8b37072fd6 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:22.758 | 1d71b937857c43f09682b72dc0ca859d | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:24.895 | 0954ea9e6dae463393cda37608c848d4 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:27.095 | 9d90d4b9d5614868a83764c39a2ed6b0 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:29.243 | 710c66fc098743f19b9540c08918a1e3 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:31.365 | b5c8de0887e44558a43f53edb837f456 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:33.496 | 3ab425ebcb9345bfbf1fd70f537d1963 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:35.649 | bb7160ea84ae46a2a142855d0d9ca9fa | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:37.813 | d41e210bdfba4c6ebdbc679a81d52ade | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:39.892 | 9dd366290a294f1f8c0933ffa1408957 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:42.037 | e58a26403e6f4b6fadd347de4faf60ac | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:44.160 | 2474b1609af04787bd699500ffd41236 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:46.428 | 1741ccca9ac4491a966df049bf5f5cae | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:48.628 | 903c71e2d32549a6ab7da0ad34f17a68 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:50.844 | 2656c474d767495d87cd3c9118783167 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:52.999 | 2091ed9892164e72b03169b927ee5468 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:55.197 | 8fac9ab6400b41afadeebc12db027314 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:56.233 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-25 14:43:56.234 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:631 - AI返回的原始内容: [
  {
    "title": "方太研发时钟校准专利技术提升智能厨电系统精度",
    "content": "国家知识产权局最新公示方太研发的「时钟校准方法及装置」专利技术，该技术通过动态匹配网络时间与设备系统时间，实现了0.1秒级的时间校准精度。此创新技术计划应用于智能厨电系统，有效解决多设备协同时的时序误差问题。该专利获批表明方太在智能厨电时序控制领域取得重要突破，为企业后续智能厨...
2025-07-25 14:43:57.393 | cf2d7853fe6c4e55a7158980fc31c16b | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:43:59.590 | cfa2eb74ef2a4ad2bea1008ec195a936 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:01.170 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-25 14:44:01.172 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: neutral
2025-07-25 14:44:01.173 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: https://www.jrj.com.cn/article/20250721/CN120342532A.html
2025-07-25 14:44:01.706 | 1fc82f18ee3a45438f85ea37501da134 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:03.897 | cbf89d3688e14ab88f22339bcdc4497e | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:06.096 | fde4f7d4a6894876a67cde4a1be193bc | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:08.276 | 06fdd79093964c42973deb06719a1431 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:08.313 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-25 14:44:08.314 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: neutral
2025-07-25 14:44:08.315 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: https://hunan.rednet.cn/content/2025/07/14/14195632.html
2025-07-25 14:44:10.393 | fa18e30d621846a49350ab4efc2a8129 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:12.504 | 9a551295b59f4eeda3b5e5c5907dd533 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:14.595 | 1e1469e814c3438e88a5be8291ebe412 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:16.727 | 354bdb50582647d7ae224fc3473d2325 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:18.814 | dd106fecc6cc4fada942c23d9a4e3625 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:20.899 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-25 14:44:20.900 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: neutral
2025-07-25 14:44:20.901 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: https://www.cheaa.com/202411/437589.html
2025-07-25 14:44:20.982 | d9e3928e9cc141c1b07099d91a3b853f | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:23.067 | 79e306ca337a4ff2b566773f6ed5b70e | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:25.215 | dda5f3d91edb40cf83a78623f1af8155 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:27.302 | f99f64fe705848f8b82404db93147d2a | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:29.456 | 62e54c2aca3c4195a0f4b7432bfe614a | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:30.705 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-25 14:44:30.707 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1187 - 使用本地情感分析结果: neutral
2025-07-25 14:44:30.708 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:667 - 使用AI返回的原始URL: https://www.hhnews.net/article/20250724/437589.html
2025-07-25 14:44:30.708 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:680 - 成功提取 4 篇文章
2025-07-25 14:44:30.709 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:603 - AI成功提取到 4 篇文章
2025-07-25 14:44:30.709 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:1227 - 情感分析统计: {'positive': 0, 'neutral': 100, 'negative': 0} (总文章数: 4)
2025-07-25 14:44:30.710 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_evaluate_search_quality:402 - 搜索质量评估完成: 总分 64.92, 等级 good
2025-07-25 14:44:30.710 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:perform_online_search:58 - 搜索结果质量评估: {'overall_quality': 'good', 'overall_score': 64.92, 'url_coverage': {'score': 100.0, 'stats': {'total': 4, 'with_url': 4}}, 'content_quality': {'score': 64.92, 'stats': {'avg_title_length': 20.0, 'avg_content_length': 149.25, 'with_source': 4, 'with_time': 4}}, 'recommendations': ['搜索结果质量良好']}
2025-07-25 14:44:30.710 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1326 - 开始保存搜索结果到数据库
2025-07-25 14:44:30.710 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1327 - 实体关键词: 方太
2025-07-25 14:44:30.710 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1328 - 选中关键词: ['用户评价', '产品功能', '用户满意度', '产品性能', '改进建议']
2025-07-25 14:44:30.711 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1329 - 搜索结果文章数量: 4
2025-07-25 14:44:30.711 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1339 - 合并后的关键词字符串: 方太,用户评价,产品功能,用户满意度,产品性能,改进建议
2025-07-25 14:44:30.712 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 1 条文章: 方太研发时钟校准专利技术提升智能厨电系统精度
2025-07-25 14:44:30.712 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: https://www.jrj.com.cn/article/20250721/CN120342532A.html
2025-07-25 14:44:30.712 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=方太研发时钟校准专利技术提升智能厨电系统精度..., url=https://www.jrj.com.cn/article/20250721/CN120342532A.html, sentiment=neutral
2025-07-25 14:44:30.744 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 1 条文章，ID: 2225
2025-07-25 14:44:30.745 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 2 条文章: 湖南娄底代理商违规收取客户资金事件引发关注
2025-07-25 14:44:30.745 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: https://hunan.rednet.cn/content/2025/07/14/14195632.html
2025-07-25 14:44:30.745 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=湖南娄底代理商违规收取客户资金事件引发关注..., url=https://hunan.rednet.cn/content/2025/07/14/14195632.html, sentiment=neutral
2025-07-25 14:44:30.785 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 2 条文章，ID: 2226
2025-07-25 14:44:30.786 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 3 条文章: 方太AI厨房系统用户认可度调查数据发布
2025-07-25 14:44:30.788 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: https://www.cheaa.com/202411/437589.html
2025-07-25 14:44:30.788 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=方太AI厨房系统用户认可度调查数据发布..., url=https://www.cheaa.com/202411/437589.html, sentiment=neutral
2025-07-25 14:44:30.818 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 3 条文章，ID: 2227
2025-07-25 14:44:30.819 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1343 - 正在保存第 4 条文章: 市场监管部门联合打击厨电行业网络谣言
2025-07-25 14:44:30.819 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1351 - 保存AI返回的原始URL: https://www.hhnews.net/article/20250724/437589.html
2025-07-25 14:44:30.820 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1371 - 准备保存数据: title=市场监管部门联合打击厨电行业网络谣言..., url=https://www.hhnews.net/article/20250724/437589.html, sentiment=neutral
2025-07-25 14:44:30.849 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1377 - 成功保存第 4 条文章，ID: 2228
2025-07-25 14:44:30.914 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1388 - 成功保存 4 条搜索结果到数据库
2025-07-25 14:44:30.914 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.service.external_api_service:perform_online_search:65 - 联网搜索完成，获取到 4 条结果，已保存 4 条到数据库
2025-07-25 14:44:30.915 | c65d4770aa004664962f9d45bde6bb00 | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:1040 - 分析执行成功
2025-07-25 14:44:31.491 | 54a4b3d0b25d40bd95aa6a2bcdf2b073 | INFO     | module_opinion.dao.analysis_progress_dao:update_task_status:129 - 更新任务状态成功: analysis_7ed1aef6ef4644af -> completed
2025-07-25 14:44:31.593 | db08a42fa2fd4f1599c45c23f2d7c925 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:31.697 | 54a4b3d0b25d40bd95aa6a2bcdf2b073 | DEBUG    | module_opinion.dao.analysis_progress_dao:add_progress_log:169 - 添加进度日志成功: analysis_7ed1aef6ef4644af - 分析任务已完成
2025-07-25 14:44:31.698 | 54a4b3d0b25d40bd95aa6a2bcdf2b073 | DEBUG    | module_opinion.service.websocket_manager:broadcast_to_task:120 - 任务没有活跃连接: analysis_7ed1aef6ef4644af
2025-07-25 14:44:31.698 | 54a4b3d0b25d40bd95aa6a2bcdf2b073 | INFO     | module_opinion.service.analysis_progress_service:complete_analysis_task:232 - 完成分析任务成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:31.699 | 54a4b3d0b25d40bd95aa6a2bcdf2b073 | INFO     | module_opinion.controller.opinion_analysis_controller:complete_analysis_task:1417 - 完成分析任务成功: analysis_7ed1aef6ef4644af
2025-07-25 14:44:31.911 | 519c355fd8ad4eae84ba87d4bbc13497 | INFO     | module_opinion.controller.opinion_analysis_controller:generate_and_upload_report:1457 - 开始生成报告并上传到OSS，需求ID: 265
2025-07-25 14:44:31.912 | 519c355fd8ad4eae84ba87d4bbc13497 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:80 - 生成HTML - report_data keys: ['totalArticles', 'totalKeywords', 'dataSources', 'sentiment', 'onlineSearchCount', 'customSourceCounts']
2025-07-25 14:44:31.912 | 519c355fd8ad4eae84ba87d4bbc13497 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:81 - 生成HTML - analysis_results keys: ['online_search']
2025-07-25 14:44:31.912 | 519c355fd8ad4eae84ba87d4bbc13497 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:82 - 生成HTML - selectedKeywords: []
2025-07-25 14:44:31.912 | 519c355fd8ad4eae84ba87d4bbc13497 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:107 - 生成HTML - 提取到 0 篇文章
2025-07-25 14:44:31.913 | 519c355fd8ad4eae84ba87d4bbc13497 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:111 - 生成HTML - 提取到 0 个关键词: []
2025-07-25 14:44:32.223 | 519c355fd8ad4eae84ba87d4bbc13497 | INFO     | utils.oss_util:_get_client:47 - OSS客户端初始化成功
2025-07-25 14:44:32.422 | 519c355fd8ad4eae84ba87d4bbc13497 | INFO     | utils.oss_util:_generate_signed_domain_url:204 - 生成可直接查看的OSS URL: opinion-reports/report_b1a29742c665_1753425871.html
2025-07-25 14:44:32.423 | 519c355fd8ad4eae84ba87d4bbc13497 | INFO     | utils.oss_util:_upload_html_standard:110 - HTML文件上传OSS成功: opinion-reports/report_b1a29742c665_1753425871.html -> https://oss.jingangai.cn/opinion-reports/report_b1a29742c665_1753425871.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1756017872&Signature=kk2BWsdSbut67L9uGn/g3LwRIHc%3D
2025-07-25 14:44:32.424 | 519c355fd8ad4eae84ba87d4bbc13497 | INFO     | utils.oss_util:_upload_html_standard:111 - OSS上传结果 - 状态码: 200, 请求ID: 688327D0AF96A13434B77023
2025-07-25 14:44:32.425 | 519c355fd8ad4eae84ba87d4bbc13497 | INFO     | module_opinion.service.report_html_generator_service:generate_report_html:45 - 报告HTML页面生成并上传OSS成功: report_b1a29742c665_1753425871 -> https://oss.jingangai.cn/opinion-reports/report_b1a29742c665_1753425871.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1756017872&Signature=kk2BWsdSbut67L9uGn/g3LwRIHc%3D
2025-07-25 14:44:32.460 | 519c355fd8ad4eae84ba87d4bbc13497 | INFO     | module_opinion.controller.opinion_analysis_controller:generate_and_upload_report:1493 - 报告生成并上传OSS成功: report_b1a29742c665_1753425871 -> https://oss.jingangai.cn/opinion-reports/report_b1a29742c665_1753425871.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1756017872&Signature=kk2BWsdSbut67L9uGn/g3LwRIHc%3D
2025-07-25 14:44:33.180 | 3ffeb6e84fc846ef8ecb2a9b501e24fc | INFO     | module_opinion.controller.opinion_analysis_controller:create_task:582 - 创建舆情任务成功
2025-07-25 14:44:33.356 | 8def545de00b48caa5194cc431193f52 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_7ed1aef6ef4644af
2025-07-25 14:47:04.025 | fdae63d7daca45d2ad2cd07e14001000 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_task:585 - 创建舆情任务失败: 任务名称"产品口碑分析_20250725144135_sipo0u - 分析报告"在该需求下已存在
2025-07-25 14:47:39.264 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 25, 14, 41, 27), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-25 14:47:39.264 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-25 14:47:39.265 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-25 14:47:39.266 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-25 14:47:39.266 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-25 14:47:39.266 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-25 14:47:39.266 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-25 14:47:39.267 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-25 14:47:39.267 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-25 14:47:39.268 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-25 14:47:39.268 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-25 14:47:39.268 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-25 14:47:39.269 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-25 14:47:39.270 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-25 14:47:39.271 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-25 14:47:39.333 | a073174fd84645d1bc3dea984303bcb5 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-25 14:47:39.334 | a073174fd84645d1bc3dea984303bcb5 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-25 14:47:39.334 | a073174fd84645d1bc3dea984303bcb5 | INFO     | utils.page_util:paginate:91 - 🔍 total: 35
2025-07-25 14:47:39.335 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 35, 当前页记录数: 10
2025-07-25 14:47:39.335 | a073174fd84645d1bc3dea984303bcb5 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共35条记录
2025-07-25 14:47:47.330 | 6995841ece8844f5bd797a731b0d26ba | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:123 - 获取关键词分类列表成功
2025-07-25 16:18:59.040 | 7e6f8c05657e4cf1ba584ab869e07be9 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-07-25 16:19:00.803 | 3195b9e5bf284e049ff7c9ab3391a87f | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-07-25 16:19:01.767 | 8b206580a12c4236b0b39bea883b6223 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为d6d3abd2-a233-4133-bbbe-f7df49e688e7的会话获取图片验证码成功
