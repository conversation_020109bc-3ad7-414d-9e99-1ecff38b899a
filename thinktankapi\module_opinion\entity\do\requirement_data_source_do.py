from datetime import datetime
from sqlalchemy import BigInteger, Column, DateTime, Integer, String, Text
from config.database import Base


class RequirementDataSource(Base):
    """
    需求数据来源表
    """

    __tablename__ = 'requirement_data_source'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    requirement_id = Column(BigInteger, nullable=False, comment='需求ID，关联opinion_requirement表')
    source_type = Column(String(50), nullable=False, comment='来源类型')
    source_name = Column(String(200), nullable=True, comment='来源名称')
    source_url = Column(String(1000), nullable=True, comment='来源URL')
    source_config = Column(Text, nullable=True, comment='来源配置信息（JSON格式）')
    is_selected = Column(Integer, default=1, comment='是否选中：0-否，1-是')
    is_active = Column(Integer, default=1, comment='是否激活：0-否，1-是')
    crawl_frequency = Column(String(50), default='daily', comment='抓取频率')
    last_crawl_time = Column(DateTime, nullable=True, comment='最后抓取时间')
    crawl_status = Column(String(20), nullable=True, comment='抓取状态')
    error_message = Column(Text, nullable=True, comment='错误信息')
    sort_order = Column(Integer, default=0, comment='排序顺序')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
