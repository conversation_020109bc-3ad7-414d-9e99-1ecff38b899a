import request from '@/utils/request'

// ==================== 预警方案管理 ====================

// 获取预警方案列表
export function listWarningScheme(query) {
  return request({
    url: '/warning/scheme/list',
    method: 'get',
    params: query
  })
}

// 获取所有启用的预警方案
export function getActiveWarningSchemes() {
  return request({
    url: '/warning/scheme/active',
    method: 'get'
  })
}

// 获取预警方案详情
export function getWarningScheme(schemeId) {
  return request({
    url: '/warning/scheme/' + schemeId,
    method: 'get'
  })
}

// 新增预警方案
export function addWarningScheme(data) {
  return request({
    url: '/warning/scheme',
    method: 'post',
    data: data
  })
}

// 修改预警方案
export function updateWarningScheme(data) {
  return request({
    url: '/warning/scheme',
    method: 'put',
    data: data
  })
}

// 删除预警方案
export function delWarningScheme(schemeIds) {
  return request({
    url: '/warning/scheme',
    method: 'delete',
    data: { scheme_ids: schemeIds }  // 使用下划线命名与后端保持一致
  })
}

// 切换预警方案状态
export function toggleWarningSchemeStatus(schemeId, isActive) {
  return request({
    url: `/warning/scheme/${schemeId}/status`,
    method: 'put',
    params: { isActive: isActive }
  })
}

// 检查方案名称唯一性
export function checkSchemeName(schemeName, schemeId) {
  return request({
    url: '/warning/scheme/check/name',
    method: 'get',
    params: { schemeName: schemeName, schemeId: schemeId }
  })
}

// ==================== 预警记录管理 ====================

// 获取预警记录列表
export function listWarningRecord(query) {
  return request({
    url: '/warning/record/list',
    method: 'get',
    params: query
  })
}

// 获取预警记录详情
export function getWarningRecord(recordId) {
  return request({
    url: '/warning/record/' + recordId,
    method: 'get'
  })
}

// 新增预警记录
export function addWarningRecord(data) {
  return request({
    url: '/warning/record',
    method: 'post',
    data: data
  })
}

// 修改预警记录
export function updateWarningRecord(data) {
  return request({
    url: '/warning/record',
    method: 'put',
    data: data
  })
}

// 删除预警记录
export function delWarningRecord(recordIds) {
  return request({
    url: '/warning/record',
    method: 'delete',
    data: { record_ids: recordIds }  // 使用下划线命名与后端保持一致
  })
}

// 更新预警记录状态
export function updateWarningRecordStatus(recordId, status) {
  return request({
    url: `/warning/record/${recordId}/status`,
    method: 'put',
    params: { status: status }
  })
}

// 获取预警统计数据
export function getWarningStatistics(schemeId) {
  return request({
    url: '/warning/record/statistics',
    method: 'get',
    params: { schemeId: schemeId }
  })
}

// 导出预警记录
export function exportWarningRecord(data) {
  return request({
    url: '/warning/record/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

// ==================== 预警设置管理 ====================

// 获取预警设置
export function getWarningSettings(schemeId) {
  return request({
    url: '/warning/settings/' + schemeId,
    method: 'get'
  })
}

// 保存预警设置
export function saveWarningSettings(data) {
  return request({
    url: '/warning/settings',
    method: 'post',
    data: data
  })
}

// 更新预警设置
export function updateWarningSettings(data) {
  return request({
    url: '/warning/settings',
    method: 'put',
    data: data
  })
}

// 获取默认预警设置
export function getDefaultWarningSettings() {
  return request({
    url: '/warning/settings/default',
    method: 'get'
  })
}

// 验证预警设置
export function validateWarningSettings(data) {
  return request({
    url: '/warning/settings/validate',
    method: 'post',
    data: data
  })
}

// 复制预警设置
export function copyWarningSettings(sourceSchemeId, targetSchemeId) {
  return request({
    url: '/warning/settings/copy',
    method: 'post',
    params: { sourceSchemeId: sourceSchemeId, targetSchemeId: targetSchemeId }
  })
}

// 重置预警设置
export function resetWarningSettings(schemeId) {
  return request({
    url: `/warning/settings/${schemeId}/reset`,
    method: 'post'
  })
}

// 获取预警设置配置选项
export function getWarningSettingsOptions() {
  return request({
    url: '/warning/settings/config/options',
    method: 'get'
  })
}

// ==================== 前端专用接口 ====================

// 获取前端页面完整数据
export function getWarningFrontendData(query) {
  return request({
    url: '/warning/frontend/data',
    method: 'get',
    params: query
  })
}

// 获取仪表板数据
export function getWarningDashboardData() {
  return request({
    url: '/warning/frontend/dashboard',
    method: 'get'
  })
}

// 初始化方案数据
export function initWarningSchemeData(schemeId) {
  return request({
    url: '/warning/frontend/init/' + schemeId,
    method: 'get'
  })
}

// 获取快速统计信息
export function getQuickStatistics(schemeId) {
  return request({
    url: '/warning/frontend/quick-stats',
    method: 'get',
    params: { schemeId: schemeId }
  })
}

// 批量处理记录
export function batchProcessRecords(recordIds, action) {
  return request({
    url: '/warning/frontend/batch-process',
    method: 'post',
    data: { record_ids: recordIds, action: action }  // 使用下划线命名与后端保持一致
  })
}
