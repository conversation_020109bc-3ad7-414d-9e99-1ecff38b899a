from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String
from sqlalchemy.orm import relationship
from config.database import Base


class EmotionDefinition(Base):
    """
    情感定义表
    """

    __tablename__ = 'emotion_definition'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='情感类型ID')
    emotion_name = Column(String(50), nullable=False, comment='情感类型名称')
    emotion_code = Column(String(20), nullable=False, comment='情感类型编码')
    emotion_color = Column(String(20), nullable=True, comment='情感类型颜色')
    description = Column(String(200), nullable=True, comment='情感类型描述')
    sort_order = Column(Integer, default=0, comment='排序顺序')
    status = Column(String(1), default='0', comment='状态（0正常 1停用）')
    create_time = Column(DateTime, nullable=True, default=datetime.now(), comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now(), comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')

    # 关联情感分析汇总表 - 暂时注释掉避免循环依赖
    # analysis_summaries = relationship('EmotionAnalysisSummary', back_populates='emotion_definition')
