<template>
  <div class="app-container">
    <div class="page-container">
      <!-- 左侧导航栏 -->
      <div class="left-sidebar" :class="{ 'collapsed': sidebarCollapsed }">
        <div class="sidebar-header">
          <el-button type="warning" class="new-scheme-btn" @click="createNewScheme">
            <i class="el-icon-plus"></i> 新建方案
          </el-button>
          <div class="sidebar-btn" @click="toggleSidebar">
            <i class="el-icon-s-fold"></i>
          </div>
        </div>

        <div class="sidebar-search">
          <el-input
            v-model="sidebarSearchText"
            placeholder="搜索"
            prefix-icon="el-icon-search"
            size="small"
            @input="searchSidebar"
          ></el-input>
        </div>

        <div class="sidebar-menu">
          <el-menu
            :default-active="activeMenuItem"
            class="sidebar-menu-list"
            @select="handleMenuSelect"
          >
            <!-- 添加"全部"选项 -->
            <el-menu-item
              key="all"
              index="全部"
              :class="{ 'active-menu-item': activeMenuItem === '全部' }"
            >
              <i class="el-icon-s-grid"></i>
              <span>全部</span>
            </el-menu-item>

            <template v-for="(item, index) in filteredMenuCategories">
              <!-- 使用唯一的key -->
              <el-menu-item
                v-if="item.isItem"
                :key="item.id ? `item-${item.id}` : `item-name-${item.name}-${Math.random()}`"
                :index="item.name"
                :class="{ 'active-menu-item': activeMenuItem === item.name }"
              >
                <span>{{ item.name }}</span>
              </el-menu-item>

              <!-- 如果是子菜单 -->
              <el-submenu
                v-else
                :key="item.id ? `submenu-${item.id}` : `submenu-name-${item.name}-${Math.random()}`"
                :index="item.name"
              >
                <template slot="title">
                  <span>{{ item.name }}({{ item.count }})</span>
                </template>
                <!-- 子菜单项 -->
                <el-menu-item
                  v-for="child in item.children"
                  :key="child.id ? `child-${child.id}` : `child-name-${child.name}-${Math.random()}`"
                  :index="child.name"
                >
                  {{ child.name }}
                </el-menu-item>
              </el-submenu>
            </template>
          </el-menu>
        </div>
      </div>

      <!-- 右侧内容区 -->
      <div class="right-content">
        <div class="report-container">
          <div class="report-header">
            <div class="title">
              方案报告
              <span v-if="activeMenuItem && activeMenuItem !== '全部'" class="category-tag">
                - {{ activeMenuItem }}
              </span>
            </div>
            <div class="actions">
              <el-button type="primary" size="small" icon="el-icon-edit" @click="editReport">模板管理</el-button>
              <el-button type="primary" size="small" icon="el-icon-document" @click="exportReport">定时任务管理</el-button>
              <el-button type="primary" size="small" icon="el-icon-plus" @click="createReport">新建报告</el-button>
            </div>
          </div>

          <!-- 报告列表视图 -->
          <div v-if="currentView === 'report'">
            <div class="filter-options">
              <div class="filter-row">
                <div class="filter-item">
                  <span>时间范围：</span>
                  <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="small"
                    style="width: 240px;"
                  ></el-date-picker>
                </div>
                <div class="filter-item">
                  <el-select v-model="filterType" placeholder="全部" size="small" style="width: 100px;">
                    <el-option label="全部" value="all"></el-option>
                    <el-option label="名称" value="name"></el-option>
                    <el-option label="类型" value="type"></el-option>
                  </el-select>
                  <el-input
                    v-model="searchKeyword"
                    placeholder="请输入内容"
                    size="small"
                    style="width: 200px; margin-left: 5px;"
                    @keyup.enter="handleQuery"
                  ></el-input>
                  <el-button
                    type="primary"
                    size="small"
                    icon="el-icon-search"
                    style="margin-left: 5px;"
                    @click="handleQuery"
                  >查询</el-button>
                </div>
                <div class="tab-container">
                  <div
                    class="tab-item"
                    :class="{ 'active': activeTab === 'normal' }"
                    @click="switchTab('normal')"
                  >
                    普通报告
                  </div>
                  <div
                    class="tab-item"
                    :class="{ 'active': activeTab === 'competitor' }"
                    @click="switchTab('competitor')"
                  >
                    竞对报告
                  </div>
                </div>
              </div>
            </div>

            <div class="report-table">
              <el-table
                :data="reportList"
                style="width: 100%"
                border
                stripe
                :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
                row-key="id"
              >
                <el-table-column
                  prop="name"
                  label="配置名称"
                  width="180"
                ></el-table-column>
                <el-table-column
                  prop="create_time"
                  label="创建时间"
                  width="180"
                >
                  <template slot-scope="scope">
                    <span>
                      {{ formatDateTime(null, null, scope.row.create_time) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="配置类型"
                  width="120"
                >
                  <template slot-scope="scope">
                    {{ scope.row.schemeType ? scope.row.schemeType.name : '未分类' }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="status"
                  label="状态"
                  width="100"
                >
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                      {{ scope.row.status === 1 ? '启用' : '禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="refreshStatus"
                  label="刷新状态"
                  width="120"
                >
                  <template slot-scope="scope">
                    <el-tag :type="getRefreshStatusType(scope.row.refreshStatus)">
                      {{ getRefreshStatusText(scope.row.refreshStatus) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  label="操作"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-view"
                      @click="viewReport(scope.row)"
                    >查看</el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      @click="editReportItem(scope.row)"
                    >编辑</el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      class="delete-btn"
                      @click="deleteReport(scope.row)"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 空数据显示 -->
              <div v-if="reportList.length === 0" class="empty-data">
                <i class="el-icon-data-analysis empty-icon"></i>
                <p>暂无数据</p>
              </div>
            </div>
          </div>

          <!-- 模板管理视图 -->
          <div v-if="currentView === 'template'">
            <div class="back-button">
              <el-button type="text" icon="el-icon-arrow-left" @click="goBack">返回</el-button>
            </div>
            <div class="template-header">
              <div class="template-title">
                <i class="el-icon-document"></i> 报告模板
              </div>
              <div class="template-actions">
                <div class="template-tab-container">
                  <div
                    class="template-tab"
                    :class="{ 'active': templateType === 'normal' }"
                    @click="switchTemplateType('normal')"
                  >
                    普通模板
                  </div>
                  <div
                    class="template-tab"
                    :class="{ 'active': templateType === 'competitor' }"
                    @click="switchTemplateType('competitor')"
                  >
                    竞对模板
                  </div>
                </div>
              </div>
            </div>

            <div class="create-template-btn">
              <el-button type="primary" plain @click="createProductTemplate">
                <i class="el-icon-plus"></i> 创建产品模板
              </el-button>
            </div>

            <div class="template-table">
              <el-table
                :data="templateList"
                style="width: 100%"
                border
                stripe
                :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
                row-key="id"
              >
                <el-table-column
                  prop="name"
                  label="模板名称"
                  width="180"
                ></el-table-column>
                <el-table-column
                  prop="create_time"
                  label="创建时间"
                  width="180"
                ></el-table-column>
                <el-table-column
                  label="操作"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-view"
                      @click="viewTemplate(scope.row)"
                    >查看</el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      @click="editTemplate(scope.row)"
                    >编辑</el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      class="delete-btn"
                      @click="deleteTemplate(scope.row)"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <div class="pagination-container">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :current-page.sync="currentPage"
              :page-size.sync="pageSize"
              :page-sizes="pageSizes"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- 查看详情弹窗 -->
    <el-dialog
      title="方案详情"
      :visible.sync="viewDialogVisible"
      width="60%"
      :close-on-click-modal="false"
    >
      <div class="view-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="方案名称">
            {{ viewData.name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="方案类型">
            {{ (viewData.schemeType && viewData.schemeType.name) || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(null, null, viewData.create_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(null, null, viewData.update_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="viewData.status === 1 ? 'success' : 'danger'">
              {{ viewData.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="刷新状态">
            <el-tag :type="getRefreshStatusType(viewData.refreshStatus)">
              {{ getRefreshStatusText(viewData.refreshStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="方案描述" :span="2">
            {{ viewData.description || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="监控关键词" :span="2">
            {{ viewData.monitoringKeywords || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="排除关键词" :span="2">
            {{ viewData.excludedKeywords || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="素材限制数量">
            {{ viewData.materialLimit || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="创建者">
            {{ viewData.createBy || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ viewData.remark || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 编辑弹窗 -->
    <el-dialog
      title="编辑方案"
      :visible.sync="editDialogVisible"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editForm"
        :model="editForm"
        :rules="editFormRules"
        label-width="120px"
      >
        <el-form-item label="方案名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入方案名称"></el-input>
        </el-form-item>
        <el-form-item label="方案类型" prop="typeId">
          <el-select v-model="editForm.typeId" placeholder="请选择方案类型" style="width: 100%">
            <el-option
              v-for="type in schemeTypes"
              :key="type.id"
              :label="type.name"
              :value="type.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="方案描述">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入方案描述"
          ></el-input>
        </el-form-item>
        <el-form-item label="监控关键词">
          <el-input
            v-model="editForm.monitoringKeywords"
            type="textarea"
            :rows="2"
            placeholder="请输入监控关键词，多个关键词用逗号分隔"
          ></el-input>
        </el-form-item>
        <el-form-item label="排除关键词">
          <el-input
            v-model="editForm.excludedKeywords"
            type="textarea"
            :rows="2"
            placeholder="请输入排除关键词，多个关键词用逗号分隔"
          ></el-input>
        </el-form-item>
        <el-form-item label="素材限制数量">
          <el-input-number
            v-model="editForm.materialLimit"
            :min="0"
            :max="10000"
            placeholder="请输入素材限制数量"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="editForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="editForm.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleEditSubmit">确定</el-button>
      </span>
    </el-dialog>

    <!-- 模板查看弹窗 -->
    <el-dialog
      title="模板详情"
      :visible.sync="templateViewDialogVisible"
      width="60%"
      :close-on-click-modal="false"
    >
      <div class="template-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模板名称">
            {{ templateViewData.name }}
          </el-descriptions-item>
          <el-descriptions-item label="模板类型">
            {{ templateViewData.templateType === 'normal' ? '普通模板' : '竞对模板' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ templateViewData.create_time }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ templateViewData.update_time }}
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ templateViewData.createBy }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="templateViewData.isActive ? 'success' : 'danger'">
              {{ templateViewData.isActive ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="模板描述" :span="2">
            {{ templateViewData.description || '暂无描述' }}
          </el-descriptions-item>
          <el-descriptions-item label="模板内容" :span="2">
            <div class="template-content">
              {{ templateViewData.templateContent || '暂无内容' }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="templateViewDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 模板编辑弹窗 -->
    <el-dialog
      title="编辑模板"
      :visible.sync="templateEditDialogVisible"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="templateEditForm"
        :model="templateEditForm"
        :rules="templateEditFormRules"
        label-width="100px"
      >
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="templateEditForm.name" placeholder="请输入模板名称"></el-input>
        </el-form-item>
        <el-form-item label="模板类型" prop="templateType">
          <el-select v-model="templateEditForm.templateType" placeholder="请选择模板类型" style="width: 100%">
            <el-option label="普通模板" value="normal"></el-option>
            <el-option label="竞对模板" value="competitor"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板描述" prop="description">
          <el-input
            v-model="templateEditForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模板描述"
          ></el-input>
        </el-form-item>
        <el-form-item label="模板内容" prop="templateContent">
          <el-input
            v-model="templateEditForm.templateContent"
            type="textarea"
            :rows="6"
            placeholder="请输入模板内容"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="isActive">
          <el-switch
            v-model="templateEditForm.isActive"
            active-text="启用"
            inactive-text="禁用"
          ></el-switch>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="templateEditForm.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="templateEditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleTemplateEditSubmit">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSchemeList,
  getSchemeDetail,
  getSchemeTypeList,
  getMenuCategories,
  getTemplateList,
  getTemplateDetail,
  getTemplateByType,
  editScheme,
  deleteScheme,
  editTemplate,
  deleteTemplate
} from '@/api/report'

export default {
  name: "ReportList",
  data() {
    return {
      dateRange: [],
      filterType: 'all',
      searchKeyword: '',
      searchTimer: null, // 搜索防抖定时器
      reportList: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizes: [10, 20, 30, 50], // 可选的页面大小
      activeTab: 'normal', // 当前激活的选项卡：normal-普通报告，competitor-竞对报告
      currentView: 'report', // 当前视图：report-报告列表，template-模板管理
      templateType: 'normal', // 当前模板类型：normal-普通模板，competitor-竞对模板
      // 模板列表数据
      templateList: [
        { name: '品牌-热议话题', createTime: '2019-11-18 14:02:08', operation: '' },
        { name: '品牌-舆论', createTime: '2019-11-18 14:06:52', operation: '' },
        { name: '品牌-竞品', createTime: '2021-04-07 15:15:00', operation: '' }
      ],
      // 侧边栏数据
      sidebarCollapsed: false,
      sidebarSearchText: '',
      activeMenuItem: '全部',
      menuCategories: [
        { name: '总监', count: 1, children: [] },
        { name: '品牌', count: 1, children: [] },
        { name: '方太', count: 0, isItem: true },
        { name: '人物', count: 0, children: [] },
        { name: '机构', count: 0, children: [] },
        { name: '产品', count: 0, children: [] },
        { name: '事件', count: 0, children: [] },
        { name: '话题', count: 0, children: [] }
      ],

      // 查看详情弹窗
      viewDialogVisible: false,
      viewData: {},

      // 编辑弹窗
      editDialogVisible: false,
      editForm: {},
      editFormRules: {
        name: [
          { required: true, message: '请输入方案名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        typeId: [
          { required: true, message: '请选择方案类型', trigger: 'change' }
        ]
      },

      // 模板查看弹窗
      templateViewDialogVisible: false,
      templateViewData: {},

      // 模板编辑弹窗
      templateEditDialogVisible: false,
      templateEditForm: {},
      templateEditFormRules: {
        name: [
          { required: true, message: '请输入模板名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        templateType: [
          { required: true, message: '请选择模板类型', trigger: 'change' }
        ],
        description: [
          { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
        ]
      },

      // 方案类型列表
      schemeTypes: []
    };
  },
  computed: {
    // 过滤后的菜单分类
    filteredMenuCategories() {
      if (!this.sidebarSearchText) {
        return this.menuCategories;
      }
      
      const searchText = this.sidebarSearchText.toLowerCase();
      return this.menuCategories.filter(item => {
        // 检查主菜单项名称
        if (item.name.toLowerCase().includes(searchText)) {
          return true;
        }
        
        // 如果有子菜单，检查子菜单项
        if (item.children && item.children.length > 0) {
          const hasMatchingChild = item.children.some(child => 
            child.name.toLowerCase().includes(searchText)
          );
          if (hasMatchingChild) {
            // 返回包含匹配子项的菜单项，并过滤子项
            return {
              ...item,
              children: item.children.filter(child => 
                child.name.toLowerCase().includes(searchText)
              )
            };
          }
        }
        
        return false;
      });
    }
  },
  methods: {
    // 侧边栏相关方法
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
    },
    handleMenuSelect(index) {
      this.activeMenuItem = index;
      // 重置页码到第一页
      this.currentPage = 1;
      // 根据选中的菜单项筛选数据
      this.fetchReportListByCategory(index);
    },
    createNewScheme() {
      // 新建方案的逻辑
      this.$message({
        message: '新建方案功能待实现',
        type: 'info'
      });
    },
    searchSidebar() {
      // 侧边栏搜索逻辑
      console.log('搜索关键词：', this.sidebarSearchText);
      // 搜索逻辑已通过computed属性filteredMenuCategories实现
    },
    // 模板管理
    editReport() {
      this.currentView = 'template';
      this.fetchTemplateList(); // 切换到模板管理时获取模板列表
      this.$message.success("切换到模板管理");
    },
    // 导出报告
    exportReport() {
      this.$message.success("定时任务管理");
    },
    // 创建新报告
    createReport() {
      this.$message.success("新建报告");
    },
    // 查看报告
    async viewReport(row) {
      try {
        const response = await getSchemeDetail(row.id);
        if (response && response.code === 200) {
          this.viewDialogVisible = true;
          this.viewData = response.data;
        } else {
          this.$message.error((response && response.msg) || '获取方案详情失败');
        }
      } catch (error) {
        console.error('获取方案详情失败:', error);
        this.$message.error('获取方案详情失败');
      }
    },
    // 编辑报告项
    async editReportItem(row) {
      try {
        const response = await getSchemeDetail(row.id);
        if (response && response.code === 200) {
          this.editDialogVisible = true;
          this.editForm = { ...response.data };
        } else {
          this.$message.error((response && response.msg) || '获取方案详情失败');
        }
      } catch (error) {
        console.error('获取方案详情失败:', error);
        this.$message.error('获取方案详情失败');
      }
    },
    // 删除报告
    async deleteReport(row) {
      try {
        await this.$confirm(`确认删除方案 "${row.name}"?`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        });

        const response = await deleteScheme({
          schemeIds: row.id.toString()
        });

        if (response && response.code === 200) {
          this.$message.success(response.msg || '删除成功');
          // 刷新列表
          this.fetchReportList();
        } else {
          this.$message.error((response && response.msg) || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除方案失败:', error);
          this.$message.error('删除失败');
        }
      }
    },
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val;
      // 优先根据当前激活的tab类型筛选数据
      if (this.activeTab && (this.activeTab === 'normal' || this.activeTab === 'competitor')) {
        this.fetchReportListByTemplateType(this.activeTab);
      } else if (this.activeMenuItem) {
        // 如果有选中的菜单项，按分类筛选
        this.fetchReportListByCategory(this.activeMenuItem);
      } else {
        // 否则获取全部数据
        this.fetchReportList();
      }
    },
    // 处理页面大小变化
    handleSizeChange(val) {
      console.log('🔧 handleSizeChange 被调用，新的页面大小:', val);
      console.log('🔧 当前pageSize:', this.pageSize);

      this.pageSize = val;
      this.currentPage = 1; // 重置到第一页

      console.log('🔧 更新后的pageSize:', this.pageSize);
      console.log('🔧 当前页码重置为:', this.currentPage);
      console.log('🔧 当前activeTab:', this.activeTab);
      console.log('🔧 当前activeMenuItem:', this.activeMenuItem);

      // 使用nextTick确保数据更新后再调用API
      this.$nextTick(() => {
        console.log('🔧 nextTick中的pageSize:', this.pageSize);
        // 优先根据当前激活的tab类型筛选数据
        if (this.activeTab && (this.activeTab === 'normal' || this.activeTab === 'competitor')) {
          console.log('🔧 调用fetchReportListByTemplateType:', this.activeTab);
          this.fetchReportListByTemplateType(this.activeTab);
        } else if (this.activeMenuItem) {
          // 如果有选中的菜单项，按分类筛选
          console.log('🔧 调用fetchReportListByCategory:', this.activeMenuItem);
          this.fetchReportListByCategory(this.activeMenuItem);
        } else {
          // 否则获取全部数据
          console.log('🔧 调用fetchReportList');
          this.fetchReportList();
        }
      });

      this.$message.success(`已切换到每页显示 ${val} 条记录`);
    },
    // 获取报告列表
    async fetchReportList() {
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize
        };

        // 根据筛选类型设置搜索参数
        if (this.searchKeyword && this.searchKeyword.trim()) {
          if (this.filterType === 'name') {
            params.name = this.searchKeyword.trim();
          } else if (this.filterType === 'type') {
            // 根据类型名称查找对应的typeId
            const schemeType = this.schemeTypes.find(type => 
              type.name.toLowerCase().includes(this.searchKeyword.trim().toLowerCase())
            );
            if (schemeType) {
              params.typeId = schemeType.id;
            } else {
              // 如果没找到匹配的类型，使用searchKeyword进行模糊搜索
              params.searchKeyword = this.searchKeyword.trim();
            }
          } else {
            // filterType为'all'或其他情况，使用searchKeyword进行全局搜索
            params.searchKeyword = this.searchKeyword.trim();
          }
        }

        console.log('🚀 fetchReportList 调用参数:', params);
        const response = await getSchemeList(params);

        console.log('API响应:', response);

        if (response && response.code === 200 && response.data) {
          this.reportList = response.data.records || [];
          this.total = response.data.total || 0;
          console.log('成功设置数据 - reportList:', this.reportList);
          console.log('数据示例 - 第一条记录:', this.reportList[0]);
        } else {
          console.error('响应格式错误或请求失败:', response);
          this.$message.error((response && response.msg) || '获取报告列表失败');
          this.reportList = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('获取报告列表失败:', error);

        // 详细的错误处理
        if (error.response) {
          const status = error.response.status;
          const data = error.response.data;
          console.error('响应状态:', status);
          console.error('响应数据:', data);

          if (status === 401) {
            this.$message.error('登录已过期，请重新登录');
            // 可以在这里跳转到登录页面
            // this.$router.push('/login');
          } else if (status === 403) {
            this.$message.error('没有权限访问此功能');
          } else {
            this.$message.error(`获取报告列表失败: ${(data && data.msg) || '服务器错误'}`);
          }
        } else if (error.request) {
          console.error('请求未收到响应:', error.request);
          this.$message.error('网络连接失败，请检查网络连接');
        } else {
          console.error('请求配置错误:', error.message);
          this.$message.error(`请求失败: ${error.message}`);
        }

        this.reportList = [];
        this.total = 0;
      }
    },
    // 根据菜单分类获取报告列表
    async fetchReportListByCategory(categoryName) {
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize
        };

        // 根据分类名称添加筛选条件
        if (categoryName && categoryName !== '全部') {
          // 如果分类名称对应方案类型，则按类型筛选
          const schemeType = this.schemeTypes.find(type => type.name === categoryName);
          if (schemeType) {
            params.typeId = schemeType.id;
          } else {
            // 否则按名称模糊搜索
            params.name = categoryName;
          }
        }

        // 根据筛选类型设置搜索参数
        if (this.searchKeyword && this.searchKeyword.trim()) {
          if (this.filterType === 'name') {
            // 如果已经有name参数（来自分类），则合并搜索条件
            if (params.name) {
              params.searchKeyword = this.searchKeyword.trim();
            } else {
              params.name = this.searchKeyword.trim();
            }
          } else if (this.filterType === 'type') {
            // 根据类型名称查找对应的typeId
            const searchSchemeType = this.schemeTypes.find(type => 
              type.name.toLowerCase().includes(this.searchKeyword.trim().toLowerCase())
            );
            if (searchSchemeType) {
              // 如果已经有typeId参数（来自分类），则需要匹配两个条件
              if (params.typeId && params.typeId !== searchSchemeType.id) {
                // 分类和搜索的类型不匹配，使用searchKeyword进行模糊搜索
                params.searchKeyword = this.searchKeyword.trim();
              } else {
                params.typeId = searchSchemeType.id;
              }
            } else {
              // 如果没找到匹配的类型，使用searchKeyword进行模糊搜索
              params.searchKeyword = this.searchKeyword.trim();
            }
          } else {
            // filterType为'all'或其他情况，使用searchKeyword进行全局搜索
            params.searchKeyword = this.searchKeyword.trim();
          }
        }

        console.log('🚀 fetchReportListByCategory 调用参数:', params);
        console.log('🚀 分类名称:', categoryName);
        const response = await getSchemeList(params);

        console.log('分类筛选API响应:', response);

        if (response && response.code === 200 && response.data) {
          this.reportList = response.data.records || [];
          this.total = response.data.total || 0;
          console.log(`成功获取 "${categoryName}" 分类数据 - reportList长度:`, this.reportList.length, 'total:', this.total);

          // 显示筛选结果提示
          if (categoryName === '全部') {
            this.$message.success(`已显示全部数据，共找到 ${this.total} 条记录`);
          } else if (categoryName) {
            this.$message.success(`已切换到 "${categoryName}" 分类，共找到 ${this.total} 条记录`);
          }
        } else {
          console.error('响应格式错误或请求失败:', response);
          this.$message.error((response && response.msg) || '获取报告列表失败');
          this.reportList = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('根据分类获取报告列表失败:', error);
        this.$message.error('获取报告列表失败，请检查网络连接');
        this.reportList = [];
        this.total = 0;
      }
    },
    // 获取侧边栏菜单分类
    async fetchMenuCategories() {
      try {
        const response = await getMenuCategories();

        if (response.code === 200) {
          this.menuCategories = response.data || [];
        } else {
          this.$message.error(response.msg || '获取菜单分类失败');
        }
      } catch (error) {
        console.error('获取菜单分类失败:', error);
        this.$message.error('获取菜单分类失败，请检查网络连接');
      }
    },
    // 防抖搜索输入处理
    handleSearchInput() {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      
      // 设置新的定时器，500ms后执行搜索
      this.searchTimer = setTimeout(() => {
        this.handleQuery();
      }, 500);
    },
    // 查询按钮点击事件
    handleQuery() {
      this.currentPage = 1; // 重置到第一页
      // 如果有选中的菜单项，按分类筛选；否则获取全部数据
      if (this.activeMenuItem) {
        this.fetchReportListByCategory(this.activeMenuItem);
      } else {
        this.fetchReportList();
      }
      this.$message.success("执行查询操作");
    },
    // 重置按钮点击事件
    handleReset() {
      this.dateRange = [];
      this.filterType = 'all';
      this.searchKeyword = '';
      this.currentPage = 1; // 重置到第一页
      // 如果有选中的菜单项，按分类筛选；否则获取全部数据
      if (this.activeMenuItem) {
        this.fetchReportListByCategory(this.activeMenuItem);
      } else {
        this.fetchReportList();
      }
      this.$message.success("重置筛选条件");
    },
    // 切换选项卡
    switchTab(tab) {
      this.activeTab = tab;
      this.currentPage = 1; // 重置到第一页
      // 根据选中的tab类型获取对应的报告数据
      this.fetchReportListByTemplateType(tab);
    },
    // 根据模板类型获取报告列表
    async fetchReportListByTemplateType(templateType) {
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          templateType: templateType
        };

        // 根据筛选类型设置搜索参数
        if (this.searchKeyword && this.searchKeyword.trim()) {
          if (this.filterType === 'name') {
            params.name = this.searchKeyword.trim();
          } else if (this.filterType === 'type') {
            // 根据类型名称查找对应的typeId
            const schemeType = this.schemeTypes.find(type => 
              type.name.toLowerCase().includes(this.searchKeyword.trim().toLowerCase())
            );
            if (schemeType) {
              params.typeId = schemeType.id;
            } else {
              // 如果没找到匹配的类型，使用searchKeyword进行模糊搜索
              params.searchKeyword = this.searchKeyword.trim();
            }
          } else {
            // filterType为'all'或其他情况，使用searchKeyword进行全局搜索
            params.searchKeyword = this.searchKeyword.trim();
          }
        }

        console.log('=== 模板切换调试信息 ===');
        console.log('当前模板类型:', templateType);
        console.log('🚀 fetchReportListByTemplateType 调用参数:', params);
        console.log('🚀 当前pageSize:', this.pageSize);
        console.log('API URL:', '/report/scheme/list');

        const response = await getSchemeList(params);

        console.log('API响应状态:', response?.code);
        console.log('API响应数据:', response?.data);
        console.log('记录数量:', response?.data?.records?.length);

        if (response && response.code === 200 && response.data) {
          this.reportList = response.data.records || [];
          this.total = response.data.total || 0;
          console.log(`成功获取 "${templateType === 'normal' ? '普通报告' : '竞对报告'}" 数据 - reportList长度:`, this.reportList.length, 'total:', this.total);

          // 显示筛选结果提示
          const typeName = templateType === 'normal' ? '普通报告' : '竞对报告';
          this.$message.success(`已切换到 "${typeName}"，共找到 ${this.total} 条记录`);
        } else {
          console.error('响应格式错误或请求失败:', response);
          this.$message.error((response && response.msg) || '获取报告列表失败');
          this.reportList = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('根据模板类型获取报告列表失败:', error);
        this.$message.error('获取报告列表失败，请检查网络连接');
        this.reportList = [];
        this.total = 0;
      }
    },
    // 创建模板
    createTemplate() {
      this.$message.success("创建新模板");
    },
    // 创建产品模板
    createProductTemplate() {
      this.$message.success("创建产品模板");
    },
    // 查看模板
    async viewTemplate(row) {
      try {
        this.loading = true;
        const response = await getTemplateDetail(row.id);
        if (response.code === 200) {
          this.templateViewData = response.data;
          this.templateViewDialogVisible = true;
        } else {
          this.$message.error(response.msg || '获取模板详情失败');
        }
      } catch (error) {
        console.error('获取模板详情失败:', error);
        this.$message.error('获取模板详情失败，请检查网络连接');
      } finally {
        this.loading = false;
      }
    },
    // 编辑模板
    async editTemplate(row) {
      try {
        this.loading = true;
        const response = await getTemplateDetail(row.id);
        if (response.code === 200) {
          this.templateEditForm = { ...response.data };
          this.templateEditDialogVisible = true;
        } else {
          this.$message.error(response.msg || '获取模板详情失败');
        }
      } catch (error) {
        console.error('获取模板详情失败:', error);
        this.$message.error('获取模板详情失败，请检查网络连接');
      } finally {
        this.loading = false;
      }
    },
    // 删除模板
    async deleteTemplate(row) {
      try {
        await this.$confirm(`确认删除模板 "${row.name}"?`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        });

        this.loading = true;
        const response = await deleteTemplate({
          templateIds: row.id.toString()
        });

        if (response.code === 200) {
          this.$message.success(response.msg || '删除成功');
          // 刷新模板列表
          this.fetchTemplateList();
        } else {
          this.$message.error(response.msg || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除模板失败:', error);
          this.$message.error('删除失败，请检查网络连接');
        }
      } finally {
        this.loading = false;
      }
    },
    // 返回按钮点击事件
    goBack() {
      this.currentView = 'report';
      this.$message.success("返回报告列表");
    },
    // 获取模板列表
    async fetchTemplateList() {
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          templateType: this.templateType
        };

        const response = await getTemplateList(params);

        if (response.code === 200) {
          this.templateList = response.data.records || [];
          this.total = response.data.total || 0;
        } else {
          this.$message.error(response.msg || '获取模板列表失败');
          this.templateList = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('获取模板列表失败:', error);
        this.$message.error('获取模板列表失败，请检查网络连接');
        this.templateList = [];
        this.total = 0;
      }
    },
    // 切换模板类型
    switchTemplateType(type) {
      this.templateType = type;
      this.fetchTemplateList(); // 切换类型后重新获取模板列表
      this.$message.success(`切换到${type === 'normal' ? '普通模板' : '竞对模板'}`);
    },
    // 格式化日期时间
    formatDateTime(row, column, cellValue) {
      try {
        console.log('formatDateTime input:', { row, column, cellValue });
        
        if (!cellValue) {
          console.log('No cellValue provided');
          return '-';
        }

        let date;
        
        // 处理不同的日期格式
        if (typeof cellValue === 'string') {
          // 处理 "9/6/2025 08:52:41" 格式
          if (cellValue.includes('/')) {
            // 将 M/d/yyyy 格式转换为标准格式
            const parts = cellValue.split(' ');
            if (parts.length === 2) {
              const datePart = parts[0];
              const timePart = parts[1];
              const dateComponents = datePart.split('/');
              
              if (dateComponents.length === 3) {
                // 重新组织为 yyyy-MM-dd HH:mm:ss 格式
                const month = dateComponents[0].padStart(2, '0');
                const day = dateComponents[1].padStart(2, '0');
                const year = dateComponents[2];
                const standardFormat = `${year}-${month}-${day} ${timePart}`;
                date = new Date(standardFormat);
              }
            }
          } else {
            date = new Date(cellValue);
          }
        } else {
          date = new Date(cellValue);
        }
        
        // 检查日期是否有效
        if (!date || isNaN(date.getTime())) {
          console.error('Invalid date:', cellValue);
          return cellValue;
        }

        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
      } catch (error) {
        console.error('Error formatting date:', error);
        return cellValue || '-';
      }
    },
    // 获取刷新状态类型
    getRefreshStatusType(status) {
      switch (status) {
        case 0: return 'info';    // 未刷新
        case 1: return 'warning'; // 刷新中
        case 2: return 'success'; // 已刷新
        default: return 'info';
      }
    },
    // 获取刷新状态文本
    getRefreshStatusText(status) {
      switch (status) {
        case 0: return '未刷新';
        case 1: return '刷新中';
        case 2: return '已刷新';
        default: return '未知';
      }
    },
    // 获取方案类型列表
    async fetchSchemeTypes() {
      try {
        const response = await getSchemeTypeList();
        if (response && response.code === 200) {
          this.schemeTypes = response.data || [];
        } else {
          this.$message.error((response && response.msg) || '获取方案类型失败');
        }
      } catch (error) {
        console.error('获取方案类型失败:', error);
        this.$message.error('获取方案类型失败');
      }
    },
    // 编辑提交
    async handleEditSubmit() {
      try {
        await this.$refs.editForm.validate();

        const response = await editScheme(this.editForm);

        if (response && response.code === 200) {
          this.$message.success((response && response.msg) || '编辑成功');
          this.editDialogVisible = false;
          // 刷新列表
          this.fetchReportList();
        } else {
          this.$message.error((response && response.msg) || '编辑失败');
        }
      } catch (error) {
        if (error !== false) { // 表单验证失败时会返回false
          console.error('编辑方案失败:', error);
          this.$message.error('编辑失败');
        }
      }
    },
    // 模板编辑提交
    async handleTemplateEditSubmit() {
      try {
        await this.$refs.templateEditForm.validate();

        const response = await editTemplate(this.templateEditForm);

        if (response && response.code === 200) {
          this.$message.success(response.msg || '编辑模板成功');
          this.templateEditDialogVisible = false;
          // 刷新模板列表
          this.fetchTemplateList();
        } else {
          this.$message.error(response.msg || '编辑模板失败');
        }
      } catch (error) {
        if (error !== false) { // 表单验证失败时会返回false
          console.error('编辑模板失败:', error);
          this.$message.error('编辑模板失败');
        }
      }
    }
  },
  created() {
    console.log('组件创建 - 初始化数据获取');
    this.fetchMenuCategories();
    this.fetchSchemeTypes();
  },
  mounted() {
    console.log('组件挂载 - 开始获取报告列表');
    // 页面挂载后，根据默认选中的菜单项获取数据
    this.$nextTick(() => {
      this.fetchReportListByCategory(this.activeMenuItem);
    });
  }
};
</script>

<style scoped>
.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-container {
  display: flex;
  height: 100%;
}

/* 左侧导航栏样式 */
.left-sidebar {
  width: 200px;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  transition: width 0.3s;
}

/* 折叠状态的侧边栏 */
.left-sidebar.collapsed {
  width: 64px;
}

.left-sidebar.collapsed .sidebar-search,
.left-sidebar.collapsed .el-menu-item span,
.left-sidebar.collapsed .el-submenu__title span {
  display: none;
}

.left-sidebar.collapsed .new-scheme-btn {
  padding: 8px 0;
  font-size: 0;
}

.left-sidebar.collapsed .new-scheme-btn i {
  font-size: 16px;
  margin: 0;
}

.sidebar-header {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.new-scheme-btn {
  flex: 1;
  font-size: 12px;
  padding: 8px 10px;
}

.sidebar-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  cursor: pointer;
  color: #909399;
}

.sidebar-search {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.sidebar-menu {
  flex: 1;
  overflow-y: auto;
}

.sidebar-menu-list {
  border-right: none;
}

.active-menu-item {
  background-color: #ecf5ff !important;
  color: #409EFF !important;
}

/* 全部选项的特殊样式 */
.sidebar-menu-list .el-menu-item[data-index="全部"] {
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 5px;
  font-weight: 500;
}

.sidebar-menu-list .el-menu-item[data-index="全部"] i {
  margin-right: 8px;
  color: #909399;
}

.sidebar-menu-list .el-menu-item[data-index="全部"].is-active i {
  color: #409EFF;
}

/* 右侧内容区样式 */
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px;
  background-color: #f5f7fa;
}

/* 覆盖Element UI的一些默认样式 */
::v-deep .el-menu-item, ::v-deep .el-submenu__title {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

::v-deep .el-submenu .el-menu-item {
  height: 36px;
  line-height: 36px;
  padding: 0 20px 0 40px;
}

/* 报告中心样式 */
.report-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  height: 100%;
  overflow-y: auto;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.report-header .title {
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.report-header .title .category-tag {
  font-size: 14px;
  color: #409EFF;
  font-weight: normal;
  margin-left: 8px;
}

.report-header .actions {
  display: flex;
  gap: 10px;
}

.filter-options {
  margin-bottom: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

.tab-container {
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  width: fit-content;
  margin-left: auto;
}

.tab-item {
  padding: 8px 20px;
  cursor: pointer;
  background-color: #fff;
  color: #606266;
  font-size: 14px;
  text-align: center;
  border-right: 1px solid #dcdfe6;
  min-width: 100px;
}

.tab-item:last-child {
  border-right: none;
}

.tab-item.active {
  background-color: #409EFF;
  color: #fff;
}

.filter-item span {
  margin-right: 10px;
  color: #606266;
  font-size: 14px;
}

.report-table {
  margin-bottom: 20px;
  position: relative;
  min-height: 300px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.report-table ::v-deep .el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  text-align: center;
}

.report-table ::v-deep .el-table td {
  text-align: center;
}

.empty-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.empty-data .empty-icon {
  font-size: 60px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.empty-data p {
  color: #909399;
  font-size: 14px;
}

.delete-btn {
  color: #f56c6c;
}

.template-detail .template-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 模板管理样式 */
.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.template-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.template-actions {
  display: flex;
  gap: 10px;
}

.template-tab-container {
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  width: fit-content;
}

.template-tab {
  padding: 8px 20px;
  cursor: pointer;
  background-color: #fff;
  color: #606266;
  font-size: 14px;
  text-align: center;
  border-right: 1px solid #dcdfe6;
  min-width: 100px;
}

.template-tab:last-child {
  border-right: none;
}

.template-tab.active {
  background-color: #409EFF;
  color: #fff;
}

.template-table {
  margin-bottom: 20px;
}

.create-template-btn {
  margin-bottom: 15px;
}

.create-template-btn .el-button {
  border-color: #f56c6c;
  color: #f56c6c;
}

.create-template-btn .el-button:hover {
  background-color: #fef0f0;
}

.back-button {
  text-align: right;
  margin-bottom: 10px;
}

.back-button .el-button {
  color: #409EFF;
  font-size: 14px;
  padding: 0;
}

.back-button .el-button:hover {
  color: #66b1ff;
}
</style>
