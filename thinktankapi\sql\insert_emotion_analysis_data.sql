-- =====================================================
-- 情感分析汇总表模拟数据插入脚本
-- 创建时间: 2025-06-23
-- 说明: 为emotion_analysis_summary表提供近30天的模拟数据
-- 用途: 支持舆情总览页面的趋势图表显示
-- =====================================================

-- 1. 首先插入情感定义数据（如果不存在）
INSERT IGNORE INTO emotion_definition (
    emotion_name, emotion_code, emotion_color, description, sort_order, 
    status, create_time, update_time, create_by, update_by, remark
) VALUES
('正面', 'positive', '#52c41a', '正面情感，包括赞扬、支持、喜爱等积极情绪', 1, '0', NOW(), NOW(), 'admin', 'admin', '正面情感定义'),
('中性', 'neutral', '#faad14', '中性情感，包括客观描述、中立观点等', 2, '0', NOW(), NOW(), 'admin', 'admin', '中性情感定义'),
('负面', 'negative', '#ff4d4f', '负面情感，包括批评、抱怨、不满等消极情绪', 3, '0', NOW(), NOW(), 'admin', 'admin', '负面情感定义');

-- 2. 清理可能存在的旧数据（可选，根据需要启用）
-- DELETE FROM emotion_analysis_summary WHERE scheme_id BETWEEN 11 AND 36;

-- 3. 为方案11-36生成近30天的情感分析汇总数据
-- 使用存储过程或批量插入来生成数据

-- 方案11的数据（品牌监控）
INSERT INTO emotion_analysis_summary (
    scheme_id, emotion_id, count, date_range_start, date_range_end, 
    create_time, update_time, create_by, update_by, remark
) VALUES
-- 近30天数据，每天3条记录（正面、中性、负面）
-- 第1天
(11, 1, 45, DATE_SUB(CURDATE(), INTERVAL 29 DAY), DATE_SUB(CURDATE(), INTERVAL 29 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 12, DATE_SUB(CURDATE(), INTERVAL 29 DAY), DATE_SUB(CURDATE(), INTERVAL 29 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 8, DATE_SUB(CURDATE(), INTERVAL 29 DAY), DATE_SUB(CURDATE(), INTERVAL 29 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第2天
(11, 1, 52, DATE_SUB(CURDATE(), INTERVAL 28 DAY), DATE_SUB(CURDATE(), INTERVAL 28 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 15, DATE_SUB(CURDATE(), INTERVAL 28 DAY), DATE_SUB(CURDATE(), INTERVAL 28 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 6, DATE_SUB(CURDATE(), INTERVAL 28 DAY), DATE_SUB(CURDATE(), INTERVAL 28 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第3天
(11, 1, 38, DATE_SUB(CURDATE(), INTERVAL 27 DAY), DATE_SUB(CURDATE(), INTERVAL 27 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 18, DATE_SUB(CURDATE(), INTERVAL 27 DAY), DATE_SUB(CURDATE(), INTERVAL 27 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 9, DATE_SUB(CURDATE(), INTERVAL 27 DAY), DATE_SUB(CURDATE(), INTERVAL 27 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第4天
(11, 1, 61, DATE_SUB(CURDATE(), INTERVAL 26 DAY), DATE_SUB(CURDATE(), INTERVAL 26 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 22, DATE_SUB(CURDATE(), INTERVAL 26 DAY), DATE_SUB(CURDATE(), INTERVAL 26 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 11, DATE_SUB(CURDATE(), INTERVAL 26 DAY), DATE_SUB(CURDATE(), INTERVAL 26 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第5天
(11, 1, 47, DATE_SUB(CURDATE(), INTERVAL 25 DAY), DATE_SUB(CURDATE(), INTERVAL 25 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 16, DATE_SUB(CURDATE(), INTERVAL 25 DAY), DATE_SUB(CURDATE(), INTERVAL 25 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 7, DATE_SUB(CURDATE(), INTERVAL 25 DAY), DATE_SUB(CURDATE(), INTERVAL 25 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第6天
(11, 1, 55, DATE_SUB(CURDATE(), INTERVAL 24 DAY), DATE_SUB(CURDATE(), INTERVAL 24 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 19, DATE_SUB(CURDATE(), INTERVAL 24 DAY), DATE_SUB(CURDATE(), INTERVAL 24 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 10, DATE_SUB(CURDATE(), INTERVAL 24 DAY), DATE_SUB(CURDATE(), INTERVAL 24 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第7天
(11, 1, 42, DATE_SUB(CURDATE(), INTERVAL 23 DAY), DATE_SUB(CURDATE(), INTERVAL 23 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 14, DATE_SUB(CURDATE(), INTERVAL 23 DAY), DATE_SUB(CURDATE(), INTERVAL 23 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 8, DATE_SUB(CURDATE(), INTERVAL 23 DAY), DATE_SUB(CURDATE(), INTERVAL 23 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第8天
(11, 1, 58, DATE_SUB(CURDATE(), INTERVAL 22 DAY), DATE_SUB(CURDATE(), INTERVAL 22 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 21, DATE_SUB(CURDATE(), INTERVAL 22 DAY), DATE_SUB(CURDATE(), INTERVAL 22 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 12, DATE_SUB(CURDATE(), INTERVAL 22 DAY), DATE_SUB(CURDATE(), INTERVAL 22 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第9天
(11, 1, 49, DATE_SUB(CURDATE(), INTERVAL 21 DAY), DATE_SUB(CURDATE(), INTERVAL 21 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 17, DATE_SUB(CURDATE(), INTERVAL 21 DAY), DATE_SUB(CURDATE(), INTERVAL 21 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 9, DATE_SUB(CURDATE(), INTERVAL 21 DAY), DATE_SUB(CURDATE(), INTERVAL 21 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第10天
(11, 1, 63, DATE_SUB(CURDATE(), INTERVAL 20 DAY), DATE_SUB(CURDATE(), INTERVAL 20 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 24, DATE_SUB(CURDATE(), INTERVAL 20 DAY), DATE_SUB(CURDATE(), INTERVAL 20 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 13, DATE_SUB(CURDATE(), INTERVAL 20 DAY), DATE_SUB(CURDATE(), INTERVAL 20 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第11天
(11, 1, 41, DATE_SUB(CURDATE(), INTERVAL 19 DAY), DATE_SUB(CURDATE(), INTERVAL 19 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 15, DATE_SUB(CURDATE(), INTERVAL 19 DAY), DATE_SUB(CURDATE(), INTERVAL 19 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 7, DATE_SUB(CURDATE(), INTERVAL 19 DAY), DATE_SUB(CURDATE(), INTERVAL 19 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第12天
(11, 1, 56, DATE_SUB(CURDATE(), INTERVAL 18 DAY), DATE_SUB(CURDATE(), INTERVAL 18 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 20, DATE_SUB(CURDATE(), INTERVAL 18 DAY), DATE_SUB(CURDATE(), INTERVAL 18 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 11, DATE_SUB(CURDATE(), INTERVAL 18 DAY), DATE_SUB(CURDATE(), INTERVAL 18 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第13天
(11, 1, 44, DATE_SUB(CURDATE(), INTERVAL 17 DAY), DATE_SUB(CURDATE(), INTERVAL 17 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 16, DATE_SUB(CURDATE(), INTERVAL 17 DAY), DATE_SUB(CURDATE(), INTERVAL 17 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 8, DATE_SUB(CURDATE(), INTERVAL 17 DAY), DATE_SUB(CURDATE(), INTERVAL 17 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第14天
(11, 1, 59, DATE_SUB(CURDATE(), INTERVAL 16 DAY), DATE_SUB(CURDATE(), INTERVAL 16 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 22, DATE_SUB(CURDATE(), INTERVAL 16 DAY), DATE_SUB(CURDATE(), INTERVAL 16 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 12, DATE_SUB(CURDATE(), INTERVAL 16 DAY), DATE_SUB(CURDATE(), INTERVAL 16 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第15天
(11, 1, 48, DATE_SUB(CURDATE(), INTERVAL 15 DAY), DATE_SUB(CURDATE(), INTERVAL 15 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 18, DATE_SUB(CURDATE(), INTERVAL 15 DAY), DATE_SUB(CURDATE(), INTERVAL 15 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 9, DATE_SUB(CURDATE(), INTERVAL 15 DAY), DATE_SUB(CURDATE(), INTERVAL 15 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第16天到第30天的数据将在下一部分继续...
(11, 1, 62, DATE_SUB(CURDATE(), INTERVAL 14 DAY), DATE_SUB(CURDATE(), INTERVAL 14 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 23, DATE_SUB(CURDATE(), INTERVAL 14 DAY), DATE_SUB(CURDATE(), INTERVAL 14 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 13, DATE_SUB(CURDATE(), INTERVAL 14 DAY), DATE_SUB(CURDATE(), INTERVAL 14 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第17天
(11, 1, 46, DATE_SUB(CURDATE(), INTERVAL 13 DAY), DATE_SUB(CURDATE(), INTERVAL 13 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 17, DATE_SUB(CURDATE(), INTERVAL 13 DAY), DATE_SUB(CURDATE(), INTERVAL 13 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 8, DATE_SUB(CURDATE(), INTERVAL 13 DAY), DATE_SUB(CURDATE(), INTERVAL 13 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第18天
(11, 1, 57, DATE_SUB(CURDATE(), INTERVAL 12 DAY), DATE_SUB(CURDATE(), INTERVAL 12 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 21, DATE_SUB(CURDATE(), INTERVAL 12 DAY), DATE_SUB(CURDATE(), INTERVAL 12 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 11, DATE_SUB(CURDATE(), INTERVAL 12 DAY), DATE_SUB(CURDATE(), INTERVAL 12 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第19天
(11, 1, 43, DATE_SUB(CURDATE(), INTERVAL 11 DAY), DATE_SUB(CURDATE(), INTERVAL 11 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 16, DATE_SUB(CURDATE(), INTERVAL 11 DAY), DATE_SUB(CURDATE(), INTERVAL 11 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 7, DATE_SUB(CURDATE(), INTERVAL 11 DAY), DATE_SUB(CURDATE(), INTERVAL 11 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第20天
(11, 1, 64, DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_SUB(CURDATE(), INTERVAL 10 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 25, DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_SUB(CURDATE(), INTERVAL 10 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 14, DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_SUB(CURDATE(), INTERVAL 10 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 第21天到第30天
(11, 1, 50, DATE_SUB(CURDATE(), INTERVAL 9 DAY), DATE_SUB(CURDATE(), INTERVAL 9 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 19, DATE_SUB(CURDATE(), INTERVAL 9 DAY), DATE_SUB(CURDATE(), INTERVAL 9 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 10, DATE_SUB(CURDATE(), INTERVAL 9 DAY), DATE_SUB(CURDATE(), INTERVAL 9 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 1, 58, DATE_SUB(CURDATE(), INTERVAL 8 DAY), DATE_SUB(CURDATE(), INTERVAL 8 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 22, DATE_SUB(CURDATE(), INTERVAL 8 DAY), DATE_SUB(CURDATE(), INTERVAL 8 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 12, DATE_SUB(CURDATE(), INTERVAL 8 DAY), DATE_SUB(CURDATE(), INTERVAL 8 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 1, 45, DATE_SUB(CURDATE(), INTERVAL 7 DAY), DATE_SUB(CURDATE(), INTERVAL 7 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 17, DATE_SUB(CURDATE(), INTERVAL 7 DAY), DATE_SUB(CURDATE(), INTERVAL 7 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 9, DATE_SUB(CURDATE(), INTERVAL 7 DAY), DATE_SUB(CURDATE(), INTERVAL 7 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 1, 61, DATE_SUB(CURDATE(), INTERVAL 6 DAY), DATE_SUB(CURDATE(), INTERVAL 6 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 24, DATE_SUB(CURDATE(), INTERVAL 6 DAY), DATE_SUB(CURDATE(), INTERVAL 6 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 13, DATE_SUB(CURDATE(), INTERVAL 6 DAY), DATE_SUB(CURDATE(), INTERVAL 6 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 1, 47, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_SUB(CURDATE(), INTERVAL 5 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 18, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_SUB(CURDATE(), INTERVAL 5 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 8, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_SUB(CURDATE(), INTERVAL 5 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 1, 55, DATE_SUB(CURDATE(), INTERVAL 4 DAY), DATE_SUB(CURDATE(), INTERVAL 4 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 21, DATE_SUB(CURDATE(), INTERVAL 4 DAY), DATE_SUB(CURDATE(), INTERVAL 4 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 11, DATE_SUB(CURDATE(), INTERVAL 4 DAY), DATE_SUB(CURDATE(), INTERVAL 4 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 1, 52, DATE_SUB(CURDATE(), INTERVAL 3 DAY), DATE_SUB(CURDATE(), INTERVAL 3 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 19, DATE_SUB(CURDATE(), INTERVAL 3 DAY), DATE_SUB(CURDATE(), INTERVAL 3 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 10, DATE_SUB(CURDATE(), INTERVAL 3 DAY), DATE_SUB(CURDATE(), INTERVAL 3 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 1, 59, DATE_SUB(CURDATE(), INTERVAL 2 DAY), DATE_SUB(CURDATE(), INTERVAL 2 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 23, DATE_SUB(CURDATE(), INTERVAL 2 DAY), DATE_SUB(CURDATE(), INTERVAL 2 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 12, DATE_SUB(CURDATE(), INTERVAL 2 DAY), DATE_SUB(CURDATE(), INTERVAL 2 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 1, 48, DATE_SUB(CURDATE(), INTERVAL 1 DAY), DATE_SUB(CURDATE(), INTERVAL 1 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 18, DATE_SUB(CURDATE(), INTERVAL 1 DAY), DATE_SUB(CURDATE(), INTERVAL 1 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 9, DATE_SUB(CURDATE(), INTERVAL 1 DAY), DATE_SUB(CURDATE(), INTERVAL 1 DAY), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
-- 今天
(11, 1, 65, CURDATE(), CURDATE(), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 2, 25, CURDATE(), CURDATE(), NOW(), NOW(), 'admin', 'admin', '每日情感统计'),
(11, 3, 15, CURDATE(), CURDATE(), NOW(), NOW(), 'admin', 'admin', '每日情感统计');

-- 由于文件长度限制，这里只展示方案11的完整数据
-- 其他方案的数据将在后续部分添加...
