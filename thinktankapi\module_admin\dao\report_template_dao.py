from sqlalchemy import and_, or_, desc, asc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Tuple
from module_admin.entity.do.report_template_do import ReportTemplateDO
from module_admin.entity.vo.report_template_vo import ReportTemplatePageQueryModel
from utils.page_util import PageUtil


class ReportTemplateDao:
    """
    报告模板数据访问层
    """

    @classmethod
    async def get_report_template_list(
        cls,
        query_db: AsyncSession,
        query_object: ReportTemplatePageQueryModel,
        is_page: bool = False
    ):
        """
        获取报告模板列表
        """
        query = select(ReportTemplateDO)
        
        # 构建查询条件
        conditions = []
        
        if query_object.name:
            conditions.append(ReportTemplateDO.name.like(f'%{query_object.name}%'))
        
        if query_object.template_type:
            conditions.append(ReportTemplateDO.template_type == query_object.template_type)
        
        if query_object.is_active is not None:
            conditions.append(ReportTemplateDO.is_active == query_object.is_active)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 排序
        query = query.order_by(desc(ReportTemplateDO.create_time))
        
        if is_page:
            # 分页查询
            return await PageUtil.paginate(
                query_db,
                query,
                query_object.page_num,
                query_object.page_size,
                is_page=True
            )
        else:
            # 不分页查询
            result = await query_db.execute(query)
            return result.scalars().all()

    @classmethod
    async def get_report_template_count(
        cls,
        query_db: AsyncSession,
        query_object: ReportTemplatePageQueryModel
    ) -> int:
        """
        获取报告模板总数
        """
        query = select(func.count(ReportTemplateDO.id))
        
        # 构建查询条件
        conditions = []
        
        if query_object.name:
            conditions.append(ReportTemplateDO.name.like(f'%{query_object.name}%'))
        
        if query_object.template_type:
            conditions.append(ReportTemplateDO.template_type == query_object.template_type)
        
        if query_object.is_active is not None:
            conditions.append(ReportTemplateDO.is_active == query_object.is_active)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await query_db.execute(query)
        return result.scalar()

    @classmethod
    async def get_report_template_detail_by_id(
        cls,
        query_db: AsyncSession,
        template_id: int
    ) -> Optional[ReportTemplateDO]:
        """
        根据模板ID获取模板详情
        """
        query = select(ReportTemplateDO).where(ReportTemplateDO.id == template_id)
        result = await query_db.execute(query)
        return result.scalar_one_or_none()

    @classmethod
    async def add_report_template_dao(
        cls,
        query_db: AsyncSession,
        template_do: ReportTemplateDO
    ) -> ReportTemplateDO:
        """
        新增报告模板
        """
        query_db.add(template_do)
        await query_db.flush()
        await query_db.refresh(template_do)
        return template_do

    @classmethod
    async def edit_report_template_dao(
        cls,
        query_db: AsyncSession,
        template_do: ReportTemplateDO
    ) -> int:
        """
        编辑报告模板
        """
        await query_db.merge(template_do)
        return template_do.id

    @classmethod
    async def delete_report_template_dao(
        cls,
        query_db: AsyncSession,
        template_ids: List[int]
    ) -> int:
        """
        删除报告模板
        """
        query = select(ReportTemplateDO).where(ReportTemplateDO.id.in_(template_ids))
        result = await query_db.execute(query)
        templates = result.scalars().all()
        
        for template in templates:
            await query_db.delete(template)
        
        return len(templates)

    @classmethod
    async def get_report_template_by_type(
        cls,
        query_db: AsyncSession,
        template_type: str,
        is_active: bool = True
    ) -> List[ReportTemplateDO]:
        """
        根据类型获取报告模板列表
        """
        query = select(ReportTemplateDO).where(
            and_(
                ReportTemplateDO.template_type == template_type,
                ReportTemplateDO.is_active == is_active
            )
        ).order_by(desc(ReportTemplateDO.create_time))
        
        result = await query_db.execute(query)
        return result.scalars().all()
