from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text, BigInteger, Float, Boolean
from sqlalchemy.orm import relationship
from config.database import Base


class News(Base):
    """
    新闻表
    """

    __tablename__ = 'news'
    __table_args__ = {'extend_existing': True}

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    title = Column(String(500), nullable=False, comment='新闻标题')
    content = Column(Text, nullable=True, comment='新闻内容')
    summary = Column(Text, nullable=True, comment='新闻摘要')
    url = Column(String(500), nullable=True, comment='新闻链接')
    source = Column(String(100), nullable=True, comment='新闻来源')
    author = Column(String(100), nullable=True, comment='作者')
    platform = Column(String(100), nullable=True, comment='平台名称')
    platform_type = Column(String(50), nullable=True, comment='平台类型：web, weibo, wechat, app, video等')
    category = Column(String(50), nullable=True, comment='新闻分类')
    language = Column(String(10), default='zh', comment='语言')
    region = Column(String(100), nullable=True, comment='地区')
    sentiment = Column(String(20), default='neutral', comment='情感倾向：positive, negative, neutral')
    sentiment_score = Column(Float, default=0.0, comment='情感分数：-1到1')
    keywords = Column(Text, nullable=True, comment='关键词，JSON格式')
    tags = Column(Text, nullable=True, comment='标签，JSON格式')
    images = Column(Text, nullable=True, comment='图片链接，JSON格式')
    videos = Column(Text, nullable=True, comment='视频链接，JSON格式')
    view_count = Column(Integer, default=0, comment='浏览数')
    share_count = Column(Integer, default=0, comment='分享数')
    comment_count = Column(Integer, default=0, comment='评论数')
    like_count = Column(Integer, default=0, comment='点赞数')
    heat_score = Column(Float, default=0.0, comment='热度分数')
    quality_score = Column(Float, default=0.0, comment='质量分数')
    credibility_score = Column(Float, default=0.0, comment='可信度分数')
    publish_time = Column(DateTime, nullable=True, comment='发布时间')
    crawl_time = Column(DateTime, nullable=True, comment='抓取时间')
    is_hot = Column(Boolean, default=False, comment='是否为热点新闻')
    is_verified = Column(Boolean, default=False, comment='是否已验证')
    is_deleted = Column(Boolean, default=False, comment='是否已删除')
    duplicate_group = Column(String(100), nullable=True, comment='去重分组')
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')

    # 关联关系
    # hot_news_extensions = relationship("HotNewsExtension", back_populates="news")  # 暂时注释掉
    # event_news = relationship("EventNews", back_populates="news")  # 暂时注释掉
    # scheme_news = relationship("SchemeNews", back_populates="news")  # 暂时注释掉
