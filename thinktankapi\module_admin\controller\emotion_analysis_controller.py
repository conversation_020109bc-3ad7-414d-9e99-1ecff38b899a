from fastapi import APIRouter, Depends, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.analysis_service import AnalysisService
from module_admin.service.login_service import LoginService
from utils.response_util import ResponseUtil
from utils.log_util import logger


emotionAnalysisController = APIRouter(prefix='/public/emotion', dependencies=[])


@emotionAnalysisController.get('/overview')
async def get_opinion_overview_data(
    request: Request,
    scheme_id: int = Query(default=1, description='方案ID'),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取舆情总览数据
    """
    try:
        overview_data = await AnalysisService.get_analysis_overview_services(query_db, scheme_id)
        logger.info(f'获取舆情总览数据成功，方案ID: {scheme_id}')

        return ResponseUtil.success(model_content=overview_data)
    except Exception as e:
        logger.error(f'获取舆情总览数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取舆情总览数据失败: {str(e)}')


# 内部接口，需要认证
internalEmotionAnalysisController = APIRouter(prefix='/system/emotion', dependencies=[Depends(LoginService.get_current_user)])


@internalEmotionAnalysisController.get('/overview')
async def get_internal_opinion_overview_data(
    request: Request,
    scheme_id: int = Query(default=1, description='方案ID'),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取舆情总览数据（内部接口）
    """
    try:
        overview_data = await AnalysisService.get_analysis_overview_services(query_db, scheme_id)
        logger.info(f'获取舆情总览数据成功，方案ID: {scheme_id}')

        return ResponseUtil.success(model_content=overview_data)
    except Exception as e:
        logger.error(f'获取舆情总览数据失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取舆情总览数据失败: {str(e)}')
