from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.get_db import get_db
from module_admin.service.login_service import LoginService
from module_opinion.service.opinion_template_service import OpinionTemplateService
from module_opinion.entity.vo.opinion_template_vo import (
    OpinionTemplatePageQueryModel,
    CreateOpinionTemplateModel,
    UpdateOpinionTemplateModel,
    DeleteOpinionTemplateModel,
    UpdateTemplateUsageModel
)
from utils.response_util import ResponseUtil
from utils.log_util import logger


opinionTemplateController = APIRouter(prefix='/opinion/template', tags=['舆情分析模板管理'])


@opinionTemplateController.get('/list', summary='获取舆情分析模板列表')
async def get_opinion_template_list(
    request: Request,
    query_object: OpinionTemplatePageQueryModel = Depends(OpinionTemplatePageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db)
):
    try:
        # 获取分页数据
        template_page_query_result = await OpinionTemplateService.get_opinion_template_list_services(
            query_db, query_object, is_page=True
        )
        logger.info('获取舆情分析模板列表成功')
        return ResponseUtil.success(model_content=template_page_query_result)
    except Exception as e:
        logger.error(f'获取舆情分析模板列表失败: {e}')
        return ResponseUtil.error(msg=str(e))


@opinionTemplateController.get('/selection', summary='获取用于选择的模板列表')
async def get_templates_for_selection(
    request: Request,
    query_db: AsyncSession = Depends(get_db)
):
    try:
        # 获取用于选择的模板列表
        templates_result = await OpinionTemplateService.get_templates_for_selection_services(query_db)
        logger.info('获取模板选择列表成功')
        return ResponseUtil.success(data=templates_result)
    except Exception as e:
        logger.error(f'获取模板选择列表失败: {e}')
        return ResponseUtil.error(msg=str(e))


@opinionTemplateController.get('/categories', summary='获取模板分类列表')
async def get_template_categories(
    request: Request,
    query_db: AsyncSession = Depends(get_db)
):
    try:
        # 获取模板分类列表
        categories_result = await OpinionTemplateService.get_template_categories_services(query_db)
        logger.info('获取模板分类列表成功')
        return ResponseUtil.success(data=categories_result)
    except Exception as e:
        logger.error(f'获取模板分类列表失败: {e}')
        return ResponseUtil.error(msg=str(e))


@opinionTemplateController.get('/{template_id}', summary='获取舆情分析模板详情')
async def get_opinion_template_detail(
    request: Request,
    template_id: int,
    query_db: AsyncSession = Depends(get_db)
):
    try:
        # 获取模板详情
        template_detail_result = await OpinionTemplateService.get_opinion_template_detail_services(
            query_db, template_id
        )
        logger.info(f'获取舆情分析模板详情成功: {template_id}')
        return ResponseUtil.success(data=template_detail_result)
    except Exception as e:
        logger.error(f'获取舆情分析模板详情失败: {e}')
        return ResponseUtil.error(msg=str(e))


@opinionTemplateController.post('/add', summary='新增舆情分析模板')
async def add_opinion_template(
    request: Request,
    add_template: CreateOpinionTemplateModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user)
):
    try:
        # 新增模板
        add_template_result = await OpinionTemplateService.add_opinion_template_services(
            query_db, add_template, current_user.get('user_id')
        )
        logger.info('新增舆情分析模板成功')
        return ResponseUtil.success(model_content=add_template_result)
    except Exception as e:
        logger.error(f'新增舆情分析模板失败: {e}')
        return ResponseUtil.error(msg=str(e))


@opinionTemplateController.put('/edit', summary='编辑舆情分析模板')
async def edit_opinion_template(
    request: Request,
    edit_template: UpdateOpinionTemplateModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user)
):
    try:
        # 编辑模板
        edit_template_result = await OpinionTemplateService.edit_opinion_template_services(
            query_db, edit_template, current_user.get('user_id')
        )
        logger.info(f'编辑舆情分析模板成功: {edit_template.id}')
        return ResponseUtil.success(model_content=edit_template_result)
    except Exception as e:
        logger.error(f'编辑舆情分析模板失败: {e}')
        return ResponseUtil.error(msg=str(e))


@opinionTemplateController.delete('/delete', summary='删除舆情分析模板')
async def delete_opinion_template(
    request: Request,
    delete_template: DeleteOpinionTemplateModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user)
):
    try:
        # 删除模板
        delete_template_result = await OpinionTemplateService.delete_opinion_template_services(
            query_db, delete_template
        )
        logger.info(f'删除舆情分析模板成功: {delete_template.ids}')
        return ResponseUtil.success(model_content=delete_template_result)
    except Exception as e:
        logger.error(f'删除舆情分析模板失败: {e}')
        return ResponseUtil.error(msg=str(e))


@opinionTemplateController.put('/usage', summary='更新模板使用次数')
async def update_template_usage(
    request: Request,
    usage_update: UpdateTemplateUsageModel,
    query_db: AsyncSession = Depends(get_db)
):
    try:
        # 更新使用次数
        update_result = await OpinionTemplateService.update_template_usage_services(
            query_db, usage_update
        )
        logger.info(f'更新模板使用次数成功: {usage_update.template_id}')
        return ResponseUtil.success(model_content=update_result)
    except Exception as e:
        logger.error(f'更新模板使用次数失败: {e}')
        return ResponseUtil.error(msg=str(e))
