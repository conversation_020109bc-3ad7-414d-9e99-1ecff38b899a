import request from '@/utils/request'

// ==================== 热点事件管理 ====================

// 获取简单热点事件列表
export function getSimpleHotEventsList(query) {
  return request({
    url: '/hot-events/simple/list',
    method: 'get',
    params: query
  })
}

// 获取热点事件地图数据
export function getSimpleHotEventsMap(query) {
  return request({
    url: '/hot-events/simple/map',
    method: 'get',
    params: query
  })
}

// 获取简单热点事件详情
export function getSimpleHotEventsDetails(eventId) {
  return request({
    url: '/hot-events/simple/details/' + eventId,
    method: 'get'
  })
}

// 按分类获取热点事件
export function getHotEventsByCategory(category, query) {
  return request({
    url: '/hot-events/category/' + category,
    method: 'get',
    params: query
  })
}

// 获取热点事件列表
export function getHotEventsList(query) {
  return request({
    url: '/hot-events/list',
    method: 'get',
    params: query
  })
}

// 获取热点事件详情
export function getHotEventsDetail(eventId) {
  return request({
    url: '/hot-events/detail/' + eventId,
    method: 'get'
  })
}

// 获取热点事件统计
export function getHotEventsStatistics(query) {
  return request({
    url: '/hot-events/statistics',
    method: 'get',
    params: query
  })
}

// 获取热点事件趋势
export function getHotEventsTrend(query) {
  return request({
    url: '/hot-events/trend',
    method: 'get',
    params: query
  })
}

// 获取热点事件排行榜
export function getHotEventsRanking(query) {
  return request({
    url: '/hot-events/ranking',
    method: 'get',
    params: query
  })
}

// 获取热点事件关键词
export function getHotEventsKeywords(eventId) {
  return request({
    url: '/hot-events/keywords/' + eventId,
    method: 'get'
  })
}

// 获取热点事件传播分析
export function getHotEventsSpreadAnalysis(eventId) {
  return request({
    url: '/hot-events/spread-analysis/' + eventId,
    method: 'get'
  })
}

// 获取热点事件影响力分析
export function getHotEventsInfluence(eventId) {
  return request({
    url: '/hot-events/influence/' + eventId,
    method: 'get'
  })
}

// 获取热点事件相关新闻
export function getHotEventsRelatedNews(eventId, query) {
  return request({
    url: '/hot-events/related-news/' + eventId,
    method: 'get',
    params: query
  })
}

// 获取热点事件时间轴
export function getHotEventsTimeline(eventId) {
  return request({
    url: '/hot-events/timeline/' + eventId,
    method: 'get'
  })
}

// 搜索热点事件
export function searchHotEvents(keyword, query) {
  return request({
    url: '/hot-events/search',
    method: 'get',
    params: { keyword: keyword, ...query }
  })
}

// 获取热点事件预测
export function getHotEventsPrediction(query) {
  return request({
    url: '/hot-events/prediction',
    method: 'get',
    params: query
  })
}

// 导出热点事件数据
export function exportHotEventsData(query) {
  return request({
    url: '/hot-events/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
