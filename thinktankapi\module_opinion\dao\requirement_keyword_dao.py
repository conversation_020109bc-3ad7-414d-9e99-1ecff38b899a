from datetime import datetime
from sqlalchemy import and_, or_, desc, asc, func, select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from module_opinion.entity.do.requirement_keyword_do import RequirementKeyword
from module_opinion.entity.do.keyword_category_definition_do import KeywordCategoryDefinition
from module_opinion.entity.vo.requirement_keyword_vo import RequirementKeywordPageQueryModel, RequirementKeywordModel
from utils.page_util import PageUtil


class RequirementKeywordDao:
    """
    需求关键词数据访问层
    """

    @classmethod
    async def get_requirement_keyword_list(
        cls, db: AsyncSession, query_object: RequirementKeywordPageQueryModel, is_page: bool = False
    ):
        """
        根据查询参数获取需求关键词列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 需求关键词列表信息对象
        """
        query = select(RequirementKeyword).where(RequirementKeyword.is_deleted == 0)
        
        # 构建查询条件
        if query_object.requirement_id:
            query = query.where(RequirementKeyword.requirement_id == query_object.requirement_id)
        if query_object.keyword:
            query = query.where(RequirementKeyword.keyword.like(f'%{query_object.keyword}%'))
        if query_object.keyword_type:
            query = query.where(RequirementKeyword.keyword_type == query_object.keyword_type)
        if query_object.category_id:
            query = query.where(RequirementKeyword.category_id == query_object.category_id)
        if query_object.is_selected is not None:
            query = query.where(RequirementKeyword.is_selected == query_object.is_selected)
        if query_object.status is not None:
            query = query.where(RequirementKeyword.status == query_object.status)

        # 排序：按权重降序，创建时间降序
        query = query.order_by(desc(RequirementKeyword.weight), desc(RequirementKeyword.create_time))

        if is_page:
            # 分页查询
            return await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size)
        else:
            # 不分页查询
            result = await db.execute(query)
            return result.scalars().all()

    @classmethod
    async def get_requirement_keyword_by_id(cls, db: AsyncSession, keyword_id: int):
        """
        根据ID获取需求关键词详细信息

        :param db: orm对象
        :param keyword_id: 关键词ID
        :return: 需求关键词信息对象
        """
        query = select(RequirementKeyword).where(
            and_(RequirementKeyword.id == keyword_id, RequirementKeyword.is_deleted == 0)
        )
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def add_requirement_keyword_dao(cls, db: AsyncSession, keyword: RequirementKeywordModel):
        """
        新增需求关键词数据库操作

        :param db: orm对象
        :param keyword: 需求关键词对象
        :return: 新增的需求关键词对象
        """
        db_keyword = RequirementKeyword(**keyword.model_dump(exclude={'id'}))
        db.add(db_keyword)
        await db.flush()
        return db_keyword

    @classmethod
    async def batch_add_requirement_keywords_dao(cls, db: AsyncSession, keywords: List[RequirementKeywordModel]):
        """
        批量新增需求关键词数据库操作

        :param db: orm对象
        :param keywords: 需求关键词对象列表
        :return: 新增的需求关键词对象列表
        """
        db_keywords = []
        for keyword in keywords:
            db_keyword = RequirementKeyword(**keyword.model_dump(exclude={'id'}))
            db_keywords.append(db_keyword)
            db.add(db_keyword)
        
        await db.flush()
        return db_keywords

    @classmethod
    async def edit_requirement_keyword_dao(cls, db: AsyncSession, keyword: dict):
        """
        编辑需求关键词数据库操作

        :param db: orm对象
        :param keyword: 需要更新的需求关键词字典
        :return:
        """
        await db.execute(
            update(RequirementKeyword).where(RequirementKeyword.id == keyword['id']).values(**keyword)
        )

    @classmethod
    async def batch_update_keywords_selection(cls, db: AsyncSession, requirement_id: int, keyword_ids: List[int]):
        """
        批量更新关键词选择状态

        :param db: orm对象
        :param requirement_id: 需求ID
        :param keyword_ids: 选中的关键词ID列表
        :return:
        """
        # 先将该需求下所有关键词设为未选中
        await db.execute(
            update(RequirementKeyword)
            .where(RequirementKeyword.requirement_id == requirement_id)
            .values(is_selected=0, update_time=datetime.now())
        )
        
        # 再将指定关键词设为选中
        if keyword_ids:
            await db.execute(
                update(RequirementKeyword)
                .where(and_(
                    RequirementKeyword.requirement_id == requirement_id,
                    RequirementKeyword.id.in_(keyword_ids)
                ))
                .values(is_selected=1, update_time=datetime.now())
            )

    @classmethod
    async def delete_requirement_keyword_dao(cls, db: AsyncSession, keyword_ids: List[int]):
        """
        删除需求关键词数据库操作（软删除）

        :param db: orm对象
        :param keyword_ids: 需求关键词ID列表
        :return:
        """
        await db.execute(
            update(RequirementKeyword)
            .where(RequirementKeyword.id.in_(keyword_ids))
            .values(is_deleted=1, update_time=datetime.now())
        )

    @classmethod
    async def get_selected_keywords_by_requirement(cls, db: AsyncSession, requirement_id: int):
        """
        获取需求下已选择的关键词

        :param db: orm对象
        :param requirement_id: 需求ID
        :return: 已选择的关键词列表
        """
        query = select(RequirementKeyword).where(
            and_(
                RequirementKeyword.requirement_id == requirement_id,
                RequirementKeyword.is_selected == 1,
                RequirementKeyword.is_deleted == 0,
                RequirementKeyword.status == 1
            )
        ).order_by(desc(RequirementKeyword.weight))
        
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_keywords_count_by_requirement(cls, db: AsyncSession, requirement_id: int, is_selected: Optional[int] = None):
        """
        获取需求下关键词数量

        :param db: orm对象
        :param requirement_id: 需求ID
        :param is_selected: 是否选中（可选）
        :return: 关键词数量
        """
        query = select(func.count(RequirementKeyword.id)).where(
            and_(
                RequirementKeyword.requirement_id == requirement_id,
                RequirementKeyword.is_deleted == 0,
                RequirementKeyword.status == 1
            )
        )
        
        if is_selected is not None:
            query = query.where(RequirementKeyword.is_selected == is_selected)
            
        result = await db.execute(query)
        return result.scalar()

    @classmethod
    async def check_keyword_exists(cls, db: AsyncSession, requirement_id: int, keyword: str):
        """
        检查关键词是否已存在

        :param db: orm对象
        :param requirement_id: 需求ID
        :param keyword: 关键词
        :return: 是否存在
        """
        query = select(func.count(RequirementKeyword.id)).where(
            and_(
                RequirementKeyword.requirement_id == requirement_id,
                RequirementKeyword.keyword == keyword,
                RequirementKeyword.is_deleted == 0
            )
        )
        
        result = await db.execute(query)
        count = result.scalar()
        return count > 0

    @classmethod
    async def get_keyword_categories(cls, db: AsyncSession):
        """
        获取关键词分类列表

        :param db: orm对象
        :return: 分类列表
        """
        query = select(KeywordCategoryDefinition).where(
            KeywordCategoryDefinition.is_active == 1
        ).order_by(asc(KeywordCategoryDefinition.sort_order), asc(KeywordCategoryDefinition.id))
        
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def clear_requirement_keywords(cls, db: AsyncSession, requirement_id: int):
        """
        清空需求下的所有关键词（软删除）

        :param db: orm对象
        :param requirement_id: 需求ID
        :return:
        """
        await db.execute(
            update(RequirementKeyword)
            .where(RequirementKeyword.requirement_id == requirement_id)
            .values(is_deleted=1, update_time=datetime.now())
        )
