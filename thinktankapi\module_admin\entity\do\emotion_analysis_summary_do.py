from datetime import datetime
from sqlalchemy import Column, DateTime, Foreign<PERSON><PERSON>, Integer, String
from sqlalchemy.orm import relationship
from config.database import Base


class EmotionAnalysisSummary(Base):
    """
    情感分析汇总表
    """

    __tablename__ = 'emotion_analysis_summary'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    scheme_id = Column(Integer, nullable=False, comment='关联的方案ID')
    emotion_id = Column(Integer, ForeignKey('emotion_definition.id'), nullable=False, comment='情感类型ID')
    count = Column(Integer, default=0, comment='该情感类型的舆情数量')
    date_range_start = Column(DateTime, nullable=True, comment='统计区间开始时间')
    date_range_end = Column(DateTime, nullable=True, comment='统计区间结束时间')
    create_time = Column(DateTime, nullable=True, default=datetime.now(), comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now(), comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')

    # 关联情感定义表 - 暂时注释掉避免循环依赖
    # emotion_definition = relationship('EmotionDefinition', back_populates='analysis_summaries')
