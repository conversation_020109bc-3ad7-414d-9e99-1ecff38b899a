from datetime import datetime
from fastapi import APIRouter, Depends, Request
from pydantic_validation_decorator import <PERSON><PERSON><PERSON><PERSON><PERSON>s
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.service.login_service import LoginService
from module_warning.service.warning_settings_service import WarningSettingsService
from module_warning.entity.vo.warning_vo import (
    WarningSettingsConfigModel,
    DeleteWarningSettingsModel
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from utils.log_util import logger
from utils.response_util import ResponseUtil


warningSettingsController = APIRouter(prefix='/warning/settings', dependencies=[Depends(LoginService.get_current_user)])


@warningSettingsController.get(
    '/{scheme_id}', dependencies=[Depends(CheckUserInterfaceAuth('warning:settings:query'))]
)
async def get_warning_settings(
    request: Request,
    scheme_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取预警设置
    """
    try:
        settings_result = await WarningSettingsService.get_warning_settings_services(query_db, scheme_id)
        logger.info(f'获取预警设置成功，方案ID: {scheme_id}')
        return ResponseUtil.success(data=settings_result)
    except Exception as e:
        logger.error(f'获取预警设置失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警设置失败: {str(e)}')


@warningSettingsController.post('', dependencies=[Depends(CheckUserInterfaceAuth('warning:settings:add'))])
@ValidateFields(validate_model='save_warning_settings')
@Log(title='预警设置', business_type=BusinessType.INSERT)
async def save_warning_settings(
    request: Request,
    settings_config: WarningSettingsConfigModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    保存预警设置
    """
    try:
        settings_config.create_by = current_user.user.user_name
        settings_config.update_by = current_user.user.user_name
        save_result = await WarningSettingsService.save_warning_settings_services(query_db, settings_config)
        logger.info(save_result.message)
        return ResponseUtil.success(msg=save_result.message)
    except Exception as e:
        logger.error(f'保存预警设置失败: {str(e)}')
        return ResponseUtil.error(msg=f'保存预警设置失败: {str(e)}')


@warningSettingsController.put('', dependencies=[Depends(CheckUserInterfaceAuth('warning:settings:edit'))])
@ValidateFields(validate_model='update_warning_settings')
@Log(title='预警设置', business_type=BusinessType.UPDATE)
async def update_warning_settings(
    request: Request,
    settings_config: WarningSettingsConfigModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新预警设置
    """
    try:
        settings_config.update_by = current_user.user.user_name
        update_result = await WarningSettingsService.save_warning_settings_services(query_db, settings_config)
        logger.info(update_result.message)
        return ResponseUtil.success(msg=update_result.message)
    except Exception as e:
        logger.error(f'更新预警设置失败: {str(e)}')
        return ResponseUtil.error(msg=f'更新预警设置失败: {str(e)}')


@warningSettingsController.delete('', dependencies=[Depends(CheckUserInterfaceAuth('warning:settings:remove'))])
@ValidateFields(validate_model='delete_warning_settings')
@Log(title='预警设置', business_type=BusinessType.DELETE)
async def delete_warning_settings(
    request: Request,
    delete_settings: DeleteWarningSettingsModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除预警设置
    """
    try:
        delete_result = await WarningSettingsService.delete_warning_settings_services(query_db, delete_settings)
        logger.info(delete_result.message)
        return ResponseUtil.success(msg=delete_result.message)
    except Exception as e:
        logger.error(f'删除预警设置失败: {str(e)}')
        return ResponseUtil.error(msg=f'删除预警设置失败: {str(e)}')


@warningSettingsController.get(
    '/default', dependencies=[Depends(CheckUserInterfaceAuth('warning:settings:query'))]
)
async def get_default_warning_settings(
    request: Request,
):
    """
    获取默认预警设置
    """
    try:
        default_settings = await WarningSettingsService.get_default_settings_services()
        logger.info('获取默认预警设置成功')
        return ResponseUtil.success(data=default_settings)
    except Exception as e:
        logger.error(f'获取默认预警设置失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取默认预警设置失败: {str(e)}')


@warningSettingsController.post(
    '/validate', dependencies=[Depends(CheckUserInterfaceAuth('warning:settings:query'))]
)
async def validate_warning_settings(
    request: Request,
    settings_config: WarningSettingsConfigModel,
):
    """
    验证预警设置
    """
    try:
        validate_result = await WarningSettingsService.validate_settings_services(settings_config)
        logger.info(f'验证预警设置: {validate_result.message}')
        if validate_result.is_success:
            return ResponseUtil.success(msg=validate_result.message)
        else:
            return ResponseUtil.failure(msg=validate_result.message)
    except Exception as e:
        logger.error(f'验证预警设置失败: {str(e)}')
        return ResponseUtil.error(msg=f'验证预警设置失败: {str(e)}')


@warningSettingsController.post(
    '/copy', dependencies=[Depends(CheckUserInterfaceAuth('warning:settings:add'))]
)
@Log(title='复制预警设置', business_type=BusinessType.INSERT)
async def copy_warning_settings(
    request: Request,
    source_scheme_id: int,
    target_scheme_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    复制预警设置
    """
    try:
        copy_result = await WarningSettingsService.copy_settings_services(
            query_db, source_scheme_id, target_scheme_id
        )
        logger.info(copy_result.message)
        return ResponseUtil.success(msg=copy_result.message)
    except Exception as e:
        logger.error(f'复制预警设置失败: {str(e)}')
        return ResponseUtil.error(msg=f'复制预警设置失败: {str(e)}')


@warningSettingsController.post(
    '/{scheme_id}/reset', dependencies=[Depends(CheckUserInterfaceAuth('warning:settings:edit'))]
)
@Log(title='重置预警设置', business_type=BusinessType.UPDATE)
async def reset_warning_settings(
    request: Request,
    scheme_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    重置预警设置为默认值
    """
    try:
        reset_result = await WarningSettingsService.reset_settings_services(query_db, scheme_id)
        logger.info(reset_result.message)
        return ResponseUtil.success(msg=reset_result.message)
    except Exception as e:
        logger.error(f'重置预警设置失败: {str(e)}')
        return ResponseUtil.error(msg=f'重置预警设置失败: {str(e)}')


@warningSettingsController.get(
    '/config/options', dependencies=[Depends(CheckUserInterfaceAuth('warning:settings:query'))]
)
async def get_settings_config_options(
    request: Request,
):
    """
    获取预警设置配置选项
    """
    try:
        config_options = {
            'platform_types': [
                {'label': '全部', 'value': 'all'},
                {'label': '网页', 'value': 'webpage'},
                {'label': '微信', 'value': 'wechat'},
                {'label': '微博', 'value': 'weibo'},
                {'label': '头条号', 'value': 'toutiao'},
                {'label': 'APP', 'value': 'app'},
                {'label': '视频', 'value': 'video'},
                {'label': '论坛', 'value': 'forum'},
                {'label': '报刊', 'value': 'newspaper'},
                {'label': '问答', 'value': 'qa'}
            ],
            'content_property': [
                {'label': '全部', 'value': 'all'},
                {'label': '是', 'value': 'yes'},
                {'label': '不是', 'value': 'no'}
            ],
            'info_type': [
                {'label': '全部', 'value': 'all'},
                {'label': '非评论', 'value': 'noncomment'},
                {'label': '评论', 'value': 'comment'}
            ],
            'match_objects': [
                {'label': '标题匹配', 'value': 'title'},
                {'label': '正文匹配', 'value': 'content'},
                {'label': '音频/图片匹配', 'value': 'media'},
                {'label': '原文匹配', 'value': 'original'}
            ],
            'match_method': [
                {'label': '精准', 'value': 'exact'},
                {'label': '模糊', 'value': 'fuzzy'}
            ]
        }
        logger.info('获取预警设置配置选项成功')
        return ResponseUtil.success(data=config_options)
    except Exception as e:
        logger.error(f'获取预警设置配置选项失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警设置配置选项失败: {str(e)}')
