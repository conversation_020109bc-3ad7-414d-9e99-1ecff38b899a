from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field
from module_admin.entity.vo.common_vo import PageQueryModel


class RequirementKeywordModel(BaseModel):
    """
    需求关键词模型
    """
    id: Optional[int] = Field(default=None, description='主键ID')
    requirement_id: int = Field(..., description='需求ID')
    keyword: str = Field(..., description='关键词')
    keyword_type: Optional[str] = Field(default='generated', description='关键词类型')
    category_id: Optional[int] = Field(default=None, description='分类ID')
    is_selected: Optional[int] = Field(default=0, description='是否选中：0-否，1-是')
    weight: Optional[int] = Field(default=1, description='权重')
    status: Optional[int] = Field(default=1, description='状态：0-禁用，1-启用')
    is_deleted: Optional[int] = Field(default=0, description='是否删除：0-否，1-是')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default='', description='创建者')
    update_by: Optional[str] = Field(default='', description='更新者')
    remark: Optional[str] = Field(default='', description='备注信息')


class RequirementKeywordPageQueryModel(PageQueryModel):
    """
    需求关键词分页查询模型
    """
    requirement_id: Optional[int] = Field(default=None, description='需求ID')
    keyword: Optional[str] = Field(default=None, description='关键词')
    keyword_type: Optional[str] = Field(default=None, description='关键词类型')
    category_id: Optional[int] = Field(default=None, description='分类ID')
    is_selected: Optional[int] = Field(default=None, description='是否选中')
    status: Optional[int] = Field(default=None, description='状态')


class GenerateKeywordsModel(BaseModel):
    """
    生成关键词模型
    """
    requirement_id: int = Field(..., description='需求ID')
    entity_keyword: str = Field(..., description='实体关键词')
    specific_requirement: str = Field(..., description='具体需求描述')
    category_id: Optional[int] = Field(default=None, description='分类ID')
    max_count: Optional[int] = Field(default=20, description='最大生成数量')


class SelectKeywordsModel(BaseModel):
    """
    选择关键词模型
    """
    requirement_id: int = Field(..., description='需求ID')
    keyword_ids: List[int] = Field(..., description='选中的关键词ID列表')


class BatchUpdateKeywordsModel(BaseModel):
    """
    批量更新关键词模型
    """
    requirement_id: int = Field(..., description='需求ID')
    keyword_updates: List[dict] = Field(..., description='关键词更新列表')


class KeywordCategoryModel(BaseModel):
    """
    关键词分类模型
    """
    id: int = Field(..., description='分类ID')
    category_name: str = Field(..., description='分类名称')
    category_code: str = Field(..., description='分类编码')
    parent_id: Optional[int] = Field(default=0, description='父分类ID')
    category_level: Optional[int] = Field(default=1, description='分类层级')
    sort_order: Optional[int] = Field(default=0, description='排序顺序')
    description: Optional[str] = Field(default=None, description='分类描述')
    status: Optional[int] = Field(default=1, description='状态')


class GeneratedKeywordModel(BaseModel):
    """
    生成的关键词模型
    """
    keyword: str = Field(..., description='关键词')
    weight: Optional[int] = Field(default=1, description='权重')
    category_id: Optional[int] = Field(default=None, description='分类ID')
    is_selected: Optional[bool] = Field(default=False, description='是否默认选中')


class GenerateKeywordsResponseModel(BaseModel):
    """
    生成关键词响应模型
    """
    requirement_id: int = Field(..., description='需求ID')
    generated_keywords: List[GeneratedKeywordModel] = Field(..., description='生成的关键词列表')
    total_count: int = Field(..., description='生成总数')
    max_selectable: int = Field(..., description='最大可选择数量')


class GenerateRelatedKeywordsRequest(BaseModel):
    """
    生成关联词请求模型
    """
    requirement_content: str = Field(..., min_length=1, max_length=2000, description='具体需求内容')
    max_count: Optional[int] = Field(default=20, ge=1, le=50, description='最大生成数量')


class GenerateRelatedKeywordsResponse(BaseModel):
    """
    生成关联词响应模型
    """
    keywords: List[str] = Field(..., description='生成的关联词列表')
    total: int = Field(..., description='生成的关联词总数')
