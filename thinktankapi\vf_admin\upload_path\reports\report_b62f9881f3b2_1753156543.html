<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#5470c6">
    <title>舆情分析报告 - 产品口碑分析_20250722115323_bhtwme</title>
    <style>
        
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            line-height: 1.5;
            color: #333;
            background-color: #f5f5f5;
            padding: 0;
            margin: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* 容器样式 */
        .report-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 报告头部 */
        .report-header {
            background: #5470c6;
            color: white;
            padding: 24px 20px;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .report-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
        }

        .report-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            font-size: 14px;
        }

        .meta-item {
            display: flex;
            align-items: center;
        }

        .meta-label {
            font-weight: 500;
            margin-right: 8px;
            opacity: 0.9;
        }

        .meta-value {
            opacity: 0.9;
        }

        /* 内容区域 */
        .section {
            padding: 24px 20px;
            border-bottom: 1px solid #eee;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-left: 10px;
            border-left: 4px solid #5470c6;
        }

        /* 概览统计 */
        .overview-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: space-around;
        }

        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            min-width: 120px;
            flex: 1;
        }

        .stat-number {
            font-size: 28px;
            font-weight: 700;
            color: #5470c6;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
        }

        /* 情感分析 */
        .sentiment-analysis {
            margin-top: 20px;
        }

        .sentiment-item {
            margin-bottom: 16px;
        }

        .sentiment-bar {
            height: 24px;
            background: #eee;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .bar-fill {
            height: 100%;
            border-radius: 4px;
        }

        .sentiment-item.positive .bar-fill {
            background: #67C23A;
        }

        .sentiment-item.neutral .bar-fill {
            background: #E6A23C;
        }

        .sentiment-item.negative .bar-fill {
            background: #F56C6C;
        }

        .sentiment-info {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
        }

        .sentiment-label {
            font-weight: 500;
        }

        .sentiment-value {
            font-weight: 600;
        }

        /* 关键词 */
        .keywords-container {
            margin-top: 20px;
        }

        .keywords-list {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .keyword-tag {
            background: #ecf5ff;
            color: #409EFF;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 14px;
            border: 1px solid #d9ecff;
        }

        /* 数据源 */
        .sources-container {
            margin-top: 20px;
        }

        .source-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
            background: #f8f9fa;
            margin-bottom: 12px;
        }

        .source-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 16px;
        }

        .source-icon.online {
            background: #ecf5ff;
            color: #409EFF;
        }

        .source-icon.custom {
            background: #f0f9eb;
            color: #67C23A;
        }

        .source-info {
            flex: 1;
        }

        .source-name {
            font-weight: 500;
            font-size: 16px;
        }

        .source-desc {
            font-size: 12px;
            color: #999;
        }

        .source-count {
            font-weight: 600;
            color: #5470c6;
        }

        /* 文章列表 */
        .articles-container {
            margin-top: 20px;
        }

        .article-item {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background: white;
        }

        .article-header {
            margin-bottom: 12px;
        }

        .article-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .article-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 12px;
        }

        .article-source {
            color: #666;
        }

        .sentiment-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .sentiment-tag.positive {
            background: #f0f9eb;
            color: #67C23A;
            border: 1px solid #c2e7b0;
        }

        .sentiment-tag.neutral {
            background: #fdf6ec;
            color: #E6A23C;
            border: 1px solid #f5dab1;
        }

        .sentiment-tag.negative {
            background: #fef0f0;
            color: #F56C6C;
            border: 1px solid #fbc4c4;
        }

        .article-content {
            margin-bottom: 12px;
        }

        .content-text {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }

        .article-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #999;
        }

        .article-link {
            color: #5470c6;
            text-decoration: none;
        }

        .article-link:hover {
            text-decoration: underline;
        }

        /* 页脚 */
        .report-footer {
            text-align: center;
            padding: 24px;
            color: #999;
            font-size: 12px;
            background: #f8f9fa;
        }

        .footer-text {
            margin-bottom: 8px;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #999;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (min-width: 768px) {
            .report-container {
                max-width: 1200px;
                margin: 20px auto;
                border-radius: 8px;
            }

            .report-header {
                border-radius: 8px 8px 0 0;
            }

            .report-title {
                font-size: 28px;
            }
        }

        @media (max-width: 767px) {
            .overview-stats {
                flex-direction: column;
                gap: 12px;
            }

            .stat-card {
                width: 100%;
                padding: 16px;
            }

            .report-meta {
                flex-direction: column;
                gap: 8px;
            }

            .section {
                padding: 20px 16px;
            }

            .section-title {
                font-size: 18px;
                margin-bottom: 16px;
            }

            .report-title {
                font-size: 20px;
            }

            .report-header {
                padding: 20px 16px;
            }

            .article-item {
                padding: 16px;
                margin-bottom: 12px;
            }

            .article-title {
                font-size: 16px;
                line-height: 1.4;
            }

            .article-summary {
                font-size: 14px;
                line-height: 1.5;
                margin: 8px 0;
            }

            .sentiment-analysis {
                gap: 16px;
            }

            .sentiment-item {
                padding: 12px;
            }

            /* 移动端触摸优化 */
            .article-link {
                display: block;
                padding: 4px 0;
                min-height: 44px;
                line-height: 1.4;
            }

            /* 防止移动端缩放 */
            input, textarea, select {
                font-size: 16px;
            }
        }
        
    </style>
</head>
<body>
    <div class="report-container">
        <!-- 报告头部 -->
        <div class="report-header">
            <div class="header-content">
                <h1 class="report-title">舆情分析报告</h1>
                <div class="report-meta">
                    <div class="meta-item">
                        <span class="meta-label">需求名称:</span>
                        <span class="meta-value">产品口碑分析_20250722115323_bhtwme</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">实体关键词:</span>
                        <span class="meta-value">方太</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">生成时间:</span>
                        <span class="meta-value">2025年07月22日 11:55</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 分析概览 -->
        <div class="section">
            <h2 class="section-title">分析概览</h2>
            <div class="overview-stats">
                <div class="stat-card">
                    <div class="stat-number">3</div>
                    <div class="stat-label">相关文章</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">关键词</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">1</div>
                    <div class="stat-label">数据源</div>
                </div>
            </div>
        </div>
        
        <!-- 情感倾向分析 -->
        <div class="section">
            <h2 class="section-title">情感倾向分析</h2>
            <div class="sentiment-analysis">
                <div class="sentiment-item positive">
                    <div class="sentiment-bar">
                        <div class="bar-fill" style="width: 0%"></div>
                    </div>
                    <div class="sentiment-info">
                        <span class="sentiment-label">正面</span>
                        <span class="sentiment-value">0%</span>
                    </div>
                </div>
                <div class="sentiment-item neutral">
                    <div class="sentiment-bar">
                        <div class="bar-fill" style="width: 100%"></div>
                    </div>
                    <div class="sentiment-info">
                        <span class="sentiment-label">中性</span>
                        <span class="sentiment-value">100%</span>
                    </div>
                </div>
                <div class="sentiment-item negative">
                    <div class="sentiment-bar">
                        <div class="bar-fill" style="width: 0%"></div>
                    </div>
                    <div class="sentiment-info">
                        <span class="sentiment-label">负面</span>
                        <span class="sentiment-value">0%</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 关键词分析 -->
        <div class="section">
            <h2 class="section-title">关键词分析</h2>
            <div class="keywords-container">
                <div class="keywords-list"><span class="keyword-tag">用户评价</span><span class="keyword-tag">产品功能</span><span class="keyword-tag">用户体验</span><span class="keyword-tag">性能测试</span><span class="keyword-tag">用户满意度</span></div>
            </div>
        </div>
        
        <!-- 数据来源统计 -->
        <div class="section">
            <h2 class="section-title">数据来源统计</h2>
            <div class="sources-container">
                
            <div class="source-item">
                <div class="source-icon online">🔍</div>
                <div class="source-info">
                    <div class="source-name">联网搜索</div>
                    <div class="source-desc">AI搜索引擎数据</div>
                </div>
                <div class="source-count">3 条</div>
            </div>
            
            </div>
        </div>
        
        <!-- 详细数据 -->
        <div class="section">
            <h2 class="section-title">详细数据</h2>
            <div class="articles-container">
                
            <div class="article-item">
                <div class="article-header">
                    <h4 class="article-title">方太2025款TE22燃气灶获市场好评，安装服务响应迅速</h4>
                    <div class="article-meta">
                        <span class="article-source">天猫平台</span>
                        <span class="sentiment-tag neutral">中性</span>
                    </div>
                </div>
                <div class="article-content">
                    <p class="content-text">根据天猫旗舰店用户反馈，方太2025款TE22系列燃气灶凭借'优雅白'外观设计赢得消费者青睐，安装服务在浙江、河北等多地实现48小时上门承诺。统计显示93%用户给予五星服务评价，在线客服平均响应速度达41秒/次，情感分析显示92%用户给予正向评价。</p>
                </div>
                <div class="article-footer">
                    <span class="publish-time">2025-07-22 14:30</span>
                    <a href="https://detail.tmall.com/item_7550234225.htm" target="_blank" class="article-link">查看原文</a>
                </div>
            </div>
            
            <div class="article-item">
                <div class="article-header">
                    <h4 class="article-title">方太洗碗机VP10型号遭集中投诉，售后服务引争议</h4>
                    <div class="article-meta">
                        <span class="article-source">京东平台</span>
                        <span class="sentiment-tag neutral">中性</span>
                    </div>
                </div>
                <div class="article-content">
                    <p class="content-text">京东维权平台数据显示，2025年7月16-18日期间，方太洗碗机02-VP10型号因'喷淋臂卡顿'问题在北京、上海地区连续出现9起F7系统报错案例。消费者反映售后处理周期超过96小时，77%的投诉直指售后服务时效问题，部分用户要求退货协调处理。</p>
                </div>
                <div class="article-footer">
                    <span class="publish-time">2025-07-22 14:30</span>
                    <a href="https://help.jd.com/user/issue/detail-1022535.html" target="_blank" class="article-link">查看原文</a>
                </div>
            </div>
            
            <div class="article-item">
                <div class="article-header">
                    <h4 class="article-title">方太厨电产品线满意度达86%，售后响应时效成痛点</h4>
                    <div class="article-meta">
                        <span class="article-source">中国家电研究院</span>
                        <span class="sentiment-tag neutral">中性</span>
                    </div>
                </div>
                <div class="article-content">
                    <p class="content-text">第三方监测显示，方太厨电产品线满意度维持86%高位，抽油烟机JCD15T型号降噪技术获中国家电研究院认证（噪音≤52dB）。但河北地区售后平均处理周期72小时，超出行业标准34%，12%维修案例存在故障解释不清晰问题，导致整体服务满意度骤降至52%。</p>
                </div>
                <div class="article-footer">
                    <span class="publish-time">2025-07-22 14:30</span>
                    <a href="https://www.12315.cn/cuserportal/complain/form" target="_blank" class="article-link">查看原文</a>
                </div>
            </div>
            
            </div>
        </div>
        
        <!-- 页面底部 -->
        <div class="report-footer">
            <p class="footer-text">本报告由智库系统自动生成</p>
            <p class="footer-time">生成时间: 2025年07月22日 11:55</p>
        </div>
    </div>
</body>
</html>