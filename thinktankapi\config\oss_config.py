"""
OSS配置类
用于管理阿里云对象存储服务的配置参数
"""
import os
from typing import Optional


class OSSConfig:
    """
    阿里云OSS配置类
    """
    
    # OSS基本配置参数
    ACCESS_KEY_ID = "LTAIHVOdjxgTfzUK"
    ACCESS_KEY_SECRET = "cbLUAqQrpgJuL0SbgijZ8S55ph4MIX"
    ENDPOINT = "http://oss-cn-hangzhou.aliyuncs.com"
    BUCKET_NAME = "jingangai"
    REGION = "cn-hangzhou"
    
    # 报告文件存储配置
    REPORT_PREFIX = "opinion-reports"  # 报告文件在OSS中的目录前缀
    
    # 文件访问权限配置
    FILE_ACL = "public-read"  # 设置文件为公网可读
    
    @classmethod
    def get_public_url(cls, object_key: str) -> str:
        """
        获取OSS对象的自定义域名访问URL

        :param object_key: OSS对象键名
        :return: 自定义域名访问URL
        """
        # 使用自定义域名格式：https://oss.jingangai.cn/{object_key}
        return f"https://oss.jingangai.cn/{object_key}"

    @classmethod
    def get_signed_domain_url(cls, object_key: str) -> str:
        """
        获取带签名参数的自定义域名访问URL

        :param object_key: OSS对象键名
        :return: 带签名的自定义域名URL
        """
        # 基础自定义域名URL
        base_url = f"https://oss.jingangai.cn/{object_key}"

        # 添加必要的OSS访问参数
        # 格式: https://oss.jingangai.cn/{object_key}?OSSAccessKeyId=xxx&Expires=xxx&Signature=xxx
        return base_url
    
    @classmethod
    def get_report_object_key(cls, page_id: str) -> str:
        """
        生成报告文件的OSS对象键名
        
        :param page_id: 页面ID
        :return: OSS对象键名
        """
        return f"{cls.REPORT_PREFIX}/{page_id}.html"
    
    @classmethod
    def validate_config(cls) -> bool:
        """
        验证OSS配置是否完整
        
        :return: 配置是否有效
        """
        required_fields = [
            cls.ACCESS_KEY_ID,
            cls.ACCESS_KEY_SECRET,
            cls.ENDPOINT,
            cls.BUCKET_NAME
        ]
        return all(field for field in required_fields)
