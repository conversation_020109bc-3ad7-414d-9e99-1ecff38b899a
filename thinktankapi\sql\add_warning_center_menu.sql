-- 添加预警中心菜单记录
-- 注意：请根据实际情况调整menu_id，确保不与现有记录冲突

-- 检查是否已存在预警中心菜单
SELECT menu_id, menu_name, path, icon FROM sys_menu WHERE menu_name LIKE '%预警%' OR path LIKE '%warning%';

-- 查看当前最大菜单ID
SELECT MAX(menu_id) as max_menu_id FROM sys_menu;

-- 添加预警中心一级菜单（如果不存在）
INSERT IGNORE INTO sys_menu (
    menu_id,
    menu_name,
    parent_id,
    order_num,
    path,
    component,
    query,
    route_name,
    is_frame,
    is_cache,
    menu_type,
    visible,
    status,
    perms,
    icon,
    create_by,
    create_time,
    update_by,
    update_time,
    remark
) VALUES (
    2000,                           -- menu_id
    '预警中心',                      -- menu_name
    0,                              -- parent_id (0表示一级菜单)
    5,                              -- order_num (排序)
    'warning-center',               -- path
    'warning-center/index',         -- component
    '',                             -- query
    'WarningCenter',                -- route_name
    1,                              -- is_frame (1表示不是外链)
    0,                              -- is_cache (0表示缓存)
    'C',                            -- menu_type (C表示菜单)
    '0',                            -- visible (0表示显示)
    '0',                            -- status (0表示正常)
    'warning:center:view',          -- perms (权限标识)
    'warning',                      -- icon (图标名称)
    'admin',                        -- create_by
    NOW(),                          -- create_time
    'admin',                        -- update_by
    NOW(),                          -- update_time
    '预警中心菜单'                   -- remark
);

-- 如果菜单已存在，更新图标
UPDATE sys_menu SET
    icon = 'warning',
    update_time = NOW()
WHERE menu_name = '预警中心' OR path = 'warning-center';

-- 验证插入结果
SELECT menu_id, menu_name, path, icon FROM sys_menu WHERE menu_name = '预警中心';
