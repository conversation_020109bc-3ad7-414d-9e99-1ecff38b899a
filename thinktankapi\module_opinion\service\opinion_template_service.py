from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from exceptions.exception import ServiceException
from module_opinion.dao.opinion_template_dao import OpinionTemplateDao
from module_opinion.entity.vo.opinion_template_vo import (
    OpinionTemplateModel,
    OpinionTemplatePageQueryModel,
    CreateOpinionTemplateModel,
    UpdateOpinionTemplateModel,
    DeleteOpinionTemplateModel,
    OpinionTemplateListModel,
    UpdateTemplateUsageModel,
    OpinionTemplateCategoryModel
)
from module_admin.entity.vo.common_vo import CrudResponseModel
from utils.common_util import CamelCaseUtil


class OpinionTemplateService:
    """
    舆情分析模板服务层
    """

    @classmethod
    async def get_opinion_template_list_services(
        cls, query_db: AsyncSession, query_object: OpinionTemplatePageQueryModel, is_page: bool = False
    ):
        """
        获取舆情分析模板列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 舆情分析模板列表信息对象
        """
        template_list_result = await OpinionTemplateDao.get_opinion_template_list(query_db, query_object, is_page)
        
        if is_page:
            return template_list_result
        else:
            return CamelCaseUtil.transform_result(template_list_result)

    @classmethod
    async def get_opinion_template_detail_services(cls, query_db: AsyncSession, template_id: int):
        """
        获取舆情分析模板详细信息service

        :param query_db: orm对象
        :param template_id: 模板ID
        :return: 舆情分析模板详细信息对象
        """
        template_detail_result = await OpinionTemplateDao.get_opinion_template_detail(query_db, template_id)
        if template_detail_result:
            return CamelCaseUtil.transform_result(template_detail_result)
        else:
            raise ServiceException(message='模板不存在')

    @classmethod
    async def add_opinion_template_services(
        cls, query_db: AsyncSession, add_template: CreateOpinionTemplateModel, current_user_id: int
    ):
        """
        新增舆情分析模板service

        :param query_db: orm对象
        :param add_template: 新增舆情分析模板对象
        :param current_user_id: 当前用户ID
        :return: 新增舆情分析模板校验结果
        """
        # 构建模板对象
        template_data = OpinionTemplateModel(
            template_name=add_template.template_name,
            template_category=add_template.template_category,
            entity_keyword=add_template.entity_keyword,
            specific_requirement=add_template.specific_requirement,
            template_description=add_template.template_description,
            priority=add_template.priority,
            max_keywords_limit=add_template.max_keywords_limit,
            template_tags=add_template.template_tags,
            sort_order=add_template.sort_order,
            is_system_template=0,  # 用户创建的模板默认为非系统模板
            is_active=1,
            create_by=str(current_user_id),
            remark=add_template.remark
        )

        try:
            new_template = await OpinionTemplateDao.add_opinion_template_dao(query_db, template_data)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功', data={'id': new_template.id})
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_opinion_template_services(
        cls, query_db: AsyncSession, edit_template: UpdateOpinionTemplateModel, current_user_id: int
    ):
        """
        编辑舆情分析模板service

        :param query_db: orm对象
        :param edit_template: 编辑舆情分析模板对象
        :param current_user_id: 当前用户ID
        :return: 编辑舆情分析模板校验结果
        """
        # 检查模板是否存在
        existing_template = await OpinionTemplateDao.get_opinion_template_detail(query_db, edit_template.id)
        if not existing_template:
            raise ServiceException(message='模板不存在')

        # 构建更新对象
        update_data = edit_template.model_dump(exclude_unset=True)
        update_data['update_by'] = str(current_user_id)
        template_data = OpinionTemplateModel(**update_data)

        try:
            await OpinionTemplateDao.edit_opinion_template_dao(query_db, template_data)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def delete_opinion_template_services(
        cls, query_db: AsyncSession, delete_template: DeleteOpinionTemplateModel
    ):
        """
        删除舆情分析模板service

        :param query_db: orm对象
        :param delete_template: 删除舆情分析模板对象
        :return: 删除舆情分析模板校验结果
        """
        try:
            await OpinionTemplateDao.delete_opinion_template_dao(query_db, delete_template.ids)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='删除成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def get_template_categories_services(cls, query_db: AsyncSession):
        """
        获取模板分类列表service

        :param query_db: orm对象
        :return: 模板分类列表
        """
        categories_result = await OpinionTemplateDao.get_template_categories(query_db)
        categories = [
            OpinionTemplateCategoryModel(
                category_name=category.template_category,
                template_count=category.template_count
            )
            for category in categories_result
        ]
        return CamelCaseUtil.transform_result(categories)

    @classmethod
    async def get_templates_for_selection_services(cls, query_db: AsyncSession):
        """
        获取用于选择的模板列表service

        :param query_db: orm对象
        :return: 模板选择列表
        """
        templates_result = await OpinionTemplateDao.get_active_templates_for_selection(query_db)
        templates = [
            OpinionTemplateListModel(
                id=template.id,
                template_name=template.template_name,
                template_category=template.template_category,
                entity_keyword=template.entity_keyword,
                specific_requirement=template.specific_requirement,
                priority=template.priority,
                usage_count=template.usage_count
            )
            for template in templates_result
        ]
        return CamelCaseUtil.transform_result(templates)

    @classmethod
    async def update_template_usage_services(
        cls, query_db: AsyncSession, usage_update: UpdateTemplateUsageModel
    ):
        """
        更新模板使用次数service

        :param query_db: orm对象
        :param usage_update: 使用次数更新对象
        :return: 更新结果
        """
        try:
            await OpinionTemplateDao.update_template_usage_count(
                query_db, usage_update.template_id, usage_update.increment
            )
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='使用次数更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e
