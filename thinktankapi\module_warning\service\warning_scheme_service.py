from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_warning.dao.warning_scheme_dao import WarningSchemeDao
from module_warning.dao.warning_settings_dao import WarningSettingsDao
from module_warning.dao.warning_record_dao import WarningRecordDao
from module_warning.entity.vo.warning_vo import (
    WarningSchemeModel,
    WarningSchemePageQueryModel,
    DeleteWarningSchemeModel
)
from utils.common_util import CamelCaseUtil
from utils.log_util import logger


class WarningSchemeService:
    """
    预警方案管理模块服务层
    """

    @classmethod
    async def get_warning_scheme_list_services(
        cls, query_db: AsyncSession, query_object: WarningSchemePageQueryModel, is_page: bool = False
    ):
        """
        获取预警方案列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 预警方案列表信息对象
        """
        try:
            scheme_list_result = await WarningSchemeDao.get_warning_scheme_list(query_db, query_object, is_page)
            logger.info('获取预警方案列表成功')
            return scheme_list_result
        except Exception as e:
            logger.error(f'获取预警方案列表失败: {str(e)}')
            raise ServiceException(message=f'获取预警方案列表失败: {str(e)}')

    @classmethod
    async def get_active_warning_schemes_services(cls, query_db: AsyncSession):
        """
        获取所有启用的预警方案service

        :param query_db: orm对象
        :return: 启用的预警方案列表
        """
        try:
            active_schemes = await WarningSchemeDao.get_active_warning_schemes(query_db)
            result = CamelCaseUtil.transform_result(active_schemes)
            logger.info('获取启用预警方案列表成功')
            return result
        except Exception as e:
            logger.error(f'获取启用预警方案列表失败: {str(e)}')
            raise ServiceException(message=f'获取启用预警方案列表失败: {str(e)}')

    @classmethod
    async def get_warning_scheme_detail_services(cls, query_db: AsyncSession, scheme_id: int):
        """
        获取预警方案详细信息service

        :param query_db: orm对象
        :param scheme_id: 预警方案id
        :return: 预警方案详细信息对象
        """
        try:
            scheme_detail_result = await WarningSchemeDao.get_warning_scheme_by_id(query_db, scheme_id)
            if scheme_detail_result:
                result = CamelCaseUtil.transform_result(scheme_detail_result)
                logger.info(f'获取预警方案详情成功，方案ID: {scheme_id}')
                return result
            else:
                raise ServiceException(message='预警方案不存在')
        except ServiceException:
            raise
        except Exception as e:
            logger.error(f'获取预警方案详情失败: {str(e)}')
            raise ServiceException(message=f'获取预警方案详情失败: {str(e)}')

    @classmethod
    async def check_scheme_name_unique_services(cls, query_db: AsyncSession, scheme_name: str, scheme_id: int = None):
        """
        校验预警方案名称唯一性service

        :param query_db: orm对象
        :param scheme_name: 预警方案名称
        :param scheme_id: 预警方案ID（编辑时排除自身）
        :return: 校验结果
        """
        try:
            is_unique = await WarningSchemeDao.check_scheme_name_unique(query_db, scheme_name, scheme_id)
            return is_unique
        except Exception as e:
            logger.error(f'校验预警方案名称唯一性失败: {str(e)}')
            raise ServiceException(message=f'校验预警方案名称唯一性失败: {str(e)}')

    @classmethod
    async def add_warning_scheme_services(cls, query_db: AsyncSession, page_object: WarningSchemeModel):
        """
        新增预警方案信息service

        :param query_db: orm对象
        :param page_object: 新增预警方案对象
        :return: 新增预警方案校验结果
        """
        try:
            # 校验方案名称唯一性
            if not await cls.check_scheme_name_unique_services(query_db, page_object.scheme_name):
                raise ServiceException(message=f'新增预警方案{page_object.scheme_name}失败，方案名称已存在')

            # 设置创建时间
            page_object.create_time = datetime.now()
            page_object.update_time = datetime.now()
            
            # 验证字段
            page_object.validate_fields()
            
            new_scheme = await WarningSchemeDao.add_warning_scheme_dao(query_db, page_object)
            await query_db.commit()
            logger.info(f'新增预警方案成功，方案名称: {page_object.scheme_name}')
            return CrudResponseModel(is_success=True, message='新增成功')
        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f'新增预警方案失败: {str(e)}')
            raise ServiceException(message=f'新增预警方案失败: {str(e)}')

    @classmethod
    async def edit_warning_scheme_services(cls, query_db: AsyncSession, page_object: WarningSchemeModel):
        """
        编辑预警方案信息service

        :param query_db: orm对象
        :param page_object: 编辑预警方案对象
        :return: 编辑预警方案校验结果
        """
        try:
            # 检查方案是否存在
            existing_scheme = await WarningSchemeDao.get_warning_scheme_by_id(query_db, page_object.id)
            if not existing_scheme:
                raise ServiceException(message='预警方案不存在')

            # 校验方案名称唯一性（排除自身）
            if not await cls.check_scheme_name_unique_services(query_db, page_object.scheme_name, page_object.id):
                raise ServiceException(message=f'编辑预警方案{page_object.scheme_name}失败，方案名称已存在')

            # 设置更新时间
            page_object.update_time = datetime.now()
            
            # 验证字段
            page_object.validate_fields()
            
            edit_scheme = page_object.model_dump(exclude_unset=True)
            await WarningSchemeDao.edit_warning_scheme_dao(query_db, edit_scheme)
            await query_db.commit()
            logger.info(f'编辑预警方案成功，方案ID: {page_object.id}')
            return CrudResponseModel(is_success=True, message='更新成功')
        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f'编辑预警方案失败: {str(e)}')
            raise ServiceException(message=f'编辑预警方案失败: {str(e)}')

    @classmethod
    async def delete_warning_scheme_services(cls, query_db: AsyncSession, page_object: DeleteWarningSchemeModel):
        """
        删除预警方案信息service

        :param query_db: orm对象
        :param page_object: 删除预警方案对象
        :return: 删除预警方案校验结果
        """
        try:
            if not page_object.scheme_ids:
                raise ServiceException(message='请选择要删除的预警方案')

            # 检查方案是否存在
            for scheme_id in page_object.scheme_ids:
                existing_scheme = await WarningSchemeDao.get_warning_scheme_by_id(query_db, scheme_id)
                if not existing_scheme:
                    raise ServiceException(message=f'预警方案ID {scheme_id} 不存在')

            # 按正确顺序删除关联数据：warning_record → warning_settings → warning_scheme
            for scheme_id in page_object.scheme_ids:
                # 1. 删除预警记录
                logger.info(f'开始删除方案 {scheme_id} 的预警记录')
                await WarningRecordDao.delete_warning_record_by_scheme_id(query_db, scheme_id)
                logger.info(f'成功删除方案 {scheme_id} 的预警记录')

                # 2. 删除预警设置
                logger.info(f'开始删除方案 {scheme_id} 的预警设置')
                await WarningSettingsDao.delete_warning_settings_by_scheme_id(query_db, scheme_id)
                logger.info(f'成功删除方案 {scheme_id} 的预警设置')

            # 3. 删除预警方案
            logger.info(f'开始删除预警方案，方案ID列表: {page_object.scheme_ids}')
            await WarningSchemeDao.delete_warning_scheme_dao(query_db, page_object.scheme_ids)
            await query_db.commit()
            logger.info(f'删除预警方案成功，方案数量: {len(page_object.scheme_ids)}')
            return CrudResponseModel(is_success=True, message='删除成功')
        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f'删除预警方案失败: {str(e)}')
            raise ServiceException(message=f'删除预警方案失败: {str(e)}')

    @classmethod
    async def toggle_scheme_status_services(
        cls, query_db: AsyncSession, scheme_id: int, is_active: bool, update_by: str
    ):
        """
        切换预警方案状态service

        :param query_db: orm对象
        :param scheme_id: 预警方案ID
        :param is_active: 是否启用
        :param update_by: 更新者
        :return: 切换结果
        """
        try:
            # 检查方案是否存在
            existing_scheme = await WarningSchemeDao.get_warning_scheme_by_id(query_db, scheme_id)
            if not existing_scheme:
                raise ServiceException(message='预警方案不存在')

            await WarningSchemeDao.toggle_scheme_status(query_db, scheme_id, is_active, update_by)
            await query_db.commit()
            status_text = '启用' if is_active else '停用'
            logger.info(f'{status_text}预警方案成功，方案ID: {scheme_id}')
            return CrudResponseModel(is_success=True, message=f'{status_text}成功')
        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f'切换预警方案状态失败: {str(e)}')
            raise ServiceException(message=f'切换预警方案状态失败: {str(e)}')
