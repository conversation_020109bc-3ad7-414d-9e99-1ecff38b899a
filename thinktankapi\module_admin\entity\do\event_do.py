from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text, BigInteger, Float, Boolean
from sqlalchemy.orm import relationship
from config.database import Base


class Event(Base):
    """
    事件表
    """

    __tablename__ = 'event'
    __table_args__ = {'extend_existing': True}

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    title = Column(String(255), nullable=False, comment='事件标题')
    description = Column(Text, nullable=True, comment='事件描述')
    event_type = Column(String(50), nullable=True, comment='事件类型：brand_crisis, product_issue, market_change等')
    priority = Column(String(20), default='medium', comment='优先级：high, medium, low')
    status = Column(String(20), default='active', comment='状态：active, resolved, closed')
    severity_level = Column(Integer, default=1, comment='严重程度：1-5级')
    impact_score = Column(Float, default=0.0, comment='影响分数')
    heat_score = Column(Float, default=0.0, comment='热度分数')
    source = Column(String(100), nullable=True, comment='事件来源')
    location = Column(String(100), nullable=True, comment='事件地点')
    keywords = Column(Text, nullable=True, comment='关键词，JSON格式')
    tags = Column(Text, nullable=True, comment='标签，JSON格式')
    start_time = Column(DateTime, nullable=True, comment='事件开始时间')
    end_time = Column(DateTime, nullable=True, comment='事件结束时间')
    peak_time = Column(DateTime, nullable=True, comment='事件峰值时间')
    is_trending = Column(Boolean, default=False, comment='是否为趋势事件')
    is_verified = Column(Boolean, default=False, comment='是否已验证')
    related_events = Column(Text, nullable=True, comment='相关事件ID，JSON格式')
    sentiment_score = Column(Float, default=0.0, comment='情感分数')
    media_coverage = Column(Integer, default=0, comment='媒体覆盖数量')
    social_mentions = Column(Integer, default=0, comment='社交媒体提及数')
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')

    # 关联关系
    # event_news = relationship("EventNews", back_populates="event")  # 暂时注释掉
