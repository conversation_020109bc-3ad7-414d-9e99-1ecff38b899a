import request from '@/utils/request'

// 获取用户订单列表（分页）
export function getUserOrdersPage(params) {
  return request({
    url: '/bill/orders',
    method: 'get',
    params
  })
}

// 获取用户会员信息
export function getUserMembershipInfo(user_id) {
  return request({
    url: `/bill/user/${user_id}/membership`,
    method: 'get'
  })
}

// 获取所有套餐
export function getAllPackages() {
  return request({
    url: '/bill/packages',
    method: 'get'
  })
}

// 根据订单ID获取订单详情
export function getOrderById(order_id) {
  return request({
    url: `/bill/order/${order_id}`,
    method: 'get'
  })
}

// 根据用户ID获取订单列表
export function getUserOrdersByUserId(user_id, limit = 10) {
  return request({
    url: `/bill/user/${user_id}/orders`,
    method: 'get',
    params: { limit }
  })
}

// 获取账单管理页面的仪表板数据
export function getBillDashboardData(user_id) {
  return request({
    url: `/bill/dashboard/${user_id}`,
    method: 'get'
  })
}

// 获取格式化的状态映射
export function getFormattedStatus() {
  return request({
    url: '/bill/status/format',
    method: 'get'
  })
}

// 格式化金额显示
export function formatAmount(amount) {
  if (!amount) return '¥0.00'
  return `¥${parseFloat(amount).toFixed(2)}`
}

// 格式化日期显示
export function formatDate(dateString) {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 格式化日期时间显示
export function formatDateTime(dateString) {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 获取支付状态的显示文本和样式
export function getPaymentStatusDisplay(status) {
  const statusMap = {
    'unpaid': { text: '未支付', type: 'warning' },
    'paid': { text: '已支付', type: 'success' },
    'failed': { text: '支付失败', type: 'danger' },
    'refunded': { text: '已退款', type: 'info' }
  }
  return statusMap[status] || { text: status, type: 'default' }
}

// 获取订单状态的显示文本和样式
export function getOrderStatusDisplay(status) {
  const statusMap = {
    'pending': { text: '待处理', type: 'warning' },
    'processing': { text: '处理中', type: 'primary' },
    'completed': { text: '已完成', type: 'success' },
    'cancelled': { text: '已取消', type: 'info' },
    'closed': { text: '已关闭', type: 'danger' },
    // 兼容旧的状态
    'paid': { text: '已完成', type: 'success' },
    'refunded': { text: '已退款', type: 'danger' }
  }
  return statusMap[status] || { text: status, type: 'default' }
}

// 获取支付方式的显示文本
export function getPaymentMethodDisplay(method) {
  const methodMap = {
    'alipay': '支付宝',
    'wechat': '微信支付',
    'stripe': '信用卡支付'
  }
  return methodMap[method] || method
}
